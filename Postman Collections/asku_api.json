{"info": {"_postman_id": "fed78a4f-e582-4385-a5cd-af292f73e144", "name": "ASKU API 01.08.07", "description": "сделана ревизия 10.09.23", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json"}, "item": [{"name": "api", "item": [{"name": "v1", "item": [{"name": "asku", "item": [{"name": "initial_data", "item": [{"name": "{plantMissile}", "item": [{"name": "POST api/v1/asku/initial_data/{plantMissile}", "id": "7863651e-4046-4b3e-889a-cfc44d7aba8c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n    \"loadTemperature\": -999.9,\n    \"latitudeRad\": \"0.52546546\",\n    \"longitudeRad\": \"0.95515121\",\n    \"altitude\": 101.0,\n    \"inclinationAngle\": -80.0,\n    \"trajectory\": \"AERO_BALLISTIC\",\n    \"readiness\": \"BG_2A\",\n    \"isProDetected\": true,\n    \"missileOperatingMode\": \"BASE_SNS\",\n    \"validatedByTlc\": true,\n    \"tlCode\": \"sdaasdagsadaxxcvdawsdasfgasd\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/asku/initial_data/:plantMissile", "host": ["{{baseUrl}}"], "path": ["api", "v1", "asku", "initial_data", ":plantMissile"], "variable": [{"id": "2c0b4225-1559-4b97-8f4e-2632e0dad741", "key": "plantMissile", "value": "84"}]}}, "response": [{"id": "10569efa-a356-45c9-a1c0-094f0545556a", "name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n  \"loadTemperature\": \"<double>\",\n  \"latitudeRad\": \"<string>\",\n  \"longitudeRad\": \"<string>\",\n  \"altitude\": \"<double>\",\n  \"inclinationAngle\": \"<double>\",\n  \"trajectory\": \"AERO_BALLISTIC\",\n  \"readiness\": \"BG_2B\",\n  \"isProDetected\": \"<boolean>\",\n  \"missileOperatingMode\": \"CHPPP_1\",\n  \"validatedByTlc\": \"<boolean>\",\n  \"tlCode\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/asku/initial_data/:plantMissile", "host": ["{{baseUrl}}"], "path": ["api", "v1", "asku", "initial_data", ":plantMissile"], "variable": [{"key": "plantMissile"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "<string>"}]}], "id": "fb11c2d3-5c4a-4313-895b-be511989a61a"}], "id": "07495e04-09f2-41f9-a694-9785f2c1b9b6"}, {"name": "initial_data_with_ts", "item": [{"name": "commit", "item": [{"name": "POST api/v1/asku/initial_data_with_ts/commit", "id": "57be83a0-d750-4790-9fe5-a7164c5c8606", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n        \n        \"loadTemperature\": -999.9,\n        \"latitudeRad\": \"0.52546546\",\n        \"longitudeRad\": \"0.95515121\",\n        \"altitude\": 101.0,\n        \"inclinationAngle\": -80.0,\n        \"trajectory\": \"AERO_BALLISTIC\",\n        \"readiness\": \"BG_2A\",\n        \"isProDetected\": true,\n        \"missileOperatingMode\": \"BASE_SNS\",\n        \"validatedByTlc\": true,\n        \"tlCode\": \"sdaasdagsadaxxcvdawsdasfgasd\"\n    }", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/asku/initial_data_with_ts/commit?isLeft=false&initial_data_id=1021", "host": ["{{baseUrl}}"], "path": ["api", "v1", "asku", "initial_data_with_ts", "commit"], "query": [{"key": "isLeft", "value": "false", "description": "(Required) "}, {"key": "initial_data_id", "value": "1021", "description": "(Required) "}]}}, "response": [{"id": "62cedffb-aced-427e-9f32-5f4d575344c2", "name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n  \"loadTemperature\": \"<double>\",\n  \"latitudeRad\": \"<string>\",\n  \"longitudeRad\": \"<string>\",\n  \"altitude\": \"<double>\",\n  \"inclinationAngle\": \"<double>\",\n  \"trajectory\": \"AERO_BALLISTIC\",\n  \"readiness\": \"BG_2B\",\n  \"isProDetected\": \"<boolean>\",\n  \"missileOperatingMode\": \"CHPPP_1\",\n  \"validatedByTlc\": \"<boolean>\",\n  \"tlCode\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/asku/initial_data_with_ts/commit?isLeft=<boolean>&initial_data_id=<long>", "host": ["{{baseUrl}}"], "path": ["api", "v1", "asku", "initial_data_with_ts", "commit"], "query": [{"description": "(Required) ", "key": "isLeft", "value": "<boolean>"}, {"description": "(Required) ", "key": "initial_data_id", "value": "<long>"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "<string>"}]}], "id": "c0d12a33-c69b-4fa2-becd-4177d90a3dfa"}, {"name": "GET api/v1/asku/initial_data_with_ts", "id": "690ad88c-4932-4724-b53f-47a09027dd80", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/asku/initial_data_with_ts?isLeft=false", "host": ["{{baseUrl}}"], "path": ["api", "v1", "asku", "initial_data_with_ts"], "query": [{"key": "isLeft", "value": "false"}]}}, "response": [{"id": "42e78501-5e33-4850-ae48-b3f0e76e493a", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/asku/initial_data_with_ts?isLeft=true", "host": ["{{baseUrl}}"], "path": ["api", "v1", "asku", "initial_data_with_ts"], "query": [{"key": "isLeft", "value": "true"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"initialDataId\": \"<long>\",\n  \"launchInitialDataDto\": {\n    \"loadTemperature\": \"<double>\",\n    \"latitudeRad\": \"<string>\",\n    \"longitudeRad\": \"<string>\",\n    \"altitude\": \"<double>\",\n    \"inclinationAngle\": \"<double>\",\n    \"trajectory\": \"BALLISTIC\",\n    \"readiness\": \"BG_3_TO_BG_1\",\n    \"isProDetected\": \"<boolean>\",\n    \"missileOperatingMode\": \"CHPPP_1\",\n    \"validatedByTlc\": \"<boolean>\",\n    \"tlCode\": \"<string>\"\n  },\n  \"timeStamp\": \"<dateTime>\"\n}"}]}], "id": "8493e74e-33e4-481d-b770-1761212810d5"}, {"name": "rocket", "item": [{"name": "{temperature}", "item": [{"name": "POST api/v1/asku/rocket/{temperature}", "id": "29bec0ad-3a3a-4ed5-8cce-5d5b4dbfc442", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/asku/rocket/:temperature/?isLeft=0&", "host": ["{{baseUrl}}"], "path": ["api", "v1", "asku", "rocket", ":temperature", ""], "query": [{"key": "isLeft", "value": "0"}, {"key": "", "value": null}], "variable": [{"id": "b0d50ee3-8eff-4495-8048-3130e154a5b1", "key": "temperature", "value": "21.0"}]}}, "response": [{"id": "26651b84-0588-4a42-ba69-9773f0fa1cf4", "name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n  \"technicalCondition\": \"<boolean>\",\n  \"dateUseM\": \"<dateTime>\",\n  \"formData\": {\n    \"createdAt\": \"<dateTime>\",\n    \"plantMissile\": \"<string>\",\n    \"warhead\": \"MFBCH\",\n    \"gsnType\": \"value\",\n    \"alpType\": \"NO_ALP\",\n    \"isTelemetryIntegrated\": \"<boolean>\",\n    \"purposeType\": \"COMBAT\"\n  },\n  \"initialData\": {\n    \"loadTemperature\": \"<double>\",\n    \"latitudeRad\": \"<string>\",\n    \"longitudeRad\": \"<string>\",\n    \"altitude\": \"<double>\",\n    \"inclinationAngle\": \"<double>\",\n    \"trajectory\": \"AERO_BALLISTIC\",\n    \"readiness\": \"description\",\n    \"isProDetected\": \"<boolean>\",\n    \"missileOperatingMode\": \"KS_SNS\",\n    \"validatedByTlc\": \"<boolean>\",\n    \"tlCode\": \"<string>\"\n  },\n  \"initialDataTS\": \"<dateTime>\",\n  \"initialDataSource\": \"other\",\n  \"initialDataSourceDescription\": \"<string>\",\n  \"storedTlKeys\": [\n    \"<string>\",\n    \"<string>\"\n  ]\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/asku/rocket?isLeft=true", "host": ["{{baseUrl}}"], "path": ["api", "v1", "asku", "rocket"], "query": [{"key": "isLeft", "value": "true"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"plateNumber\": \"<string>\",\n  \"unitTitle\": \"<string>\",\n  \"startedDate\": \"<dateTime>\",\n  \"splReadiness\": \"BG_3\",\n  \"tpcLeft\": {\n    \"id\": \"<long>\",\n    \"fNumberTpc\": \"<string>\",\n    \"plantTpc\": \"<string>\",\n    \"dateReleaseM\": \"<dateTime>\",\n    \"tpcLoadState\": \"TPC_ONLY\"\n  },\n  \"tpcRight\": {\n    \"id\": \"<long>\",\n    \"fNumberTpc\": \"<string>\",\n    \"plantTpc\": \"<string>\",\n    \"dateReleaseM\": \"<dateTime>\",\n    \"tpcLoadState\": \"TPC_WITH_ROCKET\"\n  },\n  \"leftRocket\": {\n    \"technicalCondition\": \"<boolean>\",\n    \"dateUseM\": \"<dateTime>\",\n    \"formData\": {\n      \"createdAt\": \"<dateTime>\",\n      \"plantMissile\": \"<string>\",\n      \"warhead\": \"CBCH\",\n      \"gsnType\": \"value\",\n      \"alpType\": \"NO_ALP\",\n      \"isTelemetryIntegrated\": \"<boolean>\",\n      \"purposeType\": \"TEM\"\n    },\n    \"initialData\": {\n      \"loadTemperature\": \"<double>\",\n      \"latitudeRad\": \"<string>\",\n      \"longitudeRad\": \"<string>\",\n      \"altitude\": \"<double>\",\n      \"inclinationAngle\": \"<double>\",\n      \"trajectory\": \"BALLISTIC\",\n      \"readiness\": \"BG_4\",\n      \"isProDetected\": \"<boolean>\",\n      \"missileOperatingMode\": \"CHPPP_1\",\n      \"validatedByTlc\": \"<boolean>\",\n      \"tlCode\": \"<string>\"\n    },\n    \"initialDataTS\": \"<dateTime>\",\n    \"initialDataSource\": \"other\",\n    \"initialDataSourceDescription\": \"<string>\",\n    \"storedTlKeys\": [\n      \"<string>\",\n      \"<string>\"\n    ]\n  },\n  \"rightRocket\": {\n    \"technicalCondition\": \"<boolean>\",\n    \"dateUseM\": \"<dateTime>\",\n    \"formData\": {\n      \"createdAt\": \"<dateTime>\",\n      \"plantMissile\": \"<string>\",\n      \"warhead\": \"MFBCH\",\n      \"gsnType\": \"NO_GSN\",\n      \"alpType\": \"NO_ALP\",\n      \"isTelemetryIntegrated\": \"<boolean>\",\n      \"purposeType\": \"UNDEFINED\"\n    },\n    \"initialData\": {\n      \"loadTemperature\": \"<double>\",\n      \"latitudeRad\": \"<string>\",\n      \"longitudeRad\": \"<string>\",\n      \"altitude\": \"<double>\",\n      \"inclinationAngle\": \"<double>\",\n      \"trajectory\": \"AERO_BALLISTIC\",\n      \"readiness\": \"BG_3_TO_BG_1\",\n      \"isProDetected\": \"<boolean>\",\n      \"missileOperatingMode\": \"BASE_SNS\",\n      \"validatedByTlc\": \"<boolean>\",\n      \"tlCode\": \"<string>\"\n    },\n    \"initialDataTS\": \"<dateTime>\",\n    \"initialDataSource\": \"VOICE\",\n    \"initialDataSourceDescription\": \"<string>\",\n    \"storedTlKeys\": [\n      \"<string>\",\n      \"<string>\"\n    ]\n  },\n  \"position\": {\n    \"latitude\": \"<double>\",\n    \"longitude\": \"<double>\",\n    \"altitude\": \"<double>\",\n    \"datum\": \"NAD83\"\n  }\n}"}]}], "id": "a4fede02-0e1a-41b5-9ef7-f5b034d750c9"}, {"name": "{plant_missile}", "item": [{"name": "POST api/v1/asku/rocket/{plant_missile}/remove", "id": "c89212e4-a1b9-4f2d-8d08-d8386074280f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/asku/rocket/:plant_missile/remove?isLeft=false", "host": ["{{baseUrl}}"], "path": ["api", "v1", "asku", "rocket", ":plant_missile", "remove"], "query": [{"key": "isLeft", "value": "false", "type": "text"}], "variable": [{"key": "plant_missile", "value": ""}]}}, "response": [{"id": "4d61cc7a-b6aa-4db5-98fe-386b57fe1237", "name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n  \"technicalCondition\": \"<boolean>\",\n  \"dateUseM\": \"<dateTime>\",\n  \"formData\": {\n    \"createdAt\": \"<dateTime>\",\n    \"plantMissile\": \"<string>\",\n    \"warhead\": \"MFBCH\",\n    \"gsnType\": \"value\",\n    \"alpType\": \"NO_ALP\",\n    \"isTelemetryIntegrated\": \"<boolean>\",\n    \"purposeType\": \"COMBAT\"\n  },\n  \"initialData\": {\n    \"loadTemperature\": \"<double>\",\n    \"latitudeRad\": \"<string>\",\n    \"longitudeRad\": \"<string>\",\n    \"altitude\": \"<double>\",\n    \"inclinationAngle\": \"<double>\",\n    \"trajectory\": \"AERO_BALLISTIC\",\n    \"readiness\": \"description\",\n    \"isProDetected\": \"<boolean>\",\n    \"missileOperatingMode\": \"KS_SNS\",\n    \"validatedByTlc\": \"<boolean>\",\n    \"tlCode\": \"<string>\"\n  },\n  \"initialDataTS\": \"<dateTime>\",\n  \"initialDataSource\": \"other\",\n  \"initialDataSourceDescription\": \"<string>\",\n  \"storedTlKeys\": [\n    \"<string>\",\n    \"<string>\"\n  ]\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/asku/rocket?isLeft=true", "host": ["{{baseUrl}}"], "path": ["api", "v1", "asku", "rocket"], "query": [{"key": "isLeft", "value": "true"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"plateNumber\": \"<string>\",\n  \"unitTitle\": \"<string>\",\n  \"startedDate\": \"<dateTime>\",\n  \"splReadiness\": \"BG_3\",\n  \"tpcLeft\": {\n    \"id\": \"<long>\",\n    \"fNumberTpc\": \"<string>\",\n    \"plantTpc\": \"<string>\",\n    \"dateReleaseM\": \"<dateTime>\",\n    \"tpcLoadState\": \"TPC_ONLY\"\n  },\n  \"tpcRight\": {\n    \"id\": \"<long>\",\n    \"fNumberTpc\": \"<string>\",\n    \"plantTpc\": \"<string>\",\n    \"dateReleaseM\": \"<dateTime>\",\n    \"tpcLoadState\": \"TPC_WITH_ROCKET\"\n  },\n  \"leftRocket\": {\n    \"technicalCondition\": \"<boolean>\",\n    \"dateUseM\": \"<dateTime>\",\n    \"formData\": {\n      \"createdAt\": \"<dateTime>\",\n      \"plantMissile\": \"<string>\",\n      \"warhead\": \"CBCH\",\n      \"gsnType\": \"value\",\n      \"alpType\": \"NO_ALP\",\n      \"isTelemetryIntegrated\": \"<boolean>\",\n      \"purposeType\": \"TEM\"\n    },\n    \"initialData\": {\n      \"loadTemperature\": \"<double>\",\n      \"latitudeRad\": \"<string>\",\n      \"longitudeRad\": \"<string>\",\n      \"altitude\": \"<double>\",\n      \"inclinationAngle\": \"<double>\",\n      \"trajectory\": \"BALLISTIC\",\n      \"readiness\": \"BG_4\",\n      \"isProDetected\": \"<boolean>\",\n      \"missileOperatingMode\": \"CHPPP_1\",\n      \"validatedByTlc\": \"<boolean>\",\n      \"tlCode\": \"<string>\"\n    },\n    \"initialDataTS\": \"<dateTime>\",\n    \"initialDataSource\": \"other\",\n    \"initialDataSourceDescription\": \"<string>\",\n    \"storedTlKeys\": [\n      \"<string>\",\n      \"<string>\"\n    ]\n  },\n  \"rightRocket\": {\n    \"technicalCondition\": \"<boolean>\",\n    \"dateUseM\": \"<dateTime>\",\n    \"formData\": {\n      \"createdAt\": \"<dateTime>\",\n      \"plantMissile\": \"<string>\",\n      \"warhead\": \"MFBCH\",\n      \"gsnType\": \"NO_GSN\",\n      \"alpType\": \"NO_ALP\",\n      \"isTelemetryIntegrated\": \"<boolean>\",\n      \"purposeType\": \"UNDEFINED\"\n    },\n    \"initialData\": {\n      \"loadTemperature\": \"<double>\",\n      \"latitudeRad\": \"<string>\",\n      \"longitudeRad\": \"<string>\",\n      \"altitude\": \"<double>\",\n      \"inclinationAngle\": \"<double>\",\n      \"trajectory\": \"AERO_BALLISTIC\",\n      \"readiness\": \"BG_3_TO_BG_1\",\n      \"isProDetected\": \"<boolean>\",\n      \"missileOperatingMode\": \"BASE_SNS\",\n      \"validatedByTlc\": \"<boolean>\",\n      \"tlCode\": \"<string>\"\n    },\n    \"initialDataTS\": \"<dateTime>\",\n    \"initialDataSource\": \"VOICE\",\n    \"initialDataSourceDescription\": \"<string>\",\n    \"storedTlKeys\": [\n      \"<string>\",\n      \"<string>\"\n    ]\n  },\n  \"position\": {\n    \"latitude\": \"<double>\",\n    \"longitude\": \"<double>\",\n    \"altitude\": \"<double>\",\n    \"datum\": \"NAD83\"\n  }\n}"}]}], "id": "84b31071-d92f-4de1-ab29-0a575330f636"}, {"name": "GET api/v1/asku/rocket", "id": "463983f2-a44b-4ee6-80cf-72f36d9ad599", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/asku/rocket?isLeft=0", "host": ["{{baseUrl}}"], "path": ["api", "v1", "asku", "rocket"], "query": [{"key": "isLeft", "value": "0"}]}}, "response": [{"id": "5ec1dae5-5814-4143-b965-55198a888883", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/asku/rocket?isLeft=true", "host": ["{{baseUrl}}"], "path": ["api", "v1", "asku", "rocket"], "query": [{"key": "isLeft", "value": "true"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"technicalCondition\": \"<boolean>\",\n  \"dateUseM\": \"<dateTime>\",\n  \"formData\": {\n    \"createdAt\": \"<dateTime>\",\n    \"plantMissile\": \"<string>\",\n    \"warhead\": \"MFBCH\",\n    \"gsnType\": \"value\",\n    \"alpType\": \"NO_ALP\",\n    \"isTelemetryIntegrated\": \"<boolean>\",\n    \"purposeType\": \"COMBAT\"\n  },\n  \"initialData\": {\n    \"loadTemperature\": \"<double>\",\n    \"latitudeRad\": \"<string>\",\n    \"longitudeRad\": \"<string>\",\n    \"altitude\": \"<double>\",\n    \"inclinationAngle\": \"<double>\",\n    \"trajectory\": \"AERO_BALLISTIC\",\n    \"readiness\": \"description\",\n    \"isProDetected\": \"<boolean>\",\n    \"missileOperatingMode\": \"KS_SNS\",\n    \"validatedByTlc\": \"<boolean>\",\n    \"tlCode\": \"<string>\"\n  },\n  \"initialDataTS\": \"<dateTime>\",\n  \"initialDataSource\": \"other\",\n  \"initialDataSourceDescription\": \"<string>\",\n  \"storedTlKeys\": [\n    \"<string>\",\n    \"<string>\"\n  ]\n}"}]}, {"name": "POST api/v1/asku/rocket", "id": "d8262b02-e1ab-45da-b0eb-969429b9cc59", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n    \"technicalCondition\": true,\n    \"dateUseM\": null,\n    \"formData\": {\n        // \"createdAt\": \"2023-08-29T18:33:00\",\n        \"plantMissile\": \"83\",\n        \"warhead\": \"CBCH\",\n        \"gsnType\": \"NO_GSN\",\n        \"alpType\": \"NO_ALP\",\n        \"isTelemetryIntegrated\": true,\n        \"purposeType\": \"COMBAT\"\n    },\n    \"initialData\": null,\n    \"initialDataTS\": null,\n    \"initialDataSource\": \"MSG\",\n    \"initialDataSourceDescription\": \"КМУ Лесь Підервянский\",\n    \"storedTlKeys\": [\"123\",\"123---sdfsdfsfd1465ssdfsd====\"]\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/asku/rocket?isLeft=1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "asku", "rocket"], "query": [{"key": "isLeft", "value": "1"}]}}, "response": [{"id": "96c5afa8-89fd-4247-9d41-3c46a63d6a71", "name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n  \"technicalCondition\": \"<boolean>\",\n  \"dateUseM\": \"<dateTime>\",\n  \"formData\": {\n    \"createdAt\": \"<dateTime>\",\n    \"plantMissile\": \"<string>\",\n    \"warhead\": \"MFBCH\",\n    \"gsnType\": \"value\",\n    \"alpType\": \"NO_ALP\",\n    \"isTelemetryIntegrated\": \"<boolean>\",\n    \"purposeType\": \"COMBAT\"\n  },\n  \"initialData\": {\n    \"loadTemperature\": \"<double>\",\n    \"latitudeRad\": \"<string>\",\n    \"longitudeRad\": \"<string>\",\n    \"altitude\": \"<double>\",\n    \"inclinationAngle\": \"<double>\",\n    \"trajectory\": \"AERO_BALLISTIC\",\n    \"readiness\": \"description\",\n    \"isProDetected\": \"<boolean>\",\n    \"missileOperatingMode\": \"KS_SNS\",\n    \"validatedByTlc\": \"<boolean>\",\n    \"tlCode\": \"<string>\"\n  },\n  \"initialDataTS\": \"<dateTime>\",\n  \"initialDataSource\": \"other\",\n  \"initialDataSourceDescription\": \"<string>\",\n  \"storedTlKeys\": [\n    \"<string>\",\n    \"<string>\"\n  ]\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/asku/rocket?isLeft=true", "host": ["{{baseUrl}}"], "path": ["api", "v1", "asku", "rocket"], "query": [{"key": "isLeft", "value": "true"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"plateNumber\": \"<string>\",\n  \"unitTitle\": \"<string>\",\n  \"startedDate\": \"<dateTime>\",\n  \"splReadiness\": \"BG_3\",\n  \"tpcLeft\": {\n    \"id\": \"<long>\",\n    \"fNumberTpc\": \"<string>\",\n    \"plantTpc\": \"<string>\",\n    \"dateReleaseM\": \"<dateTime>\",\n    \"tpcLoadState\": \"TPC_ONLY\"\n  },\n  \"tpcRight\": {\n    \"id\": \"<long>\",\n    \"fNumberTpc\": \"<string>\",\n    \"plantTpc\": \"<string>\",\n    \"dateReleaseM\": \"<dateTime>\",\n    \"tpcLoadState\": \"TPC_WITH_ROCKET\"\n  },\n  \"leftRocket\": {\n    \"technicalCondition\": \"<boolean>\",\n    \"dateUseM\": \"<dateTime>\",\n    \"formData\": {\n      \"createdAt\": \"<dateTime>\",\n      \"plantMissile\": \"<string>\",\n      \"warhead\": \"CBCH\",\n      \"gsnType\": \"value\",\n      \"alpType\": \"NO_ALP\",\n      \"isTelemetryIntegrated\": \"<boolean>\",\n      \"purposeType\": \"TEM\"\n    },\n    \"initialData\": {\n      \"loadTemperature\": \"<double>\",\n      \"latitudeRad\": \"<string>\",\n      \"longitudeRad\": \"<string>\",\n      \"altitude\": \"<double>\",\n      \"inclinationAngle\": \"<double>\",\n      \"trajectory\": \"BALLISTIC\",\n      \"readiness\": \"BG_4\",\n      \"isProDetected\": \"<boolean>\",\n      \"missileOperatingMode\": \"CHPPP_1\",\n      \"validatedByTlc\": \"<boolean>\",\n      \"tlCode\": \"<string>\"\n    },\n    \"initialDataTS\": \"<dateTime>\",\n    \"initialDataSource\": \"other\",\n    \"initialDataSourceDescription\": \"<string>\",\n    \"storedTlKeys\": [\n      \"<string>\",\n      \"<string>\"\n    ]\n  },\n  \"rightRocket\": {\n    \"technicalCondition\": \"<boolean>\",\n    \"dateUseM\": \"<dateTime>\",\n    \"formData\": {\n      \"createdAt\": \"<dateTime>\",\n      \"plantMissile\": \"<string>\",\n      \"warhead\": \"MFBCH\",\n      \"gsnType\": \"NO_GSN\",\n      \"alpType\": \"NO_ALP\",\n      \"isTelemetryIntegrated\": \"<boolean>\",\n      \"purposeType\": \"UNDEFINED\"\n    },\n    \"initialData\": {\n      \"loadTemperature\": \"<double>\",\n      \"latitudeRad\": \"<string>\",\n      \"longitudeRad\": \"<string>\",\n      \"altitude\": \"<double>\",\n      \"inclinationAngle\": \"<double>\",\n      \"trajectory\": \"AERO_BALLISTIC\",\n      \"readiness\": \"BG_3_TO_BG_1\",\n      \"isProDetected\": \"<boolean>\",\n      \"missileOperatingMode\": \"BASE_SNS\",\n      \"validatedByTlc\": \"<boolean>\",\n      \"tlCode\": \"<string>\"\n    },\n    \"initialDataTS\": \"<dateTime>\",\n    \"initialDataSource\": \"VOICE\",\n    \"initialDataSourceDescription\": \"<string>\",\n    \"storedTlKeys\": [\n      \"<string>\",\n      \"<string>\"\n    ]\n  },\n  \"position\": {\n    \"latitude\": \"<double>\",\n    \"longitude\": \"<double>\",\n    \"altitude\": \"<double>\",\n    \"datum\": \"NAD83\"\n  }\n}"}]}], "id": "16362e7f-c16f-4092-b8fb-a70032e05013"}, {"name": "rocket_form_data", "item": [{"name": "GET api/v1/asku/rocket_form_data", "id": "df254f46-97f0-4189-82e3-27520944f33e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/asku/rocket_form_data?isLeft=true", "host": ["{{baseUrl}}"], "path": ["api", "v1", "asku", "rocket_form_data"], "query": [{"key": "isLeft", "value": "true"}]}}, "response": [{"id": "b01a39d0-fca5-4a1a-92a7-9160db21d98f", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/asku/rocket_form_data?isLeft=true", "host": ["{{baseUrl}}"], "path": ["api", "v1", "asku", "rocket_form_data"], "query": [{"key": "isLeft", "value": "true"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"createdAt\": \"<dateTime>\",\n  \"plantMissile\": \"<string>\",\n  \"warhead\": \"UNDEFINED\",\n  \"gsnType\": \"UNDEFINED\",\n  \"alpType\": \"value\",\n  \"isTelemetryIntegrated\": \"<boolean>\",\n  \"purposeType\": \"value\"\n}"}]}], "id": "34c19336-ead1-4cc2-a5d9-752fec141452"}, {"name": "spl_readiness", "item": [{"name": "{readiness}", "item": [{"name": "POST api/v1/asku/spl_readiness/{readiness}", "id": "a391a340-241a-4ccb-b2e4-3782db2ccae0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/asku/spl_readiness/:readiness", "host": ["{{baseUrl}}"], "path": ["api", "v1", "asku", "spl_readiness", ":readiness"], "variable": [{"key": "readiness", "value": "BG_1"}]}}, "response": [{"id": "bdd9e085-a3f5-427c-9f06-190846f9c423", "name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/asku/spl_readiness/:readiness", "host": ["{{baseUrl}}"], "path": ["api", "v1", "asku", "spl_readiness", ":readiness"], "variable": [{"key": "readiness"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"plateNumber\": \"<string>\",\n  \"unitTitle\": \"<string>\",\n  \"startedDate\": \"<dateTime>\",\n  \"splReadiness\": \"BG_3\",\n  \"tpcLeft\": {\n    \"id\": \"<long>\",\n    \"fNumberTpc\": \"<string>\",\n    \"plantTpc\": \"<string>\",\n    \"dateReleaseM\": \"<dateTime>\",\n    \"tpcLoadState\": \"TPC_ONLY\"\n  },\n  \"tpcRight\": {\n    \"id\": \"<long>\",\n    \"fNumberTpc\": \"<string>\",\n    \"plantTpc\": \"<string>\",\n    \"dateReleaseM\": \"<dateTime>\",\n    \"tpcLoadState\": \"TPC_WITH_ROCKET\"\n  },\n  \"leftRocket\": {\n    \"technicalCondition\": \"<boolean>\",\n    \"dateUseM\": \"<dateTime>\",\n    \"formData\": {\n      \"createdAt\": \"<dateTime>\",\n      \"plantMissile\": \"<string>\",\n      \"warhead\": \"CBCH\",\n      \"gsnType\": \"value\",\n      \"alpType\": \"NO_ALP\",\n      \"isTelemetryIntegrated\": \"<boolean>\",\n      \"purposeType\": \"TEM\"\n    },\n    \"initialData\": {\n      \"loadTemperature\": \"<double>\",\n      \"latitudeRad\": \"<string>\",\n      \"longitudeRad\": \"<string>\",\n      \"altitude\": \"<double>\",\n      \"inclinationAngle\": \"<double>\",\n      \"trajectory\": \"BALLISTIC\",\n      \"readiness\": \"BG_4\",\n      \"isProDetected\": \"<boolean>\",\n      \"missileOperatingMode\": \"CHPPP_1\",\n      \"validatedByTlc\": \"<boolean>\",\n      \"tlCode\": \"<string>\"\n    },\n    \"initialDataTS\": \"<dateTime>\",\n    \"initialDataSource\": \"other\",\n    \"initialDataSourceDescription\": \"<string>\",\n    \"storedTlKeys\": [\n      \"<string>\",\n      \"<string>\"\n    ]\n  },\n  \"rightRocket\": {\n    \"technicalCondition\": \"<boolean>\",\n    \"dateUseM\": \"<dateTime>\",\n    \"formData\": {\n      \"createdAt\": \"<dateTime>\",\n      \"plantMissile\": \"<string>\",\n      \"warhead\": \"MFBCH\",\n      \"gsnType\": \"NO_GSN\",\n      \"alpType\": \"NO_ALP\",\n      \"isTelemetryIntegrated\": \"<boolean>\",\n      \"purposeType\": \"UNDEFINED\"\n    },\n    \"initialData\": {\n      \"loadTemperature\": \"<double>\",\n      \"latitudeRad\": \"<string>\",\n      \"longitudeRad\": \"<string>\",\n      \"altitude\": \"<double>\",\n      \"inclinationAngle\": \"<double>\",\n      \"trajectory\": \"AERO_BALLISTIC\",\n      \"readiness\": \"BG_3_TO_BG_1\",\n      \"isProDetected\": \"<boolean>\",\n      \"missileOperatingMode\": \"BASE_SNS\",\n      \"validatedByTlc\": \"<boolean>\",\n      \"tlCode\": \"<string>\"\n    },\n    \"initialDataTS\": \"<dateTime>\",\n    \"initialDataSource\": \"VOICE\",\n    \"initialDataSourceDescription\": \"<string>\",\n    \"storedTlKeys\": [\n      \"<string>\",\n      \"<string>\"\n    ]\n  },\n  \"position\": {\n    \"latitude\": \"<double>\",\n    \"longitude\": \"<double>\",\n    \"altitude\": \"<double>\",\n    \"datum\": \"NAD83\"\n  }\n}"}]}], "id": "e504c7b3-1508-4fd6-9ac1-db8c9995947f"}, {"name": "GET api/v1/asku/spl_readiness", "id": "f334579d-9ddb-4cb7-b67e-dc9ff8cb6274", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/asku/spl_readiness"}, "response": [{"id": "4f71c9c1-f823-4f68-a76c-796b1a293d89", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/asku/spl_readiness"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "UNDEFINED"}]}], "id": "ed0d0f57-1935-4dba-95a6-9931bf7b1da3"}, {"name": "state", "item": [{"name": "GET api/v1/asku/state", "id": "a5e608c4-b217-494f-8237-2d8724f5cd4d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/asku/state"}, "response": [{"id": "c8e5fbc1-60c2-4161-96b6-1422d36c32fe", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/asku/state"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"readiness\": \"BG_3_TO_BG_1\",\n  \"leftRocketCondition\": \"<boolean>\",\n  \"rightRocketCondition\": \"<boolean>\"\n}"}]}], "id": "81511187-bc19-48eb-b4ac-e18049be4268"}, {"name": "tlc", "item": [{"name": "open_keys", "item": [{"name": "GET api/v1/asku/tlc/open_keys", "id": "31609b85-39e2-44f8-a6c6-78c9b68fdaf7", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/asku/tlc/open_keys?plantMissile=1Л", "host": ["{{baseUrl}}"], "path": ["api", "v1", "asku", "tlc", "open_keys"], "query": [{"key": "plantMissile", "value": "1Л", "description": "(Required) "}]}}, "response": [{"id": "863dc32a-4e35-46fe-96f2-fd0fc3c1ccbf", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/asku/tlc/open_keys?plantMissile=<string>", "host": ["{{baseUrl}}"], "path": ["api", "v1", "asku", "tlc", "open_keys"], "query": [{"description": "(Required) ", "key": "plantMissile", "value": "<string>"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "[\n  \"<string>\",\n  \"<string>\"\n]"}]}, {"name": "POST api/v1/asku/tlc/open_keys", "id": "c41f9631-887f-4c98-9a52-092c6546da2c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "[\n  \"werwer23443dasdasdasdasdasd===\",\n  \"0123465798/*--!@#$%^&fsf3wffffffffffffffff===\"\n]", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/asku/tlc/open_keys?plantMissile=1Л", "host": ["{{baseUrl}}"], "path": ["api", "v1", "asku", "tlc", "open_keys"], "query": [{"key": "plantMissile", "value": "1Л", "description": "(Required) "}]}}, "response": [{"id": "9b598f7b-159b-4e59-a619-42b5354effb1", "name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "[\n  \"<string>\",\n  \"<string>\"\n]", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/asku/tlc/open_keys?plantMissile=<string>", "host": ["{{baseUrl}}"], "path": ["api", "v1", "asku", "tlc", "open_keys"], "query": [{"description": "(Required) ", "key": "plantMissile", "value": "<string>"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"technicalCondition\": \"<boolean>\",\n  \"dateUseM\": \"<dateTime>\",\n  \"formData\": {\n    \"createdAt\": \"<dateTime>\",\n    \"plantMissile\": \"<string>\",\n    \"warhead\": \"MFBCH\",\n    \"gsnType\": \"value\",\n    \"alpType\": \"NO_ALP\",\n    \"isTelemetryIntegrated\": \"<boolean>\",\n    \"purposeType\": \"COMBAT\"\n  },\n  \"initialData\": {\n    \"loadTemperature\": \"<double>\",\n    \"latitudeRad\": \"<string>\",\n    \"longitudeRad\": \"<string>\",\n    \"altitude\": \"<double>\",\n    \"inclinationAngle\": \"<double>\",\n    \"trajectory\": \"AERO_BALLISTIC\",\n    \"readiness\": \"description\",\n    \"isProDetected\": \"<boolean>\",\n    \"missileOperatingMode\": \"KS_SNS\",\n    \"validatedByTlc\": \"<boolean>\",\n    \"tlCode\": \"<string>\"\n  },\n  \"initialDataTS\": \"<dateTime>\",\n  \"initialDataSource\": \"other\",\n  \"initialDataSourceDescription\": \"<string>\",\n  \"storedTlKeys\": [\n    \"<string>\",\n    \"<string>\"\n  ]\n}"}]}], "id": "bc6ecb52-2d8a-47e1-b0d6-c8346635343f"}], "id": "c9110145-cbac-4162-9344-e434f28d77eb"}, {"name": "plc", "item": [{"name": "message", "item": [{"name": "POST api/v1/asku/plc/message", "id": "86e40943-624f-49a3-8659-75b3532c48d1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n  \"msgType\": \"INFO\",\n  \"systemType\": \"TPC\",\n  \"alias\": \"<string>\",\n  \"payload\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/asku/plc/message"}, "response": [{"id": "d169b195-08f0-41ef-bd16-221d1cd84f45", "name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n  \"msgType\": \"INFO\",\n  \"systemType\": \"TPC\",\n  \"alias\": \"<string>\",\n  \"payload\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/asku/plc/message"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "<string>"}, {"id": "355941d9-73ff-46eb-ae28-de882911863a", "name": "Bad Request. Request body constraints: @NotNull PlcMsgType msgType, AdjacentSystemType systemType, String alias, @NotBlank String payload", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n  \"msgType\": \"INFO\",\n  \"systemType\": \"TPC\",\n  \"alias\": \"<string>\",\n  \"payload\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/asku/plc/message"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "<string>"}, {"id": "d5656d4c-1b58-4e6f-88d8-1254ce7f874f", "name": "Not Found", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n  \"msgType\": \"INFO\",\n  \"systemType\": \"TPC\",\n  \"alias\": \"<string>\",\n  \"payload\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/asku/plc/message"}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "<string>"}]}], "id": "93a6a407-4a11-4964-b158-bc23ffd784fe"}, {"name": "POST api/v1/asku/plc/tl_keys", "id": "fa5ec226-c587-4889-9ec9-bcd0ce4bd913", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n  \"msgType\": \"INFO\",\n  \"systemType\": \"TPC\",\n  \"alias\": \"<string>\",\n  \"payload\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/asku/plc/tl_keys"}, "response": [{"id": "b12c87e5-cbfa-4d7f-b8eb-fc92ea14b9f4", "name": "POST api/v1/asku/plc/tl_keys", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n    \"version\": 1,\n    \"keys\": [\"sdfsdfsdfsdf\",\"2lk3j4lk23j4lk23j4lk23j54lk\",\"ssdlkshdlkjxhcvjlsdv\",\"asfasdasd22222\"]\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/asku/plc/tl_keys"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Mon, 02 Oct 2023 11:20:12 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "responseTime": null, "body": "{\n    \"version\": 2,\n    \"keys\": [\n        \"sdfsdfsdfsdf\",\n        \"2lk3j4lk23j4lk23j4lk23j54lk\",\n        \"ssdlkshdlkjxhcvjlsdv\",\n        \"asfasdasd22222\"\n    ]\n}"}]}, {"name": "POST api/v1/asku/plc/tl_keys Copy", "id": "078d9519-4c25-4aa0-9661-dffa4fd329bc", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/asku/plc/tl_keys"}, "response": [{"id": "8dc1e8cb-6d67-4071-bf37-fc6f8b2d749c", "name": "POST api/v1/asku/plc/tl_keys Copy", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/asku/plc/tl_keys"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Mon, 02 Oct 2023 11:20:18 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "responseTime": null, "body": "{\n    \"version\": 2,\n    \"keys\": [\n        \"sdfsdfsdfsdf\",\n        \"2lk3j4lk23j4lk23j4lk23j54lk\",\n        \"ssdlkshdlkjxhcvjlsdv\",\n        \"asfasdasd22222\"\n    ]\n}"}]}], "id": "5ef2a418-33ef-4402-a303-f28ea3e28297"}, {"name": "GET api/v1/asku/", "id": "b738b3a0-7fa2-4250-a4a4-85fade092736", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/asku/"}, "response": [{"id": "8e2cf7cc-9e60-4de2-9af9-3e1074e0808c", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/asku/"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"plateNumber\": \"<string>\",\n  \"unitTitle\": \"<string>\",\n  \"startedDate\": \"<dateTime>\",\n  \"splReadiness\": \"BG_3\",\n  \"tpcLeft\": {\n    \"id\": \"<long>\",\n    \"fNumberTpc\": \"<string>\",\n    \"plantTpc\": \"<string>\",\n    \"dateReleaseM\": \"<dateTime>\",\n    \"tpcLoadState\": \"TPC_ONLY\"\n  },\n  \"tpcRight\": {\n    \"id\": \"<long>\",\n    \"fNumberTpc\": \"<string>\",\n    \"plantTpc\": \"<string>\",\n    \"dateReleaseM\": \"<dateTime>\",\n    \"tpcLoadState\": \"TPC_WITH_ROCKET\"\n  },\n  \"leftRocket\": {\n    \"technicalCondition\": \"<boolean>\",\n    \"dateUseM\": \"<dateTime>\",\n    \"formData\": {\n      \"createdAt\": \"<dateTime>\",\n      \"plantMissile\": \"<string>\",\n      \"warhead\": \"CBCH\",\n      \"gsnType\": \"value\",\n      \"alpType\": \"NO_ALP\",\n      \"isTelemetryIntegrated\": \"<boolean>\",\n      \"purposeType\": \"TEM\"\n    },\n    \"initialData\": {\n      \"loadTemperature\": \"<double>\",\n      \"latitudeRad\": \"<string>\",\n      \"longitudeRad\": \"<string>\",\n      \"altitude\": \"<double>\",\n      \"inclinationAngle\": \"<double>\",\n      \"trajectory\": \"BALLISTIC\",\n      \"readiness\": \"BG_4\",\n      \"isProDetected\": \"<boolean>\",\n      \"missileOperatingMode\": \"CHPPP_1\",\n      \"validatedByTlc\": \"<boolean>\",\n      \"tlCode\": \"<string>\"\n    },\n    \"initialDataTS\": \"<dateTime>\",\n    \"initialDataSource\": \"other\",\n    \"initialDataSourceDescription\": \"<string>\",\n    \"storedTlKeys\": [\n      \"<string>\",\n      \"<string>\"\n    ]\n  },\n  \"rightRocket\": {\n    \"technicalCondition\": \"<boolean>\",\n    \"dateUseM\": \"<dateTime>\",\n    \"formData\": {\n      \"createdAt\": \"<dateTime>\",\n      \"plantMissile\": \"<string>\",\n      \"warhead\": \"MFBCH\",\n      \"gsnType\": \"NO_GSN\",\n      \"alpType\": \"NO_ALP\",\n      \"isTelemetryIntegrated\": \"<boolean>\",\n      \"purposeType\": \"UNDEFINED\"\n    },\n    \"initialData\": {\n      \"loadTemperature\": \"<double>\",\n      \"latitudeRad\": \"<string>\",\n      \"longitudeRad\": \"<string>\",\n      \"altitude\": \"<double>\",\n      \"inclinationAngle\": \"<double>\",\n      \"trajectory\": \"AERO_BALLISTIC\",\n      \"readiness\": \"BG_3_TO_BG_1\",\n      \"isProDetected\": \"<boolean>\",\n      \"missileOperatingMode\": \"BASE_SNS\",\n      \"validatedByTlc\": \"<boolean>\",\n      \"tlCode\": \"<string>\"\n    },\n    \"initialDataTS\": \"<dateTime>\",\n    \"initialDataSource\": \"VOICE\",\n    \"initialDataSourceDescription\": \"<string>\",\n    \"storedTlKeys\": [\n      \"<string>\",\n      \"<string>\"\n    ]\n  },\n  \"position\": {\n    \"latitude\": \"<double>\",\n    \"longitude\": \"<double>\",\n    \"altitude\": \"<double>\",\n    \"datum\": \"NAD83\"\n  }\n}"}]}], "id": "ee721c56-fa87-497a-a81b-3feed6b3d6bd"}, {"name": "adjacent-systems", "item": [{"name": "sae", "item": [{"name": "command", "item": [{"name": "{command_id}", "item": [{"name": "commit", "item": [{"name": "POST api/v1/adjacent-systems/sae/command/{command_id}/commit", "id": "c7619d2c-ed5f-47c7-8c21-84260b302f4a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/sae/command/:command_id/commit", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "sae", "command", ":command_id", "commit"], "variable": [{"key": "command_id", "value": "1005"}]}}, "response": [{"id": "d0ad7387-70b8-477c-8d25-35ba017be54f", "name": "OK", "originalRequest": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/sae/command/commit/:command_id", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "sae", "command", "commit", ":command_id"], "variable": [{"key": "command_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [], "cookie": [], "responseTime": null, "body": ""}]}], "id": "7265afe7-d144-4a83-8845-5eb21f4090a3"}, {"name": "remove", "item": [{"name": "POST api/v1/adjacent-systems/sae/command/{command_id}/remove", "id": "21693245-efae-4291-a23a-728018502f90", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/sae/command/:command_id/remove", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "sae", "command", ":command_id", "remove"], "variable": [{"key": "command_id", "value": "1008"}]}}, "response": [{"id": "a97a6552-ef47-4b3d-b42b-a0db3ac5075f", "name": "OK", "originalRequest": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/sae/command/commit/:command_id", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "sae", "command", "commit", ":command_id"], "variable": [{"key": "command_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [], "cookie": [], "responseTime": null, "body": ""}]}], "id": "a08c6063-78d2-41cf-abc6-992f6f7c2c1b"}], "id": "6adb6a02-4f84-4efd-82ce-c0f1b634d047"}, {"name": "GET api/v1/adjacent-systems/sae/command", "id": "0ee5c8e0-09d8-43cb-8aa9-caf0d99d7b5f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/sae/command"}, "response": [{"id": "2f399f3d-41c7-44fb-8d08-62412ee0b741", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/sae/command"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"id\": \"<long>\",\n  \"command\": \"<string>\",\n  \"caption\": \"<string>\",\n  \"adjacentSystem\": \"TPC\",\n  \"generationTime\": \"<dateTime>\",\n  \"executionTime\": \"<dateTime>\"\n}"}]}], "id": "be669db2-83df-436a-b7d9-1b808b16b2c9"}, {"name": "status", "item": [{"name": "connection", "item": [{"name": "GET api/v1/adjacent-systems/sae/status/connection", "id": "c106df08-dd5b-4b4d-8725-27bb9f11ccb6", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/sae/status/connection"}, "response": [{"id": "559f9807-7e8e-496f-8770-8eb73d9818db", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/sae/status/connection"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "<boolean>"}]}], "id": "8ed5f823-edea-4049-9a94-8da615a1ee8b"}, {"name": "feeders", "item": [{"name": "{feeder_name}", "item": [{"name": "GET api/v1/adjacent-systems/sae/status/feeders/{feeder_name}", "id": "4d33d8c0-99a6-4f6a-ad56-309344759dfb", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/sae/status/feeders/:feeder_name", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "sae", "status", "feeders", ":feeder_name"], "variable": [{"id": "9222581c-047a-4287-ad5b-72c68e79574f", "key": "feeder_name", "value": "Feeder6"}]}}, "response": [{"id": "eea3a2a9-256b-4178-908b-ec126cf17c5d", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/sae/status/feeders/:feeder_name", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "sae", "status", "feeders", ":feeder_name"], "variable": [{"key": "feeder_name"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "<string>"}]}, {"name": "PUT api/v1/adjacent-systems/sae/status/feeders/{feeder_name}", "id": "5aefdb65-fbec-46ff-9c35-d731e8fca88d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/sae/status/feeders/:feeder_name?feeder_status=OFF", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "sae", "status", "feeders", ":feeder_name"], "query": [{"key": "feeder_status", "value": "OFF", "description": "(Required) "}], "variable": [{"id": "fb29b5cd-b269-4396-9945-c7a585fde3b0", "key": "feeder_name", "value": "Feeder5"}]}}, "response": [{"id": "40da589b-8875-4694-8f8a-72ca4d017ce5", "name": "OK", "originalRequest": {"method": "PUT", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/sae/status/feeders/:feeder_name?feeder_status=sourceName", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "sae", "status", "feeders", ":feeder_name"], "query": [{"description": "(Required) ", "key": "feeder_status", "value": "sourceName"}], "variable": [{"key": "feeder_name"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "<string>"}]}], "id": "80b15906-f322-46d9-9865-b56b79d5a3b0"}, {"name": "GET api/v1/adjacent-systems/sae/status/feeders", "id": "f49a378d-8c6c-47fd-9567-a85324db923c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/sae/status/feeders"}, "response": [{"id": "7dcfe500-7b47-41ec-b65b-6d1c9ca4fab9", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/sae/status/feeders"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "<string>"}]}, {"name": "PUT api/v1/adjacent-systems/sae/status/feeders", "id": "b92950ac-2e93-4b42-8bf9-3c1c8223f6a9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\r\n    \"Feeder6\": \"ON\",\r\n    \"Feeder5\": \"OFF\",\r\n    \"Feeder4\": \"UNDEFINED\",\r\n    \"Feeder3\": \"ERROR\",\r\n    \"FeederNppa1\": \"ON\",\r\n    \"Feeder2\": \"ON\",\r\n    \"Feeder1\": \"ON\"\r\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/adjacent-systems/sae/status/feeders"}, "response": [{"id": "671a06c4-8654-4820-9d2e-b30e8975aebe", "name": "OK", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "<string>", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/adjacent-systems/sae/status/feeders"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"saeStatus\": \"OK\",\n  \"readiness\": \"ERROR\",\n  \"energizedByAdj\": \"sourceName\",\n  \"energizedByHds\": \"ERROR\",\n  \"energizedByExternalPowerSource\": \"sourceName\",\n  \"adjStart\": \"NOT_ACTIVE\",\n  \"adjStop\": \"ERROR\",\n  \"adjLock\": \"OK\",\n  \"adjUnlock\": \"sourceName\",\n  \"hdsStatus\": \"OK\",\n  \"voltageStatus\": \"NOT_ACTIVE\",\n  \"externalPowerSourceVoltage\": \"NOT_ACTIVE\",\n  \"feeder1Status\": \"ERROR\",\n  \"feeder2Status\": \"UNDEFINED\",\n  \"feeder3Status\": \"UNDEFINED\",\n  \"feeder4Status\": \"OFF\",\n  \"feeder5Status\": \"ON\",\n  \"feeder6Status\": \"sourceName\",\n  \"feederNppa1Status\": \"ON\",\n  \"BSVoltage\": \"ERROR\"\n}"}]}], "id": "b73cd0f2-2408-4cb3-a673-8fdaf886fbc5"}, {"name": "system", "item": [{"name": "GET api/v1/adjacent-systems/sae/status/system", "id": "196e66a6-9f72-4c94-904b-8fcfada4922c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/sae/status/system"}, "response": [{"id": "21b50ff6-cf8c-43ab-8a6d-32ef7b29bfd1", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/sae/status/system"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "ERROR"}]}, {"name": "PUT api/v1/adjacent-systems/sae/status/system", "id": "b2b11dac-9a6a-41dd-b3c6-20975aec12a4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/sae/status/system?status=NOT_CONNECTED", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "sae", "status", "system"], "query": [{"key": "status", "value": "NOT_CONNECTED", "description": "(Required) "}]}}, "response": [{"id": "ad5fefea-1a34-45f0-a04f-b6afd4aa140d", "name": "OK", "originalRequest": {"method": "PUT", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/sae/status/system?status=ERROR", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "sae", "status", "system"], "query": [{"description": "(Required) ", "key": "status", "value": "ERROR"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"saeStatus\": \"OK\",\n  \"readiness\": \"ERROR\",\n  \"energizedByAdj\": \"sourceName\",\n  \"energizedByHds\": \"ERROR\",\n  \"energizedByExternalPowerSource\": \"sourceName\",\n  \"adjStart\": \"NOT_ACTIVE\",\n  \"adjStop\": \"ERROR\",\n  \"adjLock\": \"OK\",\n  \"adjUnlock\": \"sourceName\",\n  \"hdsStatus\": \"OK\",\n  \"voltageStatus\": \"NOT_ACTIVE\",\n  \"externalPowerSourceVoltage\": \"NOT_ACTIVE\",\n  \"feeder1Status\": \"ERROR\",\n  \"feeder2Status\": \"UNDEFINED\",\n  \"feeder3Status\": \"UNDEFINED\",\n  \"feeder4Status\": \"OFF\",\n  \"feeder5Status\": \"ON\",\n  \"feeder6Status\": \"sourceName\",\n  \"feederNppa1Status\": \"ON\",\n  \"BSVoltage\": \"ERROR\"\n}"}]}], "id": "fea8cd7a-e8a0-4747-afa3-43070d079a34"}], "id": "aaff3b08-20e8-4d42-ad71-f87247e6232f"}, {"name": "GET api/v1/adjacent-systems/sae/", "id": "063b9a08-51fd-44e4-a8a6-668152cff9da", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/sae/"}, "response": [{"id": "c218d760-192a-45e0-a37a-d01c1383725b", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/sae/"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"saeStatus\": \"OK\",\n  \"readiness\": \"ERROR\",\n  \"energizedByAdj\": \"sourceName\",\n  \"energizedByHds\": \"ERROR\",\n  \"energizedByExternalPowerSource\": \"sourceName\",\n  \"adjStart\": \"NOT_ACTIVE\",\n  \"adjStop\": \"ERROR\",\n  \"adjLock\": \"OK\",\n  \"adjUnlock\": \"sourceName\",\n  \"hdsStatus\": \"OK\",\n  \"voltageStatus\": \"NOT_ACTIVE\",\n  \"externalPowerSourceVoltage\": \"NOT_ACTIVE\",\n  \"feeder1Status\": \"ERROR\",\n  \"feeder2Status\": \"UNDEFINED\",\n  \"feeder3Status\": \"UNDEFINED\",\n  \"feeder4Status\": \"OFF\",\n  \"feeder5Status\": \"ON\",\n  \"feeder6Status\": \"sourceName\",\n  \"feederNppa1Status\": \"ON\",\n  \"BSVoltage\": \"ERROR\"\n}"}]}, {"name": "PUT api/v1/adjacent-systems/sae/", "id": "577ef105-da9b-4f62-a948-208663ab1a9b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n    \"saeStatus\": \"OK\",\n    \"readiness\": \"OK\",\n    \"energizedByAdj\": \"ERROR\",\n    \"energizedByHds\": \"ERROR\",\n    \"energizedByExternalPowerSource\": \"OK\",\n    \"adjStart\": \"OK\",\n    \"adjStop\": \"OK\",\n    \"adjLock\": \"OK\",\n    \"adjUnlock\": \"OK\",\n    \"hdsStatus\": \"OK\",\n    \"voltageStatus\": \"OK\",\n    \"externalPowerSourceVoltage\": \"OK\",\n    \"feeder1Status\": \"ON\",\n    \"feeder2Status\": \"ON\",\n    \"feeder3Status\": \"ON\",\n    \"feeder4Status\": \"ON\",\n    \"feeder5Status\": \"ON\",\n    \"feeder6Status\": \"ON\",\n    \"feederNppa1Status\": \"ON\",\n    \"bsvoltage\": \"OK\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/adjacent-systems/sae/"}, "response": [{"id": "4186e3f2-861b-40b0-ac9d-a3eb2d609720", "name": "OK", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n  \"saeStatus\": \"OK\",\n  \"readiness\": \"ERROR\",\n  \"energizedByAdj\": \"sourceName\",\n  \"energizedByHds\": \"ERROR\",\n  \"energizedByExternalPowerSource\": \"sourceName\",\n  \"adjStart\": \"NOT_ACTIVE\",\n  \"adjStop\": \"ERROR\",\n  \"adjLock\": \"OK\",\n  \"adjUnlock\": \"sourceName\",\n  \"hdsStatus\": \"OK\",\n  \"voltageStatus\": \"NOT_ACTIVE\",\n  \"externalPowerSourceVoltage\": \"NOT_ACTIVE\",\n  \"feeder1Status\": \"ERROR\",\n  \"feeder2Status\": \"UNDEFINED\",\n  \"feeder3Status\": \"UNDEFINED\",\n  \"feeder4Status\": \"OFF\",\n  \"feeder5Status\": \"ON\",\n  \"feeder6Status\": \"sourceName\",\n  \"feederNppa1Status\": \"ON\",\n  \"BSVoltage\": \"ERROR\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/adjacent-systems/sae/"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"saeStatus\": \"OK\",\n  \"readiness\": \"ERROR\",\n  \"energizedByAdj\": \"sourceName\",\n  \"energizedByHds\": \"ERROR\",\n  \"energizedByExternalPowerSource\": \"sourceName\",\n  \"adjStart\": \"NOT_ACTIVE\",\n  \"adjStop\": \"ERROR\",\n  \"adjLock\": \"OK\",\n  \"adjUnlock\": \"sourceName\",\n  \"hdsStatus\": \"OK\",\n  \"voltageStatus\": \"NOT_ACTIVE\",\n  \"externalPowerSourceVoltage\": \"NOT_ACTIVE\",\n  \"feeder1Status\": \"ERROR\",\n  \"feeder2Status\": \"UNDEFINED\",\n  \"feeder3Status\": \"UNDEFINED\",\n  \"feeder4Status\": \"OFF\",\n  \"feeder5Status\": \"ON\",\n  \"feeder6Status\": \"sourceName\",\n  \"feederNppa1Status\": \"ON\",\n  \"BSVoltage\": \"ERROR\"\n}"}]}], "id": "0f93d781-d669-41fa-8302-09f1294f1399"}, {"name": "ppo", "item": [{"name": "bay", "item": [{"name": "unit", "item": [{"name": "{unit_state}", "item": [{"name": "GET api/v1/adjacent-systems/ppo/bay/unit/{unit_state}", "id": "42104d8a-a54e-419f-8441-01f6ba830ef7", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/ppo/bay/unit/:unit_state", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "ppo", "bay", "unit", ":unit_state"], "variable": [{"id": "8a336dce-fee3-477a-a571-97ffe96783fb", "key": "unit_state", "value": "sourceName"}]}}, "response": [{"id": "0380913d-5a6f-41aa-81dc-802540243a75", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/ppo/bay/unit/:unit_state", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "ppo", "bay", "unit", ":unit_state"], "variable": [{"key": "unit_state"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "[\n  {\n    \"id\": \"<long>\",\n    \"unitType\": \"unitName\",\n    \"unitName\": \"<string>\",\n    \"unitAlias\": \"<string>\",\n    \"bayType\": \"TO\",\n    \"unitStatus\": \"ERROR\"\n  },\n  {\n    \"id\": \"<long>\",\n    \"unitType\": \"UNDEFINED\",\n    \"unitName\": \"<string>\",\n    \"unitAlias\": \"<string>\",\n    \"bayType\": \"UNDEFINED\",\n    \"unitStatus\": \"ERROR\"\n  }\n]"}]}], "id": "c01b0fc4-7937-42f2-9ed2-e775c8369b96"}], "id": "739b6242-7ca2-4a96-a2cf-1cc49243e9b6"}, {"name": "{bay_type}", "item": [{"name": "unit", "item": [{"name": "{unit_id}", "item": [{"name": "PUT api/v1/adjacent-systems/ppo/bay/{bay_type}/unit/{unit_id}", "id": "34c6ffc7-be69-4d32-a272-ae0cd013cc41", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "sourceName", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/ppo/bay/:bay_type/unit/:unit_id", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "ppo", "bay", ":bay_type", "unit", ":unit_id"], "variable": [{"id": "1c07c930-2c7b-4764-a50b-967edde4871e", "key": "bay_type", "value": "bayName"}, {"id": "1b53c804-5ed4-4f91-8d87-c8a57041ab48", "key": "unit_id", "value": "<long>"}]}}, "response": [{"id": "c87fa42c-ff7e-4fad-ba5b-5ac77ec1fd48", "name": "OK", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "sourceName", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/ppo/bay/:bay_type/unit/:unit_id", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "ppo", "bay", ":bay_type", "unit", ":unit_id"], "variable": [{"key": "bay_type"}, {"key": "unit_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"id\": \"<long>\",\n  \"unitType\": \"THERMAL_SENSOR\",\n  \"unitName\": \"<string>\",\n  \"unitAlias\": \"<string>\",\n  \"bayType\": \"TO\",\n  \"unitStatus\": \"UNDEFINED\"\n}"}]}], "id": "b9ba710a-ee1e-4ca2-9353-eb6a02312188"}], "id": "74eeb904-feff-485d-9ccf-6f8fc0408055"}, {"name": "GET api/v1/adjacent-systems/ppo/bay/{bay_type}", "id": "2e6c18b3-6b8e-43f3-852c-c43c41896689", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/ppo/bay/:bay_type", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "ppo", "bay", ":bay_type"], "variable": [{"id": "d8110b3a-4e19-473d-abd7-ce0698595cec", "key": "bay_type", "value": "bayName"}]}}, "response": [{"id": "2cef7970-fc8a-4926-b6ae-37e0653b3ed5", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/ppo/bay/:bay_type", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "ppo", "bay", ":bay_type"], "variable": [{"key": "bay_type"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"id\": \"<long>\",\n  \"ppoBayType\": \"DEA\",\n  \"firePresent\": \"<boolean>\",\n  \"unitsInPPoBay\": [\n    {\n      \"id\": \"<long>\",\n      \"unitType\": \"unitName\",\n      \"unitName\": \"<string>\",\n      \"unitAlias\": \"<string>\",\n      \"bayType\": \"NPPA\",\n      \"unitStatus\": \"ON\"\n    },\n    {\n      \"id\": \"<long>\",\n      \"unitType\": \"unitName\",\n      \"unitName\": \"<string>\",\n      \"unitAlias\": \"<string>\",\n      \"bayType\": \"ASKU\",\n      \"unitStatus\": \"UNDEFINED\"\n    }\n  ]\n}"}]}, {"name": "PUT api/v1/adjacent-systems/ppo/bay/{bay_type}", "id": "443c4017-285f-4408-8cf6-572a2dd87de0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<long>\",\n  \"ppoBayType\": \"DEA\",\n  \"firePresent\": \"<boolean>\",\n  \"unitsInPPoBay\": [\n    {\n      \"id\": \"<long>\",\n      \"unitType\": \"unitName\",\n      \"unitName\": \"<string>\",\n      \"unitAlias\": \"<string>\",\n      \"bayType\": \"NPPA\",\n      \"unitStatus\": \"ON\"\n    },\n    {\n      \"id\": \"<long>\",\n      \"unitType\": \"unitName\",\n      \"unitName\": \"<string>\",\n      \"unitAlias\": \"<string>\",\n      \"bayType\": \"ASKU\",\n      \"unitStatus\": \"UNDEFINED\"\n    }\n  ]\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/ppo/bay/:bay_type", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "ppo", "bay", ":bay_type"], "variable": [{"id": "7cfdd6a0-8371-447e-ae17-e15e40d8f98a", "key": "bay_type", "value": "bayName"}]}}, "response": [{"id": "7f22d603-1690-4aac-a9e9-190ec802d586", "name": "OK", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<long>\",\n  \"ppoBayType\": \"DEA\",\n  \"firePresent\": \"<boolean>\",\n  \"unitsInPPoBay\": [\n    {\n      \"id\": \"<long>\",\n      \"unitType\": \"unitName\",\n      \"unitName\": \"<string>\",\n      \"unitAlias\": \"<string>\",\n      \"bayType\": \"NPPA\",\n      \"unitStatus\": \"ON\"\n    },\n    {\n      \"id\": \"<long>\",\n      \"unitType\": \"unitName\",\n      \"unitName\": \"<string>\",\n      \"unitAlias\": \"<string>\",\n      \"bayType\": \"ASKU\",\n      \"unitStatus\": \"UNDEFINED\"\n    }\n  ]\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/ppo/bay/:bay_type", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "ppo", "bay", ":bay_type"], "variable": [{"key": "bay_type"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"id\": \"<long>\",\n  \"ppoBayType\": \"DEA\",\n  \"firePresent\": \"<boolean>\",\n  \"unitsInPPoBay\": [\n    {\n      \"id\": \"<long>\",\n      \"unitType\": \"unitName\",\n      \"unitName\": \"<string>\",\n      \"unitAlias\": \"<string>\",\n      \"bayType\": \"NPPA\",\n      \"unitStatus\": \"ON\"\n    },\n    {\n      \"id\": \"<long>\",\n      \"unitType\": \"unitName\",\n      \"unitName\": \"<string>\",\n      \"unitAlias\": \"<string>\",\n      \"bayType\": \"ASKU\",\n      \"unitStatus\": \"UNDEFINED\"\n    }\n  ]\n}"}]}], "id": "fbb2d048-6723-4185-aa57-3f98d72312ad"}], "id": "8cf5467b-4c18-4143-a88d-68113e9de236"}, {"name": "command", "item": [{"name": "{command_id}", "item": [{"name": "commit", "item": [{"name": "POST api/v1/adjacent-systems/ppo/command/{command_id}/commit", "id": "3620d467-2394-44fc-92d3-bb865ef4add0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/ppo/command/:command_id/commit", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "ppo", "command", ":command_id", "commit"], "variable": [{"key": "command_id", "value": "100"}]}}, "response": [{"id": "a9797cc3-059e-4793-a4e6-aaf0d6950a6d", "name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/ppo/command/:command_id/commit", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "ppo", "command", ":command_id", "commit"], "variable": [{"key": "command_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"id\": \"<long>\",\n  \"command\": \"<string>\",\n  \"caption\": \"<string>\",\n  \"adjacentSystem\": \"TPC\",\n  \"generationTime\": \"<dateTime>\",\n  \"executionTime\": \"<dateTime>\"\n}"}]}], "id": "8a869f14-2ccd-481d-aa77-1bf90102e955"}, {"name": "remove", "item": [{"name": "POST api/v1/adjacent-systems/ppo/command/{command_id}/commit Copy", "id": "64e9b6df-0213-4ee1-8f8c-fe94f30042fb", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/ppo/command/:command_id/remove", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "ppo", "command", ":command_id", "remove"], "variable": [{"key": "command_id", "value": "1004"}]}}, "response": [{"id": "f74890a8-3ac3-4de0-9081-9cc78bc29145", "name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/ppo/command/:command_id/commit", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "ppo", "command", ":command_id", "commit"], "variable": [{"key": "command_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"id\": \"<long>\",\n  \"command\": \"<string>\",\n  \"caption\": \"<string>\",\n  \"adjacentSystem\": \"TPC\",\n  \"generationTime\": \"<dateTime>\",\n  \"executionTime\": \"<dateTime>\"\n}"}]}], "id": "06ae9c8f-3d3f-42fc-b4db-c5d1fc2fc4e2"}], "id": "3968977c-6c8b-4147-b344-97e8134963a4"}, {"name": "GET api/v1/adjacent-systems/ppo/command", "id": "b7adc8c3-4fbb-4b69-9eec-ce00071d4cbd", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/ppo/command"}, "response": [{"id": "f9d42482-c8c8-4d1d-aae3-09f142b2e3d6", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/ppo/command"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"id\": \"<long>\",\n  \"command\": \"<string>\",\n  \"caption\": \"<string>\",\n  \"adjacentSystem\": \"TPC\",\n  \"generationTime\": \"<dateTime>\",\n  \"executionTime\": \"<dateTime>\"\n}"}]}], "id": "c2a12653-a102-400f-ae21-c81b7cf59929"}, {"name": "status", "item": [{"name": "system", "item": [{"name": "GET api/v1/adjacent-systems/ppo/status/system", "id": "0ca03aa8-04b6-4e33-8702-00a139ea13c6", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/ppo/status/system"}, "response": [{"id": "59efb24a-80dc-4d4d-a99c-e5277fbfbcd1", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/ppo/status/system"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "ERROR"}]}], "id": "1e599da4-caea-4de2-9eb9-c13be7b14492"}], "id": "8ff6fee0-d623-4a8c-b4d7-e1d5458376ab"}, {"name": "GET api/v1/adjacent-systems/ppo/", "id": "e29d8b5e-1565-4bb4-bf12-266dd2f285e6", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/ppo/"}, "response": [{"id": "f419e84a-a5f5-44ad-8242-80deb53c5e6c", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/ppo/"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"status\": \"UNDEFINED\",\n  \"ppoBaysList\": [\n    {\n      \"id\": \"<long>\",\n      \"ppoBayType\": \"NPPA\",\n      \"firePresent\": \"<boolean>\",\n      \"unitsInPPoBay\": [\n        {\n          \"id\": \"<long>\",\n          \"unitType\": \"THERMAL_SENSOR\",\n          \"unitName\": \"<string>\",\n          \"unitAlias\": \"<string>\",\n          \"bayType\": \"bayName\",\n          \"unitStatus\": \"UNDEFINED\"\n        },\n        {\n          \"id\": \"<long>\",\n          \"unitType\": \"unitName\",\n          \"unitName\": \"<string>\",\n          \"unitAlias\": \"<string>\",\n          \"bayType\": \"bayName\",\n          \"unitStatus\": \"ON\"\n        }\n      ]\n    },\n    {\n      \"id\": \"<long>\",\n      \"ppoBayType\": \"DEA\",\n      \"firePresent\": \"<boolean>\",\n      \"unitsInPPoBay\": [\n        {\n          \"id\": \"<long>\",\n          \"unitType\": \"BALLOON\",\n          \"unitName\": \"<string>\",\n          \"unitAlias\": \"<string>\",\n          \"bayType\": \"UNDEFINED\",\n          \"unitStatus\": \"UNDEFINED\"\n        },\n        {\n          \"id\": \"<long>\",\n          \"unitType\": \"OPTICAL_SENSOR\",\n          \"unitName\": \"<string>\",\n          \"unitAlias\": \"<string>\",\n          \"bayType\": \"DEA\",\n          \"unitStatus\": \"UNDEFINED\"\n        }\n      ]\n    }\n  ]\n}"}]}, {"name": "PUT api/v1/adjacent-systems/ppo/", "id": "eea484f1-aa0f-44ac-a467-8ddbe41b2f07", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"OK\",\n    \"ppoBaysList\": [\n        {\n            \"id\": 2,\n            \"ppoBayType\": \"NPPA\",\n            \"firePresent\": true,\n            \"unitsInPPoBay\": [\n                {\n                    \"id\": 4,\n                    \"unitType\": \"BALLOON\",\n                    \"unitName\": \"балон 2\",\n                    \"unitAlias\": \"BALLOON_NPPA_2\",\n                    \"bayType\": \"NPPA\",\n                    \"unitStatus\": \"OFF\"\n                },\n                {\n                    \"id\": 3,\n                    \"unitType\": \"BALLOON\",\n                    \"unitName\": \"балон 1\",\n                    \"unitAlias\": \"BALLOON_NPPA_1\",\n                    \"bayType\": \"NPPA\",\n                    \"unitStatus\": \"ON\"\n                },\n                {\n                    \"id\": 13,\n                    \"unitType\": \"OPTICAL_SENSOR\",\n                    \"unitName\": \"ОД1\",\n                    \"unitAlias\": \"OPTICAL_SENSOR_NPPA_1\",\n                    \"bayType\": \"NPPA\",\n                    \"unitStatus\": \"UNDEFINED\"\n                },\n                {\n                    \"id\": 14,\n                    \"unitType\": \"OPTICAL_SENSOR\",\n                    \"unitName\": \"ОД2\",\n                    \"unitAlias\": \"OPTICAL_SENSOR_NPPA_2\",\n                    \"bayType\": \"NPPA\",\n                    \"unitStatus\": \"UNDEFINED\"\n                },\n                {\n                    \"id\": 15,\n                    \"unitType\": \"OPTICAL_SENSOR\",\n                    \"unitName\": \"ОД3\",\n                    \"unitAlias\": \"OPTICAL_SENSOR_NPPA_3\",\n                    \"bayType\": \"NPPA\",\n                    \"unitStatus\": \"UNDEFINED\"\n                }\n            ]\n        },\n        {\n            \"id\": 3,\n            \"ppoBayType\": \"DEA\",\n            \"firePresent\": false,\n            \"unitsInPPoBay\": [\n                {\n                    \"id\": 6,\n                    \"unitType\": \"BALLOON\",\n                    \"unitName\": \"балон 2\",\n                    \"unitAlias\": \"BALLOON_DEA_2\",\n                    \"bayType\": \"DEA\",\n                    \"unitStatus\": \"UNDEFINED\"\n                },\n                {\n                    \"id\": 5,\n                    \"unitType\": \"BALLOON\",\n                    \"unitName\": \"балон 1\",\n                    \"unitAlias\": \"BALLOON_DEA_1\",\n                    \"bayType\": \"DEA\",\n                    \"unitStatus\": \"UNDEFINED\"\n                },\n                {\n                    \"id\": 16,\n                    \"unitType\": \"THERMAL_SENSOR\",\n                    \"unitName\": \"ТД1\",\n                    \"unitAlias\": \"THERMAL_SENSOR_DEA_1\",\n                    \"bayType\": \"DEA\",\n                    \"unitStatus\": \"UNDEFINED\"\n                },\n                {\n                    \"id\": 18,\n                    \"unitType\": \"THERMAL_SENSOR\",\n                    \"unitName\": \"ТД3\",\n                    \"unitAlias\": \"THERMAL_SENSOR_DEA_3\",\n                    \"bayType\": \"DEA\",\n                    \"unitStatus\": \"UNDEFINED\"\n                },\n                {\n                    \"id\": 17,\n                    \"unitType\": \"THERMAL_SENSOR\",\n                    \"unitName\": \"ТД2\",\n                    \"unitAlias\": \"THERMAL_SENSOR_DEA_2\",\n                    \"bayType\": \"DEA\",\n                    \"unitStatus\": \"UNDEFINED\"\n                }\n            ]\n        },\n        {\n            \"id\": 1,\n            \"ppoBayType\": \"ASKU\",\n            \"firePresent\": false,\n            \"unitsInPPoBay\": [\n                {\n                    \"id\": 1,\n                    \"unitType\": \"BALLOON\",\n                    \"unitName\": \"балон 1\",\n                    \"unitAlias\": \"BALLOON_ASKU_1\",\n                    \"bayType\": \"ASKU\",\n                    \"unitStatus\": \"UNDEFINED\"\n                },\n                {\n                    \"id\": 2,\n                    \"unitType\": \"BALLOON\",\n                    \"unitName\": \"балон 2\",\n                    \"unitAlias\": \"BALLOON_ASKU_2\",\n                    \"bayType\": \"ASKU\",\n                    \"unitStatus\": \"UNDEFINED\"\n                },\n                {\n                    \"id\": 10,\n                    \"unitType\": \"OPTICAL_SENSOR\",\n                    \"unitName\": \"ОД2\",\n                    \"unitAlias\": \"OPTICAL_SENSOR_ASKU_2\",\n                    \"bayType\": \"ASKU\",\n                    \"unitStatus\": \"UNDEFINED\"\n                },\n                {\n                    \"id\": 9,\n                    \"unitType\": \"OPTICAL_SENSOR\",\n                    \"unitName\": \"ОД1\",\n                    \"unitAlias\": \"OPTICAL_SENSOR_ASKU_1\",\n                    \"bayType\": \"ASKU\",\n                    \"unitStatus\": \"UNDEFINED\"\n                },\n                {\n                    \"id\": 12,\n                    \"unitType\": \"OPTICAL_SENSOR\",\n                    \"unitName\": \"ОД4\",\n                    \"unitAlias\": \"OPTICAL_SENSOR_ASKU_4\",\n                    \"bayType\": \"ASKU\",\n                    \"unitStatus\": \"UNDEFINED\"\n                },\n                {\n                    \"id\": 11,\n                    \"unitType\": \"OPTICAL_SENSOR\",\n                    \"unitName\": \"ОД3\",\n                    \"unitAlias\": \"OPTICAL_SENSOR_ASKU_3\",\n                    \"bayType\": \"ASKU\",\n                    \"unitStatus\": \"UNDEFINED\"\n                }\n            ]\n        },\n        {\n            \"id\": 4,\n            \"ppoBayType\": \"TO\",\n            \"firePresent\": false,\n            \"unitsInPPoBay\": [\n                {\n                    \"id\": 20,\n                    \"unitType\": \"THERMAL_SENSOR\",\n                    \"unitName\": \"ТД2\",\n                    \"unitAlias\": \"THERMAL_SENSOR_TO_2\",\n                    \"bayType\": \"TO\",\n                    \"unitStatus\": \"UNDEFINED\"\n                },\n                {\n                    \"id\": 19,\n                    \"unitType\": \"THERMAL_SENSOR\",\n                    \"unitName\": \"ТД1\",\n                    \"unitAlias\": \"THERMAL_SENSOR_TO_1\",\n                    \"bayType\": \"TO\",\n                    \"unitStatus\": \"UNDEFINED\"\n                },\n                {\n                    \"id\": 21,\n                    \"unitType\": \"THERMAL_SENSOR\",\n                    \"unitName\": \"ТД3\",\n                    \"unitAlias\": \"THERMAL_SENSOR_TO_3\",\n                    \"bayType\": \"TO\",\n                    \"unitStatus\": \"UNDEFINED\"\n                },\n                {\n                    \"id\": 7,\n                    \"unitType\": \"BALLOON\",\n                    \"unitName\": \"балон 1\",\n                    \"unitAlias\": \"BALLOON_TO_1\",\n                    \"bayType\": \"TO\",\n                    \"unitStatus\": \"UNDEFINED\"\n                },\n                {\n                    \"id\": 8,\n                    \"unitType\": \"BALLOON\",\n                    \"unitName\": \"балон 2\",\n                    \"unitAlias\": \"BALLOON_TO_2\",\n                    \"bayType\": \"TO\",\n                    \"unitStatus\": \"UNDEFINED\"\n                }\n            ]\n        }\n    ]\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/adjacent-systems/ppo/"}, "response": [{"id": "97f029ac-2cc3-49d1-a991-cf4980e08fc8", "name": "OK", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"UNDEFINED\",\n  \"ppoBaysList\": [\n    {\n      \"id\": \"<long>\",\n      \"ppoBayType\": \"NPPA\",\n      \"firePresent\": \"<boolean>\",\n      \"unitsInPPoBay\": [\n        {\n          \"id\": \"<long>\",\n          \"unitType\": \"THERMAL_SENSOR\",\n          \"unitName\": \"<string>\",\n          \"unitAlias\": \"<string>\",\n          \"bayType\": \"bayName\",\n          \"unitStatus\": \"UNDEFINED\"\n        },\n        {\n          \"id\": \"<long>\",\n          \"unitType\": \"unitName\",\n          \"unitName\": \"<string>\",\n          \"unitAlias\": \"<string>\",\n          \"bayType\": \"bayName\",\n          \"unitStatus\": \"ON\"\n        }\n      ]\n    },\n    {\n      \"id\": \"<long>\",\n      \"ppoBayType\": \"DEA\",\n      \"firePresent\": \"<boolean>\",\n      \"unitsInPPoBay\": [\n        {\n          \"id\": \"<long>\",\n          \"unitType\": \"BALLOON\",\n          \"unitName\": \"<string>\",\n          \"unitAlias\": \"<string>\",\n          \"bayType\": \"UNDEFINED\",\n          \"unitStatus\": \"UNDEFINED\"\n        },\n        {\n          \"id\": \"<long>\",\n          \"unitType\": \"OPTICAL_SENSOR\",\n          \"unitName\": \"<string>\",\n          \"unitAlias\": \"<string>\",\n          \"bayType\": \"DEA\",\n          \"unitStatus\": \"UNDEFINED\"\n        }\n      ]\n    }\n  ]\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/adjacent-systems/ppo/"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"status\": \"UNDEFINED\",\n  \"ppoBaysList\": [\n    {\n      \"id\": \"<long>\",\n      \"ppoBayType\": \"NPPA\",\n      \"firePresent\": \"<boolean>\",\n      \"unitsInPPoBay\": [\n        {\n          \"id\": \"<long>\",\n          \"unitType\": \"THERMAL_SENSOR\",\n          \"unitName\": \"<string>\",\n          \"unitAlias\": \"<string>\",\n          \"bayType\": \"bayName\",\n          \"unitStatus\": \"UNDEFINED\"\n        },\n        {\n          \"id\": \"<long>\",\n          \"unitType\": \"unitName\",\n          \"unitName\": \"<string>\",\n          \"unitAlias\": \"<string>\",\n          \"bayType\": \"bayName\",\n          \"unitStatus\": \"ON\"\n        }\n      ]\n    },\n    {\n      \"id\": \"<long>\",\n      \"ppoBayType\": \"DEA\",\n      \"firePresent\": \"<boolean>\",\n      \"unitsInPPoBay\": [\n        {\n          \"id\": \"<long>\",\n          \"unitType\": \"BALLOON\",\n          \"unitName\": \"<string>\",\n          \"unitAlias\": \"<string>\",\n          \"bayType\": \"UNDEFINED\",\n          \"unitStatus\": \"UNDEFINED\"\n        },\n        {\n          \"id\": \"<long>\",\n          \"unitType\": \"OPTICAL_SENSOR\",\n          \"unitName\": \"<string>\",\n          \"unitAlias\": \"<string>\",\n          \"bayType\": \"DEA\",\n          \"unitStatus\": \"UNDEFINED\"\n        }\n      ]\n    }\n  ]\n}"}]}], "id": "4a07b199-94d2-4a32-81a2-6d505e5d5d27"}, {"name": "suto", "item": [{"name": "command", "item": [{"name": "commit", "item": [{"name": "{command_id}", "item": [{"name": "PUT api/v1/adjacent-systems/suto/command/commit/{command_id}", "id": "990c794b-51f0-4388-acf7-97f648c94f28", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/suto/command/commit/:command_id", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "suto", "command", "commit", ":command_id"], "variable": [{"key": "command_id", "value": "1004"}]}}, "response": [{"id": "53b55b45-04b3-4204-8002-56fa9bc10fc7", "name": "OK", "originalRequest": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/suto/command/commit/:command_id", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "suto", "command", "commit", ":command_id"], "variable": [{"key": "command_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [], "cookie": [], "responseTime": null, "body": ""}]}], "id": "1c6430b1-7ada-4615-98c9-9b3ce4a9186c"}], "id": "4ea7d881-6414-4cd4-97d7-c7789d6f8362"}, {"name": "remove", "item": [{"name": "{command_id}", "item": [{"name": "PUT api/v1/adjacent-systems/suto/command/commit/{command_id}", "id": "7db11178-dcf0-44fd-88e3-1410da19be3d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/suto/command/remove/:command_id", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "suto", "command", "remove", ":command_id"], "variable": [{"key": "command_id", "value": "1004"}]}}, "response": [{"id": "70b0ba92-cf97-451c-b4e1-1cd57188a659", "name": "OK", "originalRequest": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/suto/command/commit/:command_id", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "suto", "command", "commit", ":command_id"], "variable": [{"key": "command_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [], "cookie": [], "responseTime": null, "body": ""}]}], "id": "072649c4-0cc7-4bcc-bb8f-19fd1c14c0eb"}], "id": "5a855a0c-7318-4e6f-81e0-71a41eb66eb0"}, {"name": "GET api/v1/adjacent-systems/suto/command", "id": "e6de8bda-5998-48e0-96b1-b0ce71f832c6", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/suto/command"}, "response": [{"id": "bd4796c6-f442-4d6d-a282-f36fd0c6591c", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/suto/command"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"id\": \"<long>\",\n  \"command\": \"<string>\",\n  \"caption\": \"<string>\",\n  \"adjacentSystem\": \"TPC\",\n  \"generationTime\": \"<dateTime>\",\n  \"executionTime\": \"<dateTime>\"\n}"}]}], "id": "2cf7a416-7e57-4ba9-a803-2ce822a7dbe6"}, {"name": "outriggers", "item": [{"name": "{outrigger_code_name}", "item": [{"name": "POST api/v1/adjacent-systems/suto/outriggers/{outrigger_code_name}", "id": "3e74e171-6dd9-4d8d-ab86-7a348a663240", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/suto/outriggers/:outrigger_code_name?code=3", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "suto", "outriggers", ":outrigger_code_name"], "query": [{"key": "code", "value": "3", "description": "(Required) "}], "variable": [{"id": "02e757e5-0603-4b39-a515-e5644dc6fad6", "key": "outrigger_code_name", "value": "leftFrontOutriggerEmergencyCode"}]}}, "response": [{"id": "931a5914-9d31-4572-97ac-7d6b206e4364", "name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/suto/outriggers/:outrigger_code_name?code=<integer>", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "suto", "outriggers", ":outrigger_code_name"], "query": [{"description": "(Required) ", "key": "code", "value": "<integer>"}], "variable": [{"key": "outrigger_code_name"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"id\": \"<long>\",\n  \"status\": \"NOT_CONNECTED\",\n  \"stats\": {\n    \"roll\": \"<integer>\",\n    \"pitch\": \"<integer>\",\n    \"armLiftStrokePosition\": \"<integer>\",\n    \"levelingCyclesCount\": \"<integer>\",\n    \"pressureInImpulseSection\": \"<integer>\",\n    \"temperatureRR\": \"<integer>\",\n    \"workingFluidLevel\": \"<integer>\",\n    \"mainPumpRPM\": \"<integer>\",\n    \"overallOperatingTime\": \"<integer>\",\n    \"overallArmLiftingsCount\": \"<integer>\"\n  },\n  \"leftFrontOutriggerEmergencyCode\": \"<integer>\",\n  \"rightFrontOutriggerEmergencyCode\": \"<integer>\",\n  \"leftRearOutriggerEmergencyCode\": \"<integer>\",\n  \"rightRearOutriggerEmergencyCode\": \"<integer>\",\n  \"properties\": [\n    {\n      \"id\": \"<long>\",\n      \"name\": \"<string>\",\n      \"state\": \"OFF\",\n      \"propertyType\": \"REGULAR\"\n    },\n    {\n      \"id\": \"<long>\",\n      \"name\": \"<string>\",\n      \"state\": \"ON\",\n      \"propertyType\": \"REGULAR\"\n    }\n  ]\n}"}]}], "id": "cb72bc74-c89b-46e6-beeb-b8c2b32dcfad"}], "id": "20816e2c-08b1-4697-ae19-489304b9860b"}, {"name": "properties", "item": [{"name": "property", "item": [{"name": "POST api/v1/adjacent-systems/suto/properties/property", "id": "94e59079-1451-4212-80a3-400afadbd627", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "[\n    {\n        \"id\": 2,\n        \"name\": \"isChassisHorizontal\",\n        \"state\": \"ON\",\n        \"propertyType\": \"REGULAR\"\n    },\n    {\n    \"id\": 27,   \n    \"name\": \"isLoweringArm\",\n    \"state\": \"ON\",\n    \"propertyType\": \"REGULAR\"\n    },\n    {\n            \"id\": 4,\n            \"name\": \"isOutriggersMovingToPP\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        }\n]", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/adjacent-systems/suto/properties/property"}, "response": [{"id": "2659ef1d-9bf8-4b42-b32b-451c215764d0", "name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "[\n  {\n    \"id\": \"<long>\",\n    \"name\": \"<string>\",\n    \"state\": \"OFF\",\n    \"propertyType\": \"MALFUNCTION\"\n  },\n  {\n    \"id\": \"<long>\",\n    \"name\": \"<string>\",\n    \"state\": \"ON\",\n    \"propertyType\": \"MALFUNCTION\"\n  }\n]", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/adjacent-systems/suto/properties/property"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"id\": \"<long>\",\n  \"status\": \"NOT_CONNECTED\",\n  \"stats\": {\n    \"roll\": \"<integer>\",\n    \"pitch\": \"<integer>\",\n    \"armLiftStrokePosition\": \"<integer>\",\n    \"levelingCyclesCount\": \"<integer>\",\n    \"pressureInImpulseSection\": \"<integer>\",\n    \"temperatureRR\": \"<integer>\",\n    \"workingFluidLevel\": \"<integer>\",\n    \"mainPumpRPM\": \"<integer>\",\n    \"overallOperatingTime\": \"<integer>\",\n    \"overallArmLiftingsCount\": \"<integer>\"\n  },\n  \"leftFrontOutriggerEmergencyCode\": \"<integer>\",\n  \"rightFrontOutriggerEmergencyCode\": \"<integer>\",\n  \"leftRearOutriggerEmergencyCode\": \"<integer>\",\n  \"rightRearOutriggerEmergencyCode\": \"<integer>\",\n  \"properties\": [\n    {\n      \"id\": \"<long>\",\n      \"name\": \"<string>\",\n      \"state\": \"OFF\",\n      \"propertyType\": \"REGULAR\"\n    },\n    {\n      \"id\": \"<long>\",\n      \"name\": \"<string>\",\n      \"state\": \"ON\",\n      \"propertyType\": \"REGULAR\"\n    }\n  ]\n}"}]}], "id": "9825e26c-6b4a-40c9-8d58-669d1ca90602"}], "id": "e6d9a324-0aca-4fca-9975-bf0a29a353a5"}, {"name": "settings", "item": [{"name": "GET api/v1/adjacent-systems/suto/settings", "id": "d4af46d9-33b1-4024-b1eb-947fe7425f20", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/suto/settings"}, "response": [{"id": "9e7950fb-1c95-4035-95f0-85666f8c4cbe", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/suto/settings"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"leftTpcLoad\": \"TPC_WITH_ROCKET\",\n  \"rightTpcLoad\": \"TPC_WITH_ROCKET\",\n  \"chassisState\": \"MARCH\"\n}"}]}, {"name": "POST api/v1/adjacent-systems/suto/settings", "id": "88f8d0ba-8786-47a5-8b58-23fb94d5e109", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n//   \"leftTpcLoad\": \"TPC_WITH_ROCKET\",\n//   \"rightTpcLoad\": \"TPC_WITH_ROCKET\",\n  \"chassisState\": \"PARKING\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/adjacent-systems/suto/settings"}, "response": [{"id": "a3c01f5a-7fbf-46d4-b77e-e0a774397013", "name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n  \"leftTpcLoad\": \"TPC_WITH_ROCKET\",\n  \"rightTpcLoad\": \"TPC_WITH_ROCKET\",\n  \"chassisState\": \"MARCH\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/adjacent-systems/suto/settings"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"leftTpcLoad\": \"TPC_WITH_ROCKET\",\n  \"rightTpcLoad\": \"TPC_WITH_ROCKET\",\n  \"chassisState\": \"MARCH\"\n}"}]}], "id": "c57882a3-ec50-4581-b9c5-f076a11e4cd9"}, {"name": "stats", "item": [{"name": "POST api/v1/adjacent-systems/suto/stats", "id": "96a82dc4-9e83-4060-9af6-07aa3bd03f56", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n        \"roll\": 17,\n        \"pitch\": 55,\n        \"armLiftStrokePosition\": 12,\n        \"levelingCyclesCount\": 256,\n        \"pressureInImpulseSection\": 0,\n        \"temperatureRR\": 0,\n        \"workingFluidLevel\": 11,\n        \"mainPumpRPM\": 675,\n        \"overallOperatingTime\": 146,\n        \"overallArmLiftingsCount\": 229\n    }", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/adjacent-systems/suto/stats"}, "response": [{"id": "596e87c4-b40e-4322-a58c-c25860df99f7", "name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n  \"roll\": \"<integer>\",\n  \"pitch\": \"<integer>\",\n  \"armLiftStrokePosition\": \"<integer>\",\n  \"levelingCyclesCount\": \"<integer>\",\n  \"pressureInImpulseSection\": \"<integer>\",\n  \"temperatureRR\": \"<integer>\",\n  \"workingFluidLevel\": \"<integer>\",\n  \"mainPumpRPM\": \"<integer>\",\n  \"overallOperatingTime\": \"<integer>\",\n  \"overallArmLiftingsCount\": \"<integer>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/adjacent-systems/suto/stats"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"id\": \"<long>\",\n  \"status\": \"NOT_CONNECTED\",\n  \"stats\": {\n    \"roll\": \"<integer>\",\n    \"pitch\": \"<integer>\",\n    \"armLiftStrokePosition\": \"<integer>\",\n    \"levelingCyclesCount\": \"<integer>\",\n    \"pressureInImpulseSection\": \"<integer>\",\n    \"temperatureRR\": \"<integer>\",\n    \"workingFluidLevel\": \"<integer>\",\n    \"mainPumpRPM\": \"<integer>\",\n    \"overallOperatingTime\": \"<integer>\",\n    \"overallArmLiftingsCount\": \"<integer>\"\n  },\n  \"leftFrontOutriggerEmergencyCode\": \"<integer>\",\n  \"rightFrontOutriggerEmergencyCode\": \"<integer>\",\n  \"leftRearOutriggerEmergencyCode\": \"<integer>\",\n  \"rightRearOutriggerEmergencyCode\": \"<integer>\",\n  \"properties\": [\n    {\n      \"id\": \"<long>\",\n      \"name\": \"<string>\",\n      \"state\": \"OFF\",\n      \"propertyType\": \"REGULAR\"\n    },\n    {\n      \"id\": \"<long>\",\n      \"name\": \"<string>\",\n      \"state\": \"ON\",\n      \"propertyType\": \"REGULAR\"\n    }\n  ]\n}"}]}], "id": "acc197a3-**************-bf09f5d9c380"}, {"name": "GET api/v1/adjacent-systems/suto/", "id": "cf86e32b-3ecc-4d2d-aa45-219cac3c188a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/suto/"}, "response": [{"id": "c4cd7936-fa1a-4bb7-8bb2-3db38ac56d1f", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/suto/"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"id\": \"<long>\",\n  \"status\": \"NOT_CONNECTED\",\n  \"stats\": {\n    \"roll\": \"<integer>\",\n    \"pitch\": \"<integer>\",\n    \"armLiftStrokePosition\": \"<integer>\",\n    \"levelingCyclesCount\": \"<integer>\",\n    \"pressureInImpulseSection\": \"<integer>\",\n    \"temperatureRR\": \"<integer>\",\n    \"workingFluidLevel\": \"<integer>\",\n    \"mainPumpRPM\": \"<integer>\",\n    \"overallOperatingTime\": \"<integer>\",\n    \"overallArmLiftingsCount\": \"<integer>\"\n  },\n  \"leftFrontOutriggerEmergencyCode\": \"<integer>\",\n  \"rightFrontOutriggerEmergencyCode\": \"<integer>\",\n  \"leftRearOutriggerEmergencyCode\": \"<integer>\",\n  \"rightRearOutriggerEmergencyCode\": \"<integer>\",\n  \"properties\": [\n    {\n      \"id\": \"<long>\",\n      \"name\": \"<string>\",\n      \"state\": \"OFF\",\n      \"propertyType\": \"REGULAR\"\n    },\n    {\n      \"id\": \"<long>\",\n      \"name\": \"<string>\",\n      \"state\": \"ON\",\n      \"propertyType\": \"REGULAR\"\n    }\n  ]\n}"}]}, {"name": "POST api/v1/adjacent-systems/suto/", "id": "1870ae63-3c73-4cd8-b252-467ba586ff15", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n    \"id\": 1,\n    \"status\": \"OK\",\n    \"stats\": {\n        \"roll\": 1,\n        \"pitch\": 2.1,\n        \"armLiftStrokePosition\": 230,\n        \"levelingCyclesCount\": 256,\n        \"pressureInImpulseSection\": 0,\n        \"temperatureRR\": 0,\n        \"workingFluidLevel\": 11,\n        \"mainPumpRPM\": 675,\n        \"overallOperatingTime\": 146,\n        \"overallArmLiftingsCount\": 229\n    },\n    \"leftFrontOutriggerEmergencyCode\": 1,\n    \"rightFrontOutriggerEmergencyCode\": 0,\n    \"leftRearOutriggerEmergencyCode\": 0,\n    \"rightRearOutriggerEmergencyCode\": 0,\n    \"properties\": [\n        {\n            \"id\": 31,\n            \"name\": \"boomEPMalfunction\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"MALFUNCTION\"\n        },\n        {\n            \"id\": 2,\n            \"name\": \"isChassisHorizontal\",\n            \"state\": \"ON\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 27,\n            \"name\": \"isLoweringArm\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 62,\n            \"name\": \"leftFrontLockRightTPKOpened\",\n            \"state\": \"ON\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 8,\n            \"name\": \"rightFrontOutriggerInitialPosition\",\n            \"state\": \"ON\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 68,\n            \"name\": \"leftRearLockRightTPKOpened\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 11,\n            \"name\": \"armUnlocked\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 49,\n            \"name\": \"fire\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"MALFUNCTION\"\n        },\n        {\n            \"id\": 53,\n            \"name\": \"leftFrontLockLeftTPKClosed\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 66,\n            \"name\": \"rightRearLockRightTPKOpened\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 19,\n            \"name\": \"leftGasSpringLowered\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 32,\n            \"name\": \"boomLockingEPMalfunction\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"MALFUNCTION\"\n        },\n        {\n            \"id\": 39,\n            \"name\": \"SPLEngineStarting\",\n            \"state\": \"ON\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 51,\n            \"name\": \"pollutedFilterDZF2\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"MALFUNCTION\"\n        },\n        {\n            \"id\": 15,\n            \"name\": \"rightGasSpringUnlocked\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 28,\n            \"name\": \"armInMobileState\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 50,\n            \"name\": \"pollutedFilterDZF1\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"MALFUNCTION\"\n        },\n        {\n            \"id\": 38,\n            \"name\": \"armPistonStrokeMaxPressure\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"MALFUNCTION\"\n        },\n        {\n            \"id\": 25,\n            \"name\": \"leftGasSpringLocked\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 34,\n            \"name\": \"rightGasSpringLockingEPMalfunction\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"MALFUNCTION\"\n        },\n        {\n            \"id\": 47,\n            \"name\": \"engineStartImpossible2\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"MALFUNCTION\"\n        },\n        {\n            \"id\": 52,\n            \"name\": \"airPressureNotNormal\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"MALFUNCTION\"\n        },\n        {\n            \"id\": 23,\n            \"name\": \"leftGasSpringInMobileState\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 48,\n            \"name\": \"engineStopImpossible\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"MALFUNCTION\"\n        },\n        {\n            \"id\": 21,\n            \"name\": \"isRaisingLeftGasSpring\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 1,\n            \"name\": \"chassisHorizontalAlignment\",\n            \"state\": \"ON\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 69,\n            \"name\": \"SUTOStop\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 18,\n            \"name\": \"emergencySituationForArmAndGasSprings\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 43,\n            \"name\": \"unloadingElectromagnetEnabled\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 57,\n            \"name\": \"rightRearLockLeftTPKClosed\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 60,\n            \"name\": \"leftRearLockLeftTPKOpened\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 29,\n            \"name\": \"boomLocked\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 5,\n            \"name\": \"isOutriggersInMobileState\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 36,\n            \"name\": \"malfunctionRightGasReflectorEP\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"MALFUNCTION\"\n        },\n        {\n            \"id\": 9,\n            \"name\": \"rightRearOutriggerInitialPosition\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 37,\n            \"name\": \"armEmptyStrokeMaxPressure\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"MALFUNCTION\"\n        },\n        {\n            \"id\": 7,\n            \"name\": \"leftFrontOutriggerInitialPosition\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 12,\n            \"name\": \"armRaising\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 41,\n            \"name\": \"mainPumpConnectionToGS_N1\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 13,\n            \"name\": \"armRaised\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 54,\n            \"name\": \"leftFrontLockLeftTPKOpened\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 67,\n            \"name\": \"leftRearLockRightTPKClosed\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 42,\n            \"name\": \"mainPumpConnectedToGS_N1\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 45,\n            \"name\": \"emergencySituation\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"MALFUNCTION\"\n        },\n        {\n            \"id\": 40,\n            \"name\": \"SPLEngineStarted\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 14,\n            \"name\": \"leftGasSpringUnlocked\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 44,\n            \"name\": \"SPLEnginestopping\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 55,\n            \"name\": \"rightFrontLockLeftTPKClosed\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 61,\n            \"name\": \"leftFrontLockRightTPKClosed\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 58,\n            \"name\": \"rightRearLockLeftTPKOpened\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 63,\n            \"name\": \"rightFrontLockRightTPKClosed\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 20,\n            \"name\": \"rightGasSpringLowered\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 17,\n            \"name\": \"isLoweringRightGasSpring\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 56,\n            \"name\": \"rightFrontLockLeftTPKOpened\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 65,\n            \"name\": \"rightRearLockRightTPKClosed\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 64,\n            \"name\": \"rightFrontLockRightTPKOpened\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 3,\n            \"name\": \"isAlignmentImpossible\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 59,\n            \"name\": \"leftRearLockLeftTPKClosed\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 33,\n            \"name\": \"leftGasSpringLockingEPMalfunction\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"MALFUNCTION\"\n        },\n        {\n            \"id\": 16,\n            \"name\": \"isLoweringLeftGasSpring\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 4,\n            \"name\": \"isOutriggersMovingToPP\",\n            \"state\": \"ON\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 30,\n            \"name\": \"boomInEndPosition\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 6,\n            \"name\": \"isEmergencyHangingAndAlignment\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"MALFUNCTION\"\n        },\n        {\n            \"id\": 46,\n            \"name\": \"engineStartImpossible\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"MALFUNCTION\"\n        },\n        {\n            \"id\": 24,\n            \"name\": \"rightGasSpringInMobileState\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 22,\n            \"name\": \"isRaisingRightGasSpring\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 35,\n            \"name\": \"malfunctionLeftGasReflectorEP\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"MALFUNCTION\"\n        },\n        {\n            \"id\": 10,\n            \"name\": \"leftRearOutriggerInitialPosition\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        },\n        {\n            \"id\": 26,\n            \"name\": \"rightGasSpringLocked\",\n            \"state\": \"OFF\",\n            \"propertyType\": \"REGULAR\"\n        }\n    ]\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/adjacent-systems/suto/"}, "response": [{"id": "1f0daee7-b7be-4a60-aa48-05c6caa4fc76", "name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<long>\",\n  \"status\": \"NOT_CONNECTED\",\n  \"stats\": {\n    \"roll\": \"<integer>\",\n    \"pitch\": \"<integer>\",\n    \"armLiftStrokePosition\": \"<integer>\",\n    \"levelingCyclesCount\": \"<integer>\",\n    \"pressureInImpulseSection\": \"<integer>\",\n    \"temperatureRR\": \"<integer>\",\n    \"workingFluidLevel\": \"<integer>\",\n    \"mainPumpRPM\": \"<integer>\",\n    \"overallOperatingTime\": \"<integer>\",\n    \"overallArmLiftingsCount\": \"<integer>\"\n  },\n  \"leftFrontOutriggerEmergencyCode\": \"<integer>\",\n  \"rightFrontOutriggerEmergencyCode\": \"<integer>\",\n  \"leftRearOutriggerEmergencyCode\": \"<integer>\",\n  \"rightRearOutriggerEmergencyCode\": \"<integer>\",\n  \"properties\": [\n    {\n      \"id\": \"<long>\",\n      \"name\": \"<string>\",\n      \"state\": \"OFF\",\n      \"propertyType\": \"REGULAR\"\n    },\n    {\n      \"id\": \"<long>\",\n      \"name\": \"<string>\",\n      \"state\": \"ON\",\n      \"propertyType\": \"REGULAR\"\n    }\n  ]\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/adjacent-systems/suto/"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"id\": \"<long>\",\n  \"status\": \"NOT_CONNECTED\",\n  \"stats\": {\n    \"roll\": \"<integer>\",\n    \"pitch\": \"<integer>\",\n    \"armLiftStrokePosition\": \"<integer>\",\n    \"levelingCyclesCount\": \"<integer>\",\n    \"pressureInImpulseSection\": \"<integer>\",\n    \"temperatureRR\": \"<integer>\",\n    \"workingFluidLevel\": \"<integer>\",\n    \"mainPumpRPM\": \"<integer>\",\n    \"overallOperatingTime\": \"<integer>\",\n    \"overallArmLiftingsCount\": \"<integer>\"\n  },\n  \"leftFrontOutriggerEmergencyCode\": \"<integer>\",\n  \"rightFrontOutriggerEmergencyCode\": \"<integer>\",\n  \"leftRearOutriggerEmergencyCode\": \"<integer>\",\n  \"rightRearOutriggerEmergencyCode\": \"<integer>\",\n  \"properties\": [\n    {\n      \"id\": \"<long>\",\n      \"name\": \"<string>\",\n      \"state\": \"OFF\",\n      \"propertyType\": \"REGULAR\"\n    },\n    {\n      \"id\": \"<long>\",\n      \"name\": \"<string>\",\n      \"state\": \"ON\",\n      \"propertyType\": \"REGULAR\"\n    }\n  ]\n}"}]}], "id": "87513263-c49b-4b7f-a7db-d25c7794ab55"}, {"name": "nppa", "item": [{"name": "byn", "item": [{"name": "GET api/v1/adjacent-systems/nppa/byn", "id": "69fcf566-92b4-482b-9577-f9c228a65c4d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/nppa/byn"}, "response": [{"id": "fe2e5ee0-222a-4baf-ab38-602882497f8f", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/nppa/byn"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"systemStatus\": \"NOT_CONNECTED\",\n  \"operatingMode\": \"description\",\n  \"tvByn\": \"UNDEFINED\",\n  \"isConnected\": \"<boolean>\",\n  \"isNcok\": \"<boolean>\",\n  \"isRgOutNcok\": \"<boolean>\",\n  \"isBuveF2\": \"<boolean>\",\n  \"isBuveF4\": \"<boolean>\",\n  \"isBasuOtr1F3\": \"<boolean>\",\n  \"isBasuOtr2F3\": \"<boolean>\",\n  \"isF1\": \"<boolean>\",\n  \"isF2\": \"<boolean>\",\n  \"isF3\": \"<boolean>\",\n  \"isF4\": \"<boolean>\",\n  \"isF5\": \"<boolean>\",\n  \"isNppaConnected\": \"<boolean>\",\n  \"isBasuOtr1Connected\": \"<boolean>\",\n  \"isBasuOtr2Connected\": \"<boolean>\"\n}"}]}, {"name": "POST api/v1/adjacent-systems/nppa/byn", "id": "1dafcaaa-3f81-47d5-a811-74d7f435af5c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n    \"systemStatus\": \"OK\",\n    \"operatingMode\": \"COMBAT\",\n    \"tvByn\": \"UNDEFINED\",\n    \"isConnected\": false,\n    \"isNcok\": false,\n    \"isRgOutNcok\": false,\n    \"isBuveF2\": false,\n    \"isBuveF4\": false,\n    \"isBasuOtr1F3\": false,\n    \"isBasuOtr2F3\": false,\n    \"isF1\": false,\n    \"isF2\": false,\n    \"isF3\": false,\n    \"isF4\": false,\n    \"isF5\": false,\n    \"isNppaConnected\": false,\n    \"isBasuOtr1Connected\": false,\n    \"isBasuOtr2Connected\": false\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/adjacent-systems/nppa/byn"}, "response": [{"id": "3d300a74-3d9f-4d19-93f8-8baa551e8a01", "name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n  \"systemStatus\": \"NOT_CONNECTED\",\n  \"operatingMode\": \"description\",\n  \"tvByn\": \"UNDEFINED\",\n  \"isConnected\": \"<boolean>\",\n  \"isNcok\": \"<boolean>\",\n  \"isRgOutNcok\": \"<boolean>\",\n  \"isBuveF2\": \"<boolean>\",\n  \"isBuveF4\": \"<boolean>\",\n  \"isBasuOtr1F3\": \"<boolean>\",\n  \"isBasuOtr2F3\": \"<boolean>\",\n  \"isF1\": \"<boolean>\",\n  \"isF2\": \"<boolean>\",\n  \"isF3\": \"<boolean>\",\n  \"isF4\": \"<boolean>\",\n  \"isF5\": \"<boolean>\",\n  \"isNppaConnected\": \"<boolean>\",\n  \"isBasuOtr1Connected\": \"<boolean>\",\n  \"isBasuOtr2Connected\": \"<boolean>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/adjacent-systems/nppa/byn"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"status\": \"WARNING\",\n  \"ncok\": {\n    \"systemStatus\": \"UNDEFINED\",\n    \"operatingMode\": \"NOT_SELECTED\",\n    \"tvNcok\": \"OK\",\n    \"isNcokConnected\": \"<boolean>\",\n    \"isSutoConnected\": \"<boolean>\",\n    \"nppaTestResult\": \"OK\",\n    \"appPresence\": \"<boolean>\",\n    \"otr1AppPresence\": \"<boolean>\",\n    \"otr2AppPresence\": \"<boolean>\",\n    \"otr1TestResult\": \"NOT_CONNECTED\",\n    \"otr2TestResult\": \"ERROR\",\n    \"isOtr1Lunched\": \"OK\",\n    \"isOtr2Lunched\": \"WARNING\"\n  },\n  \"byn\": {\n    \"systemStatus\": \"WARNING\",\n    \"operatingMode\": \"description\",\n    \"tvByn\": \"WARNING\",\n    \"isConnected\": \"<boolean>\",\n    \"isNcok\": \"<boolean>\",\n    \"isRgOutNcok\": \"<boolean>\",\n    \"isBuveF2\": \"<boolean>\",\n    \"isBuveF4\": \"<boolean>\",\n    \"isBasuOtr1F3\": \"<boolean>\",\n    \"isBasuOtr2F3\": \"<boolean>\",\n    \"isF1\": \"<boolean>\",\n    \"isF2\": \"<boolean>\",\n    \"isF3\": \"<boolean>\",\n    \"isF4\": \"<boolean>\",\n    \"isF5\": \"<boolean>\",\n    \"isNppaConnected\": \"<boolean>\",\n    \"isBasuOtr1Connected\": \"<boolean>\",\n    \"isBasuOtr2Connected\": \"<boolean>\"\n  },\n  \"leftRocketFormData\": {\n    \"createdAt\": \"<dateTime>\",\n    \"plantMissile\": \"<string>\",\n    \"warhead\": \"UNDEFINED\",\n    \"gsnType\": \"K_GSN\",\n    \"alpType\": \"TWO_ALP\",\n    \"isTelemetryIntegrated\": \"<boolean>\",\n    \"purposeType\": \"TEM\"\n  },\n  \"rightRocketFormData\": {\n    \"createdAt\": \"<dateTime>\",\n    \"plantMissile\": \"<string>\",\n    \"warhead\": \"value\",\n    \"gsnType\": \"UNDEFINED\",\n    \"alpType\": \"value\",\n    \"isTelemetryIntegrated\": \"<boolean>\",\n    \"purposeType\": \"COMBAT\"\n  },\n  \"leftRocketInitialData\": {\n    \"loadTemperature\": \"<double>\",\n    \"latitudeRad\": \"<string>\",\n    \"longitudeRad\": \"<string>\",\n    \"altitude\": \"<double>\",\n    \"inclinationAngle\": \"<double>\",\n    \"trajectory\": \"UNKNOWN\",\n    \"readiness\": \"BG_3_TO_BG_1\",\n    \"isProDetected\": \"<boolean>\",\n    \"missileOperatingMode\": \"BASE_INERTIAL\",\n    \"validatedByTlc\": \"<boolean>\",\n    \"tlCode\": \"<string>\"\n  },\n  \"rightRocketInitialData\": {\n    \"loadTemperature\": \"<double>\",\n    \"latitudeRad\": \"<string>\",\n    \"longitudeRad\": \"<string>\",\n    \"altitude\": \"<double>\",\n    \"inclinationAngle\": \"<double>\",\n    \"trajectory\": \"BALLISTIC\",\n    \"readiness\": \"BG_2_TO_BG_1\",\n    \"isProDetected\": \"<boolean>\",\n    \"missileOperatingMode\": \"description\",\n    \"validatedByTlc\": \"<boolean>\",\n    \"tlCode\": \"<string>\"\n  }\n}"}]}], "id": "549eef10-f2b6-4a75-9585-04d29286197e"}, {"name": "command", "item": [{"name": "{command_id}", "item": [{"name": "commit", "item": [{"name": "POST api/v1/adjacent-systems/nppa/command/{command_id}/commit", "id": "08561a46-23a6-4d6d-a2d3-fb2dcae61b72", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/nppa/command/:command_id/commit", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "nppa", "command", ":command_id", "commit"], "variable": [{"key": "command_id", "value": "18"}]}}, "response": [{"id": "54e1fdbc-b103-46c0-8d06-ec38d421a062", "name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/nppa/command/:command_id/commit", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "nppa", "command", ":command_id", "commit"], "variable": [{"key": "command_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"id\": \"<long>\",\n  \"command\": \"<string>\",\n  \"caption\": \"<string>\",\n  \"adjacentSystem\": \"TPC\",\n  \"generationTime\": \"<dateTime>\",\n  \"executionTime\": \"<dateTime>\"\n}"}]}], "id": "df8f1010-38f4-4ee8-b85c-b81059f2bb45"}, {"name": "remove", "item": [{"name": "POST api/v1/adjacent-systems/nppa/command/{command_id}/remove", "id": "1c5f3698-71f2-4f3f-99b2-7a082a27bbd1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/nppa/command/:command_id/remove", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "nppa", "command", ":command_id", "remove"], "variable": [{"key": "command_id", "value": "37"}]}}, "response": [{"id": "cdd7615b-d0f9-4b1e-bf5b-7889c46b51db", "name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "*/*"}], "url": {"raw": "{{baseUrl}}/api/v1/adjacent-systems/nppa/command/:command_id/commit", "host": ["{{baseUrl}}"], "path": ["api", "v1", "adjacent-systems", "nppa", "command", ":command_id", "commit"], "variable": [{"key": "command_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"id\": \"<long>\",\n  \"command\": \"<string>\",\n  \"caption\": \"<string>\",\n  \"adjacentSystem\": \"TPC\",\n  \"generationTime\": \"<dateTime>\",\n  \"executionTime\": \"<dateTime>\"\n}"}]}], "id": "2a103d49-1998-45de-a4c3-bdaeac7c218e"}], "id": "71e4145c-13d5-4895-b823-812609fd62ed"}, {"name": "GET api/v1/adjacent-systems/nppa/command", "id": "4531db47-f19a-4a4f-b21e-c79022762125", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/nppa/command"}, "response": [{"id": "9b958eaf-fb1d-4689-b57c-99d3de73ad50", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/nppa/command"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"id\": \"<long>\",\n  \"command\": \"<string>\",\n  \"caption\": \"<string>\",\n  \"adjacentSystem\": \"TPC\",\n  \"generationTime\": \"<dateTime>\",\n  \"executionTime\": \"<dateTime>\"\n}"}]}], "id": "7a3c9230-2ee0-4428-98a2-79702b2673d6"}, {"name": "ncok", "item": [{"name": "GET api/v1/adjacent-systems/nppa/ncok", "id": "14a30c1c-f33f-4ae5-983b-ac5293661710", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/nppa/ncok"}, "response": [{"id": "796e57ac-3938-4500-9d7f-879d7d67f561", "name": "OK", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "*/*"}], "url": "{{baseUrl}}/api/v1/adjacent-systems/nppa/ncok"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"systemStatus\": \"OK\",\n  \"operatingMode\": \"description\",\n  \"tvNcok\": \"ERROR\",\n  \"isNcokConnected\": \"<boolean>\",\n  \"isSutoConnected\": \"<boolean>\",\n  \"nppaTestResult\": \"ERROR\",\n  \"appPresence\": \"<boolean>\",\n  \"otr1AppPresence\": \"<boolean>\",\n  \"otr2AppPresence\": \"<boolean>\",\n  \"otr1TestResult\": \"description\",\n  \"otr2TestResult\": \"OK\",\n  \"isOtr1Lunched\": \"WARNING\",\n  \"isOtr2Lunched\": \"WARNING\"\n}"}]}, {"name": "POST api/v1/adjacent-systems/nppa/ncok", "id": "ee39eb2e-4abd-412a-afdd-59d004cf535b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n    \"systemStatus\": \"OK\",\n    \"operatingMode\": \"COMBAT\",\n    \"tvNcok\": \"OK\",\n    \"isNcokConnected\": true,\n    \"isSutoConnected\": true,\n    \"nppaTestResult\": \"WARNING\",\n    \"appPresence\": false,\n    \"otr1AppPresence\": false,\n    \"otr2AppPresence\": false,\n    \"otr1TestResult\": \"UNDEFINED\",\n    \"otr2TestResult\": \"UNDEFINED\",\n    \"isOtr1Lunched\": \"UNDEFINED\",\n    \"isOtr2Lunched\": \"UNDEFINED\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/adjacent-systems/nppa/ncok"}, "response": [{"id": "7e9d4987-6082-4885-ae77-2235dac6ec79", "name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "*/*"}], "body": {"mode": "raw", "raw": "{\n  \"systemStatus\": \"OK\",\n  \"operatingMode\": \"description\",\n  \"tvNcok\": \"ERROR\",\n  \"isNcokConnected\": \"<boolean>\",\n  \"isSutoConnected\": \"<boolean>\",\n  \"nppaTestResult\": \"ERROR\",\n  \"appPresence\": \"<boolean>\",\n  \"otr1AppPresence\": \"<boolean>\",\n  \"otr2AppPresence\": \"<boolean>\",\n  \"otr1TestResult\": \"description\",\n  \"otr2TestResult\": \"OK\",\n  \"isOtr1Lunched\": \"WARNING\",\n  \"isOtr2Lunched\": \"WARNING\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/api/v1/adjacent-systems/nppa/ncok"}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "*/*"}], "cookie": [], "responseTime": null, "body": "{\n  \"status\": \"WARNING\",\n  \"ncok\": {\n    \"systemStatus\": \"UNDEFINED\",\n    \"operatingMode\": \"NOT_SELECTED\",\n    \"tvNcok\": \"OK\",\n    \"isNcokConnected\": \"<boolean>\",\n    \"isSutoConnected\": \"<boolean>\",\n    \"nppaTestResult\": \"OK\",\n    \"appPresence\": \"<boolean>\",\n    \"otr1AppPresence\": \"<boolean>\",\n    \"otr2AppPresence\": \"<boolean>\",\n    \"otr1TestResult\": \"NOT_CONNECTED\",\n    \"otr2TestResult\": \"ERROR\",\n    \"isOtr1Lunched\": \"OK\",\n    \"isOtr2Lunched\": \"WARNING\"\n  },\n  \"byn\": {\n    \"systemStatus\": \"WARNING\",\n    \"operatingMode\": \"description\",\n    \"tvByn\": \"WARNING\",\n    \"isConnected\": \"<boolean>\",\n    \"isNcok\": \"<boolean>\",\n    \"isRgOutNcok\": \"<boolean>\",\n    \"isBuveF2\": \"<boolean>\",\n    \"isBuveF4\": \"<boolean>\",\n    \"isBasuOtr1F3\": \"<boolean>\",\n    \"isBasuOtr2F3\": \"<boolean>\",\n    \"isF1\": \"<boolean>\",\n    \"isF2\": \"<boolean>\",\n    \"isF3\": \"<boolean>\",\n    \"isF4\": \"<boolean>\",\n    \"isF5\": \"<boolean>\",\n    \"isNppaConnected\": \"<boolean>\",\n    \"isBasuOtr1Connected\": \"<boolean>\",\n    \"isBasuOtr2Connected\": \"<boolean>\"\n  },\n  \"leftRocketFormData\": {\n    \"createdAt\": \"<dateTime>\",\n    \"plantMissile\": \"<string>\",\n    \"warhead\": \"UNDEFINED\",\n    \"gsnType\": \"K_GSN\",\n    \"alpType\": \"TWO_ALP\",\n    \"isTelemetryIntegrated\": \"<boolean>\",\n    \"purposeType\": \"TEM\"\n  },\n  \"rightRocketFormData\": {\n    \"createdAt\": \"<dateTime>\",\n    \"plantMissile\": \"<string>\",\n    \"warhead\": \"value\",\n    \"gsnType\": \"UNDEFINED\",\n    \"alpType\": \"value\",\n    \"isTelemetryIntegrated\": \"<boolean>\",\n    \"purposeType\": \"COMBAT\"\n  },\n  \"leftRocketInitialData\": {\n    \"loadTemperature\": \"<double>\",\n    \"latitudeRad\": \"<string>\",\n    \"longitudeRad\": \"<string>\",\n    \"altitude\": \"<double>\",\n    \"inclinationAngle\": \"<double>\",\n    \"trajectory\": \"UNKNOWN\",\n    \"readiness\": \"BG_3_TO_BG_1\",\n    \"isProDetected\": \"<boolean>\",\n    \"missileOperatingMode\": \"BASE_INERTIAL\",\n    \"validatedByTlc\": \"<boolean>\",\n    \"tlCode\": \"<string>\"\n  },\n  \"rightRocketInitialData\": {\n    \"loadTemperature\": \"<double>\",\n    \"latitudeRad\": \"<string>\",\n    \"longitudeRad\": \"<string>\",\n    \"altitude\": \"<double>\",\n    \"inclinationAngle\": \"<double>\",\n    \"trajectory\": \"BALLISTIC\",\n    \"readiness\": \"BG_2_TO_BG_1\",\n    \"isProDetected\": \"<boolean>\",\n    \"missileOperatingMode\": \"description\",\n    \"validatedByTlc\": \"<boolean>\",\n    \"tlCode\": \"<string>\"\n  }\n}"}]}], "id": "166f27f0-5e39-46b0-ba05-eb368f1bb482"}], "id": "b1ca5a04-7543-4ba0-86b1-b19d7555b3ca"}], "id": "2f6deac4-8cd6-4ccc-ae86-3d4a0ede98bb"}], "id": "3c9be348-b103-4ee3-9abf-f12e18e1463f"}], "id": "24a851d0-f1ab-4588-bcb0-5b2105945eae"}, {"name": "users", "item": [{"name": "api-user", "item": [{"name": "users/create-api", "id": "1d0f724a-7523-49a6-ad55-7a8a86200c41", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "123456 superUser superPassword", "value": "", "type": "text"}]}, "url": "{{baseUrl}}/users/create-api"}, "response": []}], "id": "d099b5fe-f65b-472c-b963-9c69e5cd3a81"}, {"name": "test", "id": "7d224850-6f03-449d-a659-e3ba6ef8071f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "sdf sdf 123", "value": "s", "type": "text"}]}, "url": "{{baseUrl}}/users/test"}, "response": []}, {"name": "test path", "id": "c54f019f-1871-4b7c-9d30-13c7c3e7006e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "12345 user pass", "value": "ss", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/users/test/:path", "host": ["{{baseUrl}}"], "path": ["users", "test", ":path"], "variable": [{"key": "path", "value": "2"}]}}, "response": []}], "id": "588025a2-0f1b-4279-ae02-7df696b6d985"}], "variable": [{"key": "baseUrl", "value": "http://localhost:8079"}]}