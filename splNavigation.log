2025-01-09 14:05:30.458  INFO 17032 --- [main] c.d.s.s.SplNavigationApplication         : Starting SplNavigationApplication v1.0.2 using Java ******** on WIN-ARR4C6ROFQD with PID 17032 (D:\GitHub\flow-crm-tutorial\splNavigation\target\splNavigation-1.0.2.jar started by admin in D:\GitHub\flow-crm-tutorial\vaadin-header)
2025-01-09 14:05:30.461  INFO 17032 --- [main] c.d.s.s.SplNavigationApplication         : The following 1 profile is active: "dev"
2025-01-09 14:05:31.926  INFO 17032 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-01-09 14:05:32.173  INFO 17032 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 224 ms. Found 3 JPA repository interfaces.
2025-01-09 14:05:33.145  INFO 17032 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
2025-01-09 14:05:33.159  INFO 17032 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-01-09 14:05:33.159  INFO 17032 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-01-09 14:05:33.264  INFO 17032 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-01-09 14:05:33.265  INFO 17032 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2726 ms
2025-01-09 14:05:33.729  INFO 17032 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-01-09 14:05:33.851  INFO 17032 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.14.Final
2025-01-09 14:05:34.182  INFO 17032 --- [main] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-01-09 14:05:34.359  INFO 17032 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-01-09 14:05:34.630  INFO 17032 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-01-09 14:05:34.665  INFO 17032 --- [main] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.PostgreSQLDialect
2025-01-09 14:05:35.698  INFO 17032 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-01-09 14:05:35.710  INFO 17032 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-01-09 14:05:36.268  INFO 17032 --- [main] c.v.f.s.VaadinServletContextInitializer  : Search for VaadinAppShell took 43 ms
2025-01-09 14:05:37.191  INFO 17032 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@22f4f8f2, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@bbd4791, org.springframework.security.web.context.SecurityContextPersistenceFilter@7f9fc8bd, org.springframework.security.web.header.HeaderWriterFilter@50448409, org.springframework.security.web.csrf.CsrfFilter@1115433e, org.springframework.security.web.authentication.logout.LogoutFilter@7c781c42, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3e7b65d7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3c854752, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@40a72ecd, org.springframework.security.web.session.SessionManagementFilter@75e09567, org.springframework.security.web.access.ExceptionTranslationFilter@55651434, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@52bf7bf6]
2025-01-09 14:05:37.206  WARN 17032 --- [main] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-01-09 14:05:37.207  INFO 17032 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/**']
2025-01-09 14:05:37.303  WARN 17032 --- [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-01-09 14:05:37.846  INFO 17032 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-01-09 14:05:37.908  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereHandler com.vaadin.flow.server.communication.PushAtmosphereHandler mapped to context-path: /*
2025-01-09 14:05:37.909  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed the following AtmosphereInterceptor mapped to AtmosphereHandler com.vaadin.flow.server.communication.PushAtmosphereHandler
2025-01-09 14:05:37.938  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Atmosphere is using org.atmosphere.util.VoidAnnotationProcessor for processing annotation
2025-01-09 14:05:37.947  INFO 17032 --- [main] org.atmosphere.util.ForkJoinPool         : Using ForkJoinPool  java.util.concurrent.ForkJoinPool. Set the org.atmosphere.cpr.broadcaster.maxAsyncWriteThreads to -1 to fully use its power.
2025-01-09 14:05:37.952  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed WebSocketProtocol org.atmosphere.websocket.protocol.SimpleHttpProtocol 
2025-01-09 14:05:37.959  INFO 17032 --- [main] o.a.container.JSR356AsyncSupport         : JSR 356 Mapping path /VAADIN/push
2025-01-09 14:05:37.979  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installing Default AtmosphereInterceptors
2025-01-09 14:05:37.983  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.CorsInterceptor : CORS Interceptor Support
2025-01-09 14:05:37.983  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.CacheHeadersInterceptor : Default Response's Headers Interceptor
2025-01-09 14:05:37.984  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.PaddingAtmosphereInterceptor : Browser Padding Interceptor Support
2025-01-09 14:05:37.985  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.AndroidAtmosphereInterceptor : Android Interceptor Support
2025-01-09 14:05:37.986  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Dropping Interceptor org.atmosphere.interceptor.HeartbeatInterceptor
2025-01-09 14:05:37.986  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.SSEAtmosphereInterceptor : SSE Interceptor Support
2025-01-09 14:05:37.988  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.JSONPAtmosphereInterceptor : JSONP Interceptor Support
2025-01-09 14:05:37.989  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.JavaScriptProtocol : Atmosphere JavaScript Protocol
2025-01-09 14:05:37.990  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor : org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor
2025-01-09 14:05:37.990  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.OnDisconnectInterceptor : Browser disconnection detection
2025-01-09 14:05:37.991  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.IdleResourceInterceptor : org.atmosphere.interceptor.IdleResourceInterceptor
2025-01-09 14:05:37.991  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Set org.atmosphere.cpr.AtmosphereInterceptor.disableDefaults to disable them.
2025-01-09 14:05:37.991  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor CORS Interceptor Support with priority FIRST_BEFORE_DEFAULT 
2025-01-09 14:05:37.996  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor Default Response's Headers Interceptor with priority AFTER_DEFAULT 
2025-01-09 14:05:37.997  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor Browser Padding Interceptor Support with priority AFTER_DEFAULT 
2025-01-09 14:05:37.997  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor Android Interceptor Support with priority AFTER_DEFAULT 
2025-01-09 14:05:37.998  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor SSE Interceptor Support with priority AFTER_DEFAULT 
2025-01-09 14:05:37.998  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor JSONP Interceptor Support with priority AFTER_DEFAULT 
2025-01-09 14:05:37.998  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor Atmosphere JavaScript Protocol with priority AFTER_DEFAULT 
2025-01-09 14:05:37.998  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor with priority AFTER_DEFAULT 
2025-01-09 14:05:37.998  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor Browser disconnection detection with priority AFTER_DEFAULT 
2025-01-09 14:05:37.999  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor org.atmosphere.interceptor.IdleResourceInterceptor with priority BEFORE_DEFAULT 
2025-01-09 14:05:37.999  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Using EndpointMapper class org.atmosphere.util.DefaultEndpointMapper
2025-01-09 14:05:38.000  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Using BroadcasterCache: org.atmosphere.cache.UUIDBroadcasterCache
2025-01-09 14:05:38.000  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Default Broadcaster Class: org.atmosphere.cpr.DefaultBroadcaster
2025-01-09 14:05:38.000  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Broadcaster Shared List Resources: false
2025-01-09 14:05:38.000  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Broadcaster Polling Wait Time 100
2025-01-09 14:05:38.000  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Shared ExecutorService supported: true
2025-01-09 14:05:38.000  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Messaging ExecutorService Pool Size unavailable - Not instance of ThreadPoolExecutor
2025-01-09 14:05:38.002  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Async I/O Thread Pool Size: 200
2025-01-09 14:05:38.002  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Using BroadcasterFactory: org.atmosphere.cpr.DefaultBroadcasterFactory
2025-01-09 14:05:38.002  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Using AtmosphereResurceFactory: org.atmosphere.cpr.DefaultAtmosphereResourceFactory
2025-01-09 14:05:38.002  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Using WebSocketProcessor: org.atmosphere.websocket.DefaultWebSocketProcessor
2025-01-09 14:05:38.005  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Invoke AtmosphereInterceptor on WebSocket message true
2025-01-09 14:05:38.006  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : HttpSession supported: true
2025-01-09 14:05:38.006  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Atmosphere is using DefaultAtmosphereObjectFactory for dependency injection and object creation
2025-01-09 14:05:38.007  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Atmosphere is using async support: org.atmosphere.container.JSR356AsyncSupport running under container: Apache Tomcat/9.0.71 using javax.servlet/3.0 and jsr356/WebSocket API
2025-01-09 14:05:38.007  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Atmosphere Framework 2.7.3.slf4jvaadin4 started.
2025-01-09 14:05:38.012  INFO 17032 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor  Track Message Size Interceptor using | with priority BEFORE_DEFAULT 
2025-01-09 14:05:38.068  INFO 17032 --- [main] c.v.f.s.DefaultDeploymentConfiguration   : Vaadin is running in production mode.

The following EXPERIMENTAL features are enabled:
- Enforce client / constraint / binder validation


2025-01-09 14:05:38.120  INFO 17032 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path ''
2025-01-09 14:05:38.140  INFO 17032 --- [main] c.d.s.s.SplNavigationApplication         : Started SplNavigationApplication in 8.437 seconds (JVM running for 9.062)
2025-01-09 14:05:38.197  INFO 17032 --- [main] c.d.s.s.SplNavigationApplication         : active profile [dev]
2025-01-09 14:05:38.197  INFO 17032 --- [main] c.d.s.s.service.FakeBinsService          : starting BinsService with mock bean
2025-01-09 14:05:38.197  INFO 17032 --- [main] c.d.s.s.service.FakeBinsService          : com.deb.spl.splNavigation.service.FakeBinsService is starting with parameters: 
2025-01-09 14:05:38.198  INFO 17032 --- [main] c.d.s.s.service.FakeMsuService           : starting MsuService with mock bean
2025-01-09 14:05:38.198  INFO 17032 --- [main] c.d.s.s.service.FakeMsuService           : com.deb.spl.splNavigation.service.FakeMsuService is starting with parameters: 
2025-01-09 14:10:20.303  INFO 17032 --- [http-nio-8081-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-09 14:10:20.304  INFO 17032 --- [http-nio-8081-exec-2] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-01-09 14:10:20.306  INFO 17032 --- [http-nio-8081-exec-2] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-01-09 14:19:42.698  INFO 17032 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-01-09 14:19:42.702  INFO 17032 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-01-09 14:19:42.707  INFO 17032 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-01-09 14:20:06.004  INFO 24160 --- [main] c.d.s.s.SplNavigationApplication         : Starting SplNavigationApplication v1.0.2 using Java ******** on WIN-ARR4C6ROFQD with PID 24160 (D:\GitHub\flow-crm-tutorial\splNavigation\target\splNavigation-1.0.2.jar started by admin in D:\GitHub\flow-crm-tutorial\vaadin-header)
2025-01-09 14:20:06.012  INFO 24160 --- [main] c.d.s.s.SplNavigationApplication         : The following 1 profile is active: "dev"
2025-01-09 14:20:08.342  INFO 24160 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-01-09 14:20:09.145  INFO 24160 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 760 ms. Found 3 JPA repository interfaces.
2025-01-09 14:20:10.636  INFO 24160 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
2025-01-09 14:20:10.661  INFO 24160 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-01-09 14:20:10.662  INFO 24160 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-01-09 14:20:10.868  INFO 24160 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-01-09 14:20:10.870  INFO 24160 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 4731 ms
2025-01-09 14:20:11.725  INFO 24160 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-01-09 14:20:11.906  INFO 24160 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.14.Final
2025-01-09 14:20:12.335  INFO 24160 --- [main] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-01-09 14:20:12.586  INFO 24160 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-01-09 14:20:13.032  INFO 24160 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-01-09 14:20:13.112  INFO 24160 --- [main] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.PostgreSQLDialect
2025-01-09 14:20:16.850  INFO 24160 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-01-09 14:20:16.888  INFO 24160 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-01-09 14:20:18.719  INFO 24160 --- [main] c.v.f.s.VaadinServletContextInitializer  : Search for VaadinAppShell took 72 ms
2025-01-09 14:20:20.182  INFO 8340 --- [main] c.d.s.s.SplNavigationApplication         : Starting SplNavigationApplication v1.0.2 using Java ******** on WIN-ARR4C6ROFQD with PID 8340 (D:\GitHub\flow-crm-tutorial\splNavigation\target\splNavigation-1.0.2.jar started by admin in D:\GitHub\flow-crm-tutorial\vaadin-header)
2025-01-09 14:20:20.194  INFO 8340 --- [main] c.d.s.s.SplNavigationApplication         : The following 1 profile is active: "dev"
2025-01-09 14:20:20.618  INFO 24160 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@12f49ca8, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1fd9893c, org.springframework.security.web.context.SecurityContextPersistenceFilter@c386958, org.springframework.security.web.header.HeaderWriterFilter@66236a0a, org.springframework.security.web.csrf.CsrfFilter@7f9fc8bd, org.springframework.security.web.authentication.logout.LogoutFilter@2152ab30, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@44d64d4e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1dd74143, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1b2df3aa, org.springframework.security.web.session.SessionManagementFilter@633fd91, org.springframework.security.web.access.ExceptionTranslationFilter@3bec5821, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@575b5f7d]
2025-01-09 14:20:20.625  WARN 24160 --- [main] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-01-09 14:20:20.626  INFO 24160 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/**']
2025-01-09 14:20:20.818  WARN 24160 --- [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-01-09 14:20:22.069  INFO 24160 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-01-09 14:20:22.172  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereHandler com.vaadin.flow.server.communication.PushAtmosphereHandler mapped to context-path: /*
2025-01-09 14:20:22.173  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed the following AtmosphereInterceptor mapped to AtmosphereHandler com.vaadin.flow.server.communication.PushAtmosphereHandler
2025-01-09 14:20:22.223  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Atmosphere is using org.atmosphere.util.VoidAnnotationProcessor for processing annotation
2025-01-09 14:20:22.237  INFO 24160 --- [main] org.atmosphere.util.ForkJoinPool         : Using ForkJoinPool  java.util.concurrent.ForkJoinPool. Set the org.atmosphere.cpr.broadcaster.maxAsyncWriteThreads to -1 to fully use its power.
2025-01-09 14:20:22.245  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed WebSocketProtocol org.atmosphere.websocket.protocol.SimpleHttpProtocol 
2025-01-09 14:20:22.259  INFO 24160 --- [main] o.a.container.JSR356AsyncSupport         : JSR 356 Mapping path /VAADIN/push
2025-01-09 14:20:22.285  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installing Default AtmosphereInterceptors
2025-01-09 14:20:22.286  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.CorsInterceptor : CORS Interceptor Support
2025-01-09 14:20:22.286  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.CacheHeadersInterceptor : Default Response's Headers Interceptor
2025-01-09 14:20:22.288  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.PaddingAtmosphereInterceptor : Browser Padding Interceptor Support
2025-01-09 14:20:22.290  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.AndroidAtmosphereInterceptor : Android Interceptor Support
2025-01-09 14:20:22.291  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Dropping Interceptor org.atmosphere.interceptor.HeartbeatInterceptor
2025-01-09 14:20:22.293  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.SSEAtmosphereInterceptor : SSE Interceptor Support
2025-01-09 14:20:22.295  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.JSONPAtmosphereInterceptor : JSONP Interceptor Support
2025-01-09 14:20:22.298  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.JavaScriptProtocol : Atmosphere JavaScript Protocol
2025-01-09 14:20:22.299  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor : org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor
2025-01-09 14:20:22.301  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.OnDisconnectInterceptor : Browser disconnection detection
2025-01-09 14:20:22.302  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.IdleResourceInterceptor : org.atmosphere.interceptor.IdleResourceInterceptor
2025-01-09 14:20:22.303  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Set org.atmosphere.cpr.AtmosphereInterceptor.disableDefaults to disable them.
2025-01-09 14:20:22.304  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor CORS Interceptor Support with priority FIRST_BEFORE_DEFAULT 
2025-01-09 14:20:22.313  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor Default Response's Headers Interceptor with priority AFTER_DEFAULT 
2025-01-09 14:20:22.314  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor Browser Padding Interceptor Support with priority AFTER_DEFAULT 
2025-01-09 14:20:22.315  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor Android Interceptor Support with priority AFTER_DEFAULT 
2025-01-09 14:20:22.315  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor SSE Interceptor Support with priority AFTER_DEFAULT 
2025-01-09 14:20:22.316  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor JSONP Interceptor Support with priority AFTER_DEFAULT 
2025-01-09 14:20:22.316  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor Atmosphere JavaScript Protocol with priority AFTER_DEFAULT 
2025-01-09 14:20:22.316  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor with priority AFTER_DEFAULT 
2025-01-09 14:20:22.317  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor Browser disconnection detection with priority AFTER_DEFAULT 
2025-01-09 14:20:22.317  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor org.atmosphere.interceptor.IdleResourceInterceptor with priority BEFORE_DEFAULT 
2025-01-09 14:20:22.327 ERROR 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Failed to initialize Atmosphere Framework

java.lang.IllegalStateException: Shutdown in progress
	at java.base/java.lang.ApplicationShutdownHooks.add(ApplicationShutdownHooks.java:66) ~[na:na]
	at java.base/java.lang.Runtime.addShutdownHook(Runtime.java:216) ~[na:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:928) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:833) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at com.vaadin.flow.server.communication.PushRequestHandler.initAtmosphere(PushRequestHandler.java:244) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.communication.JSR356WebsocketInitializer.initAtmosphereForVaadinServlet(JSR356WebsocketInitializer.java:186) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.communication.JSR356WebsocketInitializer.init(JSR356WebsocketInitializer.java:151) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.spring.VaadinWebsocketEndpointExporter.registerEndpoints(VaadinWebsocketEndpointExporter.java:51) ~[vaadin-spring-23.3.1.jar!/:na]
	at org.springframework.web.socket.server.standard.ServerEndpointExporter.afterSingletonsInstantiated(ServerEndpointExporter.java:112) ~[spring-websocket-5.3.25.jar!/:5.3.25]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:974) ~[spring-beans-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at com.deb.spl.splNavigation.SplNavigationApplication.main(SplNavigationApplication.java:43) ~[classes!/:1.0.2]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[splNavigation-1.0.2.jar:1.0.2]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108) ~[splNavigation-1.0.2.jar:1.0.2]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[splNavigation-1.0.2.jar:1.0.2]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65) ~[splNavigation-1.0.2.jar:1.0.2]

2025-01-09 14:20:22.331  WARN 24160 --- [main] c.v.f.s.c.JSR356WebsocketInitializer     : Failed to initialize Atmosphere for springServlet

java.lang.RuntimeException: Atmosphere init failed
	at com.vaadin.flow.server.communication.PushRequestHandler.initAtmosphere(PushRequestHandler.java:253) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.communication.JSR356WebsocketInitializer.initAtmosphereForVaadinServlet(JSR356WebsocketInitializer.java:186) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.communication.JSR356WebsocketInitializer.init(JSR356WebsocketInitializer.java:151) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.spring.VaadinWebsocketEndpointExporter.registerEndpoints(VaadinWebsocketEndpointExporter.java:51) ~[vaadin-spring-23.3.1.jar!/:na]
	at org.springframework.web.socket.server.standard.ServerEndpointExporter.afterSingletonsInstantiated(ServerEndpointExporter.java:112) ~[spring-websocket-5.3.25.jar!/:5.3.25]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:974) ~[spring-beans-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at com.deb.spl.splNavigation.SplNavigationApplication.main(SplNavigationApplication.java:43) ~[classes!/:1.0.2]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[splNavigation-1.0.2.jar:1.0.2]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108) ~[splNavigation-1.0.2.jar:1.0.2]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[splNavigation-1.0.2.jar:1.0.2]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65) ~[splNavigation-1.0.2.jar:1.0.2]
Caused by: javax.servlet.ServletException: java.lang.IllegalStateException: Shutdown in progress
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:946) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:833) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at com.vaadin.flow.server.communication.PushRequestHandler.initAtmosphere(PushRequestHandler.java:244) ~[flow-server-23.3.1.jar!/:23.3.1]
	... 22 common frames omitted
Caused by: java.lang.IllegalStateException: Shutdown in progress
	at java.base/java.lang.ApplicationShutdownHooks.add(ApplicationShutdownHooks.java:66) ~[na:na]
	at java.base/java.lang.Runtime.addShutdownHook(Runtime.java:216) ~[na:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:928) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	... 24 common frames omitted

2025-01-09 14:20:22.774  INFO 24160 --- [main] c.v.f.s.DefaultDeploymentConfiguration   : Vaadin is running in production mode.

The following EXPERIMENTAL features are enabled:
- Enforce client / constraint / binder validation


2025-01-09 14:20:22.856  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereHandler com.vaadin.flow.server.communication.PushAtmosphereHandler mapped to context-path: /*
2025-01-09 14:20:22.857  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed the following AtmosphereInterceptor mapped to AtmosphereHandler com.vaadin.flow.server.communication.PushAtmosphereHandler
2025-01-09 14:20:22.870  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Atmosphere is using org.atmosphere.util.VoidAnnotationProcessor for processing annotation
2025-01-09 14:20:22.882  INFO 24160 --- [main] org.atmosphere.util.ForkJoinPool         : Using ForkJoinPool  java.util.concurrent.ForkJoinPool. Set the org.atmosphere.cpr.broadcaster.maxAsyncWriteThreads to -1 to fully use its power.
2025-01-09 14:20:22.884  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed WebSocketProtocol org.atmosphere.websocket.protocol.SimpleHttpProtocol 
2025-01-09 14:20:22.886  INFO 24160 --- [main] o.a.container.JSR356AsyncSupport         : JSR 356 Mapping path /VAADIN/push
2025-01-09 14:20:22.887  WARN 24160 --- [main] o.a.container.JSR356AsyncSupport         : Duplicate Servlet Mapping Path /VAADIN/push. Use org.atmosphere.container.JSR356AsyncSupport.mappingPath init-param to prevent this message
2025-01-09 14:20:22.890  WARN 24160 --- [main] o.a.container.JSR356AsyncSupport         : Duplicate guess /vaadinServlet

javax.websocket.DeploymentException: Multiple Endpoints may not be deployed to the same path [/VAADIN/push] : existing endpoint was [class org.atmosphere.container.JSR356Endpoint] and new endpoint is [class org.atmosphere.container.JSR356Endpoint]
	at org.apache.tomcat.websocket.server.WsServerContainer.addEndpoint(WsServerContainer.java:207) ~[tomcat-embed-websocket-9.0.71.jar!/:na]
	at org.apache.tomcat.websocket.server.WsServerContainer.addEndpoint(WsServerContainer.java:131) ~[tomcat-embed-websocket-9.0.71.jar!/:na]
	at org.atmosphere.container.JSR356AsyncSupport.<init>(JSR356AsyncSupport.java:105) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at org.atmosphere.container.JSR356AsyncSupport.<init>(JSR356AsyncSupport.java:48) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:na]
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499) ~[na:na]
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480) ~[na:na]
	at org.atmosphere.cpr.DefaultAsyncSupportResolver.newCometSupport(DefaultAsyncSupportResolver.java:237) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at org.atmosphere.cpr.DefaultAsyncSupportResolver.resolveWebSocket(DefaultAsyncSupportResolver.java:308) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at org.atmosphere.cpr.DefaultAsyncSupportResolver.resolve(DefaultAsyncSupportResolver.java:294) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at org.atmosphere.cpr.AtmosphereFramework.autoDetectContainer(AtmosphereFramework.java:2086) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:909) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:833) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at com.vaadin.flow.server.communication.PushRequestHandler.initAtmosphere(PushRequestHandler.java:244) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.communication.PushRequestHandler.<init>(PushRequestHandler.java:96) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.VaadinServletService.createRequestHandlers(VaadinServletService.java:107) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.VaadinService.init(VaadinService.java:231) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.VaadinServletService.init(VaadinServletService.java:175) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.spring.SpringVaadinServletService.init(SpringVaadinServletService.java:103) ~[vaadin-spring-23.3.1.jar!/:na]
	at com.vaadin.flow.spring.SpringServlet.createServletService(SpringServlet.java:115) ~[vaadin-spring-23.3.1.jar!/:na]
	at com.vaadin.flow.server.VaadinServlet.createServletService(VaadinServlet.java:307) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.VaadinServlet.init(VaadinServlet.java:130) ~[flow-server-23.3.1.jar!/:23.3.1]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1161) ~[tomcat-embed-core-9.0.71.jar!/:na]
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:1010) ~[tomcat-embed-core-9.0.71.jar!/:na]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.load(TomcatEmbeddedContext.java:81) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183) ~[na:na]
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:762) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline$7$1.accept(ReferencePipeline.java:276) ~[na:na]
	at java.base/java.util.TreeMap$ValueSpliterator.forEachRemaining(TreeMap.java:3215) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499) ~[na:na]
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150) ~[na:na]
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596) ~[na:na]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.lambda$deferredLoadOnStartup$0(TomcatEmbeddedContext.java:64) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.doWithThreadContextClassLoader(TomcatEmbeddedContext.java:105) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.deferredLoadOnStartup(TomcatEmbeddedContext.java:63) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.performDeferredLoadOnStartup(TomcatWebServer.java:305) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start(TomcatWebServer.java:216) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.web.servlet.context.WebServerStartStopLifecycle.start(WebServerStartStopLifecycle.java:43) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356) ~[spring-context-5.3.25.jar!/:5.3.25]
	at java.base/java.lang.Iterable.forEach(Iterable.java:75) ~[na:na]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:935) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:586) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at com.deb.spl.splNavigation.SplNavigationApplication.main(SplNavigationApplication.java:43) ~[classes!/:1.0.2]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[splNavigation-1.0.2.jar:1.0.2]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108) ~[splNavigation-1.0.2.jar:1.0.2]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[splNavigation-1.0.2.jar:1.0.2]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65) ~[splNavigation-1.0.2.jar:1.0.2]

2025-01-09 14:20:22.986  WARN 24160 --- [main] o.a.container.JSR356AsyncSupport         : Duplicate Servlet Mapping Path /vaadinServlet. Use org.atmosphere.container.JSR356AsyncSupport.mappingPath init-param to prevent this message
2025-01-09 14:20:22.988  WARN 24160 --- [main] o.a.container.JSR356AsyncSupport         : Duplicate guess /vaadinServlet

javax.websocket.DeploymentException: Deployment of WebSocket Endpoints to the web application with path [] in host [Tomcat/localhost] is not permitted due to the failure of a previous deployment
	at org.apache.tomcat.websocket.server.WsServerContainer.addEndpoint(WsServerContainer.java:148) ~[tomcat-embed-websocket-9.0.71.jar!/:na]
	at org.apache.tomcat.websocket.server.WsServerContainer.addEndpoint(WsServerContainer.java:131) ~[tomcat-embed-websocket-9.0.71.jar!/:na]
	at org.atmosphere.container.JSR356AsyncSupport.<init>(JSR356AsyncSupport.java:105) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at org.atmosphere.container.JSR356AsyncSupport.<init>(JSR356AsyncSupport.java:48) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:na]
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499) ~[na:na]
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480) ~[na:na]
	at org.atmosphere.cpr.DefaultAsyncSupportResolver.newCometSupport(DefaultAsyncSupportResolver.java:237) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at org.atmosphere.cpr.DefaultAsyncSupportResolver.resolveWebSocket(DefaultAsyncSupportResolver.java:308) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at org.atmosphere.cpr.DefaultAsyncSupportResolver.resolve(DefaultAsyncSupportResolver.java:294) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at org.atmosphere.cpr.AtmosphereFramework.autoDetectContainer(AtmosphereFramework.java:2086) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:909) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:833) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at com.vaadin.flow.server.communication.PushRequestHandler.initAtmosphere(PushRequestHandler.java:244) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.communication.PushRequestHandler.<init>(PushRequestHandler.java:96) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.VaadinServletService.createRequestHandlers(VaadinServletService.java:107) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.VaadinService.init(VaadinService.java:231) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.VaadinServletService.init(VaadinServletService.java:175) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.spring.SpringVaadinServletService.init(SpringVaadinServletService.java:103) ~[vaadin-spring-23.3.1.jar!/:na]
	at com.vaadin.flow.spring.SpringServlet.createServletService(SpringServlet.java:115) ~[vaadin-spring-23.3.1.jar!/:na]
	at com.vaadin.flow.server.VaadinServlet.createServletService(VaadinServlet.java:307) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.VaadinServlet.init(VaadinServlet.java:130) ~[flow-server-23.3.1.jar!/:23.3.1]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1161) ~[tomcat-embed-core-9.0.71.jar!/:na]
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:1010) ~[tomcat-embed-core-9.0.71.jar!/:na]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.load(TomcatEmbeddedContext.java:81) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183) ~[na:na]
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:762) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline$7$1.accept(ReferencePipeline.java:276) ~[na:na]
	at java.base/java.util.TreeMap$ValueSpliterator.forEachRemaining(TreeMap.java:3215) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499) ~[na:na]
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150) ~[na:na]
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596) ~[na:na]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.lambda$deferredLoadOnStartup$0(TomcatEmbeddedContext.java:64) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.doWithThreadContextClassLoader(TomcatEmbeddedContext.java:105) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.deferredLoadOnStartup(TomcatEmbeddedContext.java:63) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.performDeferredLoadOnStartup(TomcatWebServer.java:305) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start(TomcatWebServer.java:216) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.web.servlet.context.WebServerStartStopLifecycle.start(WebServerStartStopLifecycle.java:43) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356) ~[spring-context-5.3.25.jar!/:5.3.25]
	at java.base/java.lang.Iterable.forEach(Iterable.java:75) ~[na:na]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:935) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:586) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at com.deb.spl.splNavigation.SplNavigationApplication.main(SplNavigationApplication.java:43) ~[classes!/:1.0.2]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[splNavigation-1.0.2.jar:1.0.2]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108) ~[splNavigation-1.0.2.jar:1.0.2]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[splNavigation-1.0.2.jar:1.0.2]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65) ~[splNavigation-1.0.2.jar:1.0.2]

2025-01-09 14:20:23.194  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installing Default AtmosphereInterceptors
2025-01-09 14:20:23.195  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.CorsInterceptor : CORS Interceptor Support
2025-01-09 14:20:23.196  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.CacheHeadersInterceptor : Default Response's Headers Interceptor
2025-01-09 14:20:23.217  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.PaddingAtmosphereInterceptor : Browser Padding Interceptor Support
2025-01-09 14:20:23.217  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.AndroidAtmosphereInterceptor : Android Interceptor Support
2025-01-09 14:20:23.218  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Dropping Interceptor org.atmosphere.interceptor.HeartbeatInterceptor
2025-01-09 14:20:23.219  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.SSEAtmosphereInterceptor : SSE Interceptor Support
2025-01-09 14:20:23.219  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.JSONPAtmosphereInterceptor : JSONP Interceptor Support
2025-01-09 14:20:23.220  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.JavaScriptProtocol : Atmosphere JavaScript Protocol
2025-01-09 14:20:23.220  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor : org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor
2025-01-09 14:20:23.221  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.OnDisconnectInterceptor : Browser disconnection detection
2025-01-09 14:20:23.221  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.IdleResourceInterceptor : org.atmosphere.interceptor.IdleResourceInterceptor
2025-01-09 14:20:23.222  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Set org.atmosphere.cpr.AtmosphereInterceptor.disableDefaults to disable them.
2025-01-09 14:20:23.222  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor CORS Interceptor Support with priority FIRST_BEFORE_DEFAULT 
2025-01-09 14:20:23.222  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor Default Response's Headers Interceptor with priority AFTER_DEFAULT 
2025-01-09 14:20:23.222  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor Browser Padding Interceptor Support with priority AFTER_DEFAULT 
2025-01-09 14:20:23.222  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor Android Interceptor Support with priority AFTER_DEFAULT 
2025-01-09 14:20:23.223  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor SSE Interceptor Support with priority AFTER_DEFAULT 
2025-01-09 14:20:23.223  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor JSONP Interceptor Support with priority AFTER_DEFAULT 
2025-01-09 14:20:23.224  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor Atmosphere JavaScript Protocol with priority AFTER_DEFAULT 
2025-01-09 14:20:23.224  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor with priority AFTER_DEFAULT 
2025-01-09 14:20:23.224  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor Browser disconnection detection with priority AFTER_DEFAULT 
2025-01-09 14:20:23.226  INFO 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor org.atmosphere.interceptor.IdleResourceInterceptor with priority BEFORE_DEFAULT 
2025-01-09 14:20:23.244 ERROR 24160 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Failed to initialize Atmosphere Framework

java.lang.IllegalStateException: Shutdown in progress
	at java.base/java.lang.ApplicationShutdownHooks.add(ApplicationShutdownHooks.java:66) ~[na:na]
	at java.base/java.lang.Runtime.addShutdownHook(Runtime.java:216) ~[na:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:928) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:833) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at com.vaadin.flow.server.communication.PushRequestHandler.initAtmosphere(PushRequestHandler.java:244) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.communication.PushRequestHandler.<init>(PushRequestHandler.java:96) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.VaadinServletService.createRequestHandlers(VaadinServletService.java:107) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.VaadinService.init(VaadinService.java:231) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.VaadinServletService.init(VaadinServletService.java:175) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.spring.SpringVaadinServletService.init(SpringVaadinServletService.java:103) ~[vaadin-spring-23.3.1.jar!/:na]
	at com.vaadin.flow.spring.SpringServlet.createServletService(SpringServlet.java:115) ~[vaadin-spring-23.3.1.jar!/:na]
	at com.vaadin.flow.server.VaadinServlet.createServletService(VaadinServlet.java:307) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.VaadinServlet.init(VaadinServlet.java:130) ~[flow-server-23.3.1.jar!/:23.3.1]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1161) ~[tomcat-embed-core-9.0.71.jar!/:na]
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:1010) ~[tomcat-embed-core-9.0.71.jar!/:na]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.load(TomcatEmbeddedContext.java:81) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183) ~[na:na]
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:762) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline$7$1.accept(ReferencePipeline.java:276) ~[na:na]
	at java.base/java.util.TreeMap$ValueSpliterator.forEachRemaining(TreeMap.java:3215) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499) ~[na:na]
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150) ~[na:na]
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596) ~[na:na]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.lambda$deferredLoadOnStartup$0(TomcatEmbeddedContext.java:64) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.doWithThreadContextClassLoader(TomcatEmbeddedContext.java:105) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.deferredLoadOnStartup(TomcatEmbeddedContext.java:63) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.performDeferredLoadOnStartup(TomcatWebServer.java:305) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start(TomcatWebServer.java:216) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.web.servlet.context.WebServerStartStopLifecycle.start(WebServerStartStopLifecycle.java:43) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356) ~[spring-context-5.3.25.jar!/:5.3.25]
	at java.base/java.lang.Iterable.forEach(Iterable.java:75) ~[na:na]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:935) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:586) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at com.deb.spl.splNavigation.SplNavigationApplication.main(SplNavigationApplication.java:43) ~[classes!/:1.0.2]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[splNavigation-1.0.2.jar:1.0.2]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108) ~[splNavigation-1.0.2.jar:1.0.2]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[splNavigation-1.0.2.jar:1.0.2]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65) ~[splNavigation-1.0.2.jar:1.0.2]

2025-01-09 14:20:23.264  WARN 24160 --- [main] c.v.flow.server.VaadinServletService     : Error initializing Atmosphere. Push will not work.

com.vaadin.flow.server.ServiceException: Failed to initialize Atmosphere for springServlet. Push will not work.
	at com.vaadin.flow.server.communication.PushRequestHandler.<init>(PushRequestHandler.java:100) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.VaadinServletService.createRequestHandlers(VaadinServletService.java:107) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.VaadinService.init(VaadinService.java:231) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.VaadinServletService.init(VaadinServletService.java:175) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.spring.SpringVaadinServletService.init(SpringVaadinServletService.java:103) ~[vaadin-spring-23.3.1.jar!/:na]
	at com.vaadin.flow.spring.SpringServlet.createServletService(SpringServlet.java:115) ~[vaadin-spring-23.3.1.jar!/:na]
	at com.vaadin.flow.server.VaadinServlet.createServletService(VaadinServlet.java:307) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.VaadinServlet.init(VaadinServlet.java:130) ~[flow-server-23.3.1.jar!/:23.3.1]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1161) ~[tomcat-embed-core-9.0.71.jar!/:na]
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:1010) ~[tomcat-embed-core-9.0.71.jar!/:na]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.load(TomcatEmbeddedContext.java:81) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183) ~[na:na]
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:762) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline$7$1.accept(ReferencePipeline.java:276) ~[na:na]
	at java.base/java.util.TreeMap$ValueSpliterator.forEachRemaining(TreeMap.java:3215) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499) ~[na:na]
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150) ~[na:na]
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596) ~[na:na]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.lambda$deferredLoadOnStartup$0(TomcatEmbeddedContext.java:64) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.doWithThreadContextClassLoader(TomcatEmbeddedContext.java:105) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.deferredLoadOnStartup(TomcatEmbeddedContext.java:63) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.performDeferredLoadOnStartup(TomcatWebServer.java:305) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start(TomcatWebServer.java:216) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.web.servlet.context.WebServerStartStopLifecycle.start(WebServerStartStopLifecycle.java:43) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356) ~[spring-context-5.3.25.jar!/:5.3.25]
	at java.base/java.lang.Iterable.forEach(Iterable.java:75) ~[na:na]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:935) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:586) ~[spring-context-5.3.25.jar!/:5.3.25]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292) ~[spring-boot-2.7.8-SNAPSHOT.jar!/:2.7.8-SNAPSHOT]
	at com.deb.spl.splNavigation.SplNavigationApplication.main(SplNavigationApplication.java:43) ~[classes!/:1.0.2]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[splNavigation-1.0.2.jar:1.0.2]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108) ~[splNavigation-1.0.2.jar:1.0.2]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[splNavigation-1.0.2.jar:1.0.2]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65) ~[splNavigation-1.0.2.jar:1.0.2]
Caused by: java.lang.RuntimeException: Atmosphere init failed
	at com.vaadin.flow.server.communication.PushRequestHandler.initAtmosphere(PushRequestHandler.java:253) ~[flow-server-23.3.1.jar!/:23.3.1]
	at com.vaadin.flow.server.communication.PushRequestHandler.<init>(PushRequestHandler.java:96) ~[flow-server-23.3.1.jar!/:23.3.1]
	... 50 common frames omitted
Caused by: javax.servlet.ServletException: java.lang.IllegalStateException: Shutdown in progress
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:946) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:833) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	at com.vaadin.flow.server.communication.PushRequestHandler.initAtmosphere(PushRequestHandler.java:244) ~[flow-server-23.3.1.jar!/:23.3.1]
	... 51 common frames omitted
Caused by: java.lang.IllegalStateException: Shutdown in progress
	at java.base/java.lang.ApplicationShutdownHooks.add(ApplicationShutdownHooks.java:66) ~[na:na]
	at java.base/java.lang.Runtime.addShutdownHook(Runtime.java:216) ~[na:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:928) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar!/:na]
	... 53 common frames omitted

2025-01-09 14:20:23.355  INFO 24160 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path ''
2025-01-09 14:20:23.383  INFO 24160 --- [main] c.d.s.s.SplNavigationApplication         : Started SplNavigationApplication in 18.513 seconds (JVM running for 19.383)
2025-01-09 14:20:23.458  INFO 24160 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-01-09 14:20:23.463  INFO 24160 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-01-09 14:20:23.474  INFO 24160 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-01-09 14:20:23.624  INFO 8340 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-01-09 14:20:23.994  INFO 8340 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 346 ms. Found 3 JPA repository interfaces.
2025-01-09 14:20:25.372  INFO 8340 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
2025-01-09 14:20:25.386  INFO 8340 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-01-09 14:20:25.387  INFO 8340 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-01-09 14:20:25.534  INFO 8340 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-01-09 14:20:25.535  INFO 8340 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 5128 ms
2025-01-09 14:20:26.294  INFO 8340 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-01-09 14:20:26.466  INFO 8340 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.14.Final
2025-01-09 14:20:27.049  INFO 8340 --- [main] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-01-09 14:20:27.414  INFO 8340 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-01-09 14:20:27.776  INFO 8340 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-01-09 14:20:27.835  INFO 8340 --- [main] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.PostgreSQLDialect
2025-01-09 14:20:29.477  INFO 8340 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-01-09 14:20:29.491  INFO 8340 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-01-09 14:20:30.306  INFO 8340 --- [main] c.v.f.s.VaadinServletContextInitializer  : Search for VaadinAppShell took 48 ms
2025-01-09 14:20:31.828  INFO 8340 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@cb39552, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1f3b992, org.springframework.security.web.context.SecurityContextPersistenceFilter@50448409, org.springframework.security.web.header.HeaderWriterFilter@513b52af, org.springframework.security.web.csrf.CsrfFilter@2d55e826, org.springframework.security.web.authentication.logout.LogoutFilter@7f608e21, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2b0dc227, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@37ad042b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2a53f215, org.springframework.security.web.session.SessionManagementFilter@1c7350b0, org.springframework.security.web.access.ExceptionTranslationFilter@1115433e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6bccd036]
2025-01-09 14:20:31.854  WARN 8340 --- [main] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-01-09 14:20:31.856  INFO 8340 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/**']
2025-01-09 14:20:32.037  WARN 8340 --- [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-01-09 14:20:33.043  INFO 8340 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-01-09 14:20:33.124  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereHandler com.vaadin.flow.server.communication.PushAtmosphereHandler mapped to context-path: /*
2025-01-09 14:20:33.125  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed the following AtmosphereInterceptor mapped to AtmosphereHandler com.vaadin.flow.server.communication.PushAtmosphereHandler
2025-01-09 14:20:33.168  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Atmosphere is using org.atmosphere.util.VoidAnnotationProcessor for processing annotation
2025-01-09 14:20:33.179  INFO 8340 --- [main] org.atmosphere.util.ForkJoinPool         : Using ForkJoinPool  java.util.concurrent.ForkJoinPool. Set the org.atmosphere.cpr.broadcaster.maxAsyncWriteThreads to -1 to fully use its power.
2025-01-09 14:20:33.186  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed WebSocketProtocol org.atmosphere.websocket.protocol.SimpleHttpProtocol 
2025-01-09 14:20:33.195  INFO 8340 --- [main] o.a.container.JSR356AsyncSupport         : JSR 356 Mapping path /VAADIN/push
2025-01-09 14:20:33.211  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installing Default AtmosphereInterceptors
2025-01-09 14:20:33.212  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.CorsInterceptor : CORS Interceptor Support
2025-01-09 14:20:33.213  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.CacheHeadersInterceptor : Default Response's Headers Interceptor
2025-01-09 14:20:33.214  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.PaddingAtmosphereInterceptor : Browser Padding Interceptor Support
2025-01-09 14:20:33.215  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.AndroidAtmosphereInterceptor : Android Interceptor Support
2025-01-09 14:20:33.217  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Dropping Interceptor org.atmosphere.interceptor.HeartbeatInterceptor
2025-01-09 14:20:33.218  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.SSEAtmosphereInterceptor : SSE Interceptor Support
2025-01-09 14:20:33.218  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.JSONPAtmosphereInterceptor : JSONP Interceptor Support
2025-01-09 14:20:33.220  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.JavaScriptProtocol : Atmosphere JavaScript Protocol
2025-01-09 14:20:33.221  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor : org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor
2025-01-09 14:20:33.222  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.OnDisconnectInterceptor : Browser disconnection detection
2025-01-09 14:20:33.222  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : 	org.atmosphere.interceptor.IdleResourceInterceptor : org.atmosphere.interceptor.IdleResourceInterceptor
2025-01-09 14:20:33.222  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Set org.atmosphere.cpr.AtmosphereInterceptor.disableDefaults to disable them.
2025-01-09 14:20:33.224  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor CORS Interceptor Support with priority FIRST_BEFORE_DEFAULT 
2025-01-09 14:20:33.230  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor Default Response's Headers Interceptor with priority AFTER_DEFAULT 
2025-01-09 14:20:33.231  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor Browser Padding Interceptor Support with priority AFTER_DEFAULT 
2025-01-09 14:20:33.232  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor Android Interceptor Support with priority AFTER_DEFAULT 
2025-01-09 14:20:33.232  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor SSE Interceptor Support with priority AFTER_DEFAULT 
2025-01-09 14:20:33.232  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor JSONP Interceptor Support with priority AFTER_DEFAULT 
2025-01-09 14:20:33.232  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor Atmosphere JavaScript Protocol with priority AFTER_DEFAULT 
2025-01-09 14:20:33.233  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor with priority AFTER_DEFAULT 
2025-01-09 14:20:33.233  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor Browser disconnection detection with priority AFTER_DEFAULT 
2025-01-09 14:20:33.235  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor org.atmosphere.interceptor.IdleResourceInterceptor with priority BEFORE_DEFAULT 
2025-01-09 14:20:33.236  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Using EndpointMapper class org.atmosphere.util.DefaultEndpointMapper
2025-01-09 14:20:33.236  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Using BroadcasterCache: org.atmosphere.cache.UUIDBroadcasterCache
2025-01-09 14:20:33.237  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Default Broadcaster Class: org.atmosphere.cpr.DefaultBroadcaster
2025-01-09 14:20:33.237  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Broadcaster Shared List Resources: false
2025-01-09 14:20:33.237  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Broadcaster Polling Wait Time 100
2025-01-09 14:20:33.237  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Shared ExecutorService supported: true
2025-01-09 14:20:33.237  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Messaging ExecutorService Pool Size unavailable - Not instance of ThreadPoolExecutor
2025-01-09 14:20:33.238  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Async I/O Thread Pool Size: 200
2025-01-09 14:20:33.238  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Using BroadcasterFactory: org.atmosphere.cpr.DefaultBroadcasterFactory
2025-01-09 14:20:33.238  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Using AtmosphereResurceFactory: org.atmosphere.cpr.DefaultAtmosphereResourceFactory
2025-01-09 14:20:33.239  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Using WebSocketProcessor: org.atmosphere.websocket.DefaultWebSocketProcessor
2025-01-09 14:20:33.241  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Invoke AtmosphereInterceptor on WebSocket message true
2025-01-09 14:20:33.242  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : HttpSession supported: true
2025-01-09 14:20:33.242  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Atmosphere is using DefaultAtmosphereObjectFactory for dependency injection and object creation
2025-01-09 14:20:33.244  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Atmosphere is using async support: org.atmosphere.container.JSR356AsyncSupport running under container: Apache Tomcat/9.0.71 using javax.servlet/3.0 and jsr356/WebSocket API
2025-01-09 14:20:33.244  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Atmosphere Framework 2.7.3.slf4jvaadin4 started.
2025-01-09 14:20:33.250  INFO 8340 --- [main] org.atmosphere.cpr.AtmosphereFramework   : Installed AtmosphereInterceptor  Track Message Size Interceptor using | with priority BEFORE_DEFAULT 
2025-01-09 14:20:33.302  INFO 8340 --- [main] c.v.f.s.DefaultDeploymentConfiguration   : Vaadin is running in production mode.

The following EXPERIMENTAL features are enabled:
- Enforce client / constraint / binder validation


2025-01-09 14:20:33.380  INFO 8340 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path ''
2025-01-09 14:20:33.404  INFO 8340 --- [main] c.d.s.s.SplNavigationApplication         : Started SplNavigationApplication in 14.864 seconds (JVM running for 17.129)
2025-01-09 14:20:33.472  INFO 8340 --- [main] c.d.s.s.SplNavigationApplication         : active profile [dev]
2025-01-09 14:20:33.472  INFO 8340 --- [main] c.d.s.s.service.FakeBinsService          : starting BinsService with mock bean
2025-01-09 14:20:33.472  INFO 8340 --- [main] c.d.s.s.service.FakeBinsService          : com.deb.spl.splNavigation.service.FakeBinsService is starting with parameters: 
2025-01-09 14:20:33.473  INFO 8340 --- [main] c.d.s.s.service.FakeMsuService           : starting MsuService with mock bean
2025-01-09 14:20:33.473  INFO 8340 --- [main] c.d.s.s.service.FakeMsuService           : com.deb.spl.splNavigation.service.FakeMsuService is starting with parameters: 
2025-01-09 14:20:43.485  INFO 8340 --- [http-nio-8081-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-09 14:20:43.485  INFO 8340 --- [http-nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-01-09 14:20:43.486  INFO 8340 --- [http-nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-01-09 14:27:26.614  INFO 8340 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-01-09 14:27:26.617  INFO 8340 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-01-09 14:27:26.625  INFO 8340 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
