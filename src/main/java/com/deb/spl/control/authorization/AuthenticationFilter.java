package com.deb.spl.control.authorization;

import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.RegexRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;


public class AuthenticationFilter extends OncePerRequestFilter {

    private final AuthenticationService authenticationService;
    List<RequestMatcher> permittedAuthenticatedMatchers = getAntMatchers(null,
            SecurityConfig.getAuthenticatedEndpoints().toArray(String[]::new));
    List<RequestMatcher> permittedToAllMatchers = getAllowedAllMatcher(null,
            SecurityConfig.getPermittedToAllEndpoints().toArray(String[]::new));

    public AuthenticationFilter(AuthenticationService authenticationService) {
        this.authenticationService = authenticationService;
    }


    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {

        if (permittedToAllMatchers.stream().anyMatch(e -> e.matches(request))) {
            return true;
        }

        return permittedAuthenticatedMatchers.stream()
                .noneMatch(e ->
                        e.matches(request));
    }

    private List<RequestMatcher> getAntMatchers(HttpMethod httpMethod, String... antPatterns) {
        List<String> methods = (httpMethod != null) ? List.of(httpMethod.toString()) :
                SecurityConfig.allowedHttpMethods.stream().map(Enum::toString).toList();

        List<RequestMatcher> matchers = new ArrayList<>();
        for (String pattern : antPatterns) {
            for (String allowedMethods : methods) {
                matchers.add(new AntPathRequestMatcher(pattern, allowedMethods));
            }
        }
        return matchers;
    }

    private List<RequestMatcher> getAllowedAllMatcher(HttpMethod httpMethod, String... antPatterns) {
        List<RequestMatcher> matchers = getAntMatchers(httpMethod, antPatterns);
        matchers.add(new RegexRequestMatcher("^$", HttpMethod.GET.toString()));

        return matchers;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        try {
                Authentication authentication = authenticationService.getAuthentication(request);
            SecurityContextHolder.getContext().setAuthentication(authentication);
        } catch (BadCredentialsException exp) {
            HttpServletResponse httpResponse = response;
            httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            httpResponse.setContentType(MediaType.APPLICATION_JSON_VALUE);
            PrintWriter writer = httpResponse.getWriter();
            writer.print(exp.getMessage());
            writer.flush();
            writer.close();
        } catch (Exception exp) {
            throw new ServletException(exp);
        }

        filterChain.doFilter(request, response);
    }
}
