package com.deb.spl.control.authorization;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

@WebFilter(filterName = "AddTokenFilter", urlPatterns = {"/api/v1/*"})
@Component
@Slf4j
public class AddResponseHeaderFilter implements Filter {
    private final String plcResponseToken;
    private final String plcRequestToken;
    private final List<String> plcAddress;

    public AddResponseHeaderFilter(@Value("${plc.response.token}") String plcResponseToken, @Value("${plc.request.token}") String plcRequestToken, @Value("#{'${plc.url}'.split(',')}") String[] plcAddress) {
        this.plcResponseToken = plcResponseToken;
        this.plcRequestToken = plcRequestToken;
        this.plcAddress = Arrays.asList(plcAddress);
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        try {
            if (((HttpServletRequest) request).getHeader("Token") != null && ((HttpServletRequest) request).getHeader("Token").equals(plcRequestToken) && plcAddress.contains(request.getRemoteAddr())) {

                HttpServletResponse httpServletResponse = (HttpServletResponse) response;
                httpServletResponse.addHeader("Token", plcResponseToken);
            }
        } catch (Exception e) {
            log.error(e.getMessage() + ": " + Arrays.toString(e.getStackTrace()));
            throw e;
        }

        chain.doFilter(request, response);
    }
}