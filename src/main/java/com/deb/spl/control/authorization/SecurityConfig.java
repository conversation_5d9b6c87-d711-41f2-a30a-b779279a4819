package com.deb.spl.control.authorization;


import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import java.util.List;
import java.util.stream.Stream;

@Configuration
@EnableWebSecurity
public class SecurityConfig {
    @Getter
    private static List<String> permittedToAllEndpoints;
    @Getter
    private static List<String> authenticatedEndpoints;
    public static final List<HttpMethod> allowedHttpMethods = List.of(HttpMethod.GET,
            HttpMethod.POST, HttpMethod.PUT, HttpMethod.DELETE);

    private final AuthenticationService authenticationService;

    public SecurityConfig(@Value("#{'${asku.security.config.endpoint.pattern.permit-all}'.split(',')}")
                          List<String> permitToAllGeneralEndpoints,
                          @Value("#{'${asku.security.config.endpoint.pattern.authenticated}'.split(',')}")
                          List<String> authenticatedEndpoints,
                          @Value("#{'${ccv.security.config.endpoint.pattern.permit-all}'.split(',')}")
                          List<String> permitToAllCcvEndpoints,
                          @Value("#{'${nsd.url}'.split(',')}")
                          List<String> nsdEndpoints,
                          AuthenticationService authenticationService) {
        this.authenticationService = authenticationService;

        List<String> permitted = permitToAllGeneralEndpoints;
        permitted = Stream.concat(permitted.stream()
                                .filter(e -> !e.isBlank()),
                        permitToAllCcvEndpoints.stream()
                                .filter(e -> !e.isBlank()))
                .distinct().toList();
        permitted = Stream.concat(permitted.stream(),
                        nsdEndpoints.stream()
                                .filter(nsd -> !nsd.isBlank()))
                .distinct().toList();

        permittedToAllEndpoints = List.copyOf(permitted);

        SecurityConfig.authenticatedEndpoints = authenticatedEndpoints.stream()
                .filter(e -> !e.isBlank())
                .distinct().toList();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {

        http.csrf().disable()
                .addFilterBefore(new AuthenticationFilter(authenticationService), UsernamePasswordAuthenticationFilter.class)
                .authorizeHttpRequests()
                .antMatchers(authenticatedEndpoints.toArray(String[]::new)).authenticated() // Require authentication for /api/v1 endpoints
                .antMatchers(permittedToAllEndpoints.toArray(String[]::new)).permitAll();

        return http.build();
    }

    @Bean
    public WebSecurityCustomizer webSecurityCustomizer() {
        List<String> ignoredUrlsRegex = List.of("^$");
        return (web -> web.ignoring()
                .antMatchers(permittedToAllEndpoints.toArray(String[]::new))
                .regexMatchers(ignoredUrlsRegex.toArray(String[]::new)));
    }
}