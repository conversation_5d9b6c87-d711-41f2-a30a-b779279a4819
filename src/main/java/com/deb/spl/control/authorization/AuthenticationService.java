package com.deb.spl.control.authorization;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;

@Service
public class AuthenticationService {
    private final String authTokenHeaderName;
    private final String authToken;

    public AuthenticationService(@Value("${plc.token.request-parameter-name:Token}") String authTokenHeaderName,
                                 @Value("${plc.request.token}") String authToken) {
        this.authTokenHeaderName = authTokenHeaderName;
        this.authToken = authToken;
    }

    public Authentication getAuthentication(HttpServletRequest request) throws BadCredentialsException {
        String apiKey = request.getHeader(authTokenHeaderName);
        if (apiKey == null || !apiKey.equals(authToken)) {
            throw new BadCredentialsException("Invalid API Key");
        }

        return new ApiKeyAuthentication(apiKey, AuthorityUtils.NO_AUTHORITIES);
    }
}
