package com.deb.spl.control.repository;

import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.NoRepositoryBean;

import java.util.List;
import java.util.Optional;

@NoRepositoryBean
public interface HistoryRepository<T,ID> extends JpaRepository<T,ID> {
    List<T> findByCommandLikeIgnoreCase(String likeFilter, PageRequest of);
    Optional<T> getTop();
}
