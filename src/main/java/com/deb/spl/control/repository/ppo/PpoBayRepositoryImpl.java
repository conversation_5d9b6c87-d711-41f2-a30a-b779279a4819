package com.deb.spl.control.repository.ppo;

import com.deb.spl.control.data.ppo.PpoBay;
import com.deb.spl.control.data.ppo.PpoBayType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceContext;
import javax.transaction.Transactional;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Repository
@Transactional
@Slf4j
public class PpoBayRepositoryImpl implements PpoBayRepository {
    @PersistenceContext
    private final EntityManager entityManager;

    public PpoBayRepositoryImpl(EntityManager entityManager) {
        this.entityManager = entityManager;
    }

    @Override
    public List<PpoBay> getAllBays() {
        return entityManager.createQuery("select pb from PpoBay pb", PpoBay.class).getResultList();
    }

    @Override
    public PpoBay findById(Long id) {
        return entityManager.find(PpoBay.class, id);
    }

    @Override
    public Optional<PpoBay> findByBayType(PpoBayType bayType) {
        Optional<PpoBay> bay = Optional.empty();
        try {
            bay = Optional.ofNullable(entityManager.createQuery("select pb from PpoBay pb where pb.ppoBayType = :bayType", PpoBay.class)
                    .setParameter("bayType", bayType)
                    .getSingleResult());
        } catch (NoResultException e) {
            log.error(Arrays.toString(e.getStackTrace()));
        }

        return bay;
    }

    @Override
    public PpoBay saveBay(PpoBay ppoBay) {
        if(ppoBay.getId()==null){
            entityManager.persist(ppoBay);
        }else {
            ppoBay= entityManager.merge(ppoBay);
        }
        return ppoBay;
    }
}
