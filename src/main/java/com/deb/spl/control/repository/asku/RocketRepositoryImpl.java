package com.deb.spl.control.repository.asku;

import com.deb.spl.control.data.asku.RocketDao;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public class RocketRepositoryImpl implements RocketRepository {
    public static final String FETCH_REFERENCE_POINTS_QUERY = """
            select r from rocket r
            left join fetch r.launchReferencePoints p
            where r in :rockets
            """;
    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Optional<RocketDao> findById(long id) {
        List<RocketDao> rockets = entityManager
                .createQuery(""" 
                        select  r from rocket r
                        left join fetch r.storedTlKeys
                        where  r.id=:id order by r.id desc""", RocketDao.class)
                .setParameter("id", id)
                .setMaxResults(1)
                .getResultList();

        rockets = entityManager.createQuery(FETCH_REFERENCE_POINTS_QUERY, RocketDao.class)
                .setParameter("rockets", rockets)
                .getResultList();

        return rockets.size() > 0 ? Optional.ofNullable(rockets.get(0)) : Optional.empty();
    }

    @Override
    public Optional<RocketDao> findByPlantMissile(String plantMissile) {
        List<RocketDao> rockets = entityManager
                .createQuery("""
                        select  r from rocket  r
                        left join fetch r.storedTlKeys
                        where r.formData IS NOT NULL and
                        r.formData.plantMissile=:plantMissile order by r.id desc""", RocketDao.class)
                .setParameter("plantMissile", plantMissile)
                .setMaxResults(1)
                .getResultList();

        rockets = entityManager.createQuery(FETCH_REFERENCE_POINTS_QUERY, RocketDao.class)
                .setParameter("rockets", rockets)
                .getResultList();


        return rockets.size() > 0 ? Optional.ofNullable(rockets.get(0)) : Optional.empty();
    }

    @Override
    public Optional<RocketDao> findByOrderEntityId(UUID orderEntityId) {
        List<RocketDao> rockets = entityManager
                .createQuery("""
                        select  r from rocket r 
                        left join fetch r.storedTlKeys
                        where r.initialData IS NOT NULL 
                        and r.initialData.orderInfo IS NOT NULL 
                        and r.initialData.orderInfo.orderEntityId=:orderEntityId 
                        order by r.id desc""", RocketDao.class)
                .setParameter("orderEntityId", orderEntityId)
                .setMaxResults(1)
                .getResultList();
        rockets = entityManager.createQuery(FETCH_REFERENCE_POINTS_QUERY, RocketDao.class)
                .setParameter("rockets", rockets)
                .getResultList();

        return rockets.size() > 0 ? Optional.ofNullable(rockets.get(0)) : Optional.empty();
    }

    @Override
    public RocketDao save(@NotNull RocketDao rocketDao) {
        if (rocketDao.getId() == null) {
            entityManager.persist(rocketDao);
        } else {
            rocketDao = entityManager.merge(rocketDao);
        }
        entityManager.persist(rocketDao);

        return rocketDao;
    }
}
