package com.deb.spl.control.repository.sae;

import com.deb.spl.control.data.sae.SaeCommandDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;
import javax.transaction.Transactional;
import java.util.List;

@Repository
@Transactional
public class SaeCommandsRepositoryImpl implements SaeCommandsRepository {
    @PersistenceContext
    private final EntityManager entityManager;

    @Autowired
    public SaeCommandsRepositoryImpl(EntityManager entityManager) {
        this.entityManager = entityManager;
    }

    @Override
    public List<SaeCommandDao> getAllCommands() {
        return entityManager.createQuery("select sc from SaeCommandDao sc", SaeCommandDao.class)
                .getResultList();
    }

    @Override
    public SaeCommandDao findById(Long id) {
        return entityManager.find(SaeCommandDao.class, id);
    }

    @Override
    public SaeCommandDao findByCommandName(String commandName) {
        TypedQuery<SaeCommandDao> findByNameQuery = entityManager
                .createQuery("Select sc From SaeCommandDao sc where sc.command = :commandName", SaeCommandDao.class);
        findByNameQuery.setParameter("commandName",commandName);

        return findByNameQuery.getSingleResult();
    }

    @Override
    public SaeCommandDao saveCommand(SaeCommandDao saeCommandDao) {
        if (saeCommandDao.getId() == null) {
             entityManager.persist(saeCommandDao);
        } else {
            saeCommandDao=entityManager.merge(saeCommandDao);
        }

        return saeCommandDao;
    }

    @Override
    public void deleteCommand(SaeCommandDao saeCommandDao) {
        if (saeCommandDao.getId() != null) {
            entityManager.remove(saeCommandDao);
        } else {
            entityManager.merge(saeCommandDao);
        }
    }
}
