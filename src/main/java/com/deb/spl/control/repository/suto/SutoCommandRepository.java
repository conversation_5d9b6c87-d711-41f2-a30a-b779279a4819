package com.deb.spl.control.repository.suto;


import com.deb.spl.control.data.suto.SutoCommandDAO;
import com.deb.spl.control.repository.HasCommandWithCaption;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SutoCommandRepository extends HasCommandWithCaption {
    List<SutoCommandDAO> getAllCommands();

    SutoCommandDAO findById(Long id);

    SutoCommandDAO saveCommand(SutoCommandDAO commandDao);

    void deleteCommand(SutoCommandDAO command);
}
