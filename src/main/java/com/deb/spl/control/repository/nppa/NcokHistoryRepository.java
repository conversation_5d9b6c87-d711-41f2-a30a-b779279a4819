package com.deb.spl.control.repository.nppa;

import com.deb.spl.control.data.nppa.NppaCommand;
import com.deb.spl.control.repository.HistoryRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface NcokHistoryRepository extends HistoryRepository<NppaCommand,Long>  /*JpaRepository<PpoCommand, Long>*/ {
    List<NppaCommand> findByOriginator(String originator);
    List<NppaCommand> findByCommand(String commandName);
    List<NppaCommand> findByGenerationTimeBetween(LocalDateTime start, LocalDateTime end);

    @Query("select command from NppaCommand command where command.adjacentSystem='NCOK'")
    Page<NppaCommand> findAll(Pageable pageable);
    @Query("select command from NppaCommand command where command.adjacentSystem='NCOK'  and (LOWER(command.command) like LOWER(CONCAT('%',:likeFilter,'%'))  " +
            "or LOWER(command.caption) like LOWER(CONCAT('%',:likeFilter,'%')))")
//    @Query("select c from NppaCommand c where c.adjacentSystem=3")
    @Override
    List<NppaCommand> findByCommandLikeIgnoreCase(String likeFilter, PageRequest of);

    @Override
    @Query(nativeQuery = true,value = "SELECT * FROM nppa_command_record ORDER BY id DESC limit 1")
    Optional<NppaCommand> getTop();
}
