package com.deb.spl.control.repository.asku;

import com.deb.spl.control.data.asku.LaunchReferencePointRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Repository
public interface LaunchReferencePointRecordsRepository extends JpaRepository<LaunchReferencePointRecord, Long> {

    @Query("""
            select r from LaunchReferencePointRecord r 
            where r.rocket is not null and r.rocket.id=:rocketId
            order by r.id
            """)
    List<LaunchReferencePointRecord> findByRocketId(@Param("rocketId") long rocketId);

    @Query("""
            select r from LaunchReferencePointRecord r 
            where r.plantMissile=:plantMissile 
            order by r.id
            """)
    List<LaunchReferencePointRecord> findByPlantMissile(@Param("plantMissile") String plantMissile);

    @Query("""
            select r from LaunchReferencePointRecord r 
            where r.rocket.initialData is not null and r.rocket.initialData.orderInfo is not null 
            and r.rocket.initialData.orderInfo.orderEntityId=:orderEntityId 
            and r.timeStamp between :selectFrom and :selectTo 
            order by r.timeStamp""")
    List<LaunchReferencePointRecord> findByFireOrderEntityIdAndTimeStampIsBetween(@Param("orderEntityId") UUID orderEntityId,
                                                                                  @Param("selectFrom") LocalDateTime selectFrom,
                                                                                  @Param("selectTo") LocalDateTime selectTo);
}
