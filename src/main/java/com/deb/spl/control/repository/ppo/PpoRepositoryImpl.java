package com.deb.spl.control.repository.ppo;

import com.deb.spl.control.data.ppo.PpoDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceContext;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Optional;

@Repository
@Slf4j
public class PpoRepositoryImpl implements PpoRepository {
    @PersistenceContext
    private final EntityManager entityManager;

    public PpoRepositoryImpl(EntityManager entityManager) {
        this.entityManager = entityManager;
    }

    @Override
    public Optional<PpoDao> load() {
        Optional<PpoDao> dao = Optional.empty();
        try {
            dao = Optional.ofNullable(
                    entityManager.createQuery("select p from PpoDao p order by p.id desc ", PpoDao.class)
                            .setMaxResults(1)
                            .getSingleResult());
        } catch (NoResultException e) {
            log.warn(e.getMessage() + " " + Arrays.asList(e.getStackTrace()));
        }

        return dao;
    }

    @Override
    public PpoDao save(@NotNull PpoDao value) {
        if (value.getId() != null) {
            value.setId(null);
        }

        entityManager.persist(value);

        return value;
    }
}
