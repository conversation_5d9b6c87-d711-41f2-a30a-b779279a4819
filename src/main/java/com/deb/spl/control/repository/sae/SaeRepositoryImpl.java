package com.deb.spl.control.repository.sae;

import com.deb.spl.control.data.sae.SaeDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceContext;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Optional;

@Slf4j
@Repository
public class SaeRepositoryImpl implements SaeRepository {

    @PersistenceContext
    private final EntityManager entityManager;

    public SaeRepositoryImpl(EntityManager entityManager) {
        this.entityManager = entityManager;
    }

    @Override
    public Optional<SaeDao> load() {
        Optional<SaeDao> dao = Optional.empty();

        try {
            dao = Optional.ofNullable(
                    entityManager.createQuery("select s from SaeDao s order by s.id desc", SaeDao.class)
                            .setMaxResults(1)
                            .getSingleResult());

        } catch (NoResultException e) {
            log.error(Arrays.toString(e.getStackTrace()));
        }

        return dao;
    }

    @Override
    public SaeDao save(@NotNull SaeDao value) {

        if (value.getId() != null) {
            value.setId(null);
        }
        entityManager.persist(value);

        return value;
    }
}
