package com.deb.spl.control.repository.sae;

import com.deb.spl.control.data.sae.SaeCommand;
import com.deb.spl.control.repository.HistoryRepository;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface SaeHistoryRepository extends HistoryRepository<SaeCommand, Long> {

    List<SaeCommand> findByOriginator(String originator);

    List<SaeCommand> findByCommand(String commandName);

    List<SaeCommand> findByGenerationTimeBetween(LocalDateTime start, LocalDateTime end);

    List<SaeCommand> findByCommandLikeIgnoreCase(String likeFilter, PageRequest of);

    @Override
    @Query(nativeQuery = true, value = "SELECT * FROM sae_record ORDER BY id DESC limit 1")
    Optional<SaeCommand> getTop();
}
