package com.deb.spl.control.repository.suto;

import com.deb.spl.control.data.suto.SutoCommand;
import com.deb.spl.control.repository.HistoryRepository;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
@Transactional
public interface SutoHistoryRepository extends HistoryRepository<SutoCommand,Long>  {
    List<SutoCommand> findByOriginator(String originator);
    List<SutoCommand> findByCommand(String commandName);
    List<SutoCommand> findByGenerationTimeBetween(LocalDateTime start, LocalDateTime end);

    List<SutoCommand> findByCommandLikeIgnoreCase(String likeFilter, PageRequest of);

    @Override
    @Query(nativeQuery = true,value = "SELECT * FROM suto_record ORDER BY id DESC limit 1")
    Optional<SutoCommand> getTop();
}
