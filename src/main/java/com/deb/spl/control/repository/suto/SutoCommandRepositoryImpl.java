package com.deb.spl.control.repository.suto;

import com.deb.spl.control.data.suto.SutoCommandDAO;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Repository
public class SutoCommandRepositoryImpl implements SutoCommandRepository {
    private final EntityManager em;

    public SutoCommandRepositoryImpl(EntityManager em) {
        this.em = em;
    }

    @Override
    public List<SutoCommandDAO> getAllCommands() {
        return em.createQuery("select sc from SutoCommandDAO sc", SutoCommandDAO.class)
                .getResultList();
    }

    @Override
    public SutoCommandDAO findById(Long id) {
        return em.find(SutoCommandDAO.class, id);
    }

    @Override
    public SutoCommandDAO findByCommandName(@NotBlank String commandName) {
        return em.createQuery("select sc from SutoCommandDAO sc where sc.command=:commandName"
                        , SutoCommandDAO.class)
                .setParameter("commandName", commandName)
                .getSingleResult();
    }

    @Override
    public SutoCommandDAO saveCommand(@NotNull @Valid SutoCommandDAO commandDao) {
        if (commandDao.getId() == null) {
            em.persist(commandDao);
        } else {
            commandDao = em.merge(commandDao);
        }
        return commandDao;
    }

    @Override
    public void deleteCommand(@NotNull SutoCommandDAO command) {
        if(command.getId()!=null){
            em.remove(command);
        }else {
            em.merge(command);
        }
    }
}
