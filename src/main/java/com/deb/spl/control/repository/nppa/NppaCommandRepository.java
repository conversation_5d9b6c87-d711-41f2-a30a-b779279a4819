package com.deb.spl.control.repository.nppa;


import com.deb.spl.control.data.nppa.NppaCommandDAO;
import com.deb.spl.control.repository.HasCommandWithCaption;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface NppaCommandRepository extends HasCommandWithCaption {
    List<NppaCommandDAO> getAllCommands();

    List<NppaCommandDAO> getAllNcokCommands();

    @Query("select c from NppaCommandDAO  c where UPPER(c.adjacentSystem) like(upper('BYN') )")
    List<NppaCommandDAO> getAllBynCommands();

    NppaCommandDAO findById(Long id);


    NppaCommandDAO saveCommand(NppaCommandDAO commandDao);

    void deleteCommand(NppaCommandDAO command);
}
