package com.deb.spl.control.repository.msu;

import com.deb.spl.control.data.bins.MsuRecord;
import com.deb.spl.control.repository.SplNavigationHistoryRepository;
import org.springframework.data.domain.PageRequest;

import java.util.List;

public interface MsuHistoryRepository extends SplNavigationHistoryRepository<MsuRecord,Long> {
    List<MsuRecord> findByOriginalSentenceLikeIgnoreCase(String likeFilter, PageRequest of);

}
