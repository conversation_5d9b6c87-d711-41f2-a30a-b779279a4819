package com.deb.spl.control.repository.asku;

import com.deb.spl.control.data.asku.RocketFormDataDao;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;

@Repository
public class RocketFormDataRepositoryImpl implements RocketFormDataRepository {

    @PersistenceContext
    private final EntityManager entityManager;

    public RocketFormDataRepositoryImpl(EntityManager entityManager) {
        this.entityManager = entityManager;
    }

    @Override
    public Optional<RocketFormDataDao> findTopByPlantMissile(String plantMissile) {
        List<RocketFormDataDao> dao = entityManager
                .createQuery("SELECT r FROM RocketFormDataDao r " +
                             "WHERE r.isArchived=false AND LOWER(r.plantMissile) LIKE LOWER(:plantMissile)",
                        RocketFormDataDao.class)
                .setParameter("plantMissile", plantMissile)
                .setMaxResults(1)
                .getResultList();
        return dao.size() > 0 ? Optional.ofNullable(dao.get(0)) : Optional.empty();
    }

    @Override
    public Optional<RocketFormDataDao> findTopByPlantMissileArchived(String plantMissile) {
        return Optional.ofNullable(entityManager
                .createQuery("SELECT r FROM RocketFormDataDao r " +
                             "WHERE r.isArchived=true AND LOWER(r.plantMissile) LIKE LOWER(:plantMissile)",
                        RocketFormDataDao.class)
                .setParameter("plantMissile", plantMissile)
                .setMaxResults(1)
                .getSingleResult());

    }

    @Override
    public RocketFormDataDao save(@NotNull
                                  @Valid
                                  RocketFormDataDao rocketFormDataDao) {
        if (rocketFormDataDao.getId() == null) {
            entityManager.persist(rocketFormDataDao);
        } else {
            rocketFormDataDao = entityManager.merge(rocketFormDataDao);
        }
        return rocketFormDataDao;
    }


}
