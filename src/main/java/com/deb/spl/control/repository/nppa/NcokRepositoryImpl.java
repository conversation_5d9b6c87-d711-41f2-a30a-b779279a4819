package com.deb.spl.control.repository.nppa;

import com.deb.spl.control.data.nppa.NcokDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.Arrays;
import java.util.Optional;

@Slf4j
@Repository
public class NcokRepositoryImpl implements NcokRepository {
    @PersistenceContext
    private final EntityManager entityManager;

    public NcokRepositoryImpl(EntityManager entityManager) {
        this.entityManager = entityManager;
    }

    @Override
    public Optional<NcokDao> findFirstByOrderByUpdatedAtDesc() {
        try {
            return Optional.of(entityManager
                    .createQuery("select n from NcokDao n order by n.updatedAt desc", NcokDao.class)
                    .setMaxResults(1)
                    .getSingleResult());
        } catch (Exception e) {
            log.error(e.getMessage() + " - " + Arrays.toString(e.getStackTrace()));
            return Optional.empty();
        }

    }

    @Override
    public NcokDao save(NcokDao ncokDao) {
        if (ncokDao.getId() != null) {
            log.error("attempt to log BynDao  with non null id " + ncokDao.toString());
            ncokDao.setId(null);
        }

        entityManager.persist(ncokDao);

        return ncokDao;

    }

    @Override
    public void detach(NcokDao dao) {
        if (dao == null) {
            log.error("attempt to detach NcokDao entity that is actually is null " + this);
        }
        entityManager.detach(dao);
    }

}
