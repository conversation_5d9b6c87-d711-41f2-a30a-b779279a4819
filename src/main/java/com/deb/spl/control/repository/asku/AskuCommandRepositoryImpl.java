package com.deb.spl.control.repository.asku;

import com.deb.spl.control.data.asku.AskuCommandDAO;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;

@Repository
public class AskuCommandRepositoryImpl implements AskuCommandRepository {
    @PersistenceContext
    private final EntityManager entityManager;

    public AskuCommandRepositoryImpl(EntityManager entityManager) {
        this.entityManager = entityManager;
    }

    @Override
    public List<AskuCommandDAO> getAllCommands() {
        return entityManager
                .createQuery("select command from AskuCommandDAO command", AskuCommandDAO.class)
                .getResultList();
    }

    @Override
    public AskuCommandDAO findById(@NotNull
                                   @Positive
                                   Long id) {
        return entityManager.find(AskuCommandDAO.class, id);
    }

    @Override
    public AskuCommandDAO findByCommandName(@NotBlank
                                            String commandName) {
        return entityManager
                .createQuery("Select command from AskuCommandDAO command where command.command =:commandName", AskuCommandDAO.class)
                .setParameter("commandName", commandName).
                getSingleResult();
    }
}
