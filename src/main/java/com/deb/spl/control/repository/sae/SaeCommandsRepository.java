package com.deb.spl.control.repository.sae;

import com.deb.spl.control.data.sae.SaeCommandDao;
import com.deb.spl.control.repository.HasCommandWithCaption;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SaeCommandsRepository extends HasCommandWithCaption {
    List<SaeCommandDao> getAllCommands();
    SaeCommandDao findById(Long id);
    SaeCommandDao saveCommand(SaeCommandDao saeCommandDao);
    void deleteCommand(SaeCommandDao command);

}
