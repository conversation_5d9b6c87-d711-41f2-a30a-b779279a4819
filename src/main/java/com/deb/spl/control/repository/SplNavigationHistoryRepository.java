package com.deb.spl.control.repository;

import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.NoRepositoryBean;

import java.util.List;

@NoRepositoryBean
public interface SplNavigationHistoryRepository<T,ID> extends JpaRepository<T,ID> {
    List<T> findByOriginalSentenceLikeIgnoreCase(String likeFilter, PageRequest of);

}
