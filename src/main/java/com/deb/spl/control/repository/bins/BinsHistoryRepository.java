package com.deb.spl.control.repository.bins;

import com.deb.spl.control.data.bins.BinsRecord;
import com.deb.spl.control.repository.SplNavigationHistoryRepository;
import org.springframework.data.domain.PageRequest;

import java.util.List;

public interface BinsHistoryRepository extends SplNavigationHistoryRepository<BinsRecord,Long>/*extends HistoryRepository<BinsRecord, Long>*/ {
    List<BinsRecord> findByOriginalSentenceLikeIgnoreCase(String likeFilter, PageRequest of);

}
