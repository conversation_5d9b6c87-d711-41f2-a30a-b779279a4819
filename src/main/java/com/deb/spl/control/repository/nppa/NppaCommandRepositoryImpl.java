package com.deb.spl.control.repository.nppa;

import com.deb.spl.control.data.nppa.NppaCommandDAO;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;

@Repository
public class NppaCommandRepositoryImpl implements NppaCommandRepository {
    @PersistenceContext
    private final EntityManager entityManager;

    public NppaCommandRepositoryImpl(EntityManager entityManager) {
        this.entityManager = entityManager;
    }

    @Override
    public List<NppaCommandDAO> getAllCommands() {
        return entityManager
                .createQuery("select command from NppaCommandDAO command", NppaCommandDAO.class)
                .getResultList();
    }

    @Override
    public List<NppaCommandDAO> getAllNcokCommands() {
        return entityManager
                .createQuery("select c from NppaCommandDAO  c where UPPER(c.adjacentSystem) like(upper('NCOK') )",
                        NppaCommandDAO.class)
                .getResultList();

    }

    @Override
    public List<NppaCommandDAO> getAllBynCommands() {
        return entityManager
                .createQuery("select c from NppaCommandDAO  c where UPPER(c.adjacentSystem) like(upper('BYN') )",
                        NppaCommandDAO.class)
                .getResultList();

    }

    @Override
    public NppaCommandDAO findById(@NotNull
                                   @Positive
                                   Long id) {
        return entityManager.find(NppaCommandDAO.class, id);
    }

    @Override
    public NppaCommandDAO findByCommandName(@NotBlank
                                            String commandName) {
        return entityManager
                .createQuery("Select command from NppaCommandDAO command where command.command =:commandName", NppaCommandDAO.class)
                .setParameter("commandName", commandName).
                getSingleResult();
    }

    @Override
    public NppaCommandDAO saveCommand(@NotNull
                                      @Valid
                                      NppaCommandDAO commandDao) {
        if (commandDao.getId() == null) {
            entityManager.persist(commandDao);
        } else {
            commandDao = entityManager.merge(commandDao);
        }

        return commandDao;
    }

    @Override
    public void deleteCommand(@NotNull
                              @Valid
                              NppaCommandDAO command) {
        if (command.getId() == null) {
            entityManager.remove(command);
        } else {
            entityManager.merge(command);
        }
    }
}
