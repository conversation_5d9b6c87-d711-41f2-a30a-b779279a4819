package com.deb.spl.control.repository.ppo;

import com.deb.spl.control.data.ppo.PpoCommand;
import com.deb.spl.control.repository.HistoryRepository;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
@Transactional
public interface PpoHistoryRepository extends HistoryRepository<PpoCommand, Long>  /*JpaRepository<PpoCommand, Long>*/ {
    List<PpoCommand> findByOriginator(String originator);

    List<PpoCommand> findByCommand(String commandName);

    List<PpoCommand> findByGenerationTimeBetween(LocalDateTime start, LocalDateTime end);

    List<PpoCommand> findByCommandLikeIgnoreCase(String likeFilter, PageRequest of);

    @Override
    @Query(nativeQuery = true, value = "SELECT * FROM ppo_record ORDER BY id DESC limit 1")
    Optional<PpoCommand> getTop();
}
