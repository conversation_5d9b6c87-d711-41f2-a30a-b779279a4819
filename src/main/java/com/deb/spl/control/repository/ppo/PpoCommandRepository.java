package com.deb.spl.control.repository.ppo;


import com.deb.spl.control.data.ppo.PpoCommandDAO;
import com.deb.spl.control.repository.HasCommandWithCaption;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PpoCommandRepository extends HasCommandWithCaption {
    List<PpoCommandDAO> getAllCommands();

    PpoCommandDAO findById(Long id);

    PpoCommandDAO findByCommandName(String commandName);

    PpoCommandDAO saveCommand(PpoCommandDAO commandDao);

    void deleteCommand(PpoCommandDAO command);
}
