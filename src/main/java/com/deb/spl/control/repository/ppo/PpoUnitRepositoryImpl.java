package com.deb.spl.control.repository.ppo;

import com.deb.spl.control.data.ppo.PpoUnit;
import com.deb.spl.control.data.ppo.PpoUnitType;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.transaction.Transactional;
import java.util.List;

@Repository
@Transactional
public class PpoUnitRepositoryImpl implements PpoUnitRepository {

    @PersistenceContext
    private final EntityManager entityManager;

    public PpoUnitRepositoryImpl(EntityManager entityManager) {
        this.entityManager = entityManager;
    }

    @Override
    public List<PpoUnit> getAllUnits() {
        return entityManager.createQuery("select u from PpoUnit  u", PpoUnit.class).getResultList();
    }

    @Override
    public PpoUnit findById(Long id) {
        return entityManager.find(PpoUnit.class, id);
    }

    @Override
    public PpoUnit findByUnitType(PpoUnitType unitType) {

        return entityManager.createQuery("select u from PpoUnit u where u .unitType =:unitType", PpoUnit.class)
                .setParameter("unitType", unitType)
                .getSingleResult();
    }

    @Override
    public PpoUnit saveUnit(PpoUnit ppoUnit) {
        if(ppoUnit.getId()==null){
            entityManager.persist(ppoUnit);
        }else {
            entityManager.merge(ppoUnit);
        }
        return ppoUnit;
    }
}
