package com.deb.spl.control.repository.suto;

import com.deb.spl.control.data.suto.SutoDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceContext;
import java.util.Arrays;
import java.util.Optional;

@Slf4j
@Repository
public class SutoRepositoryImpl implements SutoRepository {
    @PersistenceContext
    private final EntityManager entityManager;

    public SutoRepositoryImpl(EntityManager entityManager) {
        this.entityManager = entityManager;
    }

    @Override
    public Optional<SutoDao> load() {
        Optional<SutoDao> dao = Optional.empty();

        try {
            dao = Optional.ofNullable(
                    entityManager.createQuery("select s from SutoDao s order by s.id desc", SutoDao.class)
                            .setMaxResults(1)
                            .getSingleResult());

        } catch (NoResultException e) {
            log.error(Arrays.toString(e.getStackTrace()));
        }

        return dao;
    }

    @Override
    public SutoDao save(SutoDao dao) {
        if (dao.getId() != null) {
            dao.setId(null);
        }

        entityManager.persist(dao);

        return dao;
    }

}
