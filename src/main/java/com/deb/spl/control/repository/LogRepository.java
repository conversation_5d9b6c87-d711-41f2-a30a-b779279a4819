package com.deb.spl.control.repository;

import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.LogBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface LogRepository {



    Page<LogBean> selectPagedBetweenDatesWitAnyTextSystemStatus(Pageable pageable,
                                                                String textFilter,
                                                                AdjacentSystemType adjacentSystemType,
                                                                LocalDateTime startDate,
                                                                LocalDateTime endDate);

    Page<LogBean> selectPagedBetweenDatesWitAnyTextSystemStatus(Pageable pageable,
                                                                String textFilter,
                                                                List<String> adjacentSystemTypeFilter,
                                                                List<String> adjacentSystemStatus,
                                                                LocalDateTime startDate,
                                                                LocalDateTime endDate);

    List<LogBean> selectPagedBetweenDatesWitAnyTextSystemStatus(String textFilter,
                                                                List<String> adjacentSystemTypeFilter,
                                                                List<String> adjacentSystemStatus,
                                                                LocalDateTime startDate,
                                                                LocalDateTime endDate);

    List<LogBean> selectCommandsLogBetweenDates(AdjacentSystemType system,
                                                LocalDateTime startDate,
                                                LocalDateTime endDate);

    List<LogBean> selectHttpLogs(LocalDateTime startDate,
                                 LocalDateTime endDate);

}
