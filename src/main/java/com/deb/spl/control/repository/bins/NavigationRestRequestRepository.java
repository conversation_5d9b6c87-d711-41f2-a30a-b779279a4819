package com.deb.spl.control.repository.bins;

import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.bins.RestRequestDAO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface NavigationRestRequestRepository extends JpaRepository<RestRequestDAO, Long> {

    List<RestRequestDAO> findByCachedPayloadLikeIgnoreCase(String likeFilter, PageRequest of);

    Page<RestRequestDAO> findAllByAdjacentSystem(Pageable pageable, AdjacentSystemType adjacentSystem);
}
