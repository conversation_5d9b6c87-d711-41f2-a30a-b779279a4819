package com.deb.spl.control.repository.asku;

import com.deb.spl.control.data.asku.RocketFormDataDao;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotBlank;
import java.util.Optional;

@Repository
public interface RocketFormDataRepository{
    Optional<RocketFormDataDao> findTopByPlantMissile(@NotBlank String plantMissile);

    Optional<RocketFormDataDao> findTopByPlantMissileArchived(@NotBlank String plantMissile);

    RocketFormDataDao save(RocketFormDataDao rocketFormDataDao);
}
