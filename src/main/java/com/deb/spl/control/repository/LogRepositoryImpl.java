package com.deb.spl.control.repository;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.LogBean;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.Session;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

@Repository
@Slf4j
public class LogRepositoryImpl implements LogRepository {

    @PersistenceContext
    final EntityManager entityManager;

    final Session session;
    private List<AdjacentSystemType> IGNORED_FOR_EXPORT;

//    @SuppressWarnings("SqlResolve")
//    String requestString_long = """
//             select 'ASKU' as system,
//             asku.id,
//             asku.updated_at,
//                   asku.asku_status as system_status,
//                   concat ('plc_status: ', plc_status, ', spl_id: ', spl_id, ', plate_number: ', plate_number, ', spl_readiness: ', spl_readiness,
//                   ', readiness_started_at: ',readiness_started_at, ', unit_title: ', unit_title, ', left_rocket_id: ',
//                   (select concat('[ ', 'id: ',id, ', date_release_m: ',  date_release_m, ', date_use_m: ', date_use_m, ', initial_data_source: ',\s
//                   initial_data_source, ', technical_condition: ',technical_condition, ', updated_at: ',updated_at,', form_data_id: ',
//                   (select concat('[ ', 'id: ',id,', alp_type: ',alp_type,', crc: ', crc,', generation_date: ',generation_date, ', gsn_type: ',\s
//                   gsn_type,', is_archived: ', is_archived, ', is_telemetry_integrated:', is_telemetry_integrated, ', plant_missile: ',plant_missile,
//                   ', purpose_type: ',purpose_type, ', warhead: ', warhead, ' ]')from rocket_from_data where rocket.form_data_id=rocket_from_data.id),
//                   ', initial_data_id: ',(select concat ('[ ','id: ',id,', altitude: ',altitude, ', generation_date: ',generation_date, ', inclination_angle: ',
//                   inclination_angle,', is_pro_detected: ', is_pro_detected, ', latitude: ',latitude, ', longitude: ',longitude, ', missile_operating_mode: ',\s
//                   missile_operating_mode, ', readiness: ', readiness,', trajectory: ',trajectory,', tl_signature: ', tl_signature, ', loaded_to_plc: ',loaded_to_plc,\s
//                   ', load_temperature: ',load_temperature,', validated_by_tlc: ',validated_by_tlc, ' ]')from launch_initial_data where rocket.initial_data_id=launch_initial_data.id ),
//                   ', initial_datasource_description: ',initial_datasource_description,', initial_data_time_stamp: ',initial_data_time_stamp,', launch_result: ', launch_result,\s
//                   ', sensor_temperature: ',sensor_temperature, ' ]')from rocket where rocket.id=asku.left_rocket_id), ', right_rocket_id: ',
//                   (select concat('[ ', 'id: ',id, ', date_release_m: ',  date_release_m, ', date_use_m: ', date_use_m, ', initial_data_source: ', initial_data_source,\s
//                   ', technical_condition: ',technical_condition, ', updated_at: ',updated_at,', form_data_id: ',(select concat('[ ', 'id: ',id,', alp_type: ',alp_type,', crc: ',
//                   crc,', generation_date: ',generation_date, ', gsn_type: ', gsn_type,', is_archived: ', is_archived, ', is_telemetry_integrated:', is_telemetry_integrated,
//                   ', plant_missile: ',plant_missile,', purpose_type: ',purpose_type, ', warhead: ', warhead, ' ]')from rocket_from_data where rocket.form_data_id=rocket_from_data.id),
//                   ', initial_data_id: ',(select concat ('[ ','id: ',id,', altitude: ',altitude, ', generation_date: ',generation_date, ', inclination_angle: ',inclination_angle,
//                   ', is_pro_detected: ', is_pro_detected, ', latitude: ',latitude, ', longitude: ',longitude, ', missile_operating_mode: ', missile_operating_mode, ', readiness: ',
//                   readiness,', trajectory: ',trajectory,', tl_signature: ', tl_signature, ', loaded_to_plc: ',loaded_to_plc, ', load_temperature: ',load_temperature,', validated_by_tlc: ',
//                   validated_by_tlc, ' ]')from launch_initial_data where rocket.initial_data_id=launch_initial_data.id ),', initial_datasource_description: ',initial_datasource_description,
//                   ', initial_data_time_stamp: ',initial_data_time_stamp,', launch_result: ', launch_result, ', sensor_temperature: ',sensor_temperature, ' ]')
//                   from rocket where rocket.id=asku.right_rocket_id),
//                   ', tpc_left_id: ',(select CONCAT ('[ ','id: ', id, ', updated_at: ', updated_at, ', tpc_load_state: ',tpc_load_state,' ]') from tpc where  tpc.id=asku.tpc_left_id),
//                   ', tpc_right_id: ', (select CONCAT ('[ ','id: ', id, ', updated_at: ', updated_at, ', tpc_load_state: ',tpc_load_state,' ]') from tpc where  tpc.id=asku.tpc_right_id),
//                   ', started_date: ',started_date) as info
//            from asku
//            union all
//            select 'SAE' as system,
//            sae.id,
//                   sae.updated_at,
//                   sae.sae_status,
//                   concat ('readiness: ', readiness,', energized_by_adj: ', energized_by_adj, ', energized_by_hds: ', energized_by_hds, ', energized_by_external_power_source: ',
//                   energized_by_external_power_source, ', adj_start: ',adj_start,', adj_stop: ',adj_stop,', adj_lock: ',adj_lock, ', adj_unlock: ',adj_unlock, ', hds_status: ',
//                   hds_status,', voltage_status: ',voltage_status, ', external_power_source_voltage: ', external_power_source_voltage,', bs_voltage: ', bs_voltage,
//                   ', feeder_1_status: ',feeder_1_status,', feeder_2_status: ',feeder_2_status, ', feeder_3_status: ',feeder_3_status,', feeder_4_status: ',feeder_4_status,
//                   ', feeder_5_status: ',feeder_5_status,', feeder_6_status: ',feeder_6_status, ', feeder_nppa_1_status: ',feeder_nppa_1_status) as info from sae
//             union all
//             select 'BYN' as system,
//             byn.id,
//             byn.updated_at,
//             byn.system_status,
//             concat ('operating_mode: ', operating_mode,', tv_byn: ',tv_byn, ', is_connected: ',is_connected, ', is_ncok: ',is_ncok, ', is_rg_out_ncok: ',is_rg_out_ncok,
//             ', is_buve_f2: ',is_buve_f2,', is_buve_f4: ', is_buve_f4, ', is_basu_otr1_f3: ',is_basu_otr1_f3,', is_basu_otr2_f3: ',is_basu_otr2_f3,', is_f1: ',is_f1,', is_f2: ',
//             is_f2,', is_f3: ',is_f3,', is_f4: ',is_f4,', is_f5: ',is_f5,', is_nppa_connected: ',is_nppa_connected,', is_basu_otr1_connected: ',is_basu_otr1_connected,
//             ', is_basu_otr2_connected: ',is_basu_otr2_connected) as info from byn
//             union all
//             select 'NCOK' as system,
//             ncok.id,
//             ncok.updated_at,
//             ncok.system_status,
//             concat ('readiness_started_at: ',readiness_started_at,', operating_mode: ',operating_mode,', tv_ncok: ',tv_ncok,', is_ncok_connected: ',is_ncok_connected,
//             ', is_suto_connected: ',is_suto_connected,', nppa_test_result: ',nppa_test_result,', app_presence: ',app_presence,', otr1_app_presence: ',otr1_app_presence,
//             ', otr2_app_presence: ',otr2_app_presence,', otr1_test_result: ',otr1_test_result,', otr2_test_result: ',otr2_test_result,', is_otr1_lunched: ',is_otr1_lunched,
//             ', is_otr2_lunched: ',is_otr2_lunched) as info from ncok
//             union all
//             select 'SUTO' as system,
//             suto.id,
//             suto.updated_at,
//             suto.system_status,
//             concat ('left_front_outrigger_emergency_code: ',left_front_outrigger_emergency_code, ', left_rear_outrigger_emergency_code: ',left_rear_outrigger_emergency_code,
//             ', right_front_outrigger_emergency_code: ',right_front_outrigger_emergency_code,', right_rear_outrigger_emergency_code: ',right_rear_outrigger_emergency_code,
//             ', arm_lift_stroke_position: ', arm_lift_stroke_position,', leveling_cycles_count: ',leveling_cycles_count,', main_pump_rmp: ',main_pump_rmp,
//             ', overall_arm_liftings_count: ',overall_arm_liftings_count, ', overall_operating_time: ',overall_operating_time, ', pitch: ',pitch,', pressure_in_impulse_section: ',
//             pressure_in_impulse_section,', roll: ',roll,', temperature_rr: ',temperature_rr,', working_fluid_level: ',working_fluid_level, ', chassis_state: ',chassis_state,
//             ', properties_state: ',properties_state) as info from suto
//             union all
//             select 'PPO' as system,
//             ppo.id,
//             ppo.updated_at,
//             ppo.system_status,
//             ppo.bays_state as info from ppo
//            /* where updated_at>='2023-09-13 20:25:10.222 +0300' */
//             order by updated_at desc """;

    @SuppressWarnings("SqlResolve")
    String requestString = """
            select * from ( select 'ASKU' as system,
                    asku.id,
                    asku.updated_at,
                          asku.asku_status as system_status,
                          concat ('\n','{','\n',
                          '"plc_status" : ', plc_status, '\n',
                          '"spl_id" : ', spl_id, '\n',
                          '"plate_number" : ', plate_number, '\n',
                          '"spl_readiness" : ', spl_readiness,'\n',
                          '"readiness_started_at" : ',readiness_started_at, '\n',
                          '"unit_title" : ', unit_title, '\n',
                          '"left_rocket_id" : ', '\n',
                           (select concat('{', '\n',
                          '"id" : ',id, '\n',
                          '"date_release_m" : ',  date_release_m, '\n',
                          '"date_use_m" : ', date_use_m, '\n',
                          '"initial_data_source" : ', initial_data_source, '\n',
                          '"technical_condition" : ',technical_condition, '\n',
                          '"updated_at" : ',updated_at,'\n',
                          '"form_data_id" : ','\n',
                          (select concat('{ ', '\n',
                          '"id" : ',id,'\n',
                          '"alp_type" : ',alp_type,'\n',
                          '"crc" : ', crc,'\n',
                          '"generation_date" : ',generation_date, '\n',
                          '"gsn_type" : ', gsn_type,'\n',
                          '"is_archived" : ', is_archived,'\n',
                           '"is_telemetry_integrated" :', is_telemetry_integrated,'\n',
                          '"plant_missile" : ',plant_missile,'\n',
                          '"purpose_type" : ',purpose_type,'\n',
                           '"warhead" : ', warhead, '\n',
                           '}' ,'\n')from rocket_from_data where rocket.form_data_id=rocket_from_data.id),
                          '"initial_data_id" : ',(select concat ('{','\n',
                          '"id" : ',id,'\n',
                          '"altitude" : ',altitude, '\n',
                          '"generation_date" : ',generation_date,'\n',
                          '"inclination_angle" : ', inclination_angle,'\n',
                          '"is_pro_detected" : ', is_pro_detected,'\n',
                          '"latitude" : ',latitude,'\n',
                          '"longitude" : ',longitude,'\n',
                          '"missile_operating_mode" : ', missile_operating_mode,'\n',
                           '"readiness" : ', readiness,'\n',
                           '"trajectory" : ',trajectory,'\n',
                           '"tl_signature" : ', tl_signature, '\n',
                           '"loaded_to_plc" : ',loaded_to_plc,'\n',
                          '"load_temperature" : ',load_temperature,'\n',
                          '"validated_by_tlc" : ',validated_by_tlc, '\n',
                          '}', '\n')from launch_initial_data where rocket.initial_data_id=launch_initial_data.id ),
                          '"initial_datasource_description" : ',initial_datasource_description,'\n',
                          '"initial_data_time_stamp" : ',initial_data_time_stamp,'\n',
                          '"launch_result" : ', launch_result,'\n',
                          '"sensor_temperature" : ',sensor_temperature, '\n',
                          ' }', '\n')from rocket where rocket.id=asku.left_rocket_id), 
                          '"right_rocket_id" : ','\n',
                           (select concat('{ ','\n',
                            '"id" : ',id, '\n',
                            '"date_release_m" : ',  date_release_m, '\n',
                            '"date_use_m" : ', date_use_m, '\n',
                            '"initial_data_source" : ', initial_data_source,'\n',
                          '"technical_condition" : ',technical_condition,'\n',
                           '"updated_at" : ',updated_at,'\n',
                           '"form_data_id" : ','\n',
                           (select concat('{', '\n',
                           '"id" : ',id,'\n',
                           '"alp_type" : ',alp_type,'\n',
                           '"crc" : ',crc,'\n',
                           '"generation_date" : ',generation_date,'\n',
                           '"gsn_type" : ', gsn_type,'\n',
                           '"is_archived" : ', is_archived, '\n',
                           '"is_telemetry_integrated" :', is_telemetry_integrated,'\n',
                          '"plant_missile" : ',plant_missile,'\n',
                          '"purpose_type" : ',purpose_type, '\n',
                          '"warhead" : ', warhead, '\n',
                          ' }','\n')from rocket_from_data where rocket.form_data_id=rocket_from_data.id),
                          '"initial_data_id" : ','\n',
                          (select concat ('{ ','\n',
                          '"id" : ',id,'\n',
                          '"altitude" : ',altitude, '\n',
                          '"generation_date" : ',generation_date,'\n',
                          '"inclination_angle" : ',inclination_angle,'\n',
                          '"is_pro_detected" : ', is_pro_detected,'\n',
                           '"latitude" : ',latitude, '\n',
                           '"longitude" : ',longitude,'\n',
                            '"missile_operating_mode" : ', missile_operating_mode,'\n',
                            '"readiness" : ', readiness,'\n',
                            '"trajectory" : ',trajectory,'\n',
                            '"tl_signature" : ', tl_signature, '\n',
                            '"loaded_to_plc" : ',loaded_to_plc, '\n',
                            '"load_temperature" : ',load_temperature,'\n',
                            '"validated_by_tlc" : ', validated_by_tlc,'\n',
                             ' }','\n')from launch_initial_data where rocket.initial_data_id=launch_initial_data.id ),
                             '"initial_datasource_description" : ',initial_datasource_description,'\n',
                          '"initial_data_time_stamp" : ',initial_data_time_stamp,'\n',
                          '"launch_result" : ', launch_result, '\n',
                          '"sensor_temperature" : ',sensor_temperature, 
                          ' }','\n')
                          from rocket where rocket.id=asku.right_rocket_id),
                          '"tpc_left_id" : ','\n',
                          (select CONCAT ('{ ','\n',
                          '"id" : ', id, '\n',
                          '"updated_at" : ', updated_at, '\n',
                          '"tpc_load_state" : ',tpc_load_state,'\n',
                          ' }''\n') from tpc where  tpc.id=asku.tpc_left_id),'\n',
                          '"tpc_right_id" : ','\n',
                           (select CONCAT ('{ ','\n',
                           '"id" : ', id, '\n',
                           '"updated_at" : ', updated_at, '\n',
                           '"tpc_load_state" : ',tpc_load_state,'\n',
                           ' }','\n') from tpc where  tpc.id=asku.tpc_right_id),
                          '"started_date" : ',started_date, '\n',
                          ' }','\n') as info
                         from asku
                         union all
                   select 'SAE' as system,
                   sae.id,
                          sae.updated_at,
                          sae.sae_status,
                          concat ('{','\n',
                          '"readiness" : ', readiness,'\n',
                          '"energized_by_adj" : ', energized_by_adj,'\n',
                          '"energized_by_hds" : ', energized_by_hds, '\n',
                          '"energized_by_external_power_source" : ', energized_by_external_power_source,'\n',
                           '"adj_start" : ',adj_start,'\n',
                           '"adj_stop" : ',adj_stop,'\n',
                           '"adj_lock" : ',adj_lock, '\n',
                           '"adj_unlock" : ',adj_unlock, '\n',
                           '"hds_status" : ', hds_status,'\n',
                           '"voltage_status" : ',voltage_status, '\n',
                           '"external_power_source_voltage" : ', external_power_source_voltage,'\n',
                           '"bs_voltage" : ', bs_voltage,'\n',
                          '"feeder_1_status" : ',feeder_1_status,'\n',
                          '"feeder_2_status" : ',feeder_2_status,'\n',
                          '"feeder_3_status" : ',feeder_3_status,'\n',
                          '"feeder_4_status" : ',feeder_4_status,'\n',
                          '"feeder_5_status" : ',feeder_5_status,'\n',
                          '"feeder_6_status" : ',feeder_6_status, '\n',
                          '"feeder_nppa_1_status" : ',feeder_nppa_1_status,'\n',
                          '}') as info from sae     
                    union all
                    select 'BYN' as system,
                    byn.id,
                    byn.updated_at,
                    byn.system_status,
                    concat ('{','\n',
                    '"operating_mode" : ', operating_mode,'\n',
                    '"tv_byn" : ',tv_byn, '\n',
                    '"is_connected" : ',is_connected, '\n',
                    '"is_ncok" : ',is_ncok,'\n',
                    '"is_rg_out_ncok" : ',is_rg_out_ncok,'\n',
                    '"is_buve_f2" : ',is_buve_f2,'\n',
                    '"is_buve_f4" : ', is_buve_f4, '\n',
                    '"is_basu_otr1_f3" : ',is_basu_otr1_f3,'\n',
                    '"is_basu_otr2_f3" : ',is_basu_otr2_f3,'\n',
                    '"is_f1" : ',is_f1,'\n',
                    '"is_f2" : ',is_f2,'\n',
                    '"is_f3" : ',is_f3,'\n',
                    '"is_f4" : ',is_f4,'\n',
                    '"is_f5" : ',is_f5,'\n',
                    '"is_nppa_connected" : ',is_nppa_connected,'\n',
                    '"is_basu_otr1_connected" : ',is_basu_otr1_connected,'\n',
                    '"is_basu_otr2_connected" : ',is_basu_otr2_connected,'\n',
                    '}') as info from byn
                    union all
                    select 'BYN' as system,
                    msgs_from_ksak.id,
                    msgs_from_ksak.ts as updated_at,
                    'RT_DATA' as system_status,
                    concat( '{','\n',
                    '"code" :', msgs_from_ksak.code,'\n',
                    '"text" : ', msgs_from_ksak.text,'\n',
                    '"data" : ', msgs_from_ksak.data, '\n',
                    '}'
                    ) as info from msgs_from_ksak where dest='BYN'
                    union all
                    select 'NCOK' as system,
                    msgs_from_ksak.id,
                    msgs_from_ksak.ts as updated_at,
                    'RT_DATA' as system_status,
                    concat( '{','\n',
                    '"code" :', msgs_from_ksak.code,'\n',
                    '"text" : ', msgs_from_ksak.text,'\n',
                    '"data" : ', msgs_from_ksak.data, '\n',
                    '}'
                    ) as info from msgs_from_ksak where dest='NCOK'
                    union all
                    select 'BYN' as system,
                    msgs_from_nppa.id,
                    msgs_from_nppa.ksak_ts as updated_at,
                    'RT_DATA' as system_status,
                    concat( '{','\n',
                    '"code" :', msgs_from_nppa.code,'\n',
                    '"text" : ', msgs_from_nppa.text,'\n',
                    '"nppa_ts" : ', msgs_from_nppa.nppa_ts,'\n',
                    '"data" : ', msgs_from_nppa.data, '\n',
                    '}'
                    ) as info from msgs_from_nppa where src='BYN'
                    union all
                    select 'NCOK' as system,
                    msgs_from_nppa.id,
                    msgs_from_nppa.ksak_ts as updated_at,
                    'RT_DATA' as system_status,
                    concat( '{','\n',
                    '"code" :', msgs_from_nppa.code,'\n',
                    '"text" : ', msgs_from_nppa.text,'\n',
                    '"nppa_ts" : ', msgs_from_nppa.nppa_ts,'\n',
                    '"data" : ', msgs_from_nppa.data, '\n',
                    '}'
                    ) as info from msgs_from_nppa where src='NCOK'
                    union all
                    select 'NCOK' as system,
                    ncok.id,
                    ncok.updated_at,
                    ncok.system_status,
                    concat ('{ ', '\n',
                    '"readiness_started_at" : ',readiness_started_at, '\n',
                    '"operating_mode" : ',operating_mode,'\n',
                    '"tv_ncok" : ',tv_ncok,'\n',
                    '"is_ncok_connected" : ',is_ncok_connected,'\n',
                    '"is_suto_connected" : ',is_suto_connected,'\n',
                    '"nppa_test_result" : ',nppa_test_result,'\n',
                    '"app_presence" : ',app_presence,'\n',
                    '"otr1_app_presence" : ',otr1_app_presence,'\n',
                    '"otr2_app_presence" : ',otr2_app_presence,'\n',
                    '"otr1_test_result" : ',otr1_test_result,'\n',
                    '"otr2_test_result" : ',otr2_test_result,'\n',
                    '"is_otr1_lunched" : ',is_otr1_lunched,'\n',
                    '"is_otr2_lunched" : ',is_otr2_lunched,'\n',
                    '}') as info from ncok   
                    union all
                    select 'SUTO' as system,
                    suto.id,
                    suto.updated_at,
                    suto.system_status,
                    concat ('{ ', '\n',
                    '"left_front_outrigger_emergency_code" : ',left_front_outrigger_emergency_code, '\n',
                    '"left_rear_outrigger_emergency_code" : ',left_rear_outrigger_emergency_code,'\n',
                    '"right_front_outrigger_emergency_code" : ',right_front_outrigger_emergency_code,'\n',
                    '"right_rear_outrigger_emergency_code" : ',right_rear_outrigger_emergency_code,'\n',
                    '"arm_lift_stroke_position" : ', arm_lift_stroke_position,'\n',
                    '"leveling_cycles_count" : ',leveling_cycles_count,'\n',
                    '"overall_operating_time" : ',overall_operating_time, '\n',
                    '"pitch" : ',pitch,'\n',
                    '"pressure_in_impulse_section" : ', pressure_in_impulse_section,'\n',
                    '"roll" : ',roll,'\n',
                    '"temperature_rr" : ',temperature_rr,'\n',
                    '"working_fluid_level" : ',working_fluid_level, '\n',
                    '"chassis_state" : ',chassis_state,'\n',
                    '"properties_state" : ',properties_state,'\n',
                    '}') as info from suto 
                    union all
                    select 'PPO' as system,
                    ppo.id,
                    ppo.updated_at,
                    ppo.system_status,
                    ppo.bays_state as info from ppo
                   ) as combined_tables
                    where system in (:systems) and updated_at BETWEEN :startDate AND :endDate  and (system_status in (:statuses) and info like :textFilter)
                    order by updated_at desc                     
            """;

    public LogRepositoryImpl(EntityManager entityManager, Session session,
                             @Value("#{'${report.ignore-for-export:UNDEFINED,UNCLASSIFIED}'.split(',')}") List<String> ignoredForExport) {
        this.entityManager = entityManager;
        this.session = session;
        IGNORED_FOR_EXPORT = ignoredForExport.stream().map(s -> Arrays.stream(AdjacentSystemType.values())
                .anyMatch(e -> e.toString().equalsIgnoreCase(s)) ?
                AdjacentSystemType.valueOf(s) : AdjacentSystemType.UNDEFINED).distinct().toList();
    }

    @Override
    public Page<LogBean> selectPagedBetweenDatesWitAnyTextSystemStatus(Pageable pageable,
                                                                       String textFilter,
                                                                       AdjacentSystemType adjacentSystemType,
                                                                       LocalDateTime startDate,
                                                                       LocalDateTime endDate) {
        int firstResult = (pageable.getPageNumber() /*- 1*/) * pageable.getPageSize();
        String systemFilterStr = "%";

        switch (adjacentSystemType) {
            case UNDEFINED -> systemFilterStr = "";
            case UNCLASSIFIED -> systemFilterStr = "%";
            default -> systemFilterStr = adjacentSystemType.toString();
        }
        if (textFilter == null || textFilter.isEmpty()) {
            textFilter = "%";
        } else {
            textFilter = "%" + textFilter + "%";
        }

        Query query = entityManager.createNativeQuery(requestString)
                .setParameter("textFilter", textFilter)
                .setParameter("systems", systemFilterStr)
                .setParameter("startDate", startDate /*startDate*/)
                .setParameter("endDate", endDate/*endDate*/)
                .setFirstResult(Math.max(firstResult, 0)) //replace with 0 in evaluation
                .setMaxResults(pageable.getPageSize()); //replace with 45 in evaluation


        List<Object> objects = query.getResultList();

        if (objects != null) {
            return new PageImpl<LogBean>(objects.stream().map(e -> ObjectToLogBeanMapper.map(e)).toList());
        }

        return new PageImpl<>(Collections.emptyList());
    }

    @Override
    public Page<LogBean> selectPagedBetweenDatesWitAnyTextSystemStatus(Pageable pageable,
                                                                       String textFilter,
                                                                       List<String> adjacentSystemTypeFilter,
                                                                       List<String> adjacentSystemStatus,
                                                                       LocalDateTime startDate,
                                                                       LocalDateTime endDate) {
        int firstResult = (pageable.getPageNumber() /*- 1*/) * pageable.getPageSize();

        if (textFilter == null || textFilter.isEmpty()) {
            textFilter = "%";
        } else {
            textFilter = "%" + textFilter + "%";
        }

        Query query = entityManager.createNativeQuery(requestString)
                .setParameter("textFilter", textFilter)
                .setParameter("systems", adjacentSystemTypeFilter)
                .setParameter("statuses", adjacentSystemStatus)
                .setParameter("startDate", startDate /*startDate*/)
                .setParameter("endDate", endDate/*endDate*/)
                .setFirstResult(Math.max(firstResult, 0)) //replace with 0 in evaluation
                .setMaxResults(pageable.getPageSize()); //replace with 45 in evaluation


        List<Object> objects = query.getResultList();// TODO: 9/21/2024 getstream and convert

        if (objects != null) {
            return new PageImpl<LogBean>(objects.stream().map(e -> ObjectToLogBeanMapper.map(e)).toList());
        }

        return new PageImpl<>(Collections.emptyList());

    }

    @Override
    public List<LogBean> selectPagedBetweenDatesWitAnyTextSystemStatus(String textFilter,
                                                                       List<String> adjacentSystemTypeFilter,
                                                                       List<String> adjacentSystemStatus,
                                                                       LocalDateTime startDate,
                                                                       LocalDateTime endDate) {

        if (textFilter == null || textFilter.isEmpty()) {
            textFilter = "%";
        } else {
            textFilter = "%" + textFilter + "%";
        }

        Query query = entityManager.createNativeQuery(requestString)
                .setParameter("textFilter", textFilter)
                .setParameter("systems", adjacentSystemTypeFilter)
                .setParameter("statuses", adjacentSystemStatus)
                .setParameter("startDate", startDate)
                .setParameter("endDate", endDate);

        List<Object> objects = query.getResultList();

        if (objects != null) {
            return new ArrayList<LogBean>(objects.stream().map(e -> ObjectToLogBeanMapper.map(e)).toList());
        }

        return new ArrayList<>();
    }

    @SuppressWarnings("SqlResolve")
    String nmeaLogRequest = """
            select 
                   (case
                        when adjacent_system = 0 then 'BINS'
                        when adjacent_system = 1 then 'MSU'
                        else 'UNDEFINED'
                       end) as system,
                   id,
                   generation_date as updated_at,
                   'ANY' as status,
                   concat('{',
                          '"sentence" : ',original_sentence,',\n',
                          '"direction" : ',(case
                                                when direction = 0 then 'IN'
                                                when direction = 1 then 'OUT'
                                                else 'UNDEFINED'
                                            end)
                       , '
            }')    as info
            from :table_name
            where generation_date BETWEEN :startDate AND :endDate
            order by id desc
            """;

    String navigationRestLogRequest = """
            select (case
                        when adjacent_system = 0 then 'BINS'
                        when adjacent_system = 1 then 'MSU'
                        when adjacent_system = 8 then 'PLC'
                        when adjacent_system = 13 then 'NSD'
                        else 'UNDEFINED'
                end) as system,
                   id,
                   generation_date,
                   'UNDEFINED' as status,
                   concat('{\n',
                                '"URL" : ',url,'\n'
                                '"payload" : ',cached_payload,'\n'
                                '"request_query_string" : ',request_query_string,'\n'
                       '}') as info
            from spl_navigation_rest_log
            where generation_date BETWEEN :startDate AND :endDate
            order by id desc
            """;

    String postedCommandsRequestString = """
            select adjacent_system as system,
                   id,                   
                   generation_time,
                   (case execution_time
                       when null then 'UNDEFINED'
                       else 'OK'
                   end) as state,
                   concat('{\n',
                       '"command" : ',command,'\n',
                       '"description" : ',caption,'\n',
                       '"executed_at" : ', (case execution_time
                                                when null then 'Не виконано'
                                                else to_char(execution_time,'YYYY-MM-DD HH24:MI:SS.sss')
                                            end)
                       , '\n}')    as info
            from :table_name
            where generation_time BETWEEN :startDate AND :endDate
            order by id desc
            """;

    String postedCommandsRequestStringWithSystemFilter = """
            select adjacent_system as system,
                   id,                   
                   generation_time,
                   (case execution_time
                       when null then 'UNDEFINED'
                       else 'OK'
                   end) as state,
                   concat('{\n',
                       '"command" : ',command,'\n',
                       '"description" : ',caption,'\n',
                       '"executed_at" : ', (case execution_time
                                                when null then 'Не виконано'
                                                else to_char(execution_time,'YYYY-MM-DD HH24:MI:SS.sss')
                                            end)
                       , '\n}')    as info
            from :table_name
            where (generation_time BETWEEN :startDate AND :endDate) AND adjacent_system like :systemFilter
            order by id desc
            """;


    @Override
    public List<LogBean> selectCommandsLogBetweenDates(AdjacentSystemType system,
                                                       LocalDateTime startDate,
                                                       LocalDateTime endDate) {
        log.info("system - " + system + ", start date - " + startDate + ", end date - " + endDate);
        if (IGNORED_FOR_EXPORT.contains(system)) {
            return new ArrayList<>();
        }

        Query query;
        if (system.equals(AdjacentSystemType.BINS) || system.equals(AdjacentSystemType.MSU)) {
            query = entityManager.createNativeQuery(nmeaLogRequest.replace(":table_name",
                            systemFilterToCommandsTableName(system)))
                    .setParameter("startDate", startDate)
                    .setParameter("endDate", endDate);
        } else if (system.equals(AdjacentSystemType.BYN) || system.equals(AdjacentSystemType.NCOK)) {
            query = entityManager.createNativeQuery(postedCommandsRequestStringWithSystemFilter.replace(":table_name",
                            systemFilterToCommandsTableName(system)))
                    .setParameter("startDate", startDate)
                    .setParameter("endDate", endDate)
                    .setParameter("systemFilter", system.getEn());
        } else {
            query = entityManager.createNativeQuery(postedCommandsRequestString.replace(":table_name",
                            systemFilterToCommandsTableName(system)))
                    .setParameter("startDate", startDate)
                    .setParameter("endDate", endDate);
        }


        List<Object> objects = query.getResultList();

        List<LogBean> converted = new ArrayList<>();
        if (objects != null) {
            converted = objects.stream().map(ObjectToLogBeanMapper::map).toList();
        }
        return converted;
    }

    @Override
    public List<LogBean> selectHttpLogs(LocalDateTime startDate,
                                        LocalDateTime endDate) {

        Query query = entityManager.createNativeQuery(navigationRestLogRequest)
                .setParameter("startDate", startDate)
                .setParameter("endDate", endDate);


        List<Object> objects = query.getResultList();

        return objects != null ?
                objects.stream().map(ObjectToLogBeanMapper::map).toList() :
                new ArrayList<>();
    }

    private String systemFilterToCommandsTableName(AdjacentSystemType systemFilter) {
        String tableName;
        switch (systemFilter) {
            case SAE -> tableName = "sae_record";
            case SUTO -> tableName = "suto_record";
            case PPO -> tableName = "ppo_record";
            case ASKU -> tableName = "asku_command_record";
            case NPPA, BYN, NCOK -> tableName = "nppa_command_record";
            case BINS -> tableName = "bins_record";
            case MSU -> tableName = "msu_record";

            default -> tableName = "";
        }
        return tableName;
    }

    class ObjectToLogBeanMapper {
        static LogBean map(Object o) {
            if (o == null || ((Object[]) o).length < 5) {
                return null;
            }
            try {
                Object[] val = (Object[]) o;
                return LogBean.builder()
                        .system(AdjacentSystemType.valueOf(val[0].toString()))
                        .id(Long.parseLong(val[1].toString()))
                        .updatedAt(((Timestamp) val[2]).toLocalDateTime())
                        .status(AdjacentSystemStatus.valueOf(val[3].toString()))
                        .info(val[4].toString())
                        .build();
            } catch (Exception e) {
                log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                return null;
            }
        }
    }
}
