package com.deb.spl.control.repository.ppo;

import com.deb.spl.control.data.ppo.PpoCommandDAO;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;

@Repository
@Transactional
public class PpoCommandRepositoryImpl implements PpoCommandRepository {
    @PersistenceContext
    private final EntityManager entityManager;

    public PpoCommandRepositoryImpl(EntityManager entityManager) {
        this.entityManager = entityManager;
    }

    @Override
    public List<PpoCommandDAO> getAllCommands() {
        return entityManager
                .createQuery("select pc from PpoCommandDAO pc", PpoCommandDAO.class)
                .getResultList();
    }

    @Override
    public PpoCommandDAO findById(@NotNull @Positive Long id) {
        return entityManager.find(PpoCommandDAO.class, id);
    }

    @Override
    public PpoCommandDAO findByCommandName(@NotBlank String commandName) {
        return entityManager
                .createQuery("select pc from PpoCommandDAO pc where pc.command = :commandName", PpoCommandDAO.class)
                .setParameter("commandName", commandName)
                .getSingleResult();
    }

    @Override
    public PpoCommandDAO saveCommand(@NotNull @Valid PpoCommandDAO commandDao) {
        if (commandDao.getId() == null) {
            entityManager.persist(commandDao);
        } else {
            commandDao = entityManager.merge(commandDao);
        }

        return commandDao;
    }

    @Override
    public void deleteCommand(@NotNull @Valid PpoCommandDAO commandDao) {
        if (commandDao.getId() != null) {
            entityManager.remove(commandDao);
        } else {
            entityManager.merge(commandDao);
        }
    }
}
