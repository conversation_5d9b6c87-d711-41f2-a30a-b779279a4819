package com.deb.spl.control.repository.asku;

import com.deb.spl.control.data.asku.RocketDao;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface RocketRepository {
    Optional<RocketDao> findById(long id);

    Optional<RocketDao> findByPlantMissile(String plantMissile);

    Optional<RocketDao> findByOrderEntityId(UUID orderEntityId);

    RocketDao save(RocketDao rocketDao);
}
