package com.deb.spl.control.repository.sae;

import com.deb.spl.control.data.sae.SaeCommand;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;

@Repository
@Transactional
public class SaeHistoryRepositoryImpl  {

    private final EntityManager entityManager;

    public SaeHistoryRepositoryImpl(EntityManager entityManager) {
        this.entityManager = entityManager;
    }

    public SaeCommand save(SaeCommand saeCommand) {
        if (saeCommand.getId() == null) {
            entityManager.persist(saeCommand);
        } else {
            entityManager.merge(saeCommand);
        }

        return saeCommand;
    }


    public List<SaeCommand> getAll() {
        return entityManager.createQuery("select sc from SaeCommand sc", SaeCommand.class).getResultList();
    }

    public SaeCommand findById(Long id) {
        return entityManager.find(SaeCommand.class, id);
    }


    public List<SaeCommand> findByOriginator(String originator) {
        TypedQuery<SaeCommand> findByUserQuery = entityManager
                .createQuery("SELECT sc from SaeCommand  sc where sc.originator = : originator",SaeCommand.class);
        findByUserQuery.setParameter("originator",originator);

        return findByUserQuery.getResultList();
    }


    public List<SaeCommand> findByCommand(String commandName) {
        TypedQuery<SaeCommand> query = entityManager
                .createQuery("select sc from SaeCommand sc where sc.command = :commandName", SaeCommand.class);
        query.setParameter("commandName",commandName);

        return query.getResultList();
    }

    public List<SaeCommand> findBetween(LocalDateTime start, LocalDateTime end) {
        TypedQuery<SaeCommand> query = entityManager
                .createQuery("select sc from SaeCommand sc where sc.generationTime between :start and :end", SaeCommand.class)
                .setParameter("start",start)
                .setParameter("end",end);

        return query.getResultList();
    }
}
