package com.deb.spl.control.repository.nppa;

import com.deb.spl.control.data.nppa.BynDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Optional;

@Slf4j
@Repository
public class BynRepositoryImpl implements BynRepository {

    @PersistenceContext
    private  final EntityManager entityManager;

    public BynRepositoryImpl(EntityManager entityManager) {
        this.entityManager = entityManager;
    }

    @Override
    public Optional<BynDao> findFirstByOrderByUpdatedAtDesc() {
        try {
            return Optional.of(entityManager
                    .createQuery("select b from BynDao b order by b.updatedAt desc", BynDao.class)
                    .setMaxResults(1)
                    .getSingleResult());
        }catch (Exception e){
            log.error(e.getMessage() + " - "+ Arrays.toString(e.getStackTrace()));
            return Optional.empty();
        }
    }

    @Override
    public BynDao save(@NotNull @Valid BynDao bynDao) {
        if(bynDao.getId()!=null){
            log.error("attempt to log BynDao  with non null id " + bynDao.toString());
            bynDao.setId(null);
        }

        entityManager.persist(bynDao);

        return bynDao;
    }

    @Override
    public void detach(BynDao dao) {
        if(dao==null){
            log.error("attempt to detach BynDao entity that is actually is null " + this);
        }
        entityManager.detach(dao);
    }
}
