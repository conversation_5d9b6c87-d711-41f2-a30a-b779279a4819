package com.deb.spl.control.repository.asku;

import com.deb.spl.control.data.asku.LaunchReferencePointDao;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface LaunchReferencePointsRepository extends JpaRepository<LaunchReferencePointDao, Long> {

    @Query("select p.caption from LaunchReferencePointDao p where  p.command=:command")
    Optional<String> findCaption(@Param("command") String command);

    Optional<LaunchReferencePointDao> findById(long id);

    Optional<LaunchReferencePointDao> findFirstByCommand(String command);

}
