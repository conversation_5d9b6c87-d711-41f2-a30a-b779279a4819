package com.deb.spl.control.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

@Profile("!disable_ccv_task_executor_service")
@Component
@EnableAsync
@Slf4j
public class CcvTasksExecutorService {

    public static final int DELAY_AFTER_REQUEST_MILLIS = 250;
    private final CcvCommunicationService ccvCommunicationService;
    private final long ccvRefreshDelay;
    private final long ccvConnectionLostDelay;
    private final long requestDurationMillis;


    public CcvTasksExecutorService(CcvCommunicationService ccvCommunicationService,
                                   @Value("${ccv.request.time-limit.sec:4}") long requestDurationMillis,
                                   @Value("${ccv.refresh-delay.sec:20}") long ccvRefreshDelay,
                                   @Value("${ccv.connection-lost-delay.sec:60}") long ccvConnectionLostDelay

    ) {
        requestDurationMillis *= 1000;
        ccvRefreshDelay *= 1000;
        ccvConnectionLostDelay *= 1000;
        this.ccvCommunicationService = ccvCommunicationService;
        this.ccvRefreshDelay = ccvRefreshDelay > (requestDurationMillis + DELAY_AFTER_REQUEST_MILLIS) * 2 ?
                ccvRefreshDelay : (requestDurationMillis + 250) * 2;
        this.ccvConnectionLostDelay = ccvConnectionLostDelay;
        this.requestDurationMillis = requestDurationMillis;
    }

    private LocalDateTime started = LocalDateTime.now();

    private LocalDateTime connectionLost = null;

    @Scheduled(fixedRateString = "${ccv.refresh-delay.sec:10}", timeUnit = TimeUnit.SECONDS)
    void updateCcvConnectionState() {
        ccvCommunicationService.updateMasterCcvConnectionState(ccvRefreshDelay, requestDurationMillis, ccvConnectionLostDelay);
    }


}
