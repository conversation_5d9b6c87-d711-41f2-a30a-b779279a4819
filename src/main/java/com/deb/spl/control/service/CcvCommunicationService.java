package com.deb.spl.control.service;

import com.deb.spl.control.controller.BadRequestException;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.asku.MsgType;
import com.deb.spl.control.data.bins.LogRecordDirection;
import com.deb.spl.control.data.bins.RestRequestDAO;
import com.deb.spl.control.data.ccv.AppSettingsResource;
import com.deb.spl.control.data.ccv.VehicleResource;
import com.deb.spl.control.repository.bins.NavigationRestRequestRepository;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.utils.CommandsUtils;
import com.deb.spl.control.utils.logs.LoggingJsonDecoder;
import com.deb.spl.control.utils.logs.LoggingJsonEncoder;
import com.deb.spl.control.views.events.CcvVehicleEvent;
import com.deb.spl.control.views.events.MsgEvent;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;

@SuppressWarnings("MismatchedJavadocCode")
@Slf4j
@Service
public class CcvCommunicationService {
    public final String tokenRequestParameterName;
    @Getter
    private final WebClient client;
    private final NavigationRestRequestRepository restRepository;
    private StringBuffer loggedEncoderJsonBuffer = new StringBuffer();
    private StringBuffer loggedDecoderJsonBuffer = new StringBuffer();
    private LoggingJsonEncoder loggingEncoder;
    private LoggingJsonDecoder loggingDecoder;
    private final String ccvRocketStatusUrl;
    private final String ccvAppSettingsUrl;
    private final String ccvVehiclesUrl;
    private final String ccvToken;
    private AppSettingsResource appSettingsResource;
    private VehicleResource masterCcv;
    @Getter
    private final String ccvTypeJsonPropertyValue;
    private final AskuService askuService;

    public Optional<VehicleResource> getMasterCcv() {
        return Optional.ofNullable(masterCcv);
    }

    public Optional<AppSettingsResource> getAppSettingsResource() {
        ObjectMapper mapper = new ObjectMapper();
        if (appSettingsResource == null) {
            return Optional.empty();
        }

        AppSettingsResource copy;
        try {
            copy = mapper.readValue(mapper.writeValueAsString(appSettingsResource),
                    AppSettingsResource.class);
        } catch (JsonProcessingException e) {
            log.error(e.getMessage() + Arrays.toString(e.getStackTrace()));
            return Optional.empty();
        }

        return Optional.of(copy);
    }

    private final ExecutorService executorService = Executors.newSingleThreadExecutor();

    public CcvCommunicationService(AskuService askuService,
                                   @Autowired NavigationRestRequestRepository restRepository,
                                   @Autowired CommandsUtils commandsUtils,
                                   @Value("${ccv.token.request-parameter-name:Token}")
                                   String tokenRequestParameterName,
                                   @Value("${ccv.token.value}") String ccvToken,
                                   @Value("${ccv.rocket-status-endpoint.url}") String ccvRocketStatusUrl,
                                   @Value("${ccv.app-settings.url}") String ccvAppSettingsUrl,
                                   @Value("${ccv.vehicles.url}") String ccvVehiclesUrl,
                                   @Value("${ccv.json.property.type.name:CCV}") String ccvTypeJsonPropertyValue) {
        this.ccvRocketStatusUrl = ccvRocketStatusUrl;
        this.restRepository = restRepository;
        this.ccvAppSettingsUrl = ccvAppSettingsUrl;
        this.ccvVehiclesUrl = ccvVehiclesUrl;
        this.commandsUtils = commandsUtils;
        this.ccvToken = ccvToken;
        this.tokenRequestParameterName = tokenRequestParameterName;
        this.ccvTypeJsonPropertyValue = ccvTypeJsonPropertyValue;
        this.askuService = askuService;

        client = configureWebClient();
        appSettingsClient = client.mutate()
                .baseUrl(this.ccvAppSettingsUrl)
                .build();
        vehicleResourceClient = client.mutate()
                .baseUrl(this.ccvVehiclesUrl)
                .build();
    }

    private WebClient configureWebClient() {
        loggedDecoderJsonBuffer = new StringBuffer();
        loggingEncoder = new LoggingJsonEncoder(
                data -> {
                    loggedEncoderJsonBuffer.append(new String(data));
                });
        loggingDecoder = new LoggingJsonDecoder(
                data -> {
                    loggedDecoderJsonBuffer.append(new String(data));
                });

        final WebClient client = WebClient.builder()
                .codecs(clientCodecConfigurer -> {
                    clientCodecConfigurer.defaultCodecs().jackson2JsonEncoder(loggingEncoder);
                    clientCodecConfigurer.defaultCodecs().jackson2JsonDecoder(loggingDecoder);
                })
                .filters(exchangeFilterFunctions -> {
                    exchangeFilterFunctions.add(logRequest());
                    exchangeFilterFunctions.add(logResponse());
                })
                .defaultHeader(tokenRequestParameterName, ccvToken)
                .build();

        loggedDecoderJsonBuffer.setLength(0);
        return client;
    }

    private ExchangeFilterFunction logRequest() {
        return ExchangeFilterFunction.ofRequestProcessor(clientRequest -> {

            //append clientRequest method and url
            RestRequestDAO requestDAO = RestRequestDAO.builder()
                    .url(UriComponentsBuilder.fromUri(clientRequest.url()).replaceQuery("").toUriString())
                    .method(clientRequest.method().toString())
                    .requestQueryString(clientRequest.url().getQuery())
                    .adjacentSystem(AdjacentSystemType.CCV)
                    .direction(LogRecordDirection.OUT)
                    //todo figure out how to get body
                    .build();

            if (clientRequest.httpRequest() != null) {
                requestDAO.setRequestQueryString(clientRequest.httpRequest().toString());
            }
            if (clientRequest.body() != null) {
                String payload = requestDAO.getCachedPayload() == null ? "" : requestDAO.getCachedPayload();

                if (loggedEncoderJsonBuffer.length() > 0) {
                    payload += loggedEncoderJsonBuffer;
                    loggedEncoderJsonBuffer.setLength(0);
                }

                requestDAO.setCachedPayload(payload);
            }

            clientRequest
                    .headers()
                    .forEach((name, values) -> values.forEach(value ->
                            requestDAO.addHeaders(String.format("{%s} = {%s}", name, value))));

            RestRequestDAO saved = null;
            if (requestDAO.isConsiderable() || validateRequest(requestDAO.getMethod(), requestDAO.getUrl())) {
                saved = restRepository.save(requestDAO);
            }

            log.info(saved != null ? saved.toString() : requestDAO.toString());

            return Mono.just(clientRequest);
        });
    }

    private ExchangeFilterFunction logResponse() {
        return ExchangeFilterFunction.ofResponseProcessor(clientResponse -> {

            RestRequestDAO requestDAO = RestRequestDAO.builder()
                    .adjacentSystem(AdjacentSystemType.CCV)
                    .direction(LogRecordDirection.RESPONSE)
                    .build();

            if (loggedDecoderJsonBuffer.length() > 0) {
                String payload = requestDAO.getCachedPayload() == null ? "" : requestDAO.getCachedPayload();

                payload += loggedDecoderJsonBuffer;
                loggedDecoderJsonBuffer.setLength(0);

                requestDAO.setCachedPayload(payload);
            }

            clientResponse
                    .headers().asHttpHeaders()
                    .forEach((name, values) -> values.forEach(value -> requestDAO.addHeaders(String.format("{%s} = {%s}", name, value))));

            RestRequestDAO saved = null;
            if (requestDAO.isConsiderable()) {
                saved = restRepository.save(requestDAO);
            }

            log.info(saved != null ? saved.toString() : requestDAO.toString());

            return Mono.just(clientResponse);
        });
    }

    boolean validateRequest(String method, String uri) {
        if (!method.equalsIgnoreCase("POST")) {
            return false;
        }
        return uri.equalsIgnoreCase(ccvRocketStatusUrl);
    }


    private final WebClient appSettingsClient;

    /**
     * load MMHS application settings
     *
     * @param requestDurationMillis timeout expiration
     * @return true if app settings was loaded
     */
    @Async
    public CompletableFuture<Optional<AppSettingsResource>> loadAppSettingsResource(long requestDurationMillis) {

        return CompletableFuture.supplyAsync(() -> {
            AppSettingsResource appSettingsResource = null;
            try {
                ResponseEntity responseEntity = appSettingsClient
                        .get()
                        .accept(MediaType.APPLICATION_JSON)
                        .retrieve()
                        .toEntity(AppSettingsResponseData.class)
                        .block(Duration.ofMillis(requestDurationMillis));

                if (responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful() &&
                    responseEntity.getBody() != null) {

                    appSettingsResource = ((AppSettingsResponseData) responseEntity.getBody()).data();

                    if (this.appSettingsResource == null ||
                        (appSettingsResource != null && !this.appSettingsResource.equals(appSettingsResource))) {

                        appSettingsResource.getVehicleNumber().ifPresent(askuService::setSplPlateNumber);
                    }

                    this.appSettingsResource = appSettingsResource;
                    return Optional.ofNullable(appSettingsResource);

                } else if (responseEntity != null) {
                    log.error("can't get AppSettingsResource: " + responseEntity.getStatusCode() +
                              " message : " + responseEntity.getBody());
                } else {
                    // Log or handle non-successful response
                    log.debug("can't get AppSettingsResource: url " + ccvAppSettingsUrl + " failed.");
                }
            } catch (Exception e) {
                // Log or handle exception
                log.error(Arrays.toString(e.getStackTrace()));
                log.error("can't get AppSettingsResource: url" + ccvAppSettingsUrl +
                          " failed with exception.");
            }
            return Optional.empty();
        });
    }


    private final WebClient vehicleResourceClient;

    /**
     * load available vehicles dictionary from MMHS
     *
     * @param requestDurationMillis timeout expiration
     * @return loaded vehicles resources
     */
    @Async
    public CompletableFuture<List<VehicleResource>> loadVehiclesResources(boolean updateStatus, long requestDurationMillis) {
        return CompletableFuture.supplyAsync(() -> {
            if (Thread.currentThread().isInterrupted()) {
                return Collections.emptyList();
            }

            ResponseEntity response = vehicleResourceClient
                    .get()
                    .uri(uriBuilder ->
                            uriBuilder
                                    .queryParam("with_unit_working_modes", updateStatus ? 1 : 0)
                                    .build())
                    .accept(MediaType.APPLICATION_JSON)
                    .retrieve()
                    .toEntity(VehicleResourceData.class)
                    .block(Duration.ofMillis(requestDurationMillis));

            List<VehicleResource> loadedVehicleResources = Collections.emptyList();
            if (response != null && response.getStatusCode().is2xxSuccessful()) {
                loadedVehicleResources = ((VehicleResourceData) response.getBody()) == null ? Collections.emptyList() :
                        ((VehicleResourceData) response.getBody()).data();

                return loadedVehicleResources;
            }

            return loadedVehicleResources;
        });
    }

    @Getter
    private boolean hasConnection;
    @Getter
    private LocalDateTime disconnectedTs = null;

    /**
     * @param timeLimit             total time to perform action. Greater than (requestDurationMillis+250)*2
     * @param requestDurationMillis timeout expiration to perform each request to remote web service
     * @return CCV's connection state
     */
    @Async
    public void updateMasterCcvConnectionState(long timeLimit, long requestDurationMillis, long ccvConnectionLostDelay) {
        if (timeLimit < (requestDurationMillis + 250) * 2) {
            timeLimit = (requestDurationMillis + 250) * 2;
        }
        long timeLimitPerAction = timeLimit / 2;

        CompletableFuture<Optional<VehicleResource>> masterCcvFuture = loadAppSettingsResource(requestDurationMillis)
                .thenApply((dependantAppSettings) -> {
                    if (dependantAppSettings.isEmpty() || dependantAppSettings.get().getVehicleId() == null) {
                        return Optional.empty();
                    }
                    List<VehicleResource> vehiclesDictionary;
                    try {
                        vehiclesDictionary = loadVehiclesResources(true, requestDurationMillis)
                                .get(timeLimitPerAction, TimeUnit.MILLISECONDS);
                        if (vehiclesDictionary == null || vehiclesDictionary.isEmpty()) {
                            return Optional.empty();
                        }


                        Optional<VehicleResource> dependantVehicle = vehiclesDictionary.stream()
                                .filter(vehicle -> vehicle.getEntityId().equals(dependantAppSettings.get().getVehicleId()))
                                .findFirst();
                        if (dependantVehicle.isEmpty()) {
                            return Optional.empty();
                        }

                        UUID parentUnitId = dependantVehicle.get().getUnit().getParentId();

                        return vehiclesDictionary.stream()
                                .filter(vehicle -> vehicle.getUnitId().equals(parentUnitId) &&
                                                   vehicle.isCcv(getCcvTypeJsonPropertyValue()))
                                .findFirst();
                    } catch (Exception e) {
                        log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                        return Optional.empty();
                    }
                });

        Optional<VehicleResource> masterCcvResource = Optional.empty();

        try {
            masterCcvResource = masterCcvFuture.get(timeLimitPerAction, TimeUnit.MILLISECONDS);
        } catch (ExecutionException | InterruptedException | RuntimeException | TimeoutException e) {
            log.error(e.getLocalizedMessage() + " " + Arrays.asList(e.getStackTrace()));
        }

        boolean connected = masterCcvResource.map(VehicleResource::isHasConnection).orElse(false);
        if (masterCcvResource.isPresent() && this.masterCcv != masterCcvResource.get()) {
            this.masterCcv = masterCcvResource.get();
        }
// TODO: 6/2/2024 fix connection check
        if (hasConnection != connected) {
            if (connected) {
                hasConnection = true;
                disconnectedTs = null;

                Broadcaster.broadcast(CcvVehicleEvent.builder()
                        .vehicleResource(this.masterCcv)
                        .hasConnection(connected)
                        .source(this)
                        .build());

                log.info("connected to CCV " + LocalDateTime.now()
                        .format(DateTimeFormatter.ofPattern("dd/MM/yyyy\tE HH:mm:ss")));

            } else {
                disconnectedTs = disconnectedTs != null ? disconnectedTs : LocalDateTime.now();

                if (LocalDateTime.now().isAfter(disconnectedTs.plusSeconds(ccvConnectionLostDelay / 1000))) {

                    hasConnection = false;

                    Broadcaster.broadcast(CcvVehicleEvent.builder()
                            .vehicleResource(this.masterCcv)
                            .hasConnection(connected)
                            .source(this)
                            .build());

                    log.warn("lost connection with CCV " + LocalDateTime.now()
                            .format(DateTimeFormatter.ofPattern("dd/MM/yyyy\tE HH:mm:ss")));
                }
            }
        }
    }


    public Optional<String> getMasterCcvName() {
        if (masterCcv == null || masterCcv.getUnit() == null) {
            return Optional.empty();
        }
        return Optional.ofNullable(masterCcv.getNumber());
    }

    private final CommandsUtils commandsUtils;

    public boolean setErrorMessage(@NotNull MsgType msgType,
                                   AdjacentSystemType systemType,
                                   @NotBlank String payload,
                                   String alias) {
        switch (msgType) {
            case INFO, ERROR, WARNING -> {
                if (!alias.isBlank() && payload.contains(alias)) {
                    Optional<String> caption = alias.isBlank() ? Optional.empty() :
                            commandsUtils.getCaptionByAliasAndSystem(systemType, alias);

                    if (caption.isPresent()) {
                        payload = payload.replace(alias, caption.get());
                    }
                }
            }
            case COMMIT -> {
                if (systemType == null || alias.isBlank()) {
                    throw new BadRequestException("Всі поля RequestBody повинні бути заповнені. " + msgType);
                }
                Optional<String> caption = alias.isBlank() ? Optional.empty() :
                        commandsUtils.getCaptionByAliasAndSystem(systemType, alias);

                if (caption.isPresent()) {
                    payload = payload.replace(alias, caption.get());
                }
            }
            default -> {
                throw new BadRequestException("Невідомий тип повідомлення");
            }
        }

        MsgEvent event = MsgEvent.builder()
                .adjacentSystemType(systemType)
                .msgType(msgType)
                .payload(payload)
                .source(this)
                .build();

        Broadcaster.broadcast(event);
        return true;
    }


    //todo Dmitriy, [22.06.2023 18:58]
    // тогда я вешаю запрос на UUID текущей спу и если не получаю ответ  - использую по-умолчанию spl.default-spl-id =00000000-0000-0000-0000-000000000000
    // Dmitriy, [22.06.2023 18:59]
    // и раз в минут 10 опрашиваю сервис на предмет изменения id
    public Optional<UUID> getSplId() {
        if (appSettingsResource != null &&
            appSettingsResource.getVehicleId() == null) {
            return Optional.ofNullable(appSettingsResource.getVehicleId());
        }

        return Optional.empty();
    }


    private record AppSettingsResponseData(AppSettingsResource data) {

    }

    private record VehicleResourceData(List<VehicleResource> data) {

    }
}
