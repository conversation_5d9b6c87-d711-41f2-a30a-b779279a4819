package com.deb.spl.control.service.asku;

import com.deb.spl.control.controller.BadRequestException;
import com.deb.spl.control.data.asku.LaunchInitialData;
import com.deb.spl.control.data.asku.Rocket;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.Arrays;

@Service
@Slf4j
public class TLService {
    private static final double LOAD_TEMPERATURE = -999.9;


    public boolean validateKeySignature(@NotNull LaunchInitialData initialData, @NotNull Rocket rocket) {
        if (rocket.getStoredTlKeys() == null || rocket.getStoredTlKeys().size() == 0) {
            log.error("No TLKeys were found in rocket " + rocket);
            throw new BadRequestException("Не знайдено жодних TL ключів.");
        }

        StringBuilder validatingSequence = new StringBuilder();
        validatingSequence
                .append(String.valueOf(LOAD_TEMPERATURE))
                .append(initialData.getLatitudeRad())
                .append(initialData.getLongitudeRad())
                .append(initialData.getAltitude())
                .append(initialData.getInclinationAngle())
                .append(initialData.getTrajectory())
                .append(initialData.getReadiness())
                .append(initialData.isProDetected());

        for (String tlkey : rocket.getStoredTlKeys()) {
            if (validateKey(validatingSequence, initialData.getTlCode(), tlkey)) {
                log.info("validation  is successful:  initialData" + initialData +
                        " sequence - " + validatingSequence +
                        " TLCode " + initialData.getTlCode() +
                        " TlKey:" + tlkey);
                return true;
            }
        }


        log.error("validation  error:  initialData" + initialData +
                " sequence - " + validatingSequence +
                " TLCode " + initialData.getTlCode() +
                " TlKeys: " + Arrays.toString(rocket.getStoredTlKeys().toArray()));
        return false;
    }

    private boolean validateKey(StringBuilder validatingSequence, String tlCode, String tlKey) {
        // TODO: 16.08.2023 implement
        log.error("used mock method in TLService");
        return true;
    }
}
