package com.deb.spl.control.service;


import com.deb.spl.control.data.*;
import com.deb.spl.control.data.bins.LogRecordDirection;
import com.deb.spl.control.data.bins.Position;
import com.deb.spl.control.data.bins.RestRequestDAO;
import com.deb.spl.control.repository.bins.NavigationRestRequestRepository;
import com.deb.spl.control.utils.BinsToDtoConverter;
import com.deb.spl.control.utils.logs.LoggingJsonDecoder;
import com.deb.spl.control.utils.logs.LoggingJsonEncoder;
import com.deb.spl.control.views.events.AdjacentSystemUpdateEventType;
import com.deb.spl.control.views.events.BinsDtoEvent;
import com.deb.spl.control.views.events.BinsEvent;
import com.nimbusds.jose.shaded.json.JSONObject;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.sf.marineapi.nmea.parser.SentenceFactory;
import net.sf.marineapi.nmea.sentence.POHPRSentence;
import net.sf.marineapi.nmea.util.BinsCalibrationStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

import javax.validation.constraints.NotNull;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;

@Service
@Slf4j
@Data
public class BinsService {
    private final NavigationRestRequestRepository navigationRestRequestRepository;

    @Value("${bins.connection-time-out-sec}")
    int BINS_CONNECTION_TIMEOUT;

    @Getter
    @Value("${time.utc-zone-id}")
    private String utcTimeZoneString;

    private LocalDateTime lastDataUpdateDT = LocalDateTime.MIN;
    private WebClient utcGetClient;

    @Value("${bins.data-source-endpoint.url}")
    private String binsServiceUrl;
    private String BINS_DEFAULT_URL = "http://localhost:8081/bins";

    private final StringBuffer loggedEncoderJsonBuffer = new StringBuffer();
    private final StringBuffer loggedDecoderJsonBuffer = new StringBuffer();
    private final LoggingJsonEncoder loggingEncoder;
    private final LoggingJsonDecoder loggingDecoder;

    @Getter
    private AdjacentSystemStatus status = AdjacentSystemStatus.UNDEFINED; // TODO: 25.03.2023 add status updating to every corresponding request
    @Getter
    private BinsCalibrationStatus binsCalibrationStatus;
    private final WebClient client;

    private List<String> enabledEndpoints = Collections.synchronizedList(new ArrayList<>());

    public BinsService(NavigationRestRequestRepository navigationRestRequestRepository,
                       @Value("${bins.data-source-endpoint.url}")
                       String configuredUrl) {
        binsServiceUrl = configuredUrl != null ? configuredUrl : BINS_DEFAULT_URL;
        loggingEncoder = new LoggingJsonEncoder(
                data -> {
                    loggedEncoderJsonBuffer.append(new String(data));
                });

        loggingDecoder = new LoggingJsonDecoder(
                data -> {
                    loggedDecoderJsonBuffer.append(new String(data));
                });


        client = WebClient.builder()
                .codecs(clientCodecConfigurer -> {
                    clientCodecConfigurer.defaultCodecs().jackson2JsonEncoder(loggingEncoder);
                    clientCodecConfigurer.defaultCodecs().jackson2JsonDecoder(loggingDecoder);
                })
                .filters(exchangeFilterFunctions -> {
                    exchangeFilterFunctions.add(logRequest());
                    exchangeFilterFunctions.add(logResponse());
                })
                .baseUrl(binsServiceUrl)
                .build();
        this.navigationRestRequestRepository = navigationRestRequestRepository;
    }

    public UtcDateTime getLocalDateTimeUtc() {
        return UtcDateTime.builder()
                .date(LocalDate.now())
                .time(LocalTime.now())
                .build();
    }

    private ExchangeFilterFunction logRequest() {
        return ExchangeFilterFunction.ofRequestProcessor(clientRequest -> {

            //append clientRequest method and url
            RestRequestDAO requestDAO = RestRequestDAO.builder()
                    .url(UriComponentsBuilder.fromUri(clientRequest.url()).replaceQuery("").toUriString())
                    .method(clientRequest.method().toString())
                    .requestQueryString(clientRequest.url().getQuery())
                    .adjacentSystem(AdjacentSystemType.BINS)
                    .direction(LogRecordDirection.OUT)
                    .build();

            if (clientRequest.httpRequest() != null) {
                requestDAO.setRequestQueryString(clientRequest.httpRequest().toString());
            }
            if (clientRequest.body() != null) {
                String payload = requestDAO.getCachedPayload() == null ? "" : requestDAO.getCachedPayload();

                if (loggedEncoderJsonBuffer.length() > 0) {
                    payload += loggedEncoderJsonBuffer;
                    loggedEncoderJsonBuffer.setLength(0);
                }

                requestDAO.setCachedPayload(payload);
            }

            clientRequest
                    .headers()
                    .forEach((name, values) -> values.forEach(value ->
                            requestDAO.addHeaders(String.format("{%s} = {%s}", name, value))));

            RestRequestDAO saved = null;
            if (requestDAO.isConsiderable() || isValidBinsServiceRequest(requestDAO.getMethod(), requestDAO.getUrl())) {
                saved = navigationRestRequestRepository.save(requestDAO);
            }

            log.info(saved != null ? saved.toString() : requestDAO.toString());

            return Mono.just(clientRequest);
        });

    }

    boolean isValidBinsServiceRequest(String method, String uri) {
        if (!method.equalsIgnoreCase("POST")) {
            return false;
        }
        return uri.equalsIgnoreCase("http://localhost:8081/bins/reset")
               || uri.equalsIgnoreCase("http://localhost:8081/bins/calibration")
               || uri.equalsIgnoreCase("http://localhost:8081/bins/start");
    }

    private ExchangeFilterFunction logResponse() {
        return ExchangeFilterFunction.ofResponseProcessor(clientResponse -> {

            RestRequestDAO requestDAO = RestRequestDAO.builder()
                    .adjacentSystem(AdjacentSystemType.BINS)
                    .direction(LogRecordDirection.RESPONSE)
                    //todo figure out how to ge body
                    .build();


            if (loggedDecoderJsonBuffer.length() > 0) {
                String payload = requestDAO.getCachedPayload() == null ? "" : requestDAO.getCachedPayload();

                payload += loggedDecoderJsonBuffer;
                loggedDecoderJsonBuffer.setLength(0);

                requestDAO.setCachedPayload(payload);
            }

            clientResponse
                    .headers().asHttpHeaders()
                    .forEach((name, values) -> values.forEach(value -> requestDAO.addHeaders(String.format("{%s} = {%s}", name, value))));

            RestRequestDAO saved = null;
            if (requestDAO.isConsiderable()) {
                saved = navigationRestRequestRepository.save(requestDAO);
            }

            log.info(saved != null ? saved.toString() : requestDAO.toString());

            return Mono.just(clientResponse);
        });
    }

    @Value("${bins.sleep-time-on-error-sec}")
    private int binsSleepTimeOnError;
    private LocalDateTime disconnectedTs = LocalDateTime.MIN;

    /**
     * /bins/utc:
     * get:
     * summary: "GET bins/utc"
     * operationId: "getDateTime"
     * responses:
     * "200":
     * description: "OK"
     *
     * @return LocalDate date; LocalTime time
     */
    public Optional<UtcDateTime> getDateTime() {
        if (LocalDateTime.now().isBefore(disconnectedTs.plusSeconds(binsSleepTimeOnError))) {
            log.debug("bin is disconnected. Waiting  " + binsSleepTimeOnError + " sec. till next request" + this.getClass());

            return Optional.empty();
        }

        Mono<UtcDateTime> response = client.get()
                .uri("/utc")
                .retrieve()
                .bodyToMono(UtcDateTime.class)
                .onErrorComplete();
        Optional<UtcDateTime> utcDateTime = Optional.ofNullable(response.block());

        if (utcDateTime.isEmpty() && !isConnected()) {
            disconnectedTs = LocalDateTime.now();
            log.error("bins is disconnected for " + binsSleepTimeOnError + " sec.");
        }

        return utcDateTime;
    }

    /*
      /bins:
    get:
      summary: "GET bins"
      operationId: "getBinsDto"
      responses:
        "200":
          description: "OK"
     */

    public Optional<BinsDto> getBinsDto() {
        BinsDto dto = null;
        try {
            Mono<BinsDto> response = client.get()
                    .exchangeToMono(clientResponse -> {
                        if (clientResponse.statusCode() == HttpStatus.OK) {
                            return clientResponse.bodyToMono(BinsDto.class);
                        } else {
                            update(null);
                            return null;
                        }
                    });
            dto = response.block();
        } catch (Exception e) {
            log.error("can't connect to " + binsServiceUrl +
                      " The reason is  " + e.getMessage());
        }

        update(dto);

        return Optional.ofNullable(dto);
    }


    /*
      /bins/position:
    get:
      summary: "GET bins/position"
      operationId: "getPosition"
      parameters:
      - name: "convertToRad"
        in: "query"
        required: true
        schema:
          type: "boolean"
      responses:
        "200":
          description: "OK"
     */
    public Optional<Position> getBinsPosition(boolean convertToRadians) {

        Mono<Position> response = client.get()
                .uri("/position")
                .header("convertToRad", String.valueOf(convertToRadians))
                .retrieve()
                .bodyToMono(Position.class)
                .onErrorComplete();

        return Optional.ofNullable(response.block());
    }


    /*
     /bins/start:
    post:
      summary: "POST bins/start"
      operationId: "start"
      responses:
        "200":
          description: "OK"
     */
    public ResponseEntity sendStart() {
        final String startSentence = "$PORQS,START,1*3E";

        loggedEncoderJsonBuffer.setLength(0);
        loggedEncoderJsonBuffer.append(startSentence);

        ResponseEntity response = client.post()
                .uri("/start")
                .contentType(MediaType.TEXT_PLAIN)
                .body(BodyInserters.fromValue(startSentence))
                .retrieve()
                .toEntity(String.class)
                .block(Duration.ofMillis(1000));

        return response;
    }

/*
      /bins/reset:
    post:
      summary: "POST bins/reset"
      operationId: "resetCommand"
      responses:
        "200":
          description: "OK"
     */

    public ResponseEntity sendReset() {
        final String resetSentence = "$PORST*4A";

        loggedEncoderJsonBuffer.setLength(0);
        loggedEncoderJsonBuffer.append(resetSentence);

        ResponseEntity response = client.post()
                .uri("/reset")
                .contentType(MediaType.TEXT_PLAIN)
                .body(BodyInserters.fromValue(resetSentence))
                .retrieve()
                .toEntity(String.class)
                .block(Duration.ofMillis(1000));

        return response;
    }

    /*
      /bins/calibration:
    post:
      summary: "POST bins/calibration"
      operationId: "calibrate"
      responses:
        "200":
          description: "OK"
     */

    public ResponseEntity sendCalibrate() {
        final String calibSentence = "$POCLB*52";

        loggedEncoderJsonBuffer.setLength(0);
        loggedEncoderJsonBuffer.append(calibSentence);

        ResponseEntity response = client.post()
                .uri("/calibration")
                .contentType(MediaType.TEXT_PLAIN)
                .body(BodyInserters.fromValue(calibSentence))
                .retrieve()
                .toEntity(String.class)
                .block(Duration.ofMillis(1000));

        return response;
    }

    /*
      /bins/coordinates/manual:
    post:
      summary: "POST bins/coordinates/manual"
      operationId: "setCoordinatesManually"
      responses:
        "200":
          description: "OK"
     */
    public ResponseEntity sendCoordinatesManually(@NotNull net.sf.marineapi.nmea.util.Position position) {
        JSONObject body = new JSONObject();
        body.put("latitude", position.getLatitude());
        body.put("latitudeHemisphere", position.getLatitudeHemisphere());
        body.put("longitude", position.getLongitude());
        body.put("longitudeHemisphere", position.getLongitudeHemisphere());
        body.put("altitude", position.getAltitude());
        body.put("altitudeUnit", "METER");

        loggedEncoderJsonBuffer.setLength(0);
        loggedEncoderJsonBuffer.append(body);

        ResponseEntity response = client.post()
                .uri("/coordinates/manual")
                .contentType(MediaType.APPLICATION_JSON)
                .body(BodyInserters.fromValue(body))
                .retrieve()
                .toEntity(String.class)
                .block(Duration.ofMillis(1000));

        return response;
    }

    /*
      /bins/sentence/setup:
    post:
      summary: "POST bins/sentence/setup"
      operationId: "setup"
      parameters:
      - name: "name"
        in: "query"
        required: true
        schema:
          type: "string"
      - name: "enable"
        in: "query"
        required: true
        schema:
          type: "boolean"
      responses:
        "200":
          description: "OK"
     */
    public ResponseEntity sendSetupMessages(@NotNull String name, boolean isEnabled) {
        Map<String, String> sentencesEnabled = new WeakHashMap<>();
        sentencesEnabled.put("GPGGA", "$PORQS,GPGGA,1*28");
        sentencesEnabled.put("PO-HPR", "$PORQS,PO-HPR,1*2B");
        sentencesEnabled.put("GPRMC", "$PORQS,GPRMC,1*35");
        sentencesEnabled.put("POPMP", "$PORQS,POPMP,1*2C");
        sentencesEnabled.put("START", "$PORQS,START,1*3E");


        if (!sentencesEnabled.containsKey(name)) {
            log.error("attempt to send a wrong setup message");
            return (new ResponseEntity(HttpStatus.BAD_REQUEST));
        }

        ResponseEntity response = client.post()
                .uri(uriBuilder -> uriBuilder.path("/sentence/setup")
                        .queryParam("sentenceName", name)
                        .queryParam("enabled", String.valueOf(isEnabled))
                        .build())
                .retrieve()
                .toEntity(String.class)
                .block(Duration.ofMillis(1000));

        return response;
    }


    public boolean isConnected() {
        return LocalDateTime.now().isBefore(lastDataUpdateDT.plusSeconds(BINS_CONNECTION_TIMEOUT));
    }

    public AdjacentSystemStatus getStatus(BinsCalibrationStatus binsCalibrationStatus) {

        Map<BinsCalibrationStatus, AdjacentSystemStatus> comparisonMap = new WeakHashMap<>();
        comparisonMap.put(BinsCalibrationStatus.RELIABLE,
                AdjacentSystemStatus.OK);
        comparisonMap.put(BinsCalibrationStatus.RESOLVE_IS_CALCULATED_WITHOUT_PROOF_OF_RELIABLE,
                AdjacentSystemStatus.WARNING);
        comparisonMap.put(BinsCalibrationStatus.AUTOMATIC_CALIBRATION_IN_PROGRESS,
                AdjacentSystemStatus.WARNING);
        comparisonMap.put(BinsCalibrationStatus.RESOLVE_NOT_CALCULATED,
                AdjacentSystemStatus.ERROR);

        AdjacentSystemStatus calculatedStatus = comparisonMap.get(binsCalibrationStatus);
        if (calculatedStatus == null) {
            calculatedStatus = isConnected() ? status : AdjacentSystemStatus.NOT_CONNECTED;
        }

        return calculatedStatus;
    }

    private void update(BinsDto dto) {
        AdjacentSystemStatus previousStatus = status;
        if (dto == null) {
            if (status != AdjacentSystemStatus.NOT_CONNECTED && !isConnected()) {
                this.status = AdjacentSystemStatus.NOT_CONNECTED;
                fireBinsUpdateEvent(new Bins(), previousStatus, AdjacentSystemStatus.NOT_CONNECTED);
            }
            return;
        }

        fireBinsDtoUpdateEvent(dto);
        Bins bins = BinsToDtoConverter.fromDto(dto);
        if (dto.getHpr() != null) {
            try {
                SentenceFactory factory = SentenceFactory.getInstance();
                POHPRSentence hprSentence = (POHPRSentence) factory.createParser(dto.getHpr());

                String[] hprArr = dto.getHpr().split(",");
                if (hprArr.length >= 4 && !hprArr[4].isEmpty()) {
                    BinsCalibrationStatus binsCalibrationStatus = hprSentence.getResolveStatus();

                    if (binsCalibrationStatus != null) {
                        this.binsCalibrationStatus = binsCalibrationStatus;
                        this.status = getStatus(binsCalibrationStatus);
                        lastDataUpdateDT = LocalDateTime.now();
                    } else {
                        this.status = AdjacentSystemStatus.UNDEFINED;
                    }
                } else {
                    this.status = AdjacentSystemStatus.UNDEFINED;
                }
            } catch (Exception e) {
                log.error(e.getMessage());
                fireBinsUpdateEvent(new Bins(), previousStatus, AdjacentSystemStatus.UNDEFINED);
            }
        } else {
            this.binsCalibrationStatus = BinsCalibrationStatus.RESOLVE_NOT_CALCULATED;
            this.status = getStatus(binsCalibrationStatus);
        }

        fireBinsUpdateEvent(bins, previousStatus, status);
    }

    private void fireBinsDtoUpdateEvent(BinsDto dto) {
        if (dto == null) {
            return;
        }

        BinsDtoEvent event = new BinsDtoEvent(dto, this, AdjacentSystemUpdateEventType.DATA_UPDATE);
        Broadcaster.broadcast(event);
    }


    private void fireBinsUpdateEvent(Bins bins, AdjacentSystemStatus previousStatus, AdjacentSystemStatus updatedStatus) {

        Map<AdjacentSystemStatus, AdjacentSystemUpdateEventType> statusToTypeMap = new WeakHashMap<>();
        statusToTypeMap.put(AdjacentSystemStatus.OK, AdjacentSystemUpdateEventType.DATA_UPDATE);
        statusToTypeMap.put(AdjacentSystemStatus.WARNING, AdjacentSystemUpdateEventType.DATA_UPDATE);
        statusToTypeMap.put(AdjacentSystemStatus.ERROR, AdjacentSystemUpdateEventType.ERROR_OCCURRED);
        statusToTypeMap.put(AdjacentSystemStatus.NOT_CONNECTED, AdjacentSystemUpdateEventType.NOT_CONNECTED);
        statusToTypeMap.put(AdjacentSystemStatus.UNDEFINED, AdjacentSystemUpdateEventType.UNDEFINED);


        AdjacentSystemUpdateEventType eventType = null;

        if (updatedStatus == AdjacentSystemStatus.NOT_CONNECTED) {
            eventType = AdjacentSystemUpdateEventType.NOT_CONNECTED;
        } else if (statusToTypeMap.get(updatedStatus) == AdjacentSystemUpdateEventType.DATA_UPDATE) {
            eventType = AdjacentSystemUpdateEventType.DATA_UPDATE;
        } else if (statusToTypeMap.get(updatedStatus) == AdjacentSystemUpdateEventType.ERROR_OCCURRED) {
            eventType = AdjacentSystemUpdateEventType.ERROR_OCCURRED;
        } else {
            eventType = AdjacentSystemUpdateEventType.UNDEFINED;
        }


        BinsEvent dataChangeEvent = new BinsEvent(bins, this, eventType);
        Broadcaster.broadcast(dataChangeEvent);
    }

}
