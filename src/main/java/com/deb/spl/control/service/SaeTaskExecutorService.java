package com.deb.spl.control.service;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Profile("!disable_sae_task_executor_service")
@Component
@Setter
@EnableAsync
@Slf4j
public class SaeTaskExecutorService {
    private final SaeService saeService;

    @Autowired
    public SaeTaskExecutorService(SaeService saeService) {
        this.saeService = saeService;
    }

    @Scheduled(fixedDelay = 1000)
    public void updateSaeState(){// TODO: 25.04.2023 rename
        log.debug("calling for SAE event " + this.getClass());
        saeService.fireUpdateEvent();
    }

}
