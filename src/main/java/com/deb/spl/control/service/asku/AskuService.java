package com.deb.spl.control.service.asku;

import com.deb.spl.control.controller.BadRequestException;
import com.deb.spl.control.controller.InitialDataValidationException;
import com.deb.spl.control.controller.NotFoundException;
import com.deb.spl.control.controller.ServerErrorException;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.asku.*;
import com.deb.spl.control.data.bins.LogRecordDirection;
import com.deb.spl.control.data.bins.Position;
import com.deb.spl.control.data.bins.RestRequestDAO;
import com.deb.spl.control.repository.asku.*;
import com.deb.spl.control.repository.bins.NavigationRestRequestRepository;
import com.deb.spl.control.service.BinsService;
import com.deb.spl.control.service.Broadcaster;
import com.deb.spl.control.service.CcvCommunicationService;
import com.deb.spl.control.utils.AskuUtils;
import com.deb.spl.control.utils.CommandsUtils;
import com.deb.spl.control.utils.RocketUtils;
import com.deb.spl.control.utils.logs.LoggingJsonDecoder;
import com.deb.spl.control.utils.logs.LoggingJsonEncoder;
import com.deb.spl.control.views.adjacentSystems.utils.CountdownOTR;
import com.deb.spl.control.views.events.*;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.sf.marineapi.nmea.util.BinsCalibrationStatus;
import org.apache.commons.lang3.NotImplementedException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

import javax.persistence.EntityManager;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Stream;

@Slf4j
@Service
public class AskuService {
    private final LaunchReferencePointMapper launchReferencePointMapper;

    @Getter
    private Asku askuEntity;
    private final EntityManager em;
    private final TLService tlService;
    private final AskuRepository askuRepository;
    private final TpcRepository tpcRepository;
    private final RocketRepository rocketRepository;
    private final RocketFormDataRepository rocketFormDataRepository;
    private final LaunchInitialDataRepository initialDataRepository;
    private final LaunchReferencePointsRepository referencePointsRepository;
    private final LaunchReferencePointRecordsRepository referencePointRecordsRepository;
    private final OrderInfoRepository orderInfoRepository;
    private final NavigationRestRequestRepository restRepository;
    private final AskuMapper askuMapper;
    private final RocketMapper rocketMapper;
    private final RocketFormDataMapper rocketFormDataMapper;
    private final CcvCommunicationService ccvCommunicationService;
    private final BinsService binsService;
    private final LaunchInitialDataMapper initialDataMapper;
    private final OrderInfoMapper orderInfoMapper;
    private final String postRocketStatusUrl;
    private final String ccvToken;

    private final long basuTemperatureLimitHi;
    private final long basuTemperatureLimitHiHi;

    private boolean commandsLockDuringLaunch = false;

    public boolean isCommandsLockDuringLaunch() {
        return commandsLockDuringLaunch;
    }

    public void lockCommandsDuringLaunch() {
        this.commandsLockDuringLaunch = true;
    }

    public void unlockCommandsDuringLunch() {
        this.commandsLockDuringLaunch = false;
    }

    public AskuService(EntityManager em,
                       TLService tlService,
                       AskuRepository askuRepository,
                       TpcRepository tpcRepository,
                       RocketRepository rocketRepository,
                       RocketFormDataRepository rocketFormDataRepository,
                       LaunchInitialDataRepository initialDataRepository,
                       LaunchReferencePointsRepository referencePointsRepository,
                       LaunchReferencePointRecordsRepository referencePointRecordsRepository, NavigationRestRequestRepository restRepository,
                       AskuMapper askuMapper,
                       RocketMapper rocketMapper,
                       RocketFormDataMapper rocketFormDataMapper,
                       BinsService binsService,
                       LaunchInitialDataMapper initialDataMapper,
                       CommandsUtils commandUtils,
                       @Value("${spl.default-spl-id}") String defaultSplId,
                       @Value("${spl.plate-number}") String plateNumber,
                       @Value("${spl.unit.title}") String splUnitTitle,
                       @Value("${ccv.rocket-status-endpoint.url}") String ccvRocketStatusUrl,
                       @Value("${ccv.token.value:''}") String ccvToken,
                       OrderInfoRepository orderInfoRepository,
                       OrderInfoMapper orderInfoMapper,
                       @Value("${ccv.token.request-parameter-name:Token}")
                       String tokenRequestParameterName,
                       @Value("${ccv.app-settings.url}") String ccvAppSettingsUrl,
                       @Value("${ccv.vehicles.url}") String ccvVehiclesUrl,
                       @Value("${ccv.json.property.type.name:CCV}") String ccvTypeJsonPropertyValue,
                       @Value("${basu-temeparature.warning.hi:45}") long basuTemperatureLimitHi,
                       @Value("${basu-temeparature.warning.hi-hi:50}") long basuTemperatureLimitHiHi,
                       LaunchReferencePointMapper launchReferencePointMapper) {
        this.em = em;
        this.tlService = tlService;
        this.askuRepository = askuRepository;
        this.tpcRepository = tpcRepository;
        this.rocketRepository = rocketRepository;
        this.rocketFormDataRepository = rocketFormDataRepository;
        this.initialDataRepository = initialDataRepository;
        this.referencePointsRepository = referencePointsRepository;
        this.referencePointRecordsRepository = referencePointRecordsRepository;
        this.restRepository = restRepository;
        this.askuMapper = askuMapper;
        this.rocketMapper = rocketMapper;
        this.rocketFormDataMapper = rocketFormDataMapper;
        this.binsService = binsService;
        this.initialDataMapper = initialDataMapper;
        this.commandUtils = commandUtils;
        this.defaultSplId = defaultSplId;
        this.plateNumber = plateNumber;
        this.orderInfoRepository = orderInfoRepository;
        this.orderInfoMapper = orderInfoMapper;
        String convertedSplUnitTitle;
        convertedSplUnitTitle = new String(splUnitTitle.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
        this.splUnitTitle = convertedSplUnitTitle;
        this.askuEntity = getPersistedAskuStateOrInitializeDefault();
        this.basuTemperatureLimitHi = basuTemperatureLimitHi;
        this.basuTemperatureLimitHiHi = basuTemperatureLimitHiHi;
        this.ccvCommunicationService = new CcvCommunicationService(this,
                restRepository,
                commandUtils,
                tokenRequestParameterName,
                ccvToken,
                ccvRocketStatusUrl,
                ccvAppSettingsUrl,
                ccvVehiclesUrl,
                ccvTypeJsonPropertyValue);

        tlKeysForPlc = new ConcurrentHashMap<String, List<String>>();

        previousKeyVersion = -1;
        this.postRocketStatusUrl = ccvRocketStatusUrl;
        this.ccvToken = ccvToken;

        client = configureWebClient();
        this.launchReferencePointMapper = launchReferencePointMapper;
    }

    @Getter
    private final String defaultSplId;
    @Getter
    private final String plateNumber;
    @Getter
    private final String splUnitTitle;

    private AskuDao getAskuWithRockets(AskuDao askuDao) {
        if (askuDao.getLeftRocket() != null) {
            rocketRepository.findById(askuDao.getLeftRocket().getId()).ifPresent(askuDao::setLeftRocket);
        }
        if (askuDao.getRightRocket() != null) {
            rocketRepository.findById(askuDao.getRightRocket().getId()).ifPresent(askuDao::setRightRocket);
        }
        return askuDao;
    }

    private Asku getPersistedAskuStateOrInitializeDefault() {
        AskuDao askuDao = getAskuWithRockets(askuRepository.findFirstByOrderByUpdatedAtDesc());

        if (askuDao != null) {
            boolean updated = false;
            if (!askuDao.getSplId().equals(UUID.fromString(defaultSplId))) {
                log.warn("UUID СПУ змінено з " + askuDao.getSplId().toString() + " на " + defaultSplId);
                askuDao.setSplId(UUID.fromString(defaultSplId));
                updated = true;
            }
            if (!askuDao.getPlateNumber().equalsIgnoreCase(plateNumber)) {
                log.warn("Номерний знак СПУ змінено з " + askuDao.getPlateNumber() + " на " + plateNumber);
                askuDao.setPlateNumber(plateNumber);
                updated = true;
            }
            if (!askuDao.getUnitTitle().equalsIgnoreCase(splUnitTitle)) {
                log.warn("Кодову назву СПУ змінено з " + askuDao.getUnitTitle() + " на " + splUnitTitle);
                askuDao.setUnitTitle(splUnitTitle);
                updated = true;
            }

            if (updated) {
                askuDao.setId(askuDao.getId() + 1);
                askuDao = askuRepository.save(askuDao);
            }

            askuEntity = askuMapper.map(getAskuWithRockets(askuRepository.findFirstByOrderByUpdatedAtDesc()));
            log.info("ASKU loaded from DB " + askuDao);
            return askuEntity;
        } else {
            log.info("ASKU loaded from default builder");
            return initEmtpyAsku();
        }
    }

    private Asku initEmtpyAsku() {
        Optional<UUID> splId = ccvCommunicationService.getSplId();
        Position position = null;
        if (binsService.getBinsCalibrationStatus() != null && binsService.getBinsCalibrationStatus().equals(BinsCalibrationStatus.RELIABLE)) {
            position = binsService.getBinsPosition(false).orElse(null);
        }

        return AskuUtils.getDefaultAsku(splId.orElseGet(() -> UUID.fromString(defaultSplId)),
                plateNumber, position);
    }

    public void updatePosition() {
        if (!binsService.isConnected()) {
            return;
        }
        Optional<Position> position = binsService.getBinsPosition(false);
        position.ifPresent(value -> askuEntity.setPosition(value));
    }


    public Optional<TpcState> getLeftTpcLoadState() {
        if (askuEntity == null || askuEntity.getTpcLeft() == null) {
            return Optional.empty();
        }

        boolean isLeft = true;
        if (askuEntity.getTpcLeft().getTpcLoadState().equals(TpcState.TPC_WITH_ROCKET)) {
            if (getRocket(isLeft).isPresent() && !getRocket(isLeft).get().isTechnicalCondition()) {
                return Optional.of(TpcState.ERROR);
            }
        }

        return Optional.ofNullable(askuEntity.getTpcLeft().getTpcLoadState());
    }


    public Optional<TpcState> getRightTpcLoadState() {
        if (askuEntity == null || askuEntity.getTpcRight() == null) {
            return Optional.empty();
        }

        boolean isLeft = false;
        if (askuEntity.getTpcRight().getTpcLoadState().equals(TpcState.TPC_WITH_ROCKET)) {
            if (getRocket(isLeft).isPresent() && !getRocket(isLeft).get().isTechnicalCondition()) {
                return Optional.of(TpcState.ERROR);
            }
        }

        return Optional.ofNullable(askuEntity.getTpcRight().getTpcLoadState());
    }


    public AskuDto getAskuDto() {

        if (askuMapper == null) {
            throw new NotFoundException("No AskuEntity in " + this);
        }
        return askuMapper.toDto(askuEntity);

    }

    public synchronized void fireUpdateEvent() {
        AdjacentSystemUpdateEventType eventType;
        if (askuEntity == null) {
            getPersistedAskuStateOrInitializeDefault();
            askuEntity.setStatus(AdjacentSystemStatus.UNDEFINED);
        }

        updatePosition();

        switch (askuEntity.getStatus()) {
            case NOT_CONNECTED -> eventType = AdjacentSystemUpdateEventType.NOT_CONNECTED;
            case OK, WARNING -> eventType = AdjacentSystemUpdateEventType.DATA_UPDATE;
            case ERROR -> eventType = AdjacentSystemUpdateEventType.ERROR_OCCURRED;
            default -> eventType = AdjacentSystemUpdateEventType.UNDEFINED;
        }

        AskuDao dao = getAskuWithRockets(askuRepository.findFirstByOrderByUpdatedAtDesc());

        if (dao != null) {
            Asku prev = askuMapper.map(dao);
            if (!askuEntity.equals(prev)) {
                persistAskuState();
            }
        }

        if (getAskuInfo().isEmpty()) {
            log.debug("can't process empty ASKUInfo at " + this);
            return;
        }

        AskuInfoEvent askuInfoEvent = AskuInfoEvent.builder()
                .askuInfo(getAskuInfo().get())
                .source(this)
                .eventType(eventType)
                .build();

        Broadcaster.broadcast(askuInfoEvent);
    }

    public Optional<AskuInfo> getAskuInfo() {
        String leftMissileFactoryNo = "";
        if (askuEntity.getLeftRocket() != null && askuEntity.getLeftRocket().getPlantMissile().isPresent()) {
            leftMissileFactoryNo = askuEntity.getLeftRocket().getPlantMissile().get();
        }
        String rightMissileFactoryNo = "";
        if (askuEntity.getRightRocket() != null && askuEntity.getRightRocket().getPlantMissile().isPresent()) {
            rightMissileFactoryNo = askuEntity.getRightRocket().getPlantMissile().get();
        }

        return Optional.ofNullable(AskuInfo.builder()
                .splId(askuEntity.getSplId())
                .plateNumber(askuEntity.getPlateNumber())
                .unitTitle(askuEntity.getUnitTitle() != null ? askuEntity.getUnitTitle() : "")
                .startedDate(askuEntity.getStartedDate())
                .leftTpcState(askuEntity.getTpcLeft().getTpcLoadState())
                .rightTpcState(askuEntity.getTpcRight().getTpcLoadState())
                .missileLeftFactoryNumber(leftMissileFactoryNo)
                .missileRightFactoryNumber(rightMissileFactoryNo)
                .leftRocketTechnicalCondition(askuEntity.getLeftRocket() != null && askuEntity.getLeftRocket().isTechnicalCondition())
                .rightRocketTechnicalCondition(askuEntity.getRightRocket() != null && askuEntity.getRightRocket().isTechnicalCondition())
                .splReadiness(askuEntity.getSplReadiness() != null ? askuEntity.getSplReadiness() : Readiness.UNDEFINED)
                .dateUseLeftM(askuEntity.getLeftRocket() != null ? askuEntity.getLeftRocket().getDateUseM() : LocalDateTime.MIN)
                .dateUseRightM(askuEntity.getRightRocket() != null ? askuEntity.getRightRocket().getDateUseM() : LocalDateTime.MIN)
                .build());
    }

    @Transactional
    public RocketDto setLaunchInitialData(@NotNull
                                          @Size(min = 1, max = 8)
                                          String plantMissile,
                                          @NotNull LaunchInitialDataDto initialDataDto) {
        RocketDto rocketDto = setLaunchInitialData(plantMissile, initialDataDto, Optional.empty());

        return rocketDto;
    }

    @Transactional
    public RocketDto setLaunchInitialData(@NotNull
                                          @Size(min = 1, max = 8)
                                          String plantMissile,
                                          @NotNull LaunchInitialDataDto dto,
                                          Optional<OrderInfoDto> orderInfoDto) {

        if (askuEntity == null) {
            getPersistedAskuStateOrInitializeDefault();
        }

        if (!askuEntity.isLeftRocketKnown() && !askuEntity.isRightRocketKnown()) {
            throw new NotFoundException("Споряджених ракет не знайдено ");
        }

        try {
            Rocket foundRocket = null;
            foundRocket = findRocketByPlantNumber(plantMissile)
                    .orElseThrow(() -> new NotFoundException("Не зайдено ракет з номером " + plantMissile));

            assert foundRocket != null;

            OrderInfoDao orderInfoDao = null;
            if (orderInfoDto.isPresent()) {
                orderInfoDao = orderInfoRepository.findFirstByOrderEntityIdAndValidUntil(orderInfoDto.get().entityId(),
                        orderInfoDto.get().validUntil());
                if (orderInfoDao == null) {
                    orderInfoDao = orderInfoRepository.save(orderInfoMapper.toDao(orderInfoDto.get()));
                }
            }

            LaunchInitialDataDao convertedInitialDataDao = initialDataMapper.toDao(dto);
            convertedInitialDataDao.setOrderInfo(orderInfoDao);

            LaunchInitialDataDao persistedInitialDataDao = null;
            if (foundRocket.getInitialData() == null || foundRocket.getInitialData().getId() == null) {

                persistedInitialDataDao = initialDataRepository.save(convertedInitialDataDao);
            } else {
                persistedInitialDataDao = initialDataRepository.findById(foundRocket.getInitialData().getId()).get();
                if (persistedInitialDataDao == null || !persistedInitialDataDao.equals(convertedInitialDataDao)) {
                    persistedInitialDataDao = initialDataRepository.save(convertedInitialDataDao);
                }
            }

            if (persistedInitialDataDao == null || persistedInitialDataDao.getId() == null) {
                throw new NotFoundException("Споряджених ракет не знайдено з номером " + plantMissile
                                            + " не знайдено");
            }

            RocketDao rocketDao = rocketMapper.clone(rocketRepository.findById(foundRocket.getId()).get());
            rocketDao.setInitialData(persistedInitialDataDao);
            rocketDao.setId(null);
            rocketDao.setInitialDataTS(null);
            rocketDao = rocketRepository.save(rocketDao);

            AskuDao askuDao = getAskuWithRockets(askuRepository.findFirstByOrderByUpdatedAtDesc());
            boolean isLeft = false;
            if (askuDao != null) {
                em.detach(askuDao);
                askuDao.setId(null);
                if (askuDao.getLeftRocket() != null && askuDao.getLeftRocket().getFormData() != null &&
                    askuDao.getLeftRocket().getFormData().getPlantMissile().equalsIgnoreCase(plantMissile)) {
                    askuDao.setLeftRocket(rocketDao);
                    isLeft = true;
                    //set tpc state
                    Tpc tpc = setTpcState(TpcState.TPC_WITH_ROCKET, askuDao, true);
                    if (tpc != null) {
                        askuDao.setTpcLeft(tpc);
                    }
                } else if (askuDao.getRightRocket() != null && askuDao.getRightRocket().getFormData() != null &&
                           askuDao.getRightRocket().getFormData().getPlantMissile().equalsIgnoreCase(plantMissile)) {
                    askuDao.setRightRocket(rocketDao);
                    isLeft = false;
                    //set tpc state
                    Tpc tpc = setTpcState(TpcState.TPC_WITH_ROCKET, askuDao, false);
                    if (tpc != null) {
                        askuDao.setTpcRight(tpc);
                    }
                }

                askuDao = askuRepository.save(askuDao);
                this.askuEntity = askuMapper.map(askuDao);
            }
            //PLC will monitor endpoint and if ts is null - then have to download initial data

            if (rocketDao.getFormData().getPlantMissile() != null) {
                Broadcaster.broadcast(MsgEvent.builder()
                        .msgType(MsgType.INFO)
                        .payload("Завантажено вихідні дані для ОТР " + rocketDao.getFormData().getPlantMissile())
                        .adjacentSystemType(AdjacentSystemType.CCV)
                        .build());
            }

            Broadcaster.broadcast(RocketEvent.builder()
                    .rocket(rocketMapper.map(rocketDao, this))
                    .isLeft(isLeft)
                    .timeStamp(rocketDao.getInitialDataTS() != null ?
                            rocketDao.getInitialDataTS().withNano(0) : LocalDateTime.now().withNano(0))
                    .source(this)
                    .build());

            resetFireOrderInformationMessages(plantMissile);
            return rocketMapper.toDto(findRocketByPlantNumber(plantMissile).get());
        } catch (NotFoundException e) {
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage() + Arrays.toString(e.getStackTrace()));
            throw new ServerErrorException("Неможливо опрацювати вхідні дані");
        }
        // если данные кривые - 422 Unprocessable Entity
    }

    @Transactional
    public synchronized Tpc setTpcState(@NotNull TpcState newState, @NotNull AskuDao askuDao, @NotNull boolean isLeft) {
        Tpc tpc = isLeft ? askuDao.getTpcLeft() : askuDao.getTpcRight();

        if (isLeft) {
            if (tpc == null || askuDao.getTpcLeft().getTpcLoadState() != newState) {
                tpc = AskuUtils.getDefaultTpc();
                tpc.setTpcLoadState(newState);
                tpc.setId(null);
                tpc = tpcRepository.save(tpc);
            }
        } else {
            if (tpc == null || askuDao.getTpcRight().getTpcLoadState() != newState) {
                tpc = AskuUtils.getDefaultTpc();
                tpc.setTpcLoadState(newState);
                tpc.setId(null);
                tpc = tpcRepository.save(tpc);
            }
        }

        return tpc;
    }

    @Transactional
    public synchronized Optional<Tpc> setTpcStateWithAsku(@NotNull TpcState newState, @NotNull boolean isLeft) {
        if (askuEntity == null) {
            getPersistedAskuStateOrInitializeDefault();
        }

        synchronized (this) {
            AskuDao askuDao = askuMapper.toDao(askuEntity);

            Tpc tpc = setTpcState(newState, askuDao, isLeft);

            if (tpc == null) {
                return Optional.empty();
            }
            if (isLeft) {
                askuDao.setTpcLeft(tpc);
            } else {
                askuDao.setTpcRight(tpc);
            }

            askuDao.setId(null);
            askuDao = askuRepository.save(askuDao);
            askuEntity = askuMapper.map(askuDao);

            return Optional.of(isLeft ? askuEntity.getTpcLeft() : askuEntity.getTpcRight());
        }
    }

    @Transactional
    public synchronized Optional<Tpc> setLeftTpcStateWithAsku(@NotNull TpcState newState) {
        if (askuEntity == null) {
            getPersistedAskuStateOrInitializeDefault();
        }
        return setTpcStateWithAsku(newState, true);
    }


    @Transactional
    public synchronized Optional<Tpc> setRightTpcStateWithAsku(@NotNull TpcState newState) {
        if (askuEntity == null) {
            getPersistedAskuStateOrInitializeDefault();
        }
        return setTpcStateWithAsku(newState, false);
    }


    public void postRocketFormData(@NotNull @Valid RocketFormData postedData) {
        if (findRocketByPlantNumber(postedData.getPlantMissile()).isEmpty()) {
            throw new NotFoundException("can't find rocket by number for data " + postedData);
        }
        Rocket foundRocket = findRocketByPlantNumber(postedData.getPlantMissile()).get();
        if (foundRocket.getRocketFormData().isPresent()) {
            foundRocket.setRocketFormData(postedData);
        }
    }

    public Optional<Rocket> findRocketByPlantNumber(@NotNull String plantMissile) {
        if (askuEntity == null) {
            return Optional.empty();
        }

        if (askuEntity.isLeftRocketKnown() && askuEntity.getLeftRocket().getPlantMissile().isPresent()
            && askuEntity.getLeftRocket().getPlantMissile().get().equals(plantMissile)) {
            return Optional.of(askuEntity.getLeftRocket());
        }
        if (askuEntity.isRightRocketKnown() && askuEntity.getRightRocket().getPlantMissile().isPresent()
            && askuEntity.getRightRocket().getPlantMissile().get().equals(plantMissile)) {
            return Optional.of(askuEntity.getRightRocket());
        }

        return Optional.empty();
    }

    public Optional<Readiness> getSplReadiness() {
        if (askuEntity == null) {
            return Optional.empty();
        }

        return Optional.ofNullable(askuEntity.getSplReadiness());
    }

    //todo implement
    public boolean requestSplReadinessChange(Readiness value) {

        throw new NotImplementedException();
    }

    public Optional<RocketFormDataDto> getRocketFormDataDto(@NotNull boolean isLeft) {
        if (askuEntity == null) {
            getPersistedAskuStateOrInitializeDefault();
        }
        if ((isLeft && !askuEntity.isLeftRocketKnown()) || (!isLeft && !askuEntity.isRightRocketKnown())) {
            return Optional.empty();
        }

        RocketFormData formData = isLeft ? askuEntity.getLeftRocket().getFormData() : askuEntity.getRightRocket().getFormData();

        return Optional.ofNullable(rocketFormDataMapper.toDto(formData));
    }

    public Optional<RocketDto> getRocketDto(boolean isLeft) {
        Optional<Rocket> rocket = getRocket(isLeft);
        if (rocket.isPresent()) {
            return Optional.of(rocketMapper.toDto(rocket.get()));
        }
        return Optional.empty();
    }

    private Optional<Rocket> getRocket(boolean isLeft) {
        if (askuEntity == null) {
            throw new NotFoundException("Аску не інціалізовано!");
        }
        if ((isLeft && askuEntity.isLeftRocketKnown())) {
            return Optional.ofNullable(askuEntity.getLeftRocket());
        } else if (!isLeft && askuEntity.isRightRocketKnown()) {
            return Optional.ofNullable(askuEntity.getRightRocket());
        }

        return Optional.empty();
    }

    public Optional<Rocket> getRocketCopy(boolean isLeft) {
        Optional<Rocket> foundRocket = getRocket(isLeft);
        return foundRocket.map(rocketMapper::clone);
    }

    private void resetTemperatureWarnings(boolean isLeft, double value) {
        if (getRocket(isLeft).isEmpty()) {
            return;
        }
        Rocket rocket = getRocket(isLeft).get();
        if (rocket.getFormData() == null || rocket.getFormData().getPlantMissile().isEmpty()) {
            return;
        }

        if (!exceededTemperatures.containsKey(rocket.getFormData().getPlantMissile()) ||
            matchMeasureLimit(value).equals(exceededTemperatures.get(rocket.getFormData().getPlantMissile()).measureLimit)) {
            return;
        }
        exceededTemperatures.remove(rocket.getFormData().getPlantMissile());
    }

    @Transactional
    public synchronized Optional<AskuDto> setRocket(@NotNull boolean isLeft, @NotNull @Valid RocketDto rocketDto) {
        if (askuEntity == null) {
            getPersistedAskuStateOrInitializeDefault();
        }

        if ((isLeft && askuEntity.isLeftRocketKnown() &&
             RocketUtils.compareToDto(askuEntity.getLeftRocket(), rocketDto, rocketMapper, this))) {
            return Optional.of(askuMapper.toDto(askuEntity));
        }
        if (!isLeft && askuEntity.isRightRocketKnown() &&
            RocketUtils.compareToDto(askuEntity.getRightRocket(), rocketDto, rocketMapper, this)) {
            return Optional.of(askuMapper.toDto(askuEntity));
        }

        RocketDao rocketDao = null;
        rocketDao = rocketMapper.toDao(rocketDto, this);
        rocketDao.setId(null);
        rocketDao.setFormData(null);
        rocketDao.setInitialData(null);

        RocketFormDataDao rocketFormDataDao = rocketFormDataMapper.toDao(rocketDto.formData());
        rocketFormDataDao.setId(null);
        rocketFormDataDao = rocketFormDataRepository.save(rocketFormDataDao);

        rocketDao.setFormData(rocketFormDataDao);
        rocketDao = rocketRepository.save(rocketDao);

        AskuDao askuDao = askuMapper.toDao(askuEntity);
        askuDao.setId(null);

        if (isLeft) {
            askuDao.setLeftRocket(rocketDao);
            Tpc tpc = setTpcState(TpcState.TPC_WITH_ROCKET, askuDao, true);

            tpc = tpcRepository.save(tpc);
            askuDao.setTpcLeft(tpc);

            Tpc opositeTpc = null;
            //clear empty side
            if (askuEntity.getRightRocket() != null && askuEntity.isRightRocketKnown() &&
                askuEntity.getRightRocket().getFormData().getPlantMissile().equalsIgnoreCase(rocketFormDataDao.getPlantMissile())) {
                askuDao.setRightRocket(null);

                opositeTpc = setTpcState(TpcState.TPC_ONLY, askuDao, false);
                if (opositeTpc != null) {
                    askuDao.setTpcRight(opositeTpc);
                }
            }

        } else {
            askuDao.setRightRocket(rocketDao);
            Tpc tpc = setTpcState(TpcState.TPC_WITH_ROCKET, askuDao, false);
            if (tpc != null) {
                askuDao.setTpcRight(tpc);
            }

            Tpc opositeTpc = null;

            //clear empty side
            if (askuEntity.getLeftRocket() != null && askuEntity.isLeftRocketKnown() &&
                askuEntity.getLeftRocket().getFormData()
                        .getPlantMissile().equalsIgnoreCase(rocketFormDataDao.getPlantMissile())) {
                askuDao.setLeftRocket(null);

                opositeTpc = setTpcState(TpcState.TPC_ONLY, askuDao, true);
                if (opositeTpc != null) {
                    askuDao.setTpcLeft(opositeTpc);
                }
            }
        }

        askuDao.setId(null);
        askuDao = askuRepository.save(askuDao);
        askuEntity = askuMapper.map(askuDao);

        Broadcaster.broadcast(
                RocketEvent.builder()
                        .rocket(rocketMapper.map(rocketDao, this))
                        .isLeft(isLeft)
                        .timeStamp(rocketDao.getInitialDataTS() != null ?
                                rocketDao.getInitialDataTS().withNano(0) : LocalDateTime.now().withNano(0))
                        .source(this)
                        .build());

        if (rocketDao.getFormData() != null) {
            resetFireOrderInformationMessages(rocketDao.getFormData().getPlantMissile());
        }

        return Optional.ofNullable(askuMapper.toDto(askuEntity));
    }

    @Transactional
    public synchronized AskuDto removeRocket(@NotNull boolean isLeft, @NotBlank String plantMissile) {
        getRocketByPlantNumberAndSplSide(isLeft, plantMissile).orElseThrow(NotFoundException::new);

        AskuDao askuDao = askuMapper.toDao(askuEntity);
        Rocket removedRocket;
        if (isLeft) {
            removedRocket = askuEntity.getLeftRocket();
            askuDao.setLeftRocket(null);

            Tpc tpc = setTpcState(TpcState.TPC_ONLY, askuDao, true);
            if (tpc != null) {
                askuDao.setTpcLeft(tpc);
            }
        } else {
            removedRocket = askuEntity.getRightRocket();
            askuDao.setRightRocket(null);

            Tpc tpc = setTpcState(TpcState.TPC_ONLY, askuDao, true);
            if (tpc != null) {
                askuDao.setTpcRight(tpc);
            }
        }

        askuDao.setId(null);
        askuDao = askuRepository.save(askuDao);
        askuEntity = askuMapper.map(askuDao);

        //show rocket removed message
        String payload = "Ракету #" + (isLeft ? 1 : 2) + " з заводським номером "
                         + plantMissile + " було видалено за командою ПЛК";
        log.info(payload);

        MsgEvent event = MsgEvent.builder()
                .adjacentSystemType(AdjacentSystemType.ASKU)
                .msgType(MsgType.INFO)
                .payload(payload)
                .source(this)
                .build();

        Broadcaster.broadcast(event);

        RocketEvent rocketDeleteEvent = RocketEvent.builder()
                .rocket(removedRocket != null ? removedRocket : AskuUtils.getDefaultRocket())
                .timeStamp(LocalDateTime.now().withNano(0))
                .isLeft(isLeft)
                .eventType(AdjacentSystemUpdateEventType.REMOVE_ENTITY)
                .source(this)
                .build();
        Broadcaster.broadcast(rocketDeleteEvent);

        return askuMapper.toDto(askuEntity);
    }

    @Transactional
    public synchronized AskuDto launchRocket(@NotNull boolean isLeft,
                                             @NotBlank String plantMissile,
                                             @NotNull LaunchResult launchResult,
                                             LaunchResultDescription launchResultDescription) {

        Rocket rocket = getRocketByPlantNumberAndSplSide(isLeft, plantMissile)
                .orElse(AskuUtils.getDefaultRocket());

        if (rocket.equals(AskuUtils.getDefaultRocket())) {
            log.error("cant find proper rocket. Initializing default rocket.");
        }

        rocket.setLaunchResult(launchResult);
        if (launchResult.equals(LaunchResult.LAUNCH)) {
            rocket.setDateUseM(LocalDateTime.now());
        }
        rocket.setId(null);
        rocket.setLaunchResultDescription(launchResultDescription);
        RocketDao rocketDao = rocketRepository.save(rocketMapper.toDao(rocket));

        AskuDao askuDao = askuMapper.toDao(askuEntity);


        if (isLeft) {
            askuDao.setLeftRocket(rocketDao);
        } else {
            askuDao.setRightRocket(rocketDao);
        }

        if (launchResult.equals(LaunchResult.LAUNCH)) {
            TpcState tpcState = TpcState.TPC_ONLY;
            if (isLeft) {
                Tpc tpc = setTpcState(tpcState, askuDao, true);
                if (tpc != null) {
                    askuDao.setTpcLeft(tpc);
                }
            } else {
                Tpc tpc = setTpcState(tpcState, askuDao, false);
                if (tpc != null) {
                    askuDao.setTpcRight(tpc);
                }
            }

        }
        askuDao.setId(null);
        askuDao = askuRepository.save(askuDao);

        askuEntity = askuMapper.map(askuDao);

        //remove from suto rocket with tpc data source

        //refresh tpc with rocket state in sutoView
        //show rocket removed message
        String payload = "Ракету №" + (isLeft ? 1 : 2) + " з заводським номером "
                         + plantMissile + " було використано з результатом - " + launchResult.getValueUa();
        MsgType msgType;
        switch (launchResult) {
            case LAUNCH, NONE -> {
                msgType = MsgType.WARNING;
                log.info(payload);
            }
            case FAILURE -> {
                msgType = MsgType.WARNING;
                payload = "Відубулося автоматичне скасування пуску ракети №" + (isLeft ? 1 : 2)
                          + " з заводським номером " + plantMissile + " за командою ПЛК";

                RocketLaunchEvent launchEvent = RocketLaunchEvent.builder()
                        .launchResult(launchResult)
                        .plantMissile(plantMissile)
                        .isLeft(isLeft)
                        .build();
                Broadcaster.broadcast(launchEvent);

            }
            default -> {
                msgType = MsgType.ERROR;
                log.error(payload);
            }
        }

        MsgEvent event = MsgEvent.builder()
                .adjacentSystemType(AdjacentSystemType.ASKU)
                .msgType(msgType)
                .payload(payload)
                .source(this)
                .build();

        Broadcaster.broadcast(event);

        return askuMapper.toDto(askuEntity);
    }

    // TODO: 1/9/2025 move to nppa service
//    private boolean sendLunchAutoCancelAcknowledged(boolean isLeft, String plantMissile) {
//        AtomicBoolean handled = new AtomicBoolean(false);
//        nppaService.getAvailableNcokCombatModeCommands().stream()
//                .filter(command -> command.getCommand().equals(isLeft ? OTR_1_AUTOMATIC_LAUNCH_CANCEL_ACKNOWLEDGMENT : OTR_2_AUTOMATIC_LAUNCH_CANCEL_ACKNOWLEDGMENT))
//                .findFirst().ifPresentOrElse((acknowledgeCommand) ->
//                        {
//                            NppaCommand command = NppaCommand.builder()
//                                    .command(acknowledgeCommand.getCommand())
//                                    .caption(acknowledgeCommand.getCaption())
//                                    .commandState(acknowledgeCommand.getCommandState())
//                                    .adjacentSystem(acknowledgeCommand.getAdjacentSystem())
//                                    .generationTime(LocalDateTime.now())
//                                    .originator(userService.getUserName())
//                                    .build();
//
//                            CommandValidationResult validationResult = nppaService.validateCommand(command);
//                            if (!validationResult.isValid()) {
//                                log.error("can't acknowledge automatic cancell of launch of OTR " + plantMissile + " because of " +
//                                          validationResult.errorMessage() + " " + validationResult.errorMessageUa());
//                            }
//
//                            nppaService.setNppaCommand(command);
//                            handled.set(true);
//                            // TODO: 1/8/2025 add task to check if command was committed and if no - then retry several times
//                            //  there is relevant method to check if command was committed
//
//                        },
//                        () -> log.error("can't find command to handle" + (isLeft ?
//                                OTR_1_AUTOMATIC_LAUNCH_CANCEL_ACKNOWLEDGMENT :
//                                OTR_2_AUTOMATIC_LAUNCH_CANCEL_ACKNOWLEDGMENT)));
//        // TODO: 1/8/2025 send command and reload lunch badge
//
//        return handled.get();
//    }

    private Optional<Rocket> getRocketByPlantNumberAndSplSide(boolean isLeft, String plantMissile) {
        if (askuEntity == null) {
            log.error("asku doesn't exists " + this);
            throw new NotFoundException("rocket " + plantMissile + " doesn't exists");
        }

        Rocket foundRocket = findRocketByPlantNumber(plantMissile).orElseThrow(NotFoundException::new);
        if ((isLeft && !foundRocket.equals(askuEntity.getLeftRocket())) ||
            (!isLeft && !foundRocket.equals(askuEntity.getRightRocket()))) {
            String errorMsg = "found rocket is not located in expected ("
                              + (isLeft ? "left" : "right") + ") SPL side. ASKU" + askuEntity;
            log.error(errorMsg);
            throw new NotFoundException("rocket " + plantMissile + " doesn't exists");
        }
        return Optional.of(foundRocket);
    }

    @Transactional
    public synchronized boolean confirmInitialDataLoad(boolean isLeft, long initialDataId, LaunchInitialDataDto dto) {
        Rocket rocket = getRocket(isLeft).orElseThrow(NotFoundException::new);

        if (rocket.getInitialData() == null) {
            String msg = "Вихідних даних не знайдено для " + (isLeft ? "лівої" : "правої") + " ракети " + rocket;
            log.error(msg);
            throw new NotFoundException("msg");
        }

        if (rocket.getInitialData().getId() != initialDataId) {
            String msg = "Вихідних даних з номером" + initialDataId + "не знайдено для " + (isLeft ? "лівої" : "правої")
                         + " ракети " + rocket;
            log.error(msg);
            throw new NotFoundException("msg");
        }

        if (!RocketUtils.compareInitialDataToDto(rocket.getInitialData(), initialDataMapper.map(dto))) {
            String msg = "Вихідні дані з номером " + initialDataId + " для " + (isLeft ? "лівої" : "правої") +
                         " ракети не співпадають. " + "Надіслано : " + initialDataMapper.map(dto).toString() +
                         " Знайдено: " + rocket;
            log.error(msg);
            throw new NotFoundException("msg");
        }

        rocket.setInitialDataTS(LocalDateTime.now());
        rocket.setLoadedToPlc(true);

        RocketDao rocketDao = rocketMapper.toDao(rocket);
        rocketDao.setId(null);
        rocketDao = rocketRepository.save(rocketDao);

        if (isLeft) {
            askuEntity.setLeftRocket(rocketMapper.map(rocketDao, this));
        } else {
            askuEntity.setRightRocket(rocketMapper.map(rocketDao, this));
        }

        AskuDao askuDao = askuMapper.toDao(askuEntity);
        askuDao.setId(null);
        askuDao = askuRepository.save(askuDao);

        askuEntity = askuMapper.map(askuDao);


        Broadcaster.broadcast(RocketEvent.builder()
                .rocket(rocketMapper.map(rocketDao, this))
                .isLeft(isLeft)
                .timeStamp(rocketDao.getInitialDataTS() != null ?
                        rocketDao.getInitialDataTS().withNano(0) : LocalDateTime.now().withNano(0))
                .source(this)
                .build());

        return true;
    }

    public List<String> getTlcOpenKeys(@NotBlank String plantMissile) {
        if (askuEntity == null) {
            return new ArrayList<>();
        }

        List<String> tlKeys = new ArrayList<>();
        if (findRocketByPlantNumber(plantMissile).isPresent() && findRocketByPlantNumber(plantMissile).get().getStoredTlKeys() != null) {
            tlKeys = findRocketByPlantNumber(plantMissile).get().getStoredTlKeys();
        }

        return tlKeys;
    }

    @Transactional
    public synchronized List<String> setTlcOpenKeys(String plantMissile, List<String> openTlKeys) {
        if (askuEntity == null) {
            log.error("asku wasn't initialized. Reinitializing! " + this);
            getPersistedAskuStateOrInitializeDefault();
        }

        List<String> tlKeys = new ArrayList<>();
        Asku updatedAsku = null;
        Optional<Rocket> foundRocket = findRocketByPlantNumber(plantMissile);
        if (foundRocket.isPresent() && foundRocket.get().getStoredTlKeys() != null) {
            foundRocket.get().setStoredTlKeys(openTlKeys);
            RocketDao rocketDao = rocketMapper.toDao(foundRocket.get());
            rocketDao.setId(null);
            rocketDao = rocketRepository.save(rocketDao);

            if (askuEntity.isLeftRocketKnown() && askuEntity.getLeftRocket().getPlantMissile().isPresent()
                && askuEntity.getLeftRocket().getPlantMissile().get().equalsIgnoreCase(plantMissile)) {
                askuEntity.setLeftRocket(rocketMapper.map(rocketDao, this));
            } else if (askuEntity.isRightRocketKnown() && askuEntity.getRightRocket().getPlantMissile().isPresent()
                       && askuEntity.getRightRocket().getPlantMissile().get().equalsIgnoreCase(plantMissile)) {
                askuEntity.setRightRocket(rocketMapper.map(rocketDao, this));
            }
            persistAskuState();
        }

        return getTlcOpenKeys(plantMissile);
    }

    public void setSplReadiness(@NotNull Readiness readiness) {
        if (askuEntity == null) {
            log.error("asku wasn't initialized");
            throw new NotFoundException("Аску не ініціцаізовано");
        }

        if (askuEntity.getSplReadiness() == readiness) {
            return;
        }

        this.askuEntity.setSplReadiness(readiness);
        this.askuEntity.setStartedDate(LocalDateTime.now());

        fireUpdateEvent();
    }

    public void setSplPlateNumber(@NotNull String plateNumber) {
        if (askuEntity == null) {
            log.error("asku wasn't initialized");
            throw new NotFoundException("Аску не ініціцаізовано");
        }

        if (plateNumber == null || plateNumber.isBlank() || askuEntity.getPlateNumber() == null) {
            return;
        }

        if (plateNumber.equalsIgnoreCase(askuEntity.getPlateNumber())) {
            return;
        }

        askuEntity.setPlateNumber(plateNumber);

        fireUpdateEvent();
    }

    public synchronized Asku persistAskuState() {
        long prevId = askuEntity.getId();
        AskuDao persisted = getAskuWithRockets(askuRepository.findFirstByOrderByUpdatedAtDesc());
        em.detach(persisted);
        if (askuEntity == null) {
            return null;
        }

        AskuDao dao = null;
        if (persisted != null && !askuEntity.equals(askuMapper.map(persisted))) {
            dao = askuMapper.toDao(getAskuEntity());
            dao.setId(null);
            dao = askuRepository.save(dao);
            askuEntity = askuMapper.map(dao);

            if (dao.getId() == prevId) {
                log.error("cant persist ASKU state");
            }

            if (!persisted.getSplReadiness().equals(askuEntity.getSplReadiness())) {
                String payload = AskuUtils.getSplReadinessPayloadPartByReadiness(askuEntity.getSplReadiness())
                                 + getAskuInfo().get().getSplReadiness().get().getValueUa();

                MsgEvent splReadinessChangedMsg =
                        MsgEvent.builder()
                                .msgType(MsgType.INFO)
                                .payload(payload)
                                .build();
                Broadcaster.broadcast(splReadinessChangedMsg);
            }
        }

        return askuEntity;
    }

    private final CommandsUtils commandUtils;

    public boolean setPlcMessage(@NotNull MsgType msgType,
                                 AdjacentSystemType systemType,
                                 @NotBlank String payload,
                                 String alias) {
        switch (msgType) {
            case INFO, ERROR, WARNING -> {
                if (!alias.isBlank() && payload.contains(alias)) {
                    Optional<String> caption = alias.isBlank() ? Optional.empty() :
                            commandUtils.getCaptionByAliasAndSystem(systemType, alias);

                    if (caption.isPresent()) {
                        payload = payload.replace(alias, caption.get());
                    }
                }
            }
            case COMMIT -> {
                if (systemType == null || alias.isBlank()) {
                    throw new BadRequestException("Всі поля RequestBody повинні бути заповнені. " + msgType);
                }
                Optional<String> caption = alias.isBlank() ? Optional.empty() :
                        commandUtils.getCaptionByAliasAndSystem(systemType, alias);

                if (caption.isPresent()) {
                    payload = payload.replace(alias, caption.get());
                }
            }
            default -> {
                throw new BadRequestException("Невідомий тип повідомлення");
            }
        }

        MsgEvent event = MsgEvent.builder()
                .adjacentSystemType(systemType)
                .msgType(msgType)
                .payload(payload)
                .source(this)
                .build();

        Broadcaster.broadcast(event);
        return true;
    }


    @Transactional
    public Rocket saveRocketWithInitialData(@NotNull LaunchInitialDataSource initialDataSource,
                                            @NotNull String initialDataSourceDescription,
                                            @NotBlank String plantMissile,
                                            @NotNull LaunchInitialData initialData) {

        Rocket foundRocket = rocketMapper.clone(findRocketByPlantNumber(plantMissile).orElseThrow(NotFoundException::new));

        boolean isLeft = true;
        if (getRocket(true).isPresent() && getRocket(true).get().getPlantMissile().isPresent()
            && getRocket(true).get().getPlantMissile().get().equals(plantMissile)) {
            isLeft = true;
        } else if (getRocket(false).isPresent() && getRocket(false).get().getPlantMissile().isPresent()
                   && getRocket(false).get().getPlantMissile().get().equals(plantMissile)) {
            isLeft = false;
        } else {
            throw new NotFoundException("Ракет за №" + plantMissile + " не знайдено");
        }

        if (!tlService.validateKeySignature(initialData, foundRocket)) {
            throw new InitialDataValidationException("initial data cant be validated by tls. Initial data " +
                                                     initialData.toString() + "rocket " + foundRocket);
        }

        LaunchInitialDataDao initialDataDao = initialDataMapper.toDao(initialData);
        initialDataDao.setId(null);
        initialDataDao = initialDataRepository.save(initialDataDao);
        initialDataDao.setValidatedByTlc(true);

        foundRocket.setInitialDataSourceDescription(initialDataSourceDescription);
        foundRocket.setInitialDataSource(initialDataSource);
        foundRocket.setInitialData(initialDataMapper.map(initialDataDao));
        foundRocket.setInitialDataTS(null);

        RocketDao rocketDao = rocketMapper.toDao(foundRocket);
        rocketDao.setId(null);
        rocketDao = rocketRepository.save(rocketDao);

        AskuDao askuDao = askuMapper.toDao(askuEntity);
        if (isLeft) {
            askuDao.setLeftRocket(rocketDao);
        } else {
            askuDao.setRightRocket(rocketDao);
        }

        askuDao.setId(null);
        askuDao = askuRepository.save(askuDao);
        askuEntity = askuMapper.map(askuDao);

        Broadcaster.broadcast(AskuInfoEvent.builder()
                .askuInfo(getAskuInfo().get())
                .source(this)
                .eventType(AdjacentSystemUpdateEventType.DATA_UPDATE)
                .build());

        String payload = "Ракету #" + (isLeft ? 1 : 2) + " з заводським номером "
                         + plantMissile + " було відправлено віхідні дані на пуск " /*+ initialData.toString()*/;
        log.info(payload);

        MsgEvent event = MsgEvent.builder()
                .adjacentSystemType(AdjacentSystemType.ASKU)
                .msgType(MsgType.INFO)
                .payload(payload)
                .source(this)
                .build();

        Broadcaster.broadcast(event);

        return rocketMapper.map(rocketDao, this);
    }

    @Transactional
    public Rocket updateTemperatureFromSensor(boolean isLeft, double sensorTemperature) {
        Rocket foundRocket;
        if (isLeft && askuEntity.isLeftRocketKnown()) {
            foundRocket = askuEntity.getLeftRocket();
        } else if (!isLeft && askuEntity.isRightRocketKnown()) {
            foundRocket = askuEntity.getRightRocket();
        } else {
            String msg = (isLeft ? "left" : "right") + " rocket doesn't exists in the asku";
            log.error(msg);
            throw new NotFoundException(msg);
        }

        RocketDao rocketDao = rocketMapper.toDao(foundRocket);
        rocketDao.setSensorTemperature(sensorTemperature);
        rocketDao.setId(null);
        rocketDao = rocketRepository.save(rocketDao);

        AskuDao askuDao = askuMapper.toDao(askuEntity);
        if (isLeft) {
            askuDao.setLeftRocket(rocketDao);
        } else {
            askuDao.setRightRocket(rocketDao);
        }
        askuDao.setId(null);
        askuDao = askuRepository.save(askuDao);
        askuEntity = askuMapper.map(askuDao);

        Broadcaster.broadcast(RocketEvent.builder()
                .isLeft(isLeft)
                .eventType(AdjacentSystemUpdateEventType.UPDATE_VIEW_SILENTLY)
                .source(this)
                .rocket(rocketMapper.map(rocketDao, this))
                .timeStamp(LocalDateTime.now())
                .build());

        return rocketMapper.map(rocketDao, this);
    }

    private final ConcurrentHashMap<String, List<String>> tlKeysForPlc;
    private long tlKeysForPlcVersion = 1L;

    public List<String> getTlKeysForPlc(String rocketId) {
        List<String> foundKeys = tlKeysForPlc.get(rocketId);
        return foundKeys != null ? new ArrayList<>(foundKeys) : Collections.emptyList();
    }

    public Map<String, List<String>> getAllTlKeysForPlc() {
        Map<String, List<String>> foundKeys = tlKeysForPlc;
        return foundKeys != null ? new HashMap<>(foundKeys) : new HashMap<>();
    }

    public void setTlKeysForPlc(String plantMissile, long version, List<String> keys) {
        if (version < tlKeysForPlcVersion) {
            String msg = "Версія інсуючих ключів (" + tlKeysForPlcVersion + ") " +
                         "вища за ту що потрібно завантажити (" + version + "). " +
                         "Існуючі ключі - " + Arrays.toString(
                    tlKeysForPlc.entrySet().stream()
                            .map(entry -> entry.getValue().stream()
                                    .map(val -> "key: " + entry.getKey() + " - " + "value: " + val))
                            .toArray()) +
                         "Ключі котрі потрібно завантажити " + Arrays.toString(keys.stream()
                    .map(val -> "key: " + plantMissile + " - " + "value: " + val)
                    .toArray());
            log.error(msg);
            throw new BadRequestException(msg);
        }

        if (keys == null || keys.size() == 0) {
            if (tlKeysForPlc.get(plantMissile) != null) {
                tlKeysForPlc.get(plantMissile).clear();
            }
        } else {

            tlKeysForPlc.put(plantMissile, new ArrayList<>(keys));
        }

        tlKeysForPlcVersion = (version == tlKeysForPlcVersion ? ++version : version);

        Broadcaster.broadcast(new PlcTLkeysEvent(new HashMap<>(tlKeysForPlc), version, this));
    }

    public void addTlKeysForPlc(String plantMissile, long version, String... key) {
        if (version < tlKeysForPlcVersion) {
            String msg = "Версія інсуючих ключів (" + tlKeysForPlcVersion + ") " +
                         "вища за ту що потрібно завантажити (" + version + "). " +
                         "Існуючі ключі - " + Arrays.toString(
                    tlKeysForPlc.entrySet().stream()
                            .map(entry -> entry.getValue().stream()
                                    .map(val -> "key: " + entry.getKey() + " - " + "value: " + val))
                            .toArray()) +
                         "Ключі котрі потрібно завантажити " + Arrays.toString(key);
            log.error(msg);
            throw new BadRequestException(msg);
        }

        if (!tlKeysForPlc.containsKey(plantMissile)) {
            tlKeysForPlc.put(plantMissile, new ArrayList<>());
        }

        List<String> foundKeys = tlKeysForPlc.get(plantMissile);

        boolean added = false;
        for (String newKey : key) {
            if (!foundKeys.contains(newKey)) {
                foundKeys.add(newKey);
                added = true;
            }
        }

        if (added) {
            if (tlKeysForPlcVersion < version) {
                tlKeysForPlcVersion = version;
            } else {
                tlKeysForPlcVersion++;
            }
        }

        sendUpdateKeysEvent();
    }

    public long getTlKeysForPlcVersion() {
        return tlKeysForPlcVersion;
    }

    private final long previousKeyVersion;

    private void sendUpdateKeysEvent() {
        if (previousKeyVersion >= getTlKeysForPlcVersion()) {
            log.error("can't update keys - previousKeyVersion: " + previousKeyVersion +
                      ", tlKeysForPlcVersion:" + tlKeysForPlcVersion + " updated keys: " +
                      Arrays.toString(
                              tlKeysForPlc.entrySet().stream()
                                      .map(entry -> entry.getValue().stream()
                                              .map(val -> "key: " + entry.getKey() + " - " + "value: " + val))
                                      .toArray()));
            return;
        }

        Broadcaster.broadcast(new PlcTLkeysEvent(new HashMap<>(tlKeysForPlc), tlKeysForPlcVersion, this));
    }

    public Stream<String> getTlcOpenKeysStream(String plantMissile, int page, int pageSize) {
        return tlKeysForPlc.entrySet().stream()
                .filter(entry -> entry.getKey().equalsIgnoreCase(plantMissile))
                .flatMap(entry -> entry.getValue().stream());
    }

    public List<String> getTlcOpenKeysStream(String plantMissile) {
        return tlKeysForPlc.entrySet().stream()
                .filter(entry -> entry.getKey().equalsIgnoreCase(plantMissile))
                .flatMap(entry -> entry.getValue().stream()).toList();
    }

    private final WebClient client;
    private StringBuffer loggedEncoderJsonBuffer = new StringBuffer();
    private StringBuffer loggedDecoderJsonBuffer = new StringBuffer();
    private LoggingJsonEncoder loggingEncoder;
    private LoggingJsonDecoder loggingDecoder;


    private WebClient configureWebClient() {
        loggedEncoderJsonBuffer = new StringBuffer();
        loggedDecoderJsonBuffer = new StringBuffer();
        loggingEncoder = new LoggingJsonEncoder(
                data -> {
                    loggedEncoderJsonBuffer.append(new String(data));
                });
        loggingDecoder = new LoggingJsonDecoder(
                data -> {
                    loggedDecoderJsonBuffer.append(new String(data));
                });

        final WebClient client = WebClient.builder()
                .codecs(clientCodecConfigurer -> {
                    clientCodecConfigurer.defaultCodecs().jackson2JsonEncoder(loggingEncoder);
                    clientCodecConfigurer.defaultCodecs().jackson2JsonDecoder(loggingDecoder);
                })
                .filters(exchangeFilterFunctions -> {
                    exchangeFilterFunctions.add(logRequest());
                    exchangeFilterFunctions.add(logResponse());
                })
                .baseUrl(postRocketStatusUrl)
                .build();

        loggedDecoderJsonBuffer.setLength(0);
        return client;
    }

    private CompletableFuture<Boolean> currentTask;

    private final ExecutorService executorService = Executors.newSingleThreadExecutor();

    public void updateRocketInfoInRemote(String plantMissile,
                                         LaunchResult launchResult,
                                         LaunchResultDescription launchResultDescription) {

        loggedDecoderJsonBuffer.setLength(0);
        loggedEncoderJsonBuffer.append("plant_missile: ").append(plantMissile);
        loggedEncoderJsonBuffer.append("launch_result: ").append(launchResult);

        if (currentTask != null && !currentTask.isDone()) {
            currentTask.cancel(true);
        }

        launchResultDescription = launchResultDescription == null ?
                new LaunchResultDescription("", "")
                : launchResultDescription;

        currentTask = sendPostRequestWithRetryAsync(plantMissile, launchResult.name(), launchResultDescription);

        resetFireOrderInformationMessages(plantMissile);
    }

    @Async
    public CompletableFuture<Boolean> sendPostRequestWithRetryAsync(String plantMissile,
                                                                    String launchResult,
                                                                    LaunchResultDescription launchResultDescription) {
        int maxAttempts = 3;

        // Use CompletableFuture to represent the asynchronous result
        return CompletableFuture.supplyAsync(() -> {
            boolean updated = false;
            while (!updated || !Thread.currentThread().isInterrupted()) {
                for (int attempt = 1; attempt <= maxAttempts; attempt++) {
                    if (Thread.currentThread().isInterrupted()) {
                        updated = false;
                        return false;
                    }
                    try {
                        ResponseEntity responseEntity = client.post()
                                .uri(uriBuilder -> uriBuilder
                                        .path("{plant-missile}/{status}")
                                        .build(plantMissile, launchResult))
                                .header("Token", ccvToken)
                                .body(BodyInserters.fromValue(launchResultDescription))
                                .retrieve()
                                .toEntity(String.class)
                                .block(Duration.ofMillis(2000));

                        if (responseEntity != null && responseEntity.getStatusCode() == HttpStatus.OK) {
                            updated = true;
                            return true;
                        } else if (responseEntity != null) {
                            String payload = "Неможливо оновити надіслати оновлений стан рокети до ПЗ СПУ. Код помилки" +
                                             responseEntity.getStatusCode() +
                                             (responseEntity.getBody() != null ?
                                                     " Повідомлення : " + responseEntity.getBody()
                                                     : "");

                            setPlcMessage(MsgType.WARNING, AdjacentSystemType.ASKU, "", "");
                            log.error("can't post rocketStatus to endpoint " + postRocketStatusUrl +
                                      " with Code :" + responseEntity.getStatusCode() +
                                      " message : " + responseEntity.getBody());
                        } else {
                            log.debug("Attempt " + attempt + " failed. Retrying...");
                        }
                    } catch (Exception e) {
                        log.error(Arrays.toString(e.getStackTrace()));
                        log.error("Attempt " + attempt + " failed with exception. Retrying...");
                    }

                    try {
                        Thread.sleep(3000);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.error(Arrays.toString(e.getStackTrace()));
                    }
                }

                try {
                    Thread.sleep(60000);
                } catch (InterruptedException e) {
                    log.error(Arrays.toString(e.getStackTrace()));
                }
            }
            return false;
        }, executorService);
    }

    private ExchangeFilterFunction logRequest() {
        return ExchangeFilterFunction.ofRequestProcessor(clientRequest -> {

            RestRequestDAO requestDAO = RestRequestDAO.builder()
                    .url(UriComponentsBuilder.fromUri(clientRequest.url()).replaceQuery("").toUriString())
                    .method(clientRequest.method().toString())
                    .requestQueryString(clientRequest.url().getQuery())
                    .adjacentSystem(AdjacentSystemType.CCV)
                    .direction(LogRecordDirection.OUT)
                    .build();

            if (clientRequest.httpRequest() != null) {
                requestDAO.setRequestQueryString(clientRequest.httpRequest().toString());
            }
            if (clientRequest.body() != null) {
                String payload = requestDAO.getCachedPayload() == null ? "" : requestDAO.getCachedPayload();

                if (loggedEncoderJsonBuffer.length() > 0) {
                    payload += loggedEncoderJsonBuffer;
                    loggedEncoderJsonBuffer.setLength(0);
                }

                requestDAO.setCachedPayload(payload);
            }

            clientRequest
                    .headers()
                    .forEach((name, values) -> values.forEach(value ->
                            requestDAO.addHeaders(String.format("{%s} = {%s}", name, value))));

            RestRequestDAO saved = null;
            if (requestDAO.isConsiderable() || validateRequest(requestDAO.getMethod(), requestDAO.getUrl())) {
                saved = restRepository.save(requestDAO);
            }

            log.info(saved != null ? saved.toString() : requestDAO.toString());

            return Mono.just(clientRequest);
        });
    }

    private ExchangeFilterFunction logResponse() {
        return ExchangeFilterFunction.ofResponseProcessor(clientResponse -> {

            RestRequestDAO requestDAO = RestRequestDAO.builder()
                    .adjacentSystem(AdjacentSystemType.CCV)
                    .direction(LogRecordDirection.RESPONSE)
                    .build();

            if (loggedDecoderJsonBuffer.length() > 0) {
                String payload = requestDAO.getCachedPayload() == null ? "" : requestDAO.getCachedPayload();

                payload += loggedDecoderJsonBuffer;
                loggedDecoderJsonBuffer.setLength(0);

                requestDAO.setCachedPayload(payload);
            }

            clientResponse
                    .headers().asHttpHeaders()
                    .forEach((name, values) -> values.forEach(value -> requestDAO.addHeaders(String.format("{%s} = {%s}", name, value))));

            RestRequestDAO saved = null;
            if (requestDAO.isConsiderable()) {
                saved = restRepository.save(requestDAO);
            }

            log.info(saved != null ? saved.toString() : requestDAO.toString());

            return Mono.just(clientResponse);
        });
    }

    boolean validateRequest(String method, String uri) {
        if (!method.equalsIgnoreCase("POST")) {
            return false;
        }
        return uri.equalsIgnoreCase(postRocketStatusUrl);
    }

    public Optional<OrderInfoDto> getOrderInfoDto(boolean isLeft) {
        Optional<Rocket> rocket = getRocket(isLeft);
        if (rocket.isEmpty() || rocket.get().getInitialData() == null) {
            return Optional.empty();
        }
        return Optional.ofNullable(orderInfoMapper.toDto(rocket.get().getInitialData().getOrderInfo()));
    }


    private final Map<String, UUID> expiredOrders = new HashMap<>();
    private final Map<String, UUID> expiredStartTimes = new HashMap<>();

    public boolean fireOrderOrLunchTimeExpired(boolean isLeft) {
        if (getRocket(isLeft).isEmpty()) {
            return false;
        }

        Rocket rocket = getRocket(isLeft).get();
        if (rocket.getInitialData() == null) {
            return true;
        }

        boolean fireOrderExpired = rocket.getInitialData().getOrderInfo() != null &&
                                   rocket.getInitialData().getOrderInfo().getValidUntil() != null &&
                                   rocket.getInitialData().getOrderInfo().getValidUntil().isBefore(LocalDateTime.now());

        boolean scheduledLunchTimeExpired = !rocket.getInitialData().isScheduled() &&
                                            rocket.getInitialData().getStartTime() != null &&
                                            rocket.getInitialData().getStartTime().isBefore(LocalDateTime.now());

        return fireOrderExpired || scheduledLunchTimeExpired;
    }

    @Async
    public void informStartTimeTimeIsExpired(boolean isLeft) {
        if (getRocket(isLeft).isEmpty()) {
            return;
        }

        Rocket rocket = getRocket(isLeft).get();

        if (rocket.getInitialData() == null || rocket.getInitialData().getOrderInfo() == null ||
            rocket.getInitialData().getStartTime() == null) {
            return;
        }

        if (expiredStartTimes.containsKey(rocket.getFormData().getPlantMissile()) &&
            expiredStartTimes.get(rocket.getFormData().getPlantMissile())
                    .equals(rocket.getInitialData().getOrderInfo().getOrderEntityId())) {
            return;
        }

        if (rocket.getInitialData().getStartTime().isBefore(LocalDateTime.now())) {

            Broadcaster.broadcast(RocketEvent.builder()
                    .isLeft(isLeft)
                    .eventType(AdjacentSystemUpdateEventType.UPDATE_VIEW_SILENTLY)
                    .source(this)
                    .rocket(getRocket(isLeft).get())
                    .timeStamp(LocalDateTime.now())
                    .build());


            expiredStartTimes.put(rocket.getFormData().getPlantMissile(),
                    rocket.getInitialData().getOrderInfo() != null ?
                            rocket.getInitialData().getOrderInfo().getOrderEntityId() :
                            null);
        }
    }

    private boolean isRocketApplicableForUse(Rocket rocket) {
        return rocket.isTechnicalCondition() &&
               rocket.getLaunchResult() != null &&
               !rocket.getLaunchResult().equals(LaunchResult.LAUNCH) &&
               !rocket.getLaunchResult().equals(LaunchResult.FAILURE) &&
               !rocket.getLaunchResult().equals(LaunchResult.REMOVE);
    }

    @Async
    public void informOrderValidityTimeIsExpired(boolean isLeft) {
        if (getRocket(isLeft).isEmpty()) {
            return;
        }

        Rocket rocket = getRocket(isLeft).get();
        if (rocket.getInitialData() == null ||
            rocket.getInitialData().getOrderInfo() == null ||
            !isRocketApplicableForUse(rocket)) {
            return;
        }

        if (expiredOrders.containsKey(rocket.getFormData().getPlantMissile()) &&
            expiredOrders.get(rocket.getFormData().getPlantMissile())
                    .equals(rocket.getInitialData().getOrderInfo().getOrderEntityId())) {
            return;
        }

        if (rocket.getInitialData().getOrderInfo().getValidUntil() == null ||
            rocket.getInitialData().getOrderInfo().getValidUntil().isBefore(LocalDateTime.now())) {
            String payload = String.format("Увага! Час двії наказу на ураження для ОТР  № %s " +
                                           "згідно до наказу %s вичерпано!",
                    rocket.getFormData().getPlantMissile(),
                    (rocket.getInitialData().getOrderInfo().getOrderEntityId() == null ?
                            "" : rocket.getInitialData().getOrderInfo().getOrderEntityId()));

            Broadcaster.broadcast(
                    MsgEvent.builder()
                            .msgType(MsgType.WARNING)
                            .payload(payload)
                            .adjacentSystemType(AdjacentSystemType.ASKU)
                            .source(this)
                            .build());

            Broadcaster.broadcast(RocketEvent.builder()
                    .isLeft(isLeft)
                    .eventType(AdjacentSystemUpdateEventType.UPDATE_VIEW_SILENTLY)
                    .source(this)
                    .rocket(getRocket(isLeft).get())
                    .timeStamp(LocalDateTime.now())
                    .build());


            expiredOrders.put(rocket.getFormData().getPlantMissile(),
                    rocket.getInitialData().getOrderInfo().getOrderEntityId());
        }
    }

    public Optional<LocalDateTime> getRocketFireScheduledTime(boolean isLeft) {
        AtomicReference<LocalDateTime> fireScheduledTime = new AtomicReference<>();
        getRocket(isLeft).ifPresent((v) -> {
            if (v.getInitialData() != null && v.getInitialData().isScheduled()
                && v.getInitialData().getStartTime() != null) {
                fireScheduledTime.set(v.getInitialData().getStartTime());
            }
        });

        return Optional.ofNullable(fireScheduledTime.get());
    }

    private void resetFireOrderInformationMessages(String... plantMissiles) {
        if (expiredOrders != null && !expiredOrders.isEmpty()) {
            for (String plantMissile : plantMissiles) {
                if (plantMissile != null) {
                    expiredOrders.remove(plantMissile);
                }
            }
        }

        if (expiredStartTimes != null && !expiredStartTimes.isEmpty()) {
            for (String plantMissile : plantMissiles) {
                if (plantMissile != null) {
                    expiredStartTimes.remove(plantMissile);
                }
            }
        }


        fireReminderSent = false;
        estimatedFireTime = null;
    }

    private boolean fireReminderSent;

    private LocalDateTime estimatedFireTime;


    /**
     * @return the soonest scheduled fire time of both rocket.
     * Or Optional.empty if no rocket with scheduled fire found.
     */
    public Optional<LocalDateTime> getEstimatedFireTime() {
        return Optional.of(LocalDateTime.of(estimatedFireTime.getYear(),
                estimatedFireTime.getMonth(),
                estimatedFireTime.getDayOfMonth(),
                estimatedFireTime.getHour(),
                estimatedFireTime.getMinute(),
                estimatedFireTime.getSecond(),
                estimatedFireTime.getNano()));
    }

    public void fireCommandSendReminder(long reminderBeforeSecTillStart) {
        if (fireReminderSent) {
            return;
        }

        if (estimatedFireTime != null && LocalDateTime.now()
                .plusSeconds(reminderBeforeSecTillStart)
                .isBefore(estimatedFireTime)) {
            return;
        }


        getRocketFireScheduledTime(true).ifPresent((v) -> estimatedFireTime = v);

        getRocketFireScheduledTime(true).ifPresent((v) -> {
            if (v.isBefore(estimatedFireTime)) {
                estimatedFireTime = v;
            }
        });

        if (estimatedFireTime == null) {
            return;
        }

        String payload = String.format("Для пуску за плановим часом необхідно видати команду Пуск " +
                                       "до вичерпання часу (не пізніше %s)",
                estimatedFireTime.format(DateTimeFormatter.ofPattern("dd/MM/yyyy\tHH:mm:ss")));

        Broadcaster.broadcast(
                MsgEvent.builder()
                        .msgType(MsgType.WARNING)
                        .payload(payload)
                        .adjacentSystemType(AdjacentSystemType.ASKU)
                        .source(this)
                        .build());

        fireReminderSent = true;
    }

    @Transactional
    public void addReferencePoints(@NotNull String plantMissile, List<LaunchReferencePoint> referencePoints) {
        for (LaunchReferencePoint referencePoint : referencePoints) {
            if (!plantMissile.equals(referencePoint.getPlantMissile())) {
                referencePoint.setPlantMissile(plantMissile);
            }

        }
        /*Optional<RocketDao> rocket=*/
        rocketRepository.findByPlantMissile(plantMissile)
                .ifPresent(v -> addReferencePoints(v, referencePoints));
    }

    @Transactional
    public Optional<RocketDao> addReferencePoints(@NotNull RocketDao rocketDao, List<LaunchReferencePoint> referencePoints) {

        if (rocketDao == null) {
            return Optional.empty();
        }

        List<LaunchReferencePointRecord> rocketReferencePoints = rocketDao.getLaunchReferencePoints();

        for (LaunchReferencePoint newReferencePoint : referencePoints) {
            if (referencePointIsUnique(newReferencePoint, rocketReferencePoints)/*!rocketReferencePoint.equalsIgnoreId(newReferencePoint)*/) {
                if (newReferencePoint.getTimeStamp() == null) {
                    newReferencePoint = new LaunchReferencePoint(newReferencePoint.getReferencePoint(),
                            LocalDateTime.now(), newReferencePoint.getPlantMissile());
                }
                LaunchReferencePointRecord pointRecord = referencePointRecordsRepository.save(
                        LaunchReferencePointRecord.builder()
                                .referencePoint(newReferencePoint.getReferencePoint())
                                .plantMissile(newReferencePoint.getPlantMissile())
                                .timeStamp(newReferencePoint.getTimeStamp())
                                .build());

                rocketDao.getLaunchReferencePoints().add(pointRecord);
            }
        }
        rocketRepository.save(rocketDao);
        if (rocketDao.getLaunchReferencePoints() != null) {
            for (LaunchReferencePointRecord record : rocketDao.getLaunchReferencePoints()) {
                record.setRocket(rocketDao);
                referencePointRecordsRepository.save(record);
            }
        }
        rocketRepository.save(rocketDao);
        return Optional.of(rocketDao);
    }

    private boolean referencePointIsUnique(@NotNull LaunchReferencePoint referencePoint, List<LaunchReferencePointRecord> rocketReferencePoints) {
        if (rocketReferencePoints == null || rocketReferencePoints.isEmpty()) {
            return referencePoint != null;
        }

        for (LaunchReferencePointRecord rocketReferencePoint : rocketReferencePoints) {
            if (rocketReferencePoint.equalsIgnoreId(referencePoint)) {
                return false;
            }
        }

        return true;
    }


    public List<LaunchReferencePoint> getReferencePointsWithCaptionsAndSecondPrecise(UUID orderEntityId,
                                                                                     LocalDateTime selectFrom,
                                                                                     LocalDateTime selectTo) {

        return referencePointRecordsRepository
                .findByFireOrderEntityIdAndTimeStampIsBetween(orderEntityId, selectFrom, selectTo).stream()
                .map(r -> LaunchReferencePoint.builder()
                        .referencePoint(getReferencePointCaptionOrCommand(r.getReferencePoint()))
                        .timeStamp(r.getTimeStamp())
                        .plantMissile(r.getPlantMissile())
                        .build())
                .toList();
    }

    public List<LaunchReferencePoint> getReferencePointsByPlantMissileWithSecondPrecision(String plantMissile) {
        if (plantMissile == null || plantMissile.isEmpty()) {
            return Collections.emptyList();
        }

        return referencePointRecordsRepository.findByPlantMissile(plantMissile).stream()
                .map(r -> LaunchReferencePoint.builder()
                        .referencePoint(getReferencePointCaptionOrCommand(r.getReferencePoint()))
                        .timeStamp(r.getTimeStamp().withNano(0))
                        .plantMissile(r.getPlantMissile())
                        .build())
                .toList();
    }

    private String getReferencePointCaptionOrCommand(String command) {
        return referencePointsRepository.findCaption(command).orElse(command);
    }

    private record TemperatureExceedInfo(double measuredTemperature, MeasureLimit measureLimit) {

    }

    private final Map<String, TemperatureExceedInfo> exceededTemperatures = new HashMap<>();

    @Async
    public void informRocketTemperatureExceedLimit(boolean isLeft) {
        if (getRocket(isLeft).isEmpty() || getRocket(isLeft).get().getRocketFormData().isEmpty()) {
            return;
        }

        Rocket rocket = getRocket(isLeft).get();

        String plantMissile = rocket.getFormData().getPlantMissile();

        if (matchMeasureLimit(rocket.getSensorTemperature()).equals(MeasureLimit.OK) || plantMissile.isEmpty()) {
            return;
        }

        if (exceededTemperatures.containsKey(plantMissile) &&
            matchMeasureLimit(rocket.getSensorTemperature())
                    .equals(exceededTemperatures.get(plantMissile).measureLimit)) {
            return;
        }

        Broadcaster.broadcast(RocketEvent.builder()
                .isLeft(isLeft)
                .eventType(AdjacentSystemUpdateEventType.UPDATE_VIEW_SILENTLY)
                .source(this)
                .rocket(getRocket(isLeft).get())
                .timeStamp(LocalDateTime.now())
                .build());

        String payload = String.format("Увага! Температура у відсіку  № %s %s °C!",
                (isLeft ? "1" : "2"),
                (getLimitMessage(matchMeasureLimit(rocket.getSensorTemperature()))));

        Broadcaster.broadcast(
                MsgEvent.builder()
                        .msgType(MsgType.WARNING)
                        .payload(payload)
                        .adjacentSystemType(AdjacentSystemType.ASKU)
                        .source(this)
                        .build());

        exceededTemperatures.put(plantMissile,
                new TemperatureExceedInfo(rocket.getSensorTemperature(), matchMeasureLimit(rocket.getSensorTemperature())));
    }

    public MeasureLimit matchMeasureLimit(double value) {
        if (value >= basuTemperatureLimitHiHi) {
            return MeasureLimit.HI_HI;
        }
        if (value >= basuTemperatureLimitHi) {
            return MeasureLimit.HI;
        }

        return MeasureLimit.OK;
    }

    public String getLimitMessage(MeasureLimit limit) {
        switch (limit) {
            case HI_HI -> {
                return "вища за " + basuTemperatureLimitHiHi;
            }
            case HI -> {
                return "вища за " + basuTemperatureLimitHi;
            }
        }

        return "";
    }

    public Optional<Double> getRocketTemperature(boolean isLeft) {
        if (getRocket(isLeft).isPresent()) {
            return Optional.of(getRocket(isLeft).get().getSensorTemperature());
        }
        return Optional.empty();
    }

    public Optional<MeasureLimit> matchRocketMeasureLimit(boolean isLeft) {
        if (getRocketTemperature(isLeft).isEmpty()) {
            return Optional.empty();
        }

        return Optional.of(matchMeasureLimit(getRocketTemperature(isLeft).get()));
    }
}
