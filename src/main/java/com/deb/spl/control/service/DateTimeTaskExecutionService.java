package com.deb.spl.control.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Profile("!disable_date_time_task_executor_service")
@Component
@Slf4j
@EnableAsync
public class DateTimeTaskExecutionService {
    private final DateTimeService dateTimeService;

    public DateTimeTaskExecutionService(DateTimeService dateTimeService) {
        this.dateTimeService = dateTimeService;
    }


    @Scheduled(initialDelay = 300,fixedDelay = 1000)
    public void updateDateTime(){
        log.debug("calling for DateTime update " + this.getClass());
        dateTimeService.updateUtCDateTime();
    }

    @Scheduled(initialDelay = 1300,fixedDelay = 1000)
    public void riseDateTimeUpdateEvent(){
        log.debug("calling for DateTime update event" + this.getClass());
        dateTimeService.fireUpdateEvent();
    }
}
