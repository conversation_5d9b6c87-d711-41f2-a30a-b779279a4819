package com.deb.spl.control.service.asku;

import com.deb.spl.control.controller.BadRequestException;
import com.deb.spl.control.controller.NotFoundException;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.asku.CountdownEventType;
import com.deb.spl.control.data.asku.LaunchType;
import com.deb.spl.control.data.sae.CommandState;
import com.deb.spl.control.service.Broadcaster;
import com.deb.spl.control.service.NppaService;
import com.deb.spl.control.service.SutoService;
import com.deb.spl.control.views.adjacentSystems.utils.CountdownOTR;
import com.deb.spl.control.views.events.AdjacentSystemEvent;
import com.deb.spl.control.views.events.CountdownEvent;
import com.deb.spl.control.views.events.NppaEvent;
import com.deb.spl.control.views.events.SutoEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;

@Slf4j
@Component
public class PdpService implements Broadcaster.BroadcastListener {
    private final NppaService nppaService;
    private final SutoService sutoService;
    private final CountdownOTR countdownOTR;
    private final AskuService askuService;


    public PdpService(NppaService nppaService, SutoService sutoService, CountdownOTR countdownOTR, AskuService askuService) {
        this.nppaService = nppaService;
        this.sutoService = sutoService;
        this.countdownOTR = countdownOTR;
        this.askuService = askuService;

        Broadcaster.register(this, NppaEvent.class);
        Broadcaster.register(this, SutoEvent.class);
    }

    @Override
    public void receiveBroadcast(Object incomingEvent) {

        if (incomingEvent == null) {
            return;
        }
        if (!(incomingEvent instanceof AdjacentSystemEvent event)) {
            log.error("received unknown event {}", incomingEvent);
            return;
        }

        if (event.getEntity().isEmpty()) {
            log.error("received event without entity {} {}", event, this);
            return;
        }

        switch (event.getSystemType()) {
            case NCOK, BYN, NPPA -> stopTimersOnNppaApp(event);
            case SUTO -> pauseTimersOnSutoMobileState(event);
        }
    }

    public CountdownEvent handleLaunchTimers(CountdownEventType action, @NotNull LaunchType launchType) {

        if (!action.equals(CountdownEventType.START)) {
            throw new BadRequestException();
        }

        int calculatedTime = 0;
        if (askuService.getSplReadiness().isPresent() &&
            (askuService.getAskuEntity().isLeftRocketKnown() || askuService.getAskuEntity().isRightRocketKnown())) {
            switch (launchType) {
                case FIRST -> {
                    if (askuService.getRocketCopy(true).isEmpty() || askuService.getRocketCopy(true).get().getInitialData() == null) {
                        throw new NotFoundException("ракета неіснує або ВД не завантажені");
                    }
                    calculatedTime = countdownOTR.calculateLaunchTime(askuService.getRocketCopy(true).get().getInitialData().getReadiness());
                }
                case SECOND -> {
                    if (askuService.getRocketCopy(false).isEmpty() || askuService.getRocketCopy(false).get().getInitialData() == null) {
                        throw new NotFoundException("ракета неіснує або ВД не завантажені");
                    }
                    calculatedTime = countdownOTR.calculateLaunchTime(askuService.getRocketCopy(false).get().getInitialData().getReadiness());
                }
                case FIRST_AND_SECOND -> {
                    if ((askuService.getRocketCopy(false).isEmpty() || askuService.getRocketCopy(true).isEmpty()) ||
                        askuService.getRocketCopy(false).get().getInitialData() == null || askuService.getRocketCopy(true).get().getInitialData() == null) {
                        throw new NotFoundException();
                    }
                    calculatedTime = countdownOTR.calculateLaunchTime(askuService.getRocketCopy(true).get().getInitialData().getReadiness());
                }
            }
        }
        CountdownEvent event = CountdownEvent.builder()
                .eventType(action)
                .launchType(launchType)
                .timerValue(calculatedTime)
                .build();
        Broadcaster.broadcast(event);

        return event;
    }

    private void stopTimersOnNppaApp(AdjacentSystemEvent event) {
        if (event.getEntity().isEmpty()) {
            return;
        }

        if (!nppaService.getNcok().get().appPresence()) {
            return;
        }

        if (countdownOTR.getCountdownOTR1Seconds() != 0) {
            countdownOTR.stopCountdownOtr1();
        }
        if (countdownOTR.getCountdownOTR2Seconds() != 0) {
            countdownOTR.stopCountdownOtr2();
        }
    }

    private void pauseTimersOnSutoMobileState(AdjacentSystemEvent event) {
        if (event.getEntity().isEmpty()) {
            return;
        }

        //pause timers
        if (nppaService.checkCommandCommittedAndInTheTop("Otr1FromBg2bLaunch") &&
            sutoService.getSutoPropertyByName("isOutriggersInMobileState").get().getState().equals(CommandState.ON) &&
            sutoService.getStatus().equals(AdjacentSystemStatus.OK)) {
            if (countdownOTR.isCountDown1Active()) {
                countdownOTR.pauseCountDownOtr1();
            }
        }

        if (nppaService.checkCommandCommittedAndInTheTop("Otr2FromBg2bLaunch") &&
            sutoService.getSutoPropertyByName("isOutriggersInMobileState").get().getState().equals(CommandState.ON) &&
            sutoService.getStatus().equals(AdjacentSystemStatus.OK)) {

            if (countdownOTR.isCountDown2Active()) {
                countdownOTR.pauseCountDownOtr2();
            }
        }

        if (nppaService.checkCommandCommittedAndInTheTop("Otr1Otr2FromBg2bLaunch") &&
            sutoService.getSutoPropertyByName("isOutriggersInMobileState").get().getState().equals(CommandState.ON) &&
            sutoService.getStatus().equals(AdjacentSystemStatus.OK)) {
            if (countdownOTR.isCountDown1Active() && countdownOTR.isCountDown2Active()) {
                countdownOTR.pauseCountDownOtr1();
            }
            countdownOTR.pauseCountDownOtr1();
            countdownOTR.pauseCountDownOtr2();
        }
    }
}
