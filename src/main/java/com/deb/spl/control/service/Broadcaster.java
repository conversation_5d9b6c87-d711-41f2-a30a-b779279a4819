package com.deb.spl.control.service;

import com.deb.spl.control.views.events.AdjacentSystemEvent;
import com.deb.spl.control.views.events.FileCreatedEvent;
import com.deb.spl.control.views.events.MsgEvent;
import com.deb.spl.control.views.events.SystemEvent;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.WeakHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
public class Broadcaster implements Serializable {
    static ExecutorService executorService =
            Executors.newSingleThreadExecutor();

    public interface BroadcastListener {
        void receiveBroadcast(Object event);
    }

    private static WeakHashMap<BroadcastListener, ArrayList<Class>> listeners =
            new WeakHashMap<BroadcastListener, ArrayList<Class>>();

    public static synchronized void register(BroadcastListener listener, Class... event) {
        if (listener == null) {
            log.error("trying to register undefined listener " + Broadcaster.class.getName());
            return;
        }

        ArrayList<Class> dataTypes = listeners.get(listener);
        if (dataTypes != null) {
            dataTypes.addAll(Arrays.asList(event));
        } else {
            listeners.put(listener, new ArrayList<>(Arrays.asList(event)));
        }
    }

    public static synchronized void unregister(BroadcastListener listener) {
        listeners.remove(listener);
    }

    public static synchronized void broadcast(final Object event) {
        if(!isValidEvent(event)){
            return;
        }

        listeners.forEach((broadcastListener, supportedEvents) -> {
            supportedEvents.forEach(e -> {
                if (event.getClass().equals(e)) {
                    executorService.execute(new Runnable() {
                        @Override
                        public void run() {
                            broadcastListener.receiveBroadcast(event);
                        }
                    });
                }
            });
        });
    }

    private static boolean isValidEvent (Object event) {
        List<Class<?>> validEvents = List.of(
                AdjacentSystemEvent.class,
                SystemEvent.class,
                MsgEvent.class,
                FileCreatedEvent.class);
        for (Class<?> clazz : validEvents) {
            if (clazz.isInstance(event)) {
                return true;
            }
        }

        return false;
    }
}