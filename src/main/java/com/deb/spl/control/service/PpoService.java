package com.deb.spl.control.service;

import com.deb.spl.control.controller.BadRequestException;
import com.deb.spl.control.controller.NotFoundException;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.nppa.CommandValidationResult;
import com.deb.spl.control.data.ppo.*;
import com.deb.spl.control.repository.ppo.PpoBayRepository;
import com.deb.spl.control.repository.ppo.PpoCommandRepository;
import com.deb.spl.control.repository.ppo.PpoHistoryRepository;
import com.deb.spl.control.repository.ppo.PpoRepository;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.views.events.AdjacentSystemUpdateEventType;
import com.deb.spl.control.views.events.PpoEvent;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PpoService extends ServiceWithCommandCommitment<PpoCommand> {
    private AdjacentSystemStatus currentStatus;

    private final PpoBayRepository bayRepository;
    private final PpoCommandRepository ppoCommandRepository;
    private final PpoHistoryRepository ppoHistoryRepository;
    private final PpoRepository ppoRepository;
    private final PpoMapper ppoMapper;
    private final PpoBayMapper bayToDtoMapper;
    private final PpoUnitMapper ppoUnitMapper;
    private final PpoCommandToDaoMapper ppoCommandToDaoMapper;

    private Ppo currentPpoEntity;

    public PpoService(PpoBayRepository bayRepository,
                      PpoCommandRepository ppoCommandRepository,
                      PpoHistoryRepository ppoHistoryRepository,
                      PpoRepository ppoRepository,
                      PpoMapper ppoMapper,
                      PpoBayMapper bayToDtoMapper,
                      PpoUnitMapper ppoUnitMapper,
                      PpoCommandToDaoMapper ppoCommandToDaoMapper,
                      AskuService askuService,
                      @Value("${ppo.command.command-validity-time-sec}") int commandValidityTime) {
        super(ppoHistoryRepository, commandValidityTime, askuService);
        this.bayRepository = bayRepository;
        this.ppoCommandRepository = ppoCommandRepository;
        this.ppoHistoryRepository = ppoHistoryRepository;
        this.ppoRepository = ppoRepository;
        this.ppoMapper = ppoMapper;
        this.bayToDtoMapper = bayToDtoMapper;
        this.ppoUnitMapper = ppoUnitMapper;
        this.ppoCommandToDaoMapper = ppoCommandToDaoMapper;

        this.currentPpoEntity = loadPpo();
        this.currentStatus = AdjacentSystemStatus.UNDEFINED;

        initializeEvents();
        this.currentPpoEntity.setStatus(AdjacentSystemStatus.UNDEFINED);

        System.out.println(currentPpoEntity);
    }

    private Ppo loadPpo() {
        List<PpoBay> allBays = bayRepository.getAllBays();
        if (allBays.size() < 1) {
            log.error("no bays were found " + this.getClass().getSimpleName());
            throw new NotFoundException("no bays were found " + this.getClass().getSimpleName());
        }

        Optional<PpoDao> dao = ppoRepository.load();

        Ppo ppoUnderConstruction = Ppo.builder()
                .bays(new HashMap<>(Maps.uniqueIndex(allBays, PpoBay::getPpoBayType)))
                .build();

        try {
            ppoUnderConstruction = dao.isPresent() ? ppoMapper.map(dao.get()) :
                    ppoUnderConstruction;
        } catch (Exception e) {
            log.error(e.getMessage() + " " + Arrays.asList(e.getStackTrace()));
        }

        return ppoUnderConstruction;
    }

    public Optional<PpoDTO> getPpoDto() {
        if (currentPpoEntity == null) {
            log.error("PPO wasn't initialized");
            return Optional.empty();
        }

        return Optional.of(ppoMapper.toDto(currentPpoEntity));
    }

    @Transactional
    public Optional<PpoDTO> updateWithDto(@NotNull PpoDTO dto) {
        Ppo ppoUnderProcessing = ppoMapper.map(dto);
        updatePpoStatus(ppoUnderProcessing);

        return Optional.of(ppoMapper.toDto(this.currentPpoEntity));
    }

    public AdjacentSystemStatus getCurrentStatus() {
        return currentStatus;
    }

    public Optional<PpoBayDTO> getPpoBayDtoByType(PpoBayType bayType) {
        PpoBay ppoBay = this.currentPpoEntity.getBays().get(bayType);

        if (ppoBay == null) {
            log.error("cant find PpoBay of type" + bayType);

            throw new NotFoundException("cant find PpoBay of type" + bayType);
        }

        return Optional.of(bayToDtoMapper.toDto(ppoBay));
    }

    public List<PpoUnitDTO> getUnitsWithState(@NotNull PpoUnitStatus unitState) {
        return this.currentPpoEntity.getBays().values().stream()
                .filter(Objects::nonNull)
                .map(ppoBay -> ppoBay.getUnitsWithState(unitState)).flatMap(List::stream)
                .map(ppoUnitMapper::toDto).toList();
    }

    @Transactional
    public Optional<PpoBayDTO> updateByBayType(@NotNull PpoBayType bayType, @NotNull PpoBayDTO bayDTO) {
        Ppo ppoUnderUpdate = Ppo.copy(this.currentPpoEntity);

        PpoBay oldBay = ppoUnderUpdate.getBays().get(bayType);
        if (oldBay == null) {
            log.error("Cant find the bay of type " + bayType);
            throw new NotFoundException("Cant find the bay of type " + bayType);
        }
        if (bayDTO.unitsInPPoBay() == null || bayDTO.unitsInPPoBay().size() == 0) {

            log.error("List unitsInPPoBay cant be empty" + bayDTO);
            throw new BadRequestException("List unitsInPPoBay cant be empty" + bayDTO);
        }
        if (oldBay.getUnitsInPPoBay().size() != bayDTO.unitsInPPoBay().size()) {
            log.error("Unexpected size of unitsInPPoBay in the bay " + bayDTO);
            throw new BadRequestException("Unexpected size of unitsInPPoBay in the bay " + bayDTO);
        }

        PpoBay bayFromDto = bayToDtoMapper.toPpoBay(bayDTO);
        for (@NotNull PpoUnit newUnit : bayFromDto.getUnitsInPPoBay().values()) {
            Optional<PpoUnit> oldUnit = oldBay.getUnitByAlias(newUnit.getUnitAlias());
            if (oldUnit.isEmpty()) {
                log.error("cant find PpoUnit in bay. PpoUnit.Alias: " + newUnit.getUnitAlias()
                          + " unit form bayDTO: " + newUnit + " DTO: " + bayFromDto);
                throw new BadRequestException("cant find PpoUnit in bay. PpoUnit.Alias: " + newUnit.getUnitAlias()
                                              + " unit form bayDTO: " + newUnit + " DTO: " + bayFromDto);
            }
            oldUnit.get().setUnitStatus(newUnit.getUnitStatus());
        }

        oldBay.setFirePresent(bayDTO.firePresent());

        updatePpoStatus(ppoUnderUpdate);

        return Optional.of(bayToDtoMapper.toDto(this.currentPpoEntity.getBays().get(bayType)));
    }

    public Optional<PpoUnitDTO> updateByBayTypeAndId(@NotNull PpoBayType bayType,
                                                     @NotNull Long id,
                                                     @NotNull PpoUnitDTO ppoUnitDTO) {

        ObjectMapper objectMapper = new ObjectMapper();
        Ppo ppoUnderUpdate = Ppo.copy(this.currentPpoEntity);

        PpoBay bay = ppoUnderUpdate.getBays().get(bayType);
        if (bay == null) {
            throw new NotFoundException("Cant find the bay of type " + bayType);
        }

        PpoUnit newUnit = ppoUnitMapper.toPpoUnit(ppoUnitDTO);

        for (PpoUnit oldUnit : bay.getUnitsInPPoBay().values()) {
            if (oldUnit.getId().equals(id) && oldUnit.getUnitType().equals(newUnit.getUnitType())) {
                oldUnit.setUnitStatus(newUnit.getUnitStatus());

                updatePpoStatus(ppoUnderUpdate);
                return Optional.of(ppoUnitMapper.toDto(oldUnit));
            }
        }

        log.error("Cant find pop unit with id " + id + "in  the bay type" + bayType);
        throw new NotFoundException("Cant find pop unit with id " + id + "in  the bay type" + bayType);
    }

    @Transactional
    public Optional<PpoUnitDTO> updateByBayTypeAndIdWithUnitStatus(@NotNull PpoBayType bayType,
                                                                   @NotNull Long unitId,
                                                                   @NotNull PpoUnitStatus unitStatus) {
        Ppo ppoUnderUpdate = Ppo.copy(this.currentPpoEntity);
        PpoBay bay = ppoUnderUpdate.getBays().get(bayType);
        if (bay == null) {
            throw new NotFoundException("Cant find the bay of type " + bayType);
        }

        PpoUnit foundUnit = null;
        for (PpoUnit unit : bay.getUnitsInPPoBay().values()) {
            if (unit.getId() == unitId) {
                foundUnit = unit;
            }
        }
        if (foundUnit == null) {
            throw new NotFoundException();
        }
        foundUnit.setUnitStatus(unitStatus);
        updatePpoStatus(ppoUnderUpdate);

        return Optional.of(ppoUnitMapper.toDto(foundUnit));
    }

    @Transactional
    public void updatePpoStatus(@NotNull Ppo ppoUnderProcessing) {

        if (ppoUnderProcessing.getStatus() == null) {
            log.warn("Received ppo without status " + ppoUnderProcessing + " .Setting status automatically to UNDEFINED");
            ppoUnderProcessing.setStatus(AdjacentSystemStatus.UNDEFINED);
        }

        if (ppoUnderProcessing.equals(currentPpoEntity)) {
            return;
        }

        //check if ids equals to entities
        if (!validatePpo(ppoUnderProcessing)) {
            throw new BadRequestException();
        }

        switch (ppoUnderProcessing.getStatus()) {
            case OK -> this.currentStatus = AdjacentSystemStatus.OK;
            case WARNING -> this.currentStatus = AdjacentSystemStatus.WARNING;
            case ERROR -> this.currentStatus = AdjacentSystemStatus.ERROR;
            case NOT_CONNECTED -> this.currentStatus = AdjacentSystemStatus.NOT_CONNECTED;
            default -> this.currentStatus = AdjacentSystemStatus.UNDEFINED;
        }

        PpoDao ppoDao = ppoRepository.save(ppoMapper.toDao(ppoUnderProcessing));
        ppoUnderProcessing = ppoMapper.map(ppoDao);

        this.currentPpoEntity = ppoUnderProcessing;

        updatePpoEvent(ppoUnderProcessing);
    }

    @Transactional
    public boolean validatePpo(Ppo ppoUnderProcessing) {
        try {
            for (PpoBay bay : ppoUnderProcessing.getBays().values()) {
                Optional<PpoBay> foundBay = bayRepository.findByBayType(bay.getPpoBayType());
                if (foundBay.isEmpty()) {
                    log.error("Bay doesnt exists. BayType " + bay.getPpoBayType());
                    throw new BadRequestException("Bay doesnt exists. BayType " + bay.getPpoBayType());
                }
                if (!bay.getId().equals(foundBay.get().getId())) {
                    log.error("Bay doesnt exists. Bay id " + bay.getId() + " Actual is " + foundBay.get().getId());
                    throw new BadRequestException("Bay doesnt exists. Bay id " + bay.getId() + " Actual is " + foundBay.get().getId());
                }

                for (PpoUnit unit : bay.getUnitsInPPoBay().values()) {
                    PpoUnit foundUnit = foundBay.get().getUnitsInPPoBay().get(unit.getUnitAlias());
                    if (foundUnit == null) {
                        log.error("unit with doesn't exists: alias" + unit.getUnitAlias()
                                  + " in bay id " + bay.getId());
                        throw new BadRequestException("unit with doesn't exists: alias" + unit.getUnitAlias()
                                                      + " in bay id " + bay.getId());
                    }

                    if (!foundUnit.getId().equals(unit.getId())) {
                        log.error("wrong unit id. received is " + unit.getId() + " actual is " + foundUnit.getId() +
                                  "bay " + bay);
                        throw new BadRequestException("wrong unit id. received is " + unit.getId() + " actual is "
                                                      + foundUnit.getId() + "bay " + bay);
                    }
                    if (!foundUnit.getUnitType().equals(unit.getUnitType())) {
                        log.error("wrong unit type. received is " + unit.getUnitType() + " actual is "
                                  + foundUnit.getUnitType() + "bay " + bay);
                        throw new BadRequestException("wrong unit type. received is " + unit.getUnitType() + " actual is "
                                                      + foundUnit.getUnitType() + "bay " + bay);

                    }
                    if (!foundUnit.getBayType().equals(unit.getBayType())) {
                        throw new BadRequestException("wrong unit bayType. received is " + unit.getBayType() + " actual is "
                                                      + foundUnit.getBayType() + "bay " + bay);
                    }
                }
            }
        } catch (Exception e) {
            log.error(Arrays.toString(e.getStackTrace()));
            throw new BadRequestException(Arrays.toString(e.getStackTrace()));
        }

        return true;
    }


    private PpoEvent currentPpoEvent;
    private PpoEvent previousPpoEvent;

    private void initializeEvents() {
        this.currentPpoEvent = PpoEvent.builder()
                .ppo(this.currentPpoEntity)
                .eventType(AdjacentSystemUpdateEventType.UNDEFINED)
                .source(this)
                .build();
    }

    private void updatePpoEvent(@NotNull Ppo ppoUnderProcessing) {
        if (currentPpoEvent != null) {
            previousPpoEvent = currentPpoEvent;
        }
        try {
            AdjacentSystemUpdateEventType eventType;
            if (ppoUnderProcessing.isMalfunctionPresent()) {
                eventType = (AdjacentSystemUpdateEventType.ERROR_OCCURRED);
            } else {
                switch (ppoUnderProcessing.getStatus()) {
                    case OK -> eventType = AdjacentSystemUpdateEventType.DATA_UPDATE;
                    case NOT_CONNECTED -> eventType = AdjacentSystemUpdateEventType.NOT_CONNECTED;
                    default -> eventType = AdjacentSystemUpdateEventType.UNDEFINED;
                }
            }
            this.currentPpoEvent = PpoEvent.builder()
                    .eventType(eventType)
                    .ppo(ppoUnderProcessing)
                    .source(currentPpoEvent.getSource())
                    .build();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    void fireUpdateEvent() {
        if (currentPpoEvent == null) {
            log.info("no ppo event for update");
            return;
        }

        if (currentPpoEvent.equals(previousPpoEvent)) {
            log.debug("ppo event is the same as the previous");
        } else {
            previousPpoEvent = currentPpoEvent;
        }

        Broadcaster.broadcast(currentPpoEvent);
    }

    public void setPpoCommand(@Valid PpoCommand command) {
        if (!command.getAdjacentSystem().equals(AdjacentSystemType.PPO)) {
            log.error("received unexpected command " + command + " in the" + this.getClass().getSimpleName());
            return;
        }

        if (this.topMostCommand != null && this.topMostCommand.equals(command)) {
            if (!LocalDateTime.now().isAfter(commandGenerationTime.plusSeconds(getCommandValidityTimeSec()))) {
                return;
            }
        }

        CommandValidationResult validationResult = validateCommandOnTimeDelay(command);
        if (!validationResult.isValid()) {
            try {
                showValidationErrorMsg(validationResult.errorMessageUa(), AdjacentSystemType.PPO, this.getClass());
            } catch (Exception e) {
                log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
            }
            return;
        }

        this.commandGenerationTime = command.getGenerationTime();

        this.topMostCommand = command;
        ppoHistoryRepository.save(command);


        log.info("added ppo command " + command + " at" + commandGenerationTime.toString());
    }

    public Optional<PpoCommand> getCommand() {
        if (topMostCommand == null && commandGenerationTime == null) {
            String message = "none active command was found";
            log.debug(message);
            throw new NotFoundException();
        }

        if (LocalDateTime.now().isAfter(commandGenerationTime.plusSeconds(getCommandValidityTimeSec()))) {
            return Optional.empty();
        }
        Optional<PpoCommand> validCommand = Optional.ofNullable(topMostCommand);
        if (validCommand.isEmpty()) {
            log.debug("cant find any command for PPO");
            throw new NotFoundException("cant find any command for PPO");
        }

        return validCommand;
    }

    public List<PpoCommand> getAvailablePpoCommands() {
        return ppoCommandRepository.getAllCommands().stream()
                .map(ppoCommandToDaoMapper::toPpoCommand)
                .collect(Collectors.toList());
    }
}
