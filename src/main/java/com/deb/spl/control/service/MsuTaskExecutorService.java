package com.deb.spl.control.service;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
@Setter
@EnableAsync
@Slf4j
public class MsuTaskExecutorService {
    private final MsuService msuService;

    @Autowired
    public MsuTaskExecutorService(MsuService msuService) {
        this.msuService = msuService;
    }

    @Value("${bins.sleep-time-on-error-sec}")
    private int msuSleepTimeOnError;
    private LocalDateTime disconnectedTs = LocalDateTime.MIN;

    @Scheduled(fixedDelay = 5000)
    public void updateMsuState() {
        if (!msuService.isConnected() && LocalDateTime.now().isBefore(disconnectedTs.plusSeconds(msuSleepTimeOnError))) {
            log.debug("MSU is disconnected. Waiting  " + msuSleepTimeOnError + " sec. till next request" + this.getClass());

            return;
        }
        if (msuService.update() == null && !msuService.isConnected()) {
            disconnectedTs = LocalDateTime.now();
            log.error("MSU is disconnected for " + msuSleepTimeOnError + " sec.");
        } else {
            log.debug("updating MSU state " + this.getClass());
        }
    }

}
