package com.deb.spl.control.service.asku;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Profile("!disable_asku_executor_service")
@Component
@Slf4j
@EnableAsync
public class AskuTasksExecutorService {
    private final PlcService plcService;
    private final AskuService askuService;
    private final long fireCommandReminderBeforeScheduledSec;
    private final boolean leftRocketSelected=true;

    public AskuTasksExecutorService(PlcService plcService, AskuService askuService,
                                    @Value("${asku.msg.fire-command-reminder-before-scheduled-sec:30000}")
                                    long fireCommandReminderBeforeScheduledSec) {
        this.plcService = plcService;
        this.askuService = askuService;
        this.fireCommandReminderBeforeScheduledSec = fireCommandReminderBeforeScheduledSec;
    }

    @Scheduled(fixedDelay = 2000)
    public void updatePlc() {
        log.debug("calling for PLC event " + this.getClass());
        plcService.fireUpdateEvent();
    }

    @Scheduled(fixedDelay = 2100)
    public void fireAskuInfoUpdateEvent() {
        log.debug("calling for Asku info event " + this.getClass());
        askuService.fireUpdateEvent();
    }

    @Scheduled(fixedDelay = 2200)
    public void showFireOrderInformationMessages() {
        askuService.informOrderValidityTimeIsExpired(true);
        askuService.informOrderValidityTimeIsExpired(false);

        askuService.informStartTimeTimeIsExpired(true);
        askuService.informStartTimeTimeIsExpired(false);

        askuService.fireCommandSendReminder(fireCommandReminderBeforeScheduledSec);
    }

    @Scheduled(fixedDelayString = "${basu.msg.rocket-temperature.refresh-rate-sec:1}", timeUnit = TimeUnit.SECONDS)
    public void showRocketTemperatureIfExceedLimits() {
        askuService.informRocketTemperatureExceedLimit(true);
        askuService.informRocketTemperatureExceedLimit(false);
    }
}
