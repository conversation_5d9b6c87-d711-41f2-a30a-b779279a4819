package com.deb.spl.control.service.asku;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.asku.Plc;
import com.deb.spl.control.data.suto.SutoMapper;
import com.deb.spl.control.data.nppa.BynMapper;
import com.deb.spl.control.data.nppa.NcokMapper;
import com.deb.spl.control.data.nppa.Nppa;
import com.deb.spl.control.data.nppa.NppaMapper;
import com.deb.spl.control.data.ppo.Ppo;
import com.deb.spl.control.data.ppo.PpoMapper;
import com.deb.spl.control.data.suto.Suto;
import com.deb.spl.control.service.*;
import com.deb.spl.control.views.events.AdjacentSystemUpdateEventType;
import com.deb.spl.control.views.events.PlcEvent;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Optional;

@Service
@Slf4j
public class PlcService {
    @Getter
    @Setter
    private Plc plc;
    @Getter
    private LocalDateTime lastRequestFromPlc = LocalDateTime.MIN;
    private final SutoService sutoService;
    private final SaeService saeService;
    private final NppaService nppaService;
    private final PpoService ppoService;
    private final SutoMapper sutoMapper;
    private final BynMapper bynMapper;
    private final NcokMapper ncokMapper;
    private final PpoMapper ppoMapper;

    public PlcService(SutoService sutoService,
                      SaeService saeService,
                      NppaService nppaService,
                      PpoService ppoService,
                      SutoMapper sutoMapper, BynMapper bynMapper, NcokMapper ncokMapper, PpoMapper ppoMapper) {
        this.bynMapper = bynMapper;
        this.ncokMapper = ncokMapper;
        this.ppoMapper = ppoMapper;
        plc = new Plc();
        plc.setStatus(AdjacentSystemStatus.UNDEFINED);

        this.sutoService = sutoService;
        this.saeService = saeService;
        this.nppaService = nppaService;
        this.ppoService = ppoService;
        this.sutoMapper = sutoMapper;
    }

    public void setStatus(@NotNull AdjacentSystemStatus status) {
        if (plc == null) {
            plc = new Plc();
        }
        plc.setStatus(status);

        fireUpdateEvent();
    }

    public Optional<AdjacentSystemStatus> getAdjacentSystemStatus() {
        if (plc == null) {
            return Optional.empty();
        }

        return Optional.of(plc.getStatus() != null ? plc.getStatus() : AdjacentSystemStatus.UNDEFINED);
    }

    public AdjacentSystemStatus getStatus() {
        if (plc == null) {
            return AdjacentSystemStatus.UNDEFINED;
        }

        return plc.getStatus() != null ? plc.getStatus() :
                AdjacentSystemStatus.UNDEFINED;
    }


    @Value("${plc.connection-time-out-sec}")
    int plcConnectionTimeOutSec;
    @Value("${plc.connection-error-retries}")
    int plcConnectionRetriesLimit;
    private int totalConnectionRetries = 0;

    public void fireUpdateEvent() {
        AdjacentSystemUpdateEventType eventType;
        if (plc == null) {
            plc = new Plc();
            plc.setStatus(AdjacentSystemStatus.UNDEFINED);
        }

        if (LocalDateTime.now().isAfter(lastRequestFromPlc.plusSeconds(plcConnectionTimeOutSec))) {
            plc.setStatus(AdjacentSystemStatus.NOT_CONNECTED);
            if (totalConnectionRetries <= plcConnectionRetriesLimit) {
                totalConnectionRetries++;
                log.error("lost connection with plc " + LocalDateTime.now());
            }
        }

        switch (plc.getStatus()) {
            case NOT_CONNECTED -> {
                eventType = AdjacentSystemUpdateEventType.NOT_CONNECTED;
                resetConnectedSystemStatues();
            }
            case OK, WARNING -> eventType = AdjacentSystemUpdateEventType.DATA_UPDATE;
            case ERROR -> eventType = AdjacentSystemUpdateEventType.ERROR_OCCURRED;
            default -> eventType = AdjacentSystemUpdateEventType.UNDEFINED;

        }

        PlcEvent currentPlcEvent = PlcEvent.builder()
                .plc(this.plc)
                .eventType(eventType)
                .source(this)
                .build();

        Broadcaster.broadcast(currentPlcEvent);
    }

    private void resetConnectedSystemStatues() {
        if (sutoService.getSutoDto().isPresent()) {
            Suto suto = sutoMapper.map(sutoService.getSutoDto().get());
            suto.setStatus(AdjacentSystemStatus.NOT_CONNECTED);

            sutoService.updateWithDto(sutoMapper.toDto(suto));

        }


        if (saeService.getSae().isPresent()) {
            saeService.updateSystemStatus(AdjacentSystemStatus.NOT_CONNECTED);
        }

        if (nppaService.getNppa().isPresent()) {
            Nppa nppa = nppaService.getNppa().get();
            nppa.setStatus(AdjacentSystemStatus.NOT_CONNECTED);
            nppa.getByn().setSystemStatus(AdjacentSystemStatus.NOT_CONNECTED);
            nppa.getNcok().setSystemStatus(AdjacentSystemStatus.NOT_CONNECTED);

            nppaService.updateByn(bynMapper.toDto(nppa.getByn()));
            nppaService.updateNcok(ncokMapper.toDto(nppa.getNcok()));
        }

        if (ppoService.getPpoDto().isPresent()) {
            Ppo ppo = ppoMapper.map(ppoService.getPpoDto().get());
            ppo.setStatus(AdjacentSystemStatus.NOT_CONNECTED);

            ppoService.updatePpoStatus(ppo);
        }


    }

    public void updateRequestTime(@NotNull LocalDateTime now) {
        lastRequestFromPlc = now;
        totalConnectionRetries = 0;
        if (plc.getStatus() == AdjacentSystemStatus.NOT_CONNECTED || plc.getStatus() == AdjacentSystemStatus.UNDEFINED) {
            plc.setStatus(AdjacentSystemStatus.OK);
        }
    }
}
