package com.deb.spl.control.service;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

@Component
@Setter
@EnableAsync
@Slf4j
public class BinsTaskExecutorService {
    private final BinsService binsService;

    public BinsTaskExecutorService(BinsService binsService) {
        this.binsService = binsService;
    }

    @Value("${bins.sleep-time-on-error-sec}")
    private int binsSleepTimeOnError;
    private LocalDateTime disconnectedTs = LocalDateTime.MIN;

    @Scheduled(fixedDelay = 1, timeUnit = TimeUnit.SECONDS)
    public void updateBinsState() {
        if (LocalDateTime.now().isBefore(disconnectedTs.plusSeconds(binsSleepTimeOnError))) {
            log.debug("bins is disconnected. Waiting  " + binsSleepTimeOnError + " sec. till next request" + this.getClass());

            return;
        }
        if (binsService.getBinsDto().isEmpty() || !binsService.isConnected()) {
            disconnectedTs = LocalDateTime.now();
            log.error("bins is disconnected for " + binsSleepTimeOnError + " sec.");
        } else {
            log.debug("updating BINS state " + this.getClass());
        }
    }

}
