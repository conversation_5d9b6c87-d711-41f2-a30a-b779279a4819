package com.deb.spl.control.service;

import com.deb.spl.control.data.UtcDateTime;
import com.deb.spl.control.views.events.AdjacentSystemUpdateEventType;
import com.deb.spl.control.views.events.DateTimeUpdateEvent;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Optional;

@Service
@Slf4j
public class DateTimeService {
    private final BinsService binsService;

    private LocalDateTime dateTime = null;
    private boolean isSynchronizedByBins;

    @Getter
    private final ZoneId timeZoneId;

    @Getter
    private boolean USE_BINS_TIME;

    public DateTimeService(BinsService binsService,
                           @Value("${time.from-bins:false}") boolean useBinsTime) {
        ZoneId defferedZoneId = null;
        this.binsService = binsService;

        try {
            defferedZoneId = ZoneId.of(binsService.getUtcTimeZoneString());
        } catch (Exception e) {
            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
        }

        timeZoneId = defferedZoneId != null ?
                defferedZoneId :
                ZoneId.systemDefault();
        dateTime = LocalDateTime.now();
        isSynchronizedByBins = false;
        this.USE_BINS_TIME = useBinsTime;
    }


    public Optional<UtcDateTime> getUtcDateTimeFromBins() {
        Optional<UtcDateTime> dt = binsService.getDateTime();
        if (dt.isPresent()) {
            if (dt.get().getTime() == LocalTime.from(LocalTime.MIDNIGHT)) {
                return Optional.empty();
            }
        }

        return dt;
    }

    public Optional<LocalDateTime> getDateTime() {
        return Optional.ofNullable(dateTime);
    }

    void updateUtCDateTime() {
        Optional<UtcDateTime> fromBins = getUtcDateTimeFromBins();
        if (fromBins.isPresent()) {
            if (fromBins.get().getDate() != null) {
                LocalDate localDate = LocalDate.parse(fromBins.get().getDate().toString(), DateTimeFormatter.ISO_LOCAL_DATE);
                dateTime = dateTime
                        .withYear(localDate.getYear())
                        .withMonth(localDate.getMonthValue())
                        .withDayOfMonth(localDate.getDayOfMonth());
            } else {
                dateTime = LocalDateTime.now();
            }

            if (fromBins.get().getTime() != null) {
                dateTime = dateTime
                        .withHour(fromBins.get().getTime().getHour())
                        .withMinute(fromBins.get().getTime().getMinute())
                        .withSecond(fromBins.get().getTime().getSecond())
                        .withNano(fromBins.get().getTime().getNano());

                isSynchronizedByBins = true;
            }
        } else {
            dateTime = LocalDateTime.now();
            isSynchronizedByBins = false;

        }

    }

    void fireUpdateEvent() {

        AdjacentSystemUpdateEventType eventType = AdjacentSystemUpdateEventType.DATA_UPDATE;
        LocalDateTime updatedDateTime = (isSynchronizedByBins && USE_BINS_TIME) ? dateTime : LocalDateTime.now();

        DateTimeUpdateEvent dataChangeEvent = new DateTimeUpdateEvent(updatedDateTime, this, eventType);
        Broadcaster.broadcast(dataChangeEvent);
    }


}
