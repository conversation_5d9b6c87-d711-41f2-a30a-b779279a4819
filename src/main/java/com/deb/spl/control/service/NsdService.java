package com.deb.spl.control.service;

import com.deb.spl.control.controller.BadRequestException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.List;

@Service
@Slf4j
public class NsdService {

    @Getter
    private String user;
    @Getter
    private String password;
    @Getter
    private String remoteToken;
    @Getter
    private final String local_TOKEN;

    public NsdService(@Value("${nsd.token}") String nsdToken) {
        local_TOKEN = nsdToken;
    }

    public void setVariables(@NotNull List<String> variables) {
        if (variables.size() < 3) {
            String errorMsg = "Wrong number of parameters";
            log.error(errorMsg);
            throw new BadRequestException(errorMsg);
        }
        remoteToken = variables.get(0);
        user = variables.get(1);
        password = variables.get(2);
    }

    public boolean validateToken(String remoteToken) {
        return remoteToken.equals(local_TOKEN);
    }
}
