package com.deb.spl.control.service;

import com.deb.spl.control.data.AdjacentSystemCommand;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.VoltageStatus;
import com.deb.spl.control.data.nppa.CommandValidationResult;
import com.deb.spl.control.data.sae.*;
import com.deb.spl.control.repository.sae.SaeCommandsRepository;
import com.deb.spl.control.repository.sae.SaeHistoryRepository;
import com.deb.spl.control.repository.sae.SaeRepository;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.views.events.AdjacentSystemUpdateEventType;
import com.deb.spl.control.views.events.SaeEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SaeService extends ServiceWithCommandCommitment<SaeCommand> {
    private AdjacentSystemStatus currentStatus = AdjacentSystemStatus.UNDEFINED;
    private Sae saeEntity;
    private final SaeMapper saeMapper;
    private final SaeCommandsRepository commandsRepository;
    private final SaeHistoryRepository saeHistoryRepository;
    private final SaeCommandMapper saeCommandMapper;
    private final SaeRepository saeRepository;
    private final List<String> saeCommandsAvailableAtPdp;

    public SaeService(SaeMapper saeMapper,
                      SaeCommandsRepository saeCommandsRepository,
                      SaeHistoryRepository saeHistoryRepository,
                      SaeCommandMapper saeCommandMapper,
                      SaeRepository saeRepository, AskuService askuService,
                      @Value("${sae.command.command-validity-time-sec}") int commandValidityTime,
                      @Value("#{'${sae.command.pdp.automation}'.split(',')}") List<String> saeCommandsAvailableAtPdp) {
        super(saeHistoryRepository, commandValidityTime, askuService);
        this.saeMapper = saeMapper;
        this.commandsRepository = saeCommandsRepository;
        this.saeHistoryRepository = saeHistoryRepository;
        this.saeCommandMapper = saeCommandMapper;
        this.saeRepository = saeRepository;
        this.saeEntity = loadSaeOrDefault();
        this.saeCommandsAvailableAtPdp = saeCommandsAvailableAtPdp;
        initializeSaeEvents();
    }

    Sae loadSaeOrDefault() {
        Optional<SaeDao> dao = saeRepository.load();

        if (dao.isEmpty()) {
            log.error("Cant load Sae. Check if DB is corrupted or valid Sae entity exists. Loading default sae");
        }

        return dao.isPresent() ? saeMapper.map(dao.get())
                : getDafautSae();
    }


    public Optional<SaeDto> getSaeDto() {

        return Optional.of(saeMapper.toDto(saeEntity));
    }

    public Optional<Sae> getSae() {
        if (saeEntity == null) {
            return Optional.empty();
        }

        return Optional.of(saeMapper.clone(saeEntity));
    }

    @Transactional
    public void updateWithDto(@Valid SaeDto dto) {
        Sae saeUnderProcessing = saeMapper.map(dto);

        updateSae(saeUnderProcessing);
    }

    public AdjacentSystemStatus getStatus() {
        return saeEntity.getStatus() == null ? AdjacentSystemStatus.UNDEFINED : saeEntity.getStatus();
    }

    @Transactional
    public void updateSystemStatus(@NotNull AdjacentSystemStatus status) {
        if (saeEntity == null || saeEntity.getSaeStatus().equals(status)) {
            return;
        }

        Sae saeUnderProcessing = new Sae(saeEntity);
        saeUnderProcessing.setSaeStatus(SaeStatus.fromAdjacentSystemStatus(status));
        updateSae(saeUnderProcessing);
    }

    @Transactional
    public void updateFeedersStatuses(@NotNull Map<String, SaeFeederStatus> feeders) {
        if (feeders.size() == 0) {
            log.debug("attempt to update sae feeder with empty map " + this.getClass().getName());
            return;
        }

        Sae saeUnderProcessing = new Sae(saeEntity);
        saeUnderProcessing.updateFeeders(feeders);

        updateSae(saeUnderProcessing);
    }

    private SaeEvent currentSaeEvent;
    private SaeEvent previousSaeEvent;

    private void initializeSaeEvents() {
        this.currentSaeEvent = SaeEvent.builder()
                .sae(this.saeEntity)
                .eventType(AdjacentSystemUpdateEventType.UNDEFINED)
                .source(this)
                .build();

        this.previousSaeEvent = this.currentSaeEvent;
    }

    @Transactional
    public void updateSae(@NotNull Sae saeUnderProcessing) {

        if (saeUnderProcessing.getSaeStatus() == null) {
            this.currentStatus = AdjacentSystemStatus.UNDEFINED;
            log.warn("Sae status is " + this.currentStatus.getValueUa() + "in" + this.getClass().getSimpleName());
            return;
        }

        if (this.saeEntity.equals(saeUnderProcessing)) {
            return;
        }

        switch (saeUnderProcessing.getSaeStatus()) {
            case OK -> this.currentStatus = AdjacentSystemStatus.OK;
            case WARNING -> this.currentStatus = AdjacentSystemStatus.WARNING;
            case ERROR -> this.currentStatus = AdjacentSystemStatus.ERROR;
        }

        SaeDao dao = saeMapper.toDao(saeUnderProcessing);
        dao.setId(null);
        dao = saeRepository.save(dao);
        this.saeEntity = saeMapper.map(dao);

        updateSaeEvent(saeUnderProcessing);
    }

    private void updateSaeEvent(Sae saeUnderProcessing) {
        if (saeUnderProcessing == null) {
            return;
        }

        if (currentSaeEvent != null) {
            previousSaeEvent = this.currentSaeEvent;
        }

        try {
            AdjacentSystemUpdateEventType eventType;
            if (saeUnderProcessing.isMalfunctionPresent()) {
                eventType = (AdjacentSystemUpdateEventType.ERROR_OCCURRED);
            } else {
                switch (saeUnderProcessing.getSaeStatus()) {
                    case OK -> eventType = AdjacentSystemUpdateEventType.DATA_UPDATE;
                    case NOT_CONNECTED -> eventType = AdjacentSystemUpdateEventType.NOT_CONNECTED;
                    default -> eventType = AdjacentSystemUpdateEventType.UNDEFINED;
                }
            }

            this.currentSaeEvent = SaeEvent.builder()
                    .eventType(eventType)
                    .sae(saeUnderProcessing)
                    .source(currentSaeEvent.getSource())
                    .build();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    void fireUpdateEvent() {
        if (currentSaeEvent == null) {
            log.info("no sae event for update");
            return;
        }

        if (currentSaeEvent.equals(previousSaeEvent)) {
            log.debug("sae event is the same as the previous");
            return;
        }
        Broadcaster.broadcast(currentSaeEvent);
    }

    public List<SaeCommand> getAvailableCommands() {

        return commandsRepository.getAllCommands().stream()
                .map(saeCommandMapper::daoToSaeCommand)
                .collect(Collectors.toList());
    }

    public List<SaeCommand> getAvailablePdpCommands() {

        return commandsRepository.getAllCommands().stream()
                .filter(command->saeCommandsAvailableAtPdp.contains(command.getCommand()))
                .map(saeCommandMapper::daoToSaeCommand)
                .collect(Collectors.toList());
    }

    @Value("${sae.command.command-validity-time-sec}")
    private Integer commandValidityTime;

    public void setSaeCommand(@Valid SaeCommand saeCommand) {
        if (!saeCommand.getAdjacentSystem().equals(AdjacentSystemType.SAE)) {
            log.error("received unexpected command " + saeCommand + " in the" + this.getClass().getSimpleName());
            return;
        }

        if (this.commandGenerationTime != null &&
            !LocalDateTime.now().isAfter(commandGenerationTime.plusSeconds(commandValidityTime))) {
            if (this.topMostCommand == saeCommand) {
                return;
            }
        }

        CommandValidationResult validationResult = validateCommandOnTimeDelay(saeCommand);
        if (!validationResult.isValid()) {
            try {
                showValidationErrorMsg(validationResult.errorMessageUa(), AdjacentSystemType.SAE, this.getClass());
            } catch (Exception e) {
                log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
            }
            return;
        }


        this.commandGenerationTime = saeCommand.getGenerationTime();

        saeHistoryRepository.save(saeCommand);
        this.topMostCommand = saeCommand;

        log.info("added sae command " + saeCommand + " at" + commandGenerationTime.toString());
    }

    public Optional<SaeCommand> getCommand() {
        if (topMostCommand == null && commandGenerationTime == null) {
            String message = "none active command was found";
            log.debug(message);
            return Optional.empty();
        }

        if (LocalDateTime.now().isAfter(commandGenerationTime.plusSeconds(commandValidityTime))) {
            return Optional.empty();
        }
        Optional<SaeCommand> validCommand = Optional.ofNullable(topMostCommand);

        return validCommand;
    }

    public Sae getDafautSae() {
        return Sae.builder()
                .saeStatus(SaeStatus.fromAdjacentSystemStatus(AdjacentSystemStatus.NOT_CONNECTED))
                .readiness(AdjacentSystemStatus.UNDEFINED)
                .energizedByHds(SaePowerSourceStatus.OFF)
                .energizedByAdj(SaePowerSourceStatus.OFF)
                .energizedByExternalPowerSource(SaePowerSourceStatus.OFF)
                .adjStart(ADJstatus.NOT_ACTIVE)
                .adjStop(ADJstatus.NOT_ACTIVE)
                .adjLock(ADJstatus.NOT_ACTIVE)
                .adjUnlock(ADJstatus.NOT_ACTIVE)
                .hdsStatus(HDSstatus.NOT_ACTIVE)
                .voltageStatus(VoltageStatus.NOT_ACTIVE)
                .externalPowerSourceVoltage(VoltageStatus.NOT_ACTIVE)
                .feeder1Status(SaeFeederStatus.OFF)
                .feeder2Status(SaeFeederStatus.OFF)
                .feeder3Status(SaeFeederStatus.OFF)
                .feeder4Status(SaeFeederStatus.OFF)
                .feeder5Status(SaeFeederStatus.OFF)
                .feeder6Status(SaeFeederStatus.OFF)
                .feederNppa1Status(SaeFeederStatus.OFF)
                .BSVoltage(VoltageStatus.NOT_ACTIVE)
                .build();
    }

}
