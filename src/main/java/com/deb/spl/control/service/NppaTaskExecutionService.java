package com.deb.spl.control.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Profile("!disable_nppa_task_executor_service")
@Component
@Slf4j
@EnableAsync
public class NppaTaskExecutionService {
    private final NppaService nppaService;

    public NppaTaskExecutionService(NppaService nppaService) {
        this.nppaService = nppaService;
    }

    @Scheduled(initialDelay = 2000, fixedDelay = 1000)
    public void riseNppaUpdateEvent() {
        log.debug("calling for Nppa event " + this.getClass());
        nppaService.fireUpdateEvent();
    }
}
