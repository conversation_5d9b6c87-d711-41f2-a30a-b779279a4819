package com.deb.spl.control.service;

import com.deb.spl.control.controller.NotFoundException;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.asku.LaunchResult;
import com.deb.spl.control.data.asku.Readiness;
import com.deb.spl.control.data.nppa.*;
import com.deb.spl.control.data.sae.CommandState;
import com.deb.spl.control.repository.nppa.*;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.utils.NppaUtils;
import com.deb.spl.control.views.events.AdjacentSystemUpdateEventType;
import com.deb.spl.control.views.events.NppaEvent;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureTask;

import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.deb.spl.control.views.automotion.PdpView.OTR_1_AUTOMATIC_LAUNCH_CANCEL_ACKNOWLEDGMENT;
import static com.deb.spl.control.views.automotion.PdpView.OTR_2_AUTOMATIC_LAUNCH_CANCEL_ACKNOWLEDGMENT;

@Service
@Slf4j
public class NppaService extends ServiceWithCommandCommitment<NppaCommand> {
    final AskuService askuService;
    private final NppaCommandRepository nppaCommandRepository;
    private final NcokHistoryRepository ncokHistoryRepository;
    private final BynRepository bynRepository;
    private final NcokRepository ncokRepository;
    private final NppaMapper nppaMapper;
    private final NppaCommandMapper nppaCommandMapper;
    private final NcokMapper ncokMapper;
    private final BynMapper bynMapper;
    private final UserService userService;

    private Nppa nppa;

    @Getter
    private final boolean ncokModeUpdated;
    @Getter
    private final boolean bynModeUpdated;
    List<String> commandsFroLaunchSettingsExclusions;
    private final List<String> nppaPdpAutomationCommands;
    private final List<String> ncokCommandsEnabledInNotConnected;

    public NppaService(AskuService askuService,
                       NppaCommandRepository nppaCommandRepository,
                       BynHistoryRepository bynHistoryRepository,
                       NppaCommandMapper nppaCommandMapper,
                       NcokHistoryRepository ncokHistoryRepository,
                       BynRepository bynRepository,
                       NcokRepository ncokRepository,
                       NppaMapper nppaMapper,
                       NcokMapper ncokMapper,
                       BynMapper bynMapper,
                       @Value("${nppa.command.command-validity-time-sec}") int commandValidityTime,
                       UserService userService, @Value("${nppa.command.commit-awaiting-time-sec:30}") long commandCommitTimeLimit,
                       @Value("#{'${nppa.command.launch.control.exclude}'.split(',')}")
                       List<String> commandsFroLaunchSettingsExclusions,
                       @Value("#{'${nppa.command.pdp.automation}'.split(',')}")
                       List<String> nppaPdpAutomationCommands,
                       @Value("#{'${nppa.commands.enabled-in-ncok-not-connected}'.split(',')}")
                       List<String> ncokCommandsEnabledInNotConnected) {
        super(bynHistoryRepository, commandValidityTime, askuService);
        this.askuService = askuService;
        this.nppaCommandRepository = nppaCommandRepository;
        this.nppaCommandMapper = nppaCommandMapper;
        this.ncokHistoryRepository = ncokHistoryRepository;
        this.bynRepository = bynRepository;
        this.ncokRepository = ncokRepository;
        this.nppaMapper = nppaMapper;
        this.ncokMapper = ncokMapper;
        this.bynMapper = bynMapper;
        this.userService = userService;
        this.commandCommitTimeLimit = commandCommitTimeLimit;
        this.commandsFroLaunchSettingsExclusions = commandsFroLaunchSettingsExclusions;
        this.ncokCommandsEnabledInNotConnected = ncokCommandsEnabledInNotConnected;

        ncokModeUpdated = false;
        bynModeUpdated = false;
        nppa = initializeNppa();
        initializeEvents();

        this.nppaPdpAutomationCommands = nppaPdpAutomationCommands;
    }

    private Nppa initializeNppa() {
        return NppaUtils.getDafultNppa();
    }

    public List<NppaCommand> getAvailableNcokWorkflowCommands() {
        return nppaCommandRepository.getAllNcokCommands()
                .stream()
                .filter(NppaCommandDAO::isUsedInWorkflow)
                .map(nppaCommandMapper::map)
                .collect(Collectors.toList());
    }

    public List<NppaCommand> getAvailableNcokWorkflowAutomationCommands() {
        return getAvailableNcokWorkflowCommands(nppaPdpAutomationCommands);
    }

    public List<NppaCommand> getAvailableNcokWorkflowCommands(List<String> commandFilter) {
        return nppaCommandRepository.getAllNcokCommands()
                .stream()
                .filter(dao -> commandFilter.contains(dao.getCommand()))
                .map(nppaCommandMapper::map)
                .collect(Collectors.toList());
    }

    public List<NppaCommand> getAvailableNcokCombatModeCommands() {
        return nppaCommandRepository.getAllNcokCommands()
                .stream()
                .filter(NppaCommandDAO::isUsedInCombatMode)
                .map(nppaCommandMapper::map)
                .collect(Collectors.toList());
    }

    public List<NppaCommand> getAvailableNcokTestModeCommands() {
        return nppaCommandRepository.getAllNcokCommands()
                .stream()
                .filter(NppaCommandDAO::isUsedInTestMode)
                .map(nppaCommandMapper::map)
                .collect(Collectors.toList());
    }

    public List<NppaCommand> getAvailableBynWorkflowCommands() {
        return nppaCommandRepository.getAllBynCommands()
                .stream()
                .filter(NppaCommandDAO::isUsedInWorkflow)
                .map(nppaCommandMapper::map)
                .collect(Collectors.toList());

    }

    public List<NppaCommand> getAvailableBynCombatModeCommands() {
        return nppaCommandRepository.getAllBynCommands()
                .stream()
                .filter(NppaCommandDAO::isUsedInCombatMode)
                .map(nppaCommandMapper::map)
                .collect(Collectors.toList());
    }

    public List<NppaCommand> getAvailableBynTestModeCommands() {
        return nppaCommandRepository.getAllBynCommands()
                .stream()
                .filter(NppaCommandDAO::isUsedInTestMode)
                .map(nppaCommandMapper::map)
                .collect(Collectors.toList());
    }

    public Optional<NcokDto> getNcok() {
        if (!(nppa != null && nppa.getNcok() != null)) {
            return Optional.empty();
        }
        return Optional.of(ncokMapper.toDto(nppa.getNcok()));
    }

    public Optional<Nppa> getNppa() {
        return nppa == null ? Optional.empty() : Optional.of(Nppa.copy(nppa));
    }

    @Transactional
    public Optional<NppaDto> updateNcok(@NotNull NcokDto dto) {
        Ncok ncokUpdate = ncokMapper.map(dto);
        unlockLaunchCommand(ncokUpdate);
        Nppa nppaUnderUpdate = nppa != null ? Nppa.copy(nppa) : Nppa.copy(initializeNppa());

        if (nppaUnderUpdate.getNcok() == null || !nppaUnderUpdate.getNcok().equals(ncokUpdate)) {
            nppaUnderUpdate.setNcok(ncokUpdate);

            NcokDao dao = ncokRepository.findFirstByOrderByUpdatedAtDesc().orElse(null);
            if (dao != null) {
                ncokRepository.detach(dao);
            }
            long prevId = (dao != null && dao.getId() != null) ? dao.getId() : -1;

            dao = ncokMapper.toDao(ncokUpdate);
            dao.setId(null);
            dao = ncokRepository.save(dao);
            if (dao.getId().equals(prevId)) {
                log.error("can't persist NCOK state. ncokUnderUpdate is " + nppaUnderUpdate.getByn() +
                          " previousBNcok is " + nppa.getByn());
            }
            nppaUnderUpdate.setNcok(ncokMapper.map(dao));

            updateNppaEvent(nppaUnderUpdate);
            nppa = nppaUnderUpdate;
        }
        return Optional.of(nppaMapper.toDto(nppa));
    }

    private void unlockLaunchCommand(Ncok ncokUpdate) {
        if (ncokUpdate.isAppPresence() || ncokUpdate.isOtr1AppPresence() || ncokUpdate.isOtr2AppPresence()) {
            askuService.unlockCommandsDuringLunch();
            String unlockReason = ncokUpdate.isOtr1AppPresence() ? "AppOtr1" : ncokUpdate.isOtr2AppPresence() ? "AppOtr2" : "NcokApp";
            log.info("command output unlocked with {}", unlockReason);
        }
        if (ncokUpdate.getIsOtr1Lunched().equals(BaseProperty.OK)
            || ncokUpdate.getIsOtr2Lunched().equals(BaseProperty.OK)) {
            askuService.unlockCommandsDuringLunch();

            String unlockReason = ncokUpdate.getIsOtr1Lunched().equals(BaseProperty.OK) ?
                    "Otr1Lunched" :
                    "Otr2Lunched";
            log.info("command output unlocked with {}", unlockReason);
        }

    }

    public Optional<BynDto> getByn() {
        if (!(nppa != null && nppa.getByn() != null)) {
            return Optional.empty();
        }
        return Optional.of(bynMapper.toDto(nppa.getByn()));
    }

    @Transactional
    public Optional<NppaDto> updateByn(BynDto dto) {
        Byn bynUpdate = bynMapper.map(dto);

        Nppa nppaUnderUpdate = nppa != null ? Nppa.copy(nppa) : Nppa.copy(initializeNppa());

        if (nppaUnderUpdate.getByn() == null || !nppaUnderUpdate.getByn().equals(bynUpdate)) {
            BynDao dao = bynRepository.findFirstByOrderByUpdatedAtDesc().orElse(null);
            if (dao != null) {
                bynRepository.detach(dao);
            }
            long prevId = (dao != null && dao.getId() != null) ? dao.getId() : -1;

            dao = bynMapper.toDao(bynUpdate);
            dao.setId(null);
            dao = bynRepository.save(dao);
            if (dao.getId().equals(prevId)) {
                log.error("can't persist byn state. bynUnderUpdate is " + nppaUnderUpdate.getByn() +
                          " previousByn is " + nppa.getByn());
            }
            nppaUnderUpdate.setByn(bynMapper.map(dao));

            updateNppaEvent(nppaUnderUpdate);
            nppa = nppaUnderUpdate;
        }
        return Optional.of(nppaMapper.toDto(nppa));
    }


    private NppaEvent previousNppaEvent;
    private NppaEvent currentNppaEvent;

    private void initializeEvents() {
        this.currentNppaEvent = NppaEvent
                .builder()
                .nppa(this.nppa)
                .eventType(AdjacentSystemUpdateEventType.UNDEFINED)
                .source(this)
                .build();
    }

    private void updateNppaEvent(Nppa nppaUnderProcessing) {
        if (currentNppaEvent != null) {
            previousNppaEvent = currentNppaEvent;
        }
        try {
            AdjacentSystemUpdateEventType eventType;
            if (nppaUnderProcessing.isMalfunctionPresent()) {
                eventType = (AdjacentSystemUpdateEventType.ERROR_OCCURRED);
            } else {
                switch (nppaUnderProcessing.getStatus()) {
                    case OK -> eventType = AdjacentSystemUpdateEventType.DATA_UPDATE;
                    case NOT_CONNECTED -> eventType = AdjacentSystemUpdateEventType.NOT_CONNECTED;
                    default -> eventType = AdjacentSystemUpdateEventType.UNDEFINED;
                }
            }
            this.currentNppaEvent = NppaEvent.builder()
                    .eventType(eventType)
                    .nppa(nppaUnderProcessing)
                    .source(currentNppaEvent.getSource())
                    .build();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }


    void fireUpdateEvent() {
        if (currentNppaEvent == null) {
            log.info("no SUTO event for update");
            return;
        }

        if (currentNppaEvent.equals(previousNppaEvent)) {
            log.debug("NPPA event is the same as the previous");
            return;
        } else {
            previousNppaEvent = currentNppaEvent;
        }
        Broadcaster.broadcast(currentNppaEvent);
    }


    @Value("${nppa.command.command-validity-time-sec}")
    private Integer commandValidityTime;

    public void setNppaCommand(@NotNull
                               @Valid
                               NppaCommand command) {

        if (!command.getAdjacentSystem().equals(AdjacentSystemType.NCOK) &&
            !command.getAdjacentSystem().equals(AdjacentSystemType.BYN)) {
            log.error("received unexpected command " + command + " in the" + this.getClass().getSimpleName());
            return;
        }

        CommandValidationResult validationResult = validateCommand(command);
        if (!validationResult.isValid()) {
            String errorMsg = validationResult.errorMessageUa();
            log.error(errorMsg + " " + this);
            try {
                showValidationErrorMsg(errorMsg, AdjacentSystemType.NPPA, this.getClass());
            } catch (Exception e) {
                log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
            }
            return;
        }

        if (this.topMostCommand != null && this.topMostCommand.equals(command)) {
            if (!LocalDateTime.now().isAfter(commandGenerationTime.plusSeconds(commandValidityTime))) {
                return;
            }
        }

        this.commandGenerationTime = command.getGenerationTime();

        this.topMostCommand = command;
        command = ncokHistoryRepository.save(command);

        log.info("added Nppa command " + command + " at" + commandGenerationTime.toString());
    }

    public Optional<NppaCommand> getCommand() {
        if (topMostCommand == null && commandGenerationTime == null) {
            String message = "none active command was found for NCOK";
            log.info(message);
            throw new NotFoundException("message");
        }

        if (LocalDateTime.now().isAfter(commandGenerationTime.plusSeconds(commandValidityTime))) {
            String message = "commandValidityTime has expired";
            log.info(message);
            throw new NotFoundException(message);
        }
        Optional<NppaCommand> validCommand = Optional.ofNullable(topMostCommand);
        if (validCommand.isEmpty()) {
            String message = "none valid NCOK command was found";
            log.info(message);
            throw new NotFoundException(message);
        }
        return validCommand;
    }

    public CommandValidationResult validateCommand(@NotNull NppaCommand command) {
        NppaCommandDAO commandDAO = nppaCommandRepository.findByCommandName(command.getCommand());
        if (commandDAO == null) {
            log.error("attempt to send unknown command declined " + command);
            return new CommandValidationResult(false, "attempt to send unknown command declined " + command,
                    "Неможливо надіслати невідомоу команду " + command);
        }

        CommandValidationResult availabilityDuringLaunchValidation = checkAvailabilityDuringLaunch(command.getCommand());
        if (!availabilityDuringLaunchValidation.isValid()) {
            return availabilityDuringLaunchValidation;
        }

        if (LocalDateTime.now().isBefore(commandGenerationTime.plusSeconds(commandValidityTime))) {
            log.info("attempt to send command {} before time limit ({} sec) ends ", command, commandValidityTime);

            return new CommandValidationResult(false, "attempt to send command {} before time limit ({} sec) ends "
                                                      + command,
                    "Неможливо виконати команду   " + command.getCaption().toUpperCase() + ". Попередня команда " +
                    " не оброблена контролером або не вичерпан ліміт часу. Зачекайте " + commandValidityTime + " сек." +
                    " та повторіть спробу");
        }

        if (!commandDAO.isHasBlocker()) {
            return new CommandValidationResult(true, "", "");
        }

        CommandValidationResult operationValidationResult = validateOperatingMode(command, commandDAO);
        if (!operationValidationResult.isValid()) {
            return operationValidationResult;
        }

        CommandValidationResult toggleValidationInNCOKNotConnected = validateToggleWIthNCOKNotConnected(commandDAO);
        if (!toggleValidationInNCOKNotConnected.isValid()) {
            return toggleValidationInNCOKNotConnected;
        }

        return validateReadiness(commandDAO);
    }

    private CommandValidationResult validateToggleWIthNCOKNotConnected(NppaCommandDAO commandDAO) {
        CommandValidationResult validCommand = new CommandValidationResult(true, "", "");

        if (commandDAO.getAdjacentSystem() != AdjacentSystemType.NCOK) {
            return validCommand;
        }

        if (ncokCommandsEnabledInNotConnected.contains(commandDAO.getCommand()) &&
            getNcok().isPresent()) {
            AdjacentSystemStatus ncokStatus = getNcok().get().systemStatus() != null ? getNcok().get().systemStatus() :
                    AdjacentSystemStatus.UNDEFINED;

            return ncokStatus.equals(AdjacentSystemStatus.NOT_CONNECTED) ? validCommand :
                    new CommandValidationResult(false, "Command" + commandDAO.getCommand() + " available only in NCOK.NOT_CONNECTED mode",
                            "Команда " + commandDAO.getCaption() + " доступна тільки в режимі НЦОК - Немає зв'язку");
        }

        return validCommand;
    }

    private CommandValidationResult validateOperatingMode(@NotNull NppaCommand command, @NotNull NppaCommandDAO commandDAO) {
        NppaOperatingMode activeOperatingMode = NppaOperatingMode.NOT_SELECTED;
        try {
            if (commandDAO.getAdjacentSystem() == AdjacentSystemType.NCOK) {
                activeOperatingMode = nppa.getNcok().getOperatingMode() != null ? nppa.getNcok().getOperatingMode() :
                        NppaOperatingMode.NOT_SELECTED;
            } else if (commandDAO.getAdjacentSystem() == AdjacentSystemType.BYN) {
                activeOperatingMode = nppa.getByn().getOperatingMode() != null ? nppa.getByn().getOperatingMode() :
                        NppaOperatingMode.NOT_SELECTED;
            }
        } catch (NullPointerException e) {
            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
        }

        if (commandDAO.isUsedInCombatMode() && activeOperatingMode != NppaOperatingMode.COMBAT) {
            log.error("Attempt to send NPPA command declined " + commandDAO + ". Command used in " + activeOperatingMode +
                      " while allowed in  " + NppaOperatingMode.COMBAT);
            return new CommandValidationResult(false,
                    "Switch to " + NppaOperatingMode.COMBAT + " mode prior to send command",
                    "Для використання команди ввімкніть режим " + NppaOperatingMode.COMBAT.getValueUa());
        } else if (commandDAO.isUsedInTestMode() && activeOperatingMode != NppaOperatingMode.TESTING) {
            return new CommandValidationResult(false,
                    "Switch to " + NppaOperatingMode.TESTING + " mode prior to send command",
                    "Для використання команди ввімкніть режим " + NppaOperatingMode.TESTING.getValueUa());
        }
        return new CommandValidationResult(true, "", "");
    }

    private CommandValidationResult validateReadiness(@NotNull NppaCommandDAO commandDAO) {
        if (commandDAO.getAvailableAtReadiness().size() == 0) {
            return new CommandValidationResult(true, "", "");
        }

        Readiness splReadiness = askuService.getSplReadiness().orElse(Readiness.UNDEFINED);
        if (commandDAO.getAvailableAtReadiness().stream().unordered().noneMatch(r -> r.equals(splReadiness))) {
            log.error("Attempt to send NPPA command declined " + commandDAO + ". Command used in " + splReadiness +
                      " while allowed in  " + commandDAO.getAvailableAtReadiness().stream()
                              .map(Readiness::getDescription)
                              .collect(Collectors.joining(", ")));

            return new CommandValidationResult(false,
                    "Command can be used only in on of the followed Spl readiness "
                    + commandDAO.getAvailableAtReadiness().stream()
                            .map(Readiness::getDescription)
                            .collect(Collectors.joining(", ")),
                    "Для використання команди перейдіть до  режиму - " + commandDAO.getAvailableAtReadiness().stream()
                            .map(Readiness::getValueUa)
                            .collect(Collectors.joining(", чи ")));
        }
        return new CommandValidationResult(true, "", "");
    }

    public List<NppaCommand> getAvailableNppaCommands() {
        return nppaCommandRepository.getAllCommands().stream()
                .map(nppaCommandMapper::map)
                .collect(Collectors.toList());
    }

    private final ScheduledExecutorService executorService = Executors.newScheduledThreadPool(1);
    private final long commandCommitTimeLimit;

    @Async
    public ListenableFuture<Boolean> checkForCommandCommitDelayed(NppaCommand nppaCommand) {

        ListenableFutureTask<Boolean> future = new ListenableFutureTask<>(() -> {
            try {
                TimeUnit.MILLISECONDS.sleep(1000);

                while (LocalDateTime.now().isBefore(nppaCommand.getGenerationTime().plusSeconds(commandCommitTimeLimit))) {
                    if (checkCommandCommittedAndInTheTop(nppaCommand.getCommand())) {
                        executorService.shutdown();
                        return true;
                    }

                    TimeUnit.MILLISECONDS.sleep(200);
                }

                return false;
            } catch (InterruptedException e) {
                log.error(Arrays.toString(e.getStackTrace()));
                return false;
            }
        });
        Executors.newSingleThreadExecutor().submit(future);

        return future;
    }

    public void switchAfterLunchLock(NppaCommand command) {
        if (commandsFroLaunchSettings.stream().anyMatch(e -> e.equalsIgnoreCase(command.getCommand()))) {
            askuService.lockCommandsDuringLaunch();
            log.info("command output locked during launch with command " + command);
        }
        if (commandsFroLaunchSettingsExclusions.stream().anyMatch(e -> e.equalsIgnoreCase(command.getCommand()))) {
            askuService.unlockCommandsDuringLunch();
            log.info("command output unlocked with command " + command);
        }
    }


    public boolean acknowledgeLaunchCancel(boolean isLeft, String plantMissile, LaunchResult launchResult) {
        if (launchResult != LaunchResult.FAILURE) {
            return false;
        }
        AtomicBoolean acknowledged = new AtomicBoolean(false);
        getAvailableNcokCombatModeCommands().stream()
                .filter(command -> command.getCommand().equals(isLeft ? OTR_1_AUTOMATIC_LAUNCH_CANCEL_ACKNOWLEDGMENT : OTR_2_AUTOMATIC_LAUNCH_CANCEL_ACKNOWLEDGMENT))
                .findFirst().ifPresentOrElse((acknowledgeCommand) ->
                        {
                            NppaCommand command = NppaCommand.builder()
                                    .command(acknowledgeCommand.getCommand())
                                    .caption(acknowledgeCommand.getCaption())
                                    .commandState(acknowledgeCommand.getCommandState())
                                    .adjacentSystem(acknowledgeCommand.getAdjacentSystem())
                                    .generationTime(LocalDateTime.now())
                                    .originator(userService.getUserName())
                                    .build();

                            if (LocalDateTime.now().isBefore(commandGenerationTime.plusSeconds(commandValidityTime))) {
                                resetActiveCommand();
                            }

                            CommandValidationResult validationResult = validateCommand(command);
                            if (!validationResult.isValid()) {
                                log.error("can't acknowledge automatic cancell of launch of OTR " + plantMissile + " because of " +
                                          validationResult.errorMessage() + " " + validationResult.errorMessageUa());
                                return;
                            }

                            setNppaCommand(command);
                            acknowledged.set(true);

                            // TODO: 1/8/2025 add task to check if command was committed and if no - then retry several times
                            //  there is relevant method to check if command was committed

                        },
                        () -> log.error("can't find command to handle" + (isLeft ?
                                OTR_1_AUTOMATIC_LAUNCH_CANCEL_ACKNOWLEDGMENT :
                                OTR_2_AUTOMATIC_LAUNCH_CANCEL_ACKNOWLEDGMENT)));

        return acknowledged.get();
    }

    private boolean resetActiveCommand() {
        boolean noActiveCommandsLeft = false;

        Optional<NppaCommand> activeCommand = getActiveCommand();
        if (activeCommand.isPresent()) {
            NppaCommand command = activeCommand.get();
            command.setCommandState(CommandState.CANCELLED);
            ncokHistoryRepository.save(command);
            commandGenerationTime = LocalDateTime.MIN;

            log.info("command " + command + " was cancelled because of launch failure processing request");
            return noActiveCommandsLeft = true;


        } else {
            log.info("no active nppa command found to reset");
            return true;
        }
    }
}
