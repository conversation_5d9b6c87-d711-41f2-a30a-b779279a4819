package com.deb.spl.control.service;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.bins.LogRecordDirection;
import com.deb.spl.control.data.bins.RestRequestDAO;
import com.deb.spl.control.data.msu.MeteoStationUnit;
import com.deb.spl.control.repository.bins.NavigationRestRequestRepository;
import com.deb.spl.control.utils.logs.LoggingJsonDecoder;
import com.deb.spl.control.utils.logs.LoggingJsonEncoder;
import com.deb.spl.control.views.events.AdjacentSystemUpdateEventType;
import com.deb.spl.control.views.events.MsuEvent;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.sf.marineapi.nmea.parser.SDSParser;
import net.sf.marineapi.nmea.sentence.SDSSentence;
import net.sf.marineapi.nmea.util.MSU.MsuFunctionSwitchCommand;
import net.sf.marineapi.nmea.util.MSU.MsuHeatingMode;
import net.sf.marineapi.nmea.util.MSU.MsuPosture;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Arrays;

import static com.deb.spl.control.data.AdjacentSystemStatus.*;

@Service
@Slf4j
@Data
public class MsuService {
    private final NavigationRestRequestRepository navigationRestRequestRepository;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    private MeteoStationUnit meteoStationUnit;

    @Value("${msu.connection-time-out-sec}")
    private int MSU_CONNECTION_TIMEOUT;
    private LocalDateTime lastDataUpdateDT = LocalDateTime.now();

    private WebClient msuWebClient;

    @Value("${msu.data-source-endpoint.url}")
    private String msuServiceUrl;
    private String MSU_DEFAULT_URL = "http://localhost:8081";

    private final StringBuffer loggedEncoderJsonBuffer = new StringBuffer();
    private final StringBuffer loggedDecoderJsonBuffer = new StringBuffer();
    private final LoggingJsonEncoder loggingEncoder;
    private final LoggingJsonDecoder loggingDecoder;

    @Getter
    private AdjacentSystemStatus status = UNDEFINED;


    public MsuService(NavigationRestRequestRepository navigationRestRequestRepository,
                      @Value("${msu.data-source-endpoint.url}") String serviceUrl,
                      @Value("${msu.connection-time-out-sec}") int timeOut) {
        msuServiceUrl = serviceUrl != null ? serviceUrl : MSU_DEFAULT_URL;
        MSU_CONNECTION_TIMEOUT = timeOut;

        this.navigationRestRequestRepository = navigationRestRequestRepository;

        meteoStationUnit = new MeteoStationUnit();
//        meteoStationUnit.setNormal(true);
        meteoStationUnit.setConnected(false);

        loggingEncoder = new LoggingJsonEncoder(
                data -> {
                    loggedEncoderJsonBuffer.append(new String(data));
                });

        loggingDecoder = new LoggingJsonDecoder(
                data -> {
                    loggedDecoderJsonBuffer.append(new String(data));
                });

        msuWebClient = WebClient.builder()
                .codecs(clientCodecConfigurer -> {
                    clientCodecConfigurer.defaultCodecs().jackson2JsonDecoder(loggingDecoder);
                })
                .filters(exchangeFilterFunctions -> {
                    exchangeFilterFunctions.add(logRequest());
                    exchangeFilterFunctions.add(logResponse());
                })
                .baseUrl(msuServiceUrl)
                .build();

    }


    @Value("${msu.heating-command-delay-ms}")
    int heatingModeDelay;

    /**
     * @param command NMEA String represented MSU command
     */
    @Async
    public void sendCommand(String command) {
        try {
            SDSSentence sdsSentence = new SDSParser(command);

            if (sdsSentence.getHeatingOnCommandState().equals(MsuFunctionSwitchCommand.REQUEST_IS_PRESENT)) {
                command = getHeatingCommand(sdsSentence);
                meteoStationUnit.setHeatingMode(MsuHeatingMode.ON);
            }


            if (sdsSentence.getHeatingOffCommandState().equals(MsuFunctionSwitchCommand.REQUEST_IS_PRESENT)) {
                command = getHeatingCommand(sdsSentence);
                meteoStationUnit.setHeatingMode(MsuHeatingMode.OFF);
            }

            loggedEncoderJsonBuffer.setLength(0);
            loggedEncoderJsonBuffer.append(command);

            Command body = new Command(command);

            ResponseEntity response = msuWebClient.post()
                    .uri("/msu/command")
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(Mono.just(body), Command.class)
                    .retrieve()
                    .toEntity(String.class)
                    .block(Duration.ofMillis(5000));

            update();
        } catch (Exception e) {
            log.error(e.getMessage() + "\n " + Arrays.toString(e.getStackTrace()));
        }
    }

    record Command(String command) {
    }


    @Async
    public MeteoStationUnit update() {
        MeteoStationUnit previousState = meteoStationUnit;

        try {
            Mono<MeteoStationUnit> msuResponse = msuWebClient.get()
                    .uri("/msu")
                    .retrieve()
                    .bodyToMono(MeteoStationUnit.class);

            meteoStationUnit = msuResponse.block();

             if (meteoStationUnit == null && !isConnected()) {
                status = NOT_CONNECTED;
            } else {
                meteoStationUnit.setConnected(true);
                lastDataUpdateDT = LocalDateTime.now();
                status = meteoStationUnit.isMalfunctionPresent() ? ERROR : OK;
            }

        } catch (Exception e) {
            if (!isConnected()) {
                status = NOT_CONNECTED;
                meteoStationUnit.setConnected(false);
            }
            log.error(e.getMessage() + "\n" + Arrays.toString(e.getStackTrace()));
        }

        fireUpdateEvent(previousState);
        return meteoStationUnit;
    }

    public boolean isConnected() {
        return LocalDateTime.now().isBefore(lastDataUpdateDT.plusSeconds(MSU_CONNECTION_TIMEOUT));
    }

    private String getHeatingCommand(SDSSentence sdsSentence) throws InterruptedException {
        String command;
        Thread.sleep(heatingModeDelay);
        if (meteoStationUnit != null) {
            if (meteoStationUnit.getPosture().equals(MsuPosture.ANTENNA_ROD_PULLING_IN)) {
                sdsSentence.setTransportingPostureCommandState(MsuFunctionSwitchCommand.REQUEST_IS_PRESENT);
            } else if (meteoStationUnit.getPosture().equals(MsuPosture.ANTENNA_ROD_PULLING_OUT)) {
                sdsSentence.setOperatingPostureCommandState(MsuFunctionSwitchCommand.REQUEST_IS_PRESENT);
            }
        }
        command = sdsSentence.toSentence();
        return command;
    }

    private ExchangeFilterFunction logRequest() {
        return ExchangeFilterFunction.ofRequestProcessor(clientRequest -> {

            //append clientRequest method and url
            RestRequestDAO requestDAO = RestRequestDAO.builder()
                    .url(UriComponentsBuilder.fromUri(clientRequest.url()).replaceQuery("").toUriString())
                    .method(clientRequest.method().toString())
                    .requestQueryString(clientRequest.url().getQuery())
                    .adjacentSystem(AdjacentSystemType.MSU)
                    .direction(LogRecordDirection.OUT)
                    //todo figure out how to ge body
                    .build();

            if (clientRequest.httpRequest() != null) {
                requestDAO.setRequestQueryString(clientRequest.httpRequest().toString());
            }
            if (clientRequest.body() != null) {
                String payload = requestDAO.getCachedPayload() == null ? "" : requestDAO.getCachedPayload();

                if (loggedEncoderJsonBuffer.length() > 0) {
                    payload += loggedEncoderJsonBuffer;
                    loggedEncoderJsonBuffer.setLength(0);
                }

                requestDAO.setCachedPayload(payload);
            }

            clientRequest
                    .headers()
                    .forEach((name, values) -> values.forEach(value -> requestDAO.addHeaders(String.format("{%s} = {%s}", name, value)) /* append header key/value */));

            RestRequestDAO saved = null;
            if (requestDAO.isConsiderable() || isValidServiceRequest(requestDAO.getMethod(), requestDAO.getUrl())) {
                saved = navigationRestRequestRepository.save(requestDAO);
            }

            log.info(saved != null ? saved.toString() : requestDAO.toString());

            return Mono.just(clientRequest);
        });

    }

    boolean isValidServiceRequest(String method, String uri) {
        if (!method.equalsIgnoreCase("POST")) {
            return false;
        }
        return uri.equalsIgnoreCase("http://localhost:8081/msu/reset")
               || uri.equalsIgnoreCase("http://localhost:8081/msu/calibration")
               || uri.equalsIgnoreCase("http://localhost:8081/msu/start");
    }

    private ExchangeFilterFunction logResponse() {
        return ExchangeFilterFunction.ofResponseProcessor(clientResponse -> {

            RestRequestDAO requestDAO = RestRequestDAO.builder()
                    .adjacentSystem(AdjacentSystemType.MSU)
                    .direction(LogRecordDirection.RESPONSE)
                    //todo figure out how to ge body
                    .build();

            if (loggedDecoderJsonBuffer.length() > 0) {
                String payload = requestDAO.getCachedPayload() == null ? "" : requestDAO.getCachedPayload();

                payload += loggedDecoderJsonBuffer;
                loggedDecoderJsonBuffer.setLength(0);

                requestDAO.setCachedPayload(payload);
            }

            clientResponse
                    .headers().asHttpHeaders()
                    .forEach((name, values) -> values.forEach(value -> requestDAO.addHeaders(String.format("{%s} = {%s}", name, value)) /* append header key/value */));

            RestRequestDAO saved = null;
            if (requestDAO.isConsiderable()) {
                saved = navigationRestRequestRepository.save(requestDAO);
            }

            log.info(saved != null ? saved.toString() : requestDAO.toString());

            return Mono.just(clientResponse);
        });
    }


    private void fireUpdateEvent(MeteoStationUnit previousState) {
        AdjacentSystemUpdateEventType eventType;

        if (status.equals(NOT_CONNECTED)) {
            eventType = AdjacentSystemUpdateEventType.NOT_CONNECTED;

//            meteoStationUnit = MeteoStationUnit.builder()
//                    .windDirection(-360.99)
//                    .windSpeed(-99.99)
//                    .airTemperature(-99.99)
//                    .voltage(-99.99)
//                    .msuTemperature(-99.99)
//                    .posture(null)
//                    .operatingMode(null)
//                    .heatingMode(null)
//                    .heatingModePermission(MsuHeatingModePermission.DISABLED)
//                    .temperatureSensorBlowing(MsuTemperatureSensorBlowing.OFF)
//                    .temperatureSensorMalfunction(null)
//                    .windSpeedSensorMalfunction(null)
//                    .electricalDriveMalfunction(null)
//                    .endSwitchesMalfunction(null)
//                    .voltageMalfunction(null)
//                    .postureSwitchingMalfunction(MsuPostureSwitchMalfunction.NORMAL)
//                    .dataExchangeMalfunction(MsuDataExchangeMalfunction.NORMAL)
//                    .malfunctionPresent(false)
//                    .build();
        } else if (meteoStationUnit == null) {
            eventType = AdjacentSystemUpdateEventType.UNDEFINED;
        } else if (meteoStationUnit.isMalfunctionPresent()) {
            eventType = (AdjacentSystemUpdateEventType.ERROR_OCCURRED);
        } else {
            eventType = (AdjacentSystemUpdateEventType.DATA_UPDATE);
        }

        MsuEvent dataChangeEvent = new MsuEvent(meteoStationUnit, this, eventType);
        Broadcaster.broadcast(dataChangeEvent);
    }

}
