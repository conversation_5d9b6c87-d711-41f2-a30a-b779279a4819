package com.deb.spl.control.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Profile("!disable_ppo_task_executor_service")
@Component
@Slf4j
@EnableAsync
public class PpoTaskExecutionService {
    private final PpoService ppoService;

    public PpoTaskExecutionService(PpoService ppoService) {
        this.ppoService = ppoService;
    }

    @Scheduled(initialDelay = 100,fixedDelay = 1000)
    public void risePpoUpdateEvent(){
        log.debug("calling for PPO event " + this.getClass());
        ppoService.fireUpdateEvent();
    }
}
