package com.deb.spl.control.service;

import com.deb.spl.control.controller.NotFoundException;
import com.deb.spl.control.data.AdjacentSystemCommand;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.nppa.CommandValidationResult;
import com.deb.spl.control.repository.HistoryRepository;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.views.adjacentSystems.utils.ConfigurationUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import javax.transaction.Transactional;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

@Slf4j
public class ServiceWithCommandCommitment<T extends AdjacentSystemCommand> {
    protected final HistoryRepository<T, Long> historyRepository;
    private AskuService askuService;
    protected T topMostCommand;
    protected LocalDateTime commandGenerationTime = LocalDateTime.MIN;
    @Getter
    private final int commandValidityTimeSec;

    @Value("#{'${nppa.command.launch.control}'.split(',')}")
    List<String> commandsFroLaunchSettings;
    @Value("#{'${nppa.command.launch.control.exclude}'.split(',')}")
    List<String> commandsForLaunchSettingsExclusions;

    public ServiceWithCommandCommitment(HistoryRepository historyRepository,
                                        int commandValidityTimeSec,
                                        AskuService askuService) {
        this.historyRepository = historyRepository;
        this.commandValidityTimeSec = commandValidityTimeSec;
        this.askuService = askuService;
    }

    public Optional<T> removeCommand(@NotNull Long id) {
        if (topMostCommand == null) {
            String message = "none active command was found. Requested id " + id;
            log.info(message);
            throw new NotFoundException(message);
        }

        if (LocalDateTime.now().isAfter(topMostCommand.getGenerationTime().plusSeconds(commandValidityTimeSec))) {
            topMostCommand = null;
            commandGenerationTime = LocalDateTime.MIN;

            String message = "none active command was found. Requested id " + id;
            log.info(message);
            throw new NotFoundException(message);
        }

        if (!topMostCommand.getId().equals(id)) {
            String activeCommandMsg = topMostCommand != null ? ("active command is " + topMostCommand.toString()) : " active command is null";
            log.warn("Attempt to commit a command that doesn't exist id:" + id + " " + activeCommandMsg);
            throw new NotFoundException(activeCommandMsg);
        }

        Optional<T> removedCommand = Optional.empty();
        synchronized (this) {
            removedCommand = Optional.ofNullable(topMostCommand);
            this.topMostCommand = null;
            commandGenerationTime = LocalDateTime.MIN;

            log.info("Command wit Id {} was accepted by plc at {}", id, LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd/MM/yyyy\tE HH:mm:ss")));
        }

        return removedCommand;
    }

    @Transactional
    public Optional<T> setCommandTimeStampById(@NotNull Long id) {
        Optional<T> foundCommand = historyRepository.findById(id);
        if (foundCommand.isEmpty()) {
            String message = "Command doesn't exist in DB. Requested id " + id;
            log.error(message);
            throw new NotFoundException(message);
        }

        T logRecord = null;
        LocalDateTime executionTs = LocalDateTime.now();
        if (foundCommand.isPresent()) {
            logRecord = foundCommand.get();
            logRecord.setExecutionTime(executionTs);
            logRecord = historyRepository.save(logRecord);
        }

        if (logRecord != null) {
            log.debug("Command wit Id {} was executed at {}", id, executionTs);
        }

        return Optional.ofNullable(logRecord);
    }

    /**
     * @param commandName command alias to filter against column "command"
     * @return true if command with selected name is on the top of result
     */
    public boolean checkCommandCommittedAndInTheTop(String commandName) {
        //get top and check in execution time not null
        Optional<T> topCommand = historyRepository.getTop();

        return (topCommand.isPresent() && topCommand.get().getCommand().equalsIgnoreCase(commandName)
                && topCommand.get().getExecutionTime() != null);
    }

    Optional<T> getActiveCommand() {
        if(LocalDateTime.now().isAfter(commandGenerationTime.plusSeconds(commandValidityTimeSec))) {
            return Optional.empty();
        }

        return historyRepository.getTop();
    }

    public boolean topMostCommandIsLaunch() {
        Optional<T> topCommand = historyRepository.getTop();

        if (topCommand.isEmpty()) {
            return false;
        }

        return commandsFroLaunchSettings.stream().anyMatch(e -> e.equalsIgnoreCase(topCommand.get().getCommand()));
    }

    public CommandValidationResult checkAvailabilityDuringLaunch(String commandName) {
        if (askuService.isCommandsLockDuringLaunch() && topMostCommandIsLaunch()
            && commandsForLaunchSettingsExclusions.stream().noneMatch(e -> e.equalsIgnoreCase(commandName))) {
            log.info("attempt to send command {} during launch was blocked {}", commandName, this);

            return new CommandValidationResult(false, "attempt to send command {} during launch was blocked {}"
                                                      + commandName,
                    "Не можливо обробити " + commandName +
                    " Відбуваються пускові операції.");
        }
        return new CommandValidationResult(true, "", "");
    }

    CommandValidationResult validateCommandOnTimeDelay(T command) {
        if (LocalDateTime.now().isBefore(commandGenerationTime.plusSeconds(commandValidityTimeSec))) {
            log.info("attempt to send command {} before time limit ({} sec) ends ", command, commandValidityTimeSec);

            return new CommandValidationResult(false, "attempt to send command {} before time limit ({} sec) ends "
                                                      + command,
                    "Неможливо виконати команду   " + command.getCaption().toUpperCase() + ". Попередня команда " +
                    " не оброблена контролером або не вичерпан ліміт часу. Зачекайте " + commandValidityTimeSec + " сек." +
                    " та повторіть спробу");
        }

        return new CommandValidationResult(true, "", "");
    }

    void showValidationErrorMsg(String errorMsg, AdjacentSystemType systemType, Class sender) {
        ConfigurationUtils.getErrorNotification(errorMsg, "",
                0, true).open();
    }
}
