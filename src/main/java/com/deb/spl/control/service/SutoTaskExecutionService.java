package com.deb.spl.control.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


@Profile("!disable_suto_task_executor_service")
@Component
@Slf4j
@EnableAsync
public class SutoTaskExecutionService {
    private final SutoService sutoService;

    public SutoTaskExecutionService(SutoService sutoService) {
        this.sutoService = sutoService;
    }

    @Scheduled(initialDelay = 100,fixedDelay = 1000)
    public void riseSutoUpdateEvent(){
        log.debug("calling for SUTO event " + this.getClass());
        sutoService.fireUpdateEvent();
    }
}
