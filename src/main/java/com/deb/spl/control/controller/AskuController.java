package com.deb.spl.control.controller;

import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.asku.*;
import com.deb.spl.control.service.NppaService;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.service.asku.PdpService;
import com.deb.spl.control.utils.AskuUtils;
import com.deb.spl.control.views.events.CountdownEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.CharEncoding;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;

@RestController
@RequestMapping("/api/v1/asku/")
@Slf4j
public class AskuController {
    private final AskuService askuService;
    private final NppaService nppaService;
    private final RocketMapper rocketMapper;
    private final LaunchInitialDataMapper initialDataMapper;
    private final MultiValueMap<String, String> responseTokenHeader;
    private final PdpService pdpService;

    public AskuController(AskuService askuService,
                          RocketMapper rocketMapper,
                          LaunchInitialDataMapper initialDataMapper,
                          @Value("${plc.request.token}") String plcRequestToken,
                          @Value("${plc.response.token}") String plcResponseToken, NppaService nppaService, PdpService pdpService) {
        this.askuService = askuService;
        this.rocketMapper = rocketMapper;

        this.initialDataMapper = initialDataMapper;
        this.nppaService = nppaService;
        this.pdpService = pdpService;
        Map<String, List<String>> tokenMap = new WeakHashMap<>();
        tokenMap.put("Token", List.of(plcResponseToken));
        responseTokenHeader = new LinkedMultiValueMap<>(tokenMap);
    }

    @GetMapping
    public ResponseEntity<AskuDto> getAsku() {
        return ResponseEntity.ok(askuService.getAskuDto());
    }

    @Deprecated
    @PostMapping("initial_data/{plantMissile}")
    public ResponseEntity<String> updateLaunchInitialData(@PathVariable
                                                          @Valid
                                                          String plantMissile,
                                                          @RequestBody LaunchInitialDataDto dto) {
        return new ResponseEntity<>(HttpStatus.GONE);
    }

    @PostMapping("fire_order/{plantMissile}")
    public ResponseEntity<String> setFireOrder(@PathVariable
                                               @NotNull
                                               String plantMissile,
                                               @Valid @RequestBody FireOrderDto dto) {

        try {
            if (askuService.setLaunchInitialData(plantMissile, dto.launchInitialData(),
                    Optional.of(dto.orderInfo())) != null) {
                return new ResponseEntity<>(HttpStatus.OK);
            } else {
                throw new NotFoundException("Ракет на знайдено");
            }
        } catch (NotFoundException notFound) {
            log.error(Arrays.toString(notFound.getStackTrace()));
            return new ResponseEntity<>(notFound.getMessage(), HttpStatus.NOT_FOUND);
        } catch (ServerErrorException serverError) {
            log.error(Arrays.toString(serverError.getStackTrace()));
            return new ResponseEntity<>(serverError.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("fire_order")
    public ResponseEntity getFireOrder(@RequestParam(name = "isLeft", defaultValue = "false") boolean isLeft) {
        Optional<RocketDto> rocketDto = askuService.getRocketDto(isLeft);
        if (rocketDto.isEmpty()) {
            return new ResponseEntity<>("ракету на знайдено", HttpStatus.NOT_FOUND);
        }

        Optional<OrderInfoDto> orderInfoDto = askuService.getOrderInfoDto(isLeft);

        return orderInfoDto.isPresent() ? new ResponseEntity<>(new FireOrderDto(rocketDto.get().initialData(), orderInfoDto.get()), HttpStatus.OK) :
                new ResponseEntity<>("даних про наказ не знайдено", HttpStatus.NOT_FOUND);
    }

    @GetMapping("rocket_form_data")
    public RocketFormDataDto getRocketFormData(@RequestParam(name = "isLeft", defaultValue = "true")
                                               boolean isLeft) {
        return askuService.getRocketFormDataDto(isLeft).orElseThrow(NotFoundException::new);
    }

    @GetMapping("rocket")
    public RocketDto getRocket(@RequestParam(name = "isLeft", defaultValue = "true")
                               boolean isLeft) {
        return askuService.getRocketDto(isLeft).orElseThrow(NotFoundException::new);
    }

    @PostMapping("rocket")
    public AskuDto setRocket(@RequestParam(name = "isLeft", defaultValue = "true")
                             boolean isLeft,
                             @RequestBody
                             RocketDto rocketDto,
                             @RequestParam(name = "encoding", defaultValue = CharEncoding.UTF_8)
                             String encoding) {
        if (rocketDto.formData() != null && !rocketDto.formData().plantMissile().isBlank()) {
            if (encoding.equals(CharEncoding.UTF_16)) {
                rocketDto = AskuUtils.convertPlantMissileEncodingFromUtf16ToUtf8(rocketDto, askuService, rocketMapper);
            } else if (encoding.equals(CharEncoding.UTF_8)) {
                rocketDto = AskuUtils.convertPlantMissileEncodingFromUtf8ToUtf16(rocketDto, askuService, rocketMapper);
            }
        }

        return askuService.setRocket(isLeft, rocketDto).orElseThrow(NotFoundException::new);
    }

    @PostMapping("encoding/test")
    public ResponseEntity testUtf16Array(@RequestBody byte[] array) {
        if (array == null || array.length == 0) {
            return new ResponseEntity(HttpStatus.BAD_REQUEST);
        }
        String number = new String(array, StandardCharsets.UTF_16);

        return new ResponseEntity(HttpStatus.OK);
    }

    @GetMapping("encoding/test")
    public ResponseEntity<byte[]> testUtf16Array(@RequestBody String sample) {


        return new ResponseEntity(sample.getBytes(StandardCharsets.UTF_16), HttpStatus.OK);
    }

    @DeleteMapping("rocket/{plant_missile}/remove")
    @ResponseStatus(HttpStatus.NOT_FOUND)
    AskuDto removeRocket(@PathVariable(name = "plant_missile")
                         String plantMissile,
                         @RequestParam(name = "isLeft")
                         boolean isLeft) {
        AskuDto removedRocket = askuService.removeRocket(isLeft, plantMissile);
        askuService.updateRocketInfoInRemote(plantMissile, LaunchResult.REMOVE,
                new LaunchResultDescription("", "Видалено за командою ПЛК"));

        return removedRocket;
    }

    @PostMapping("rocket/{plant_missile}/launch")
    ResponseEntity<AskuDto> launchRocket(@PathVariable(name = "plant_missile")
                                         String plantMissile,
                                         @RequestParam(name = "isLeft")
                                         boolean isLeft,
                                         @RequestParam(name = "launch_result") @NotNull
                                         LaunchResult launchResult,
                                         @RequestBody LaunchResultDescription launchResultDescription) {
        AskuDto askuDto;
        //failure can be handled once per 30 sec because of command send timeout
        // TODO: 1/10/2025 return error if command cant be processed
        boolean acknowledged = !launchResult.equals(LaunchResult.FAILURE) || nppaService.acknowledgeLaunchCancel(isLeft, plantMissile, launchResult);
        if (!acknowledged) {
            return new ResponseEntity<>(askuService.getAskuDto(), HttpStatus.CONFLICT);
        }
        askuDto = askuService.launchRocket(isLeft, plantMissile, launchResult, launchResultDescription);
        askuService.updateRocketInfoInRemote(plantMissile, launchResult, launchResultDescription);

        return new ResponseEntity<>(askuDto, HttpStatus.OK);
    }

    @PostMapping("rocket/sensors")
    public ResponseEntity<String> setTemperature(@RequestParam(name = "temperature") Double temperature,
                                                 @RequestParam(name = "isLeft")
                                                 boolean isLeft) {

        Rocket rocket = askuService.updateTemperatureFromSensor(isLeft, temperature);
        if (rocket.getSensorTemperature() == temperature) {
            return new ResponseEntity<>(HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }


    @GetMapping("state")
    public AskuStateDto getAskuReadiness() {
        return new AskuStateDto(askuService.getSplReadiness().orElse(Readiness.UNDEFINED),
                (askuService.getAskuEntity().getLeftRocket() != null &&
                 askuService.getAskuEntity().getLeftRocket().isTechnicalCondition()),
                (askuService.getAskuEntity().getRightRocket() != null &&
                 askuService.getAskuEntity().getRightRocket().isTechnicalCondition()));
    }

    record AskuStateDto(Readiness readiness,
                        boolean leftRocketCondition,
                        boolean rightRocketCondition) {
    }

    @GetMapping("tlc/open_keys")
    public List<String> getTlcKeys(@RequestParam(name = "plantMissile")
                                   String plantMissile) {
        List<String> openKeys = askuService.getTlcOpenKeys(plantMissile);
        if (openKeys.size() == 0) {
            throw new NotFoundException("Відкритих TL ключей не знайдено");
        }
        return openKeys;
    }

    @PostMapping("tlc/open_keys")
    public RocketDto setTlcKeys(@RequestParam(name = "plantMissile") String plantMissile,
                                @RequestBody List<String> openTlKeys) {
        if (openTlKeys.size() < 1) {
            throw new BadRequestException("specify at least 1 key");
        }
        askuService.setTlcOpenKeys(plantMissile, openTlKeys);

        return rocketMapper.toDto(askuService.findRocketByPlantNumber(plantMissile).orElseThrow(NotFoundException::new));
    }

    @PostMapping("spl_readiness/{readiness}")
    public AskuDto setSplReadiness(@PathVariable(name = "readiness") Readiness readiness) {
        askuService.setSplReadiness(readiness);
        return askuService.getAskuDto();
    }

    @GetMapping("spl_readiness")
    public Readiness getSplReadiness() {
        return askuService.getAskuEntity().getSplReadiness();
    }


    @GetMapping("initial_data_with_ts")
    public InitialDataWithTimeStamp getInitialDataWithTimeStamp(@RequestParam(name = "isLeft", defaultValue = "true")
                                                                boolean isLeft) {
        Rocket rocket = askuService.getRocketCopy(isLeft)
                .orElseThrow(NotFoundException::new);
        if (rocket.getInitialData() == null) {
            throw new NotFoundException();
        } else {
            return new InitialDataWithTimeStamp(rocket.getInitialData().getId(),
                    initialDataMapper.toDto(rocket.getInitialData()),
                    rocket.getInitialDataTS());
        }
    }

    @PostMapping("initial_data_with_ts/commit")
    public ResponseEntity<String> commitInitialDataWithTimeStamp(@RequestParam(name = "isLeft") boolean isLeft,
                                                                 @RequestParam(name = "initial_data_id") long initialDataId,
                                                                 @RequestBody LaunchInitialDataDto dto) {
        if (askuService.confirmInitialDataLoad(isLeft, initialDataId, dto)) {
            return new ResponseEntity<>(HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    record InitialDataWithTimeStamp(
            long initialDataId,
            LaunchInitialDataDto launchInitialDataDto,
            LocalDateTime timeStamp
    ) {

    }

    @PostMapping("plc/message")
    public ResponseEntity<String> setPlcMessage(@RequestBody @Valid PlcMsg msg) {
        try {
            if (askuService.setPlcMessage(msg.msgType, msg.systemType, msg.payload, msg.alias)) {
                return new ResponseEntity<>(HttpStatus.OK);
            }
        } catch (Exception e) {
            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }

        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }

    @PostMapping("rocket/{plant_missile}/reference_points")
    public ResponseEntity<List<LaunchReferencePoint>> addReferencePointsByRocket(@PathVariable(name = "plant_missile")
                                                                                 String plantMissile,
                                                                                 @RequestBody List<LaunchReferencePoint> dto) {
        if (dto == null || dto.size() == 0) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }

        if (askuService.findRocketByPlantNumber(plantMissile).isEmpty()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }

        askuService.addReferencePoints(plantMissile, dto);

        List<LaunchReferencePoint> foundReferencePoints = askuService.getReferencePointsByPlantMissileWithSecondPrecision(plantMissile);
        return ResponseEntity.ok().body(foundReferencePoints);
    }


    @GetMapping("/reference_points")
    public ResponseEntity<List<LaunchReferencePoint>> getLaunchMilestone(@RequestParam("fire_order_entity_id") UUID fireOrderEntityId,
                                                                         @RequestParam(name = "select_from", required = false)
                                                                         @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
                                                                         LocalDateTime selectFromParam,
                                                                         @RequestParam(name = "select_to", required = false)
                                                                         @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
                                                                         LocalDateTime selectToParam) {


        LocalDateTime selectFrom = (selectFromParam == null) ? LocalDateTime.of(1, 1, 1, 0, 0) :
                selectFromParam;
        LocalDateTime selectTo = (selectToParam == null) ? LocalDateTime.now() : selectToParam;
        List<LaunchReferencePoint> referencePoints = askuService.getReferencePointsWithCaptionsAndSecondPrecise(fireOrderEntityId, selectFrom, selectTo);

        return ResponseEntity.ok(referencePoints);
    }

    private record PlcMsg(@NotNull MsgType msgType,
                          AdjacentSystemType systemType,
                          String alias,
                          @NotBlank String payload) {

    }

    @PostMapping("/plc/launch_timers")
    private ResponseEntity handleLaunchTimers(@RequestParam(name = "action") CountdownEventType action,
                                              @Valid @RequestBody StartRecord startRecord) {
        if (!action.equals(CountdownEventType.START)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("only action=START is allowed");
        }

        CountdownEvent generatedEvent;
        try {
            generatedEvent = pdpService.handleLaunchTimers(action, startRecord.launchType());
        } catch (NotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        }

        return ResponseEntity.status(HttpStatus.OK)
                .body(generatedEvent);
    }

    private record StartRecord(LaunchType launchType) {
    }

    @GetMapping("/plc/tl_keys")
    public ResponseEntity<TlKeysForPlcVersioned> loadKeys(@RequestParam(name = "plantMissile") String plantMissile) {
        return ResponseEntity.status(HttpStatus.NOT_IMPLEMENTED).build();
    }

    @PostMapping("/plc/tl_keys")
    public ResponseEntity<TlKeysForPlcVersioned> loadKeys(@RequestParam(name = "plantMissile") String plantMissile,
                                                          @RequestBody TlKeysForPlcVersioned keysRecord) {
        // TODO: 5/27/2024 disable after update
        return ResponseEntity.status(HttpStatus.NOT_IMPLEMENTED).build();

    }

    @GetMapping("/plc/tl_keys/version")
    public ResponseEntity<Long> getKeysVersion() {
        return ResponseEntity.status(HttpStatus.GONE).build();
    }

    private record TlKeysForPlcVersioned(long version,
                                         List<String> keys) {
    }
}
