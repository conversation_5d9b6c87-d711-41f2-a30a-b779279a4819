package com.deb.spl.control.controller;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.ppo.*;
import com.deb.spl.control.service.PpoService;
import com.deb.spl.control.service.asku.PlcService;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/api/v1/adjacent-systems/ppo/")
public class PpoController {
    private final PpoService ppoService;
    private final PlcService plcService;

    public PpoController(PpoService ppoService, PlcService plcService) {
        this.ppoService = ppoService;
        this.plcService = plcService;
    }

    @GetMapping
    public PpoDTO getPpoDto() {
        plcService.updateRequestTime(LocalDateTime.now());
        return ppoService.getPpoDto().orElseThrow(NotFoundException::new);
    }

    @PutMapping
    public PpoDTO updateWithDto(@RequestBody @NotNull PpoDTO dto) {
        plcService.updateRequestTime(LocalDateTime.now());
        return ppoService.updateWithDto(dto).get();
    }

    @GetMapping("/status/system")
    public AdjacentSystemStatus getStatus() {
        plcService.updateRequestTime(LocalDateTime.now());
        return ppoService.getCurrentStatus();
    }

    @GetMapping("/bay/{bay_type}")
    public PpoBayDTO getPpoBayDtoByType(@PathVariable("bay_type")
                                        @NotNull PpoBayType bayType) {
        plcService.updateRequestTime(LocalDateTime.now());
        return ppoService.getPpoBayDtoByType(bayType).get();
    }

    @GetMapping("/bay/unit/{unit_state}")
    public List<PpoUnitDTO> getUnitsWithState(@PathVariable("unit_state")
                                              @NotNull PpoUnitStatus unitState) {
        plcService.updateRequestTime(LocalDateTime.now());
        return ppoService.getUnitsWithState(unitState);
    }


    @PutMapping("/bay/{bay_type}")
    public PpoBayDTO updateByBayType(@PathVariable("bay_type") @NotNull PpoBayType bayType,
                                     @RequestBody @NotNull PpoBayDTO bayDTO) {
        plcService.updateRequestTime(LocalDateTime.now());
        return ppoService.updateByBayType(bayType, bayDTO).get();
    }

    @PutMapping("/bay/{bay_type}/unit/{unit_id}")
    public PpoUnitDTO updateByBayTypeAndIdWithUnitStatus(@PathVariable("bay_type") @NotNull PpoBayType bayType,
                                                         @PathVariable("unit_id") @NotNull Long unitId,
                                                         @RequestBody @NotNull PpoUnitStatus status) {
        plcService.updateRequestTime(LocalDateTime.now());
        return ppoService.updateByBayTypeAndIdWithUnitStatus(bayType, unitId, status).get();
    }

    @GetMapping("/command")
    public PpoCommand getCommand() {
        plcService.updateRequestTime(LocalDateTime.now());
        return ppoService.getCommand().orElseThrow(NotFoundException::new);
    }

    @PostMapping("/command/{command_id}/commit")
    public PpoCommand commitCommand(@PathVariable(name = "command_id") @Positive Long commandId) {
        plcService.updateRequestTime(LocalDateTime.now());
        return ppoService.setCommandTimeStampById(commandId)
                .orElseThrow(NotFoundException::new);
    }

    @PostMapping("/command/{command_id}/remove")
    public PpoCommand removeCommand(@PathVariable(name = "command_id") @Positive Long commandId) {
        plcService.updateRequestTime(LocalDateTime.now());

        return ppoService.removeCommand(commandId)
                .orElseThrow(NotFoundException::new);
    }

}
