package com.deb.spl.control.controller;

import com.deb.spl.control.service.NsdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@RequestMapping("/users")
@Slf4j
public class NsdController {

    public NsdController(NsdService nsdService, @Value("${nsd.token}") String nsdToken) {
        this.nsdService = nsdService;
        this.nsdToken = nsdToken;
    }

    private final NsdService nsdService;

    private String nsdToken;

    @PostMapping(path = "/test", consumes = "application/x-www-form-urlencoded")
    public ResponseEntity<String> createUserObj(NsdPair nsdPair) {
        if (!nsdService.validateToken(nsdService.getRemoteToken())) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
        }
        return ResponseEntity.status(HttpStatus.CREATED).build();
    }

    record NsdPair(String key, String value) {
    }

    @PostMapping(value = "/create-api", consumes = "application/x-www-form-urlencoded")
    public ResponseEntity<String> createUser(HashMap<String, String> pair) {
        if (!nsdService.validateToken(nsdService.getRemoteToken())) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
        }
        return ResponseEntity.status(HttpStatus.CREATED).build();
    }
}
