package com.deb.spl.control.controller;

import com.deb.spl.control.data.nppa.BynDto;
import com.deb.spl.control.data.nppa.NcokDto;
import com.deb.spl.control.data.nppa.NppaCommand;
import com.deb.spl.control.data.nppa.NppaDto;
import com.deb.spl.control.service.NppaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.time.LocalDateTime;
import java.util.Optional;

@RestController
@RequestMapping("/api/v1/adjacent-systems/nppa/")
@Slf4j
public class NppaController {
    private final NppaService nppaService;


    public NppaController(NppaService nppaService) {

        this.nppaService = nppaService;
    }

    @GetMapping("ncok")
    public NcokDto getNcok() {
        return nppaService.getNcok().orElseThrow(NotFoundException::new);
    }

    @PostMapping("ncok")
    public NppaDto updateNcok(@RequestBody @NotNull @Valid NcokDto dto) {
        Optional<NppaDto> updatedNppa = nppaService.updateNcok(dto);

        return updatedNppa.get();
    }

    @GetMapping("byn")
    public BynDto getByn() {
        return nppaService.getByn().orElseThrow(NotFoundException::new);
    }

    @PostMapping("byn")
    public NppaDto updateByn(@RequestBody @NotNull @Valid BynDto dto) {
        Optional<NppaDto> updatedNppa = nppaService.updateByn(dto);

        return updatedNppa.get();
    }

    @GetMapping("command")
    public NppaCommand getCommand() {
        NppaCommand command = nppaService.getCommand().orElseThrow(NotFoundException::new);
        //used by plc to synchronize time. Requested by plc software developer.
        command.setGenerationTime(LocalDateTime.now());
        return command;
    }

    @PostMapping("command/{command_id}/commit")
    public NppaCommand commitCommand(@PathVariable(name = "command_id") @Positive Long commandId) {
        NppaCommand command = nppaService.setCommandTimeStampById(commandId).orElseThrow(() -> new NotFoundException(""));
        nppaService.switchAfterLunchLock(command);

        return command;
    }

    @PostMapping("command/{command_id}/remove")
    public NppaCommand removeCommand(@PathVariable(name = "command_id") @Positive Long commandId) {
        return nppaService.removeCommand(commandId)
                .orElseThrow(NotFoundException::new);
    }
}
