package com.deb.spl.control.controller;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.sae.SaeCommand;
import com.deb.spl.control.data.sae.SaeDto;
import com.deb.spl.control.data.sae.SaeFeederStatus;
import com.deb.spl.control.data.sae.SaeStatus;
import com.deb.spl.control.service.SaeService;
import com.helger.commons.collection.map.MapEntry;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;


@RestController
@RequestMapping("/api/v1/adjacent-systems/sae/")
public class SaeController {

    private final SaeService saeService;

    public SaeController(SaeService saeService) {
        this.saeService = saeService;
    }


    @GetMapping
    public SaeDto getSaeDto() {
        Optional<SaeDto> dto = saeService.getSaeDto();

        return dto.orElseGet(SaeDto::new);
    }

    @PutMapping
    public SaeDto updateSaeDto(@Valid @RequestBody SaeDto dto) {
        saeService.updateWithDto(dto);

        return saeService.getSaeDto().orElse(new SaeDto());
    }

    @GetMapping("/status/connection")
    public boolean getConnectionState() {
        Optional<SaeDto> dto = saeService.getSaeDto();

        return dto.isPresent() ? dto.get().isConnected() : false;
    }

    /**
     * @param status system status  {@link  AdjacentSystemStatus}
     * @return updated SaeDto
     */
    @PutMapping("/status/system")
    public SaeDto updateSystemStatus(@NotNull @RequestParam AdjacentSystemStatus status) {
        saeService.updateSystemStatus(status);

        return saeService.getSaeDto().orElse(new SaeDto());
    }

    /**
     * @return system status  {@link  AdjacentSystemStatus}
     */
    @GetMapping("/status/system")
    public SaeStatus getSystemStatus() {

        Optional<SaeDto> dto = saeService.getSaeDto();

        return dto.isPresent() ? dto.get().getSaeStatus() : SaeStatus.NOT_CONNECTED;
    }

    /**
     * @param feeders {@link SaeFeederStatus} to update
     * @return updated {@link SaeDto}
     * @example{ "Feeder1": "ON",
     * "Feeder5": "OFF",
     * "Feeder4": "ERROR",}
     */
    @PutMapping("/status/feeders")
    public SaeDto updateFeedersStatuses(@RequestBody @NotNull Map<String, SaeFeederStatus> feeders) {
        saeService.updateFeedersStatuses(feeders);

        return saeService.getSaeDto().get();
    }

    /**
     * @return all feeders with {@link  SaeFeederStatus}
     * @example{ "Feeder6": "ON",
     * "Feeder5": "OFF",
     * "Feeder4": "OFF",
     * "Feeder3": "OFF",
     * "FeederNppa1": "OFF",
     * "Feeder2": "OFF",
     * "Feeder1": "OFF"
     * }
     */
    @GetMapping("/status/feeders")
    public Map<String, SaeFeederStatus> getFeedersStatuses() {
        Optional<SaeDto> dto = saeService.getSaeDto();

        return dto.orElseThrow(NotFoundException::new).listFeeders();
    }

    @PutMapping("/status/feeders/{feeder_name}")
    public MapEntry<String, SaeFeederStatus> updateFeederStatus(@PathVariable("feeder_name") @NotBlank String feederName,
                                                                @RequestParam("feeder_status") @NotBlank SaeFeederStatus feederStatus) {
        Map<String, SaeFeederStatus> feeder = new HashMap<>();
        feeder.put(feederName, feederStatus);
        saeService.updateFeedersStatuses(feeder);

        Optional<SaeDto> dto = saeService.getSaeDto();

        SaeFeederStatus foundStatus = dto.orElseThrow(NotFoundException::new)
                .listFeeders().entrySet().stream()
                .filter(entry -> entry.getKey().equalsIgnoreCase(feederName)).findFirst()
                .orElseThrow(NotFoundException::new)
                .getValue();

        return new MapEntry<>(feederName, foundStatus);
    }

    @GetMapping("/status/feeders/{feeder_name}")
    public MapEntry<String, SaeFeederStatus> getFeederStatus(@PathVariable("feeder_name") @NotBlank String feederName) {
        Optional<SaeDto> dto = saeService.getSaeDto();

        SaeFeederStatus foundStatus = dto.orElseThrow(NotFoundException::new)
                .listFeeders().entrySet().stream()
                .filter(entry -> entry.getKey().equalsIgnoreCase(feederName)).findFirst()
                .orElseThrow(NotFoundException::new)
                .getValue();

        return new MapEntry<>(feederName, foundStatus);
    }

    @GetMapping("/command")
    public SaeCommand getCommand() {
        return saeService.getCommand().orElseThrow(NotFoundException::new);
    }

    @PostMapping("/command/{command_id}/commit")
    public void commitCommand(@PathVariable("command_id") Long commandId) {
        Optional<SaeCommand> result = saeService.setCommandTimeStampById(commandId);

        result.orElseThrow(NotFoundException::new);
    }

    @PostMapping("/command/{command_id}/remove")
    public void removeCommand(@PathVariable("command_id") Long commandId) {
        Optional<SaeCommand> result = saeService.removeCommand(commandId);

        result.orElseThrow(NotFoundException::new);
    }


}
