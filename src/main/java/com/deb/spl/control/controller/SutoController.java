package com.deb.spl.control.controller;

import com.deb.spl.control.data.suto.*;
import com.deb.spl.control.service.SutoService;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@RestController
@RequestMapping("/api/v1/adjacent-systems/suto/")
public class SutoController {

    private final SutoService sutoService;

    public SutoController(SutoService sutoService) {
        this.sutoService = sutoService;
    }


    @GetMapping
    private SutoDto getSutoDto() {
        return sutoService.getSutoDto().orElseThrow(NotFoundException::new);
    }

    @PostMapping
    private SutoDto updateWithDto(@RequestBody @NotNull SutoDto dto) {
        return sutoService.updateWithDto(dto);
    }

    @PostMapping("/stats")
    private SutoDto updateStats(@RequestBody @NotNull SutoStatsDto dto) {
        return sutoService.updateWithStats(dto);
    }

    @PostMapping("/outriggers/{outrigger_code_name}")
    public SutoDto updateOutriggerMalfunctionCode(
            @PathVariable(name = "outrigger_code_name") @NotBlank String outriggerCodeName,
            @RequestParam(name = "code") @NotNull int code) {
        return sutoService.updateWithOutriggerMalfunctionCodes(outriggerCodeName, code);
    }

    @PostMapping("/properties/property")
    public SutoDto updateSutoProperty(@RequestBody @NotNull List<SutoPropertyDto> dto) {
        return sutoService.updateSutoProperty(dto);
    }

    @GetMapping("/settings")
    public SutoSettingsDto getSutoSettings() {
        return sutoService.getSutoSettingsDto().orElseThrow(NotFoundException::new);
    }

    @PostMapping("/settings")
    public SutoSettingsDto updateSettingsWithDto(@RequestBody @NotNull SutoSettingsDto dto) {
        sutoService.updateSutoSettingsWithDto(dto);
        return sutoService.getSutoSettingsDto().orElseThrow(NotFoundException::new);
    }

    @GetMapping("/command")
    public SutoCommand getCommand() {
        return sutoService.getCommand().orElseThrow(NotFoundException::new);
    }

    @PostMapping("/command/{command_id}/commit")
    public void commitCommand(@PathVariable("command_id") Long commandId) {
        SutoCommand result = sutoService.setCommandTimeStampById(commandId).orElseThrow(NotFoundException::new);
    }

    @PostMapping("/command/{command_id}/remove")
    public void removeCommand(@PathVariable("command_id") Long commandId) {
        SutoCommand result = sutoService.removeCommand(commandId).orElseThrow(NotFoundException::new);
    }

}
