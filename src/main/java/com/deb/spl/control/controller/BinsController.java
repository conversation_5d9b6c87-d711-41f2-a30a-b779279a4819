package com.deb.spl.control.controller;

import com.deb.spl.control.service.BinsService;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.UtcDateTime;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

@RestController
@RequestMapping("/api/v1/adjacent-systems/bins/")
public class BinsController {
    private final BinsService binsService;

    public BinsController(BinsService binsService) {
        this.binsService = binsService;
    }

    @GetMapping("is_connected")
    public boolean getBinsConnection() {
        try {
            return binsService.isConnected();
        } catch (Exception e) {
            throw new ServerErrorException(e.getMessage());
        }
    }

    @GetMapping("status")
    public AdjacentSystemStatus getBinsStatus() {
        try {
            return binsService.getStatus();
        } catch (Exception e) {
            throw new NotFoundException(e.getMessage());
        }
    }

    @GetMapping("local_date_time")
    public UtcDateTime getLocalDateTimeUtc() {
        try {
            return binsService.getLocalDateTimeUtc();
        } catch (Exception e) {
            throw new ServerErrorException(e.getMessage());
        }
    }

    @GetMapping("local_date_time_non_utc")
    public LocalDateTime getLocalDateTimeNonUtc() {
        try {
            return LocalDateTime.now();
        } catch (Exception e) {
            throw new ServerErrorException(e.getMessage());
        }
    }

}
