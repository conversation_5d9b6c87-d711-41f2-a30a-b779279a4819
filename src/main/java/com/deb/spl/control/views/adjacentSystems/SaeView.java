package com.deb.spl.control.views.adjacentSystems;

import com.deb.spl.control.data.VoltageStatus;
import com.deb.spl.control.data.asku.RocketMapper;
import com.deb.spl.control.data.sae.*;
import com.deb.spl.control.repository.sae.SaeHistoryRepository;
import com.deb.spl.control.service.*;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.service.asku.PlcService;
import com.deb.spl.control.views.MainLayout;
import com.deb.spl.control.views.events.SaeEvent;
import com.vaadin.flow.component.AttachEvent;
import com.vaadin.flow.component.DetachEvent;
import com.vaadin.flow.component.Html;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.dialog.Dialog;
import com.vaadin.flow.component.grid.ColumnTextAlign;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.grid.GridVariant;
import com.vaadin.flow.component.html.Span;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.component.textfield.TextFieldVariant;
import com.vaadin.flow.data.binder.Binder;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import lombok.extern.slf4j.Slf4j;
import org.vaadin.tabs.PagedTabs;

import java.time.LocalDateTime;
import java.util.List;

@PageTitle("Сатус систем | САЕ")
@Route(value = "adjacent-systems/sae", layout = MainLayout.class)
@Slf4j
public class SaeView extends AdjacentSystemView implements Broadcaster.BroadcastListener {

    private final UserService userService;
    private final Binder<Sae> saeBinder = new Binder<>(Sae.class);
    private final SaeService saeService;
    private final SaeHistoryRepository historyRepository;

    @Override
    protected void onAttach(AttachEvent attachEvent) {
        super.onAttach(attachEvent);
        Broadcaster.register(this, SaeEvent.class);
    }

    @Override
    protected void onDetach(DetachEvent detachEvent) {
        super.onDetach(detachEvent);
        Broadcaster.unregister(this);
    }

    public SaeView(MsuService msuService,
                   PpoService ppoService,
                   SutoService sutoService,
                   BinsService binsService,
                   SaeService saeService,
                   AskuService askuService,
                   PlcService plcService,
                   UserService userService,
                   SaeHistoryRepository historyRepository,
                   RocketMapper rocketMapper,
                   NppaService nppaService,
                   CcvCommunicationService ccvCommunicationService) {
        super(msuService,
                ppoService,
                sutoService,
                binsService,
                saeService,
                askuService,
                plcService,
                "Система автономного електропостачання",
                rocketMapper,
                nppaService,
                ccvCommunicationService);
        this.userService = userService;
        this.saeService = saeService;
        this.historyRepository = historyRepository;

        SaeCommandsTab saeCommandsTab = new SaeCommandsTab();
        VerticalLayout commandTab = new VerticalLayout();
        saeCommandsTab.addClassName("sae-command-tab");
        commandTab.addClassName("command-tab-sae");
        commandTab.setWidthFull();
        commandTab.setWidth("100%");
        commandTab.setMaxWidth("100%");
        commandTab.add(saeCommandsTab);//commands class

        SaeStatusTabFirstColumn saeStatusTabFirstColumn = new SaeStatusTabFirstColumn();
        saeStatusTabFirstColumn.addClassName("sae-status-tab-first-column");
        SaeStatusTabSecondColumn saeStatusTabSecondColumn = new SaeStatusTabSecondColumn();
        saeStatusTabSecondColumn.addClassName("sae-status-tab-second-column");
        SaeStatusTabThirdColumn saeStatusTabThirdColumn = new SaeStatusTabThirdColumn();
        saeStatusTabThirdColumn.addClassName("sae-status-tab-third-column");
        SaeStatusTabFourthColumn saeStatusTabFourthColumn = new SaeStatusTabFourthColumn();
        saeStatusTabFourthColumn.addClassName("sae-status-tab-fourth-column");
        VerticalLayout statusTab = new VerticalLayout();
        SaeStatusTabFirstRow saeStatusTabFirstRow = new SaeStatusTabFirstRow();
        saeStatusTabFirstRow.addClassName("sae-status-tab-first-row");
        SaeStatusTabSecondRow saeStatusTabSecondRow = new SaeStatusTabSecondRow();
        saeStatusTabSecondRow.addClassName("sae-status-tab-second-row");
        statusTab.addClassName("status-tab");
        statusTab.setWidth("100%");
        statusTab.setMaxWidth("100%");
        statusTab.add(saeStatusTabFirstRow, saeStatusTabSecondRow);//status class
        saeStatusTabFirstRow.add(saeStatusTabFirstColumn, saeStatusTabSecondColumn, saeStatusTabThirdColumn, saeStatusTabFourthColumn);

        VerticalLayout saeLogTab = new LogTab(historyRepository, "sae-log-tab");
        VerticalLayout logTab = new VerticalLayout();
        logTab.addClassName("log-tab");
        logTab.add(saeLogTab); //log class

        VerticalLayout tabContainer = new VerticalLayout();
        PagedTabs tabs = new PagedTabs(tabContainer);
        tabs.getContent();

        String commandTabHeader = "Команди";
        String statusHeader = "Статус";
        String logTabHeader = "Журнал";

        tabs.add(statusHeader, statusTab, false);
        tabs.add(commandTabHeader, commandTab, false);
        tabs.add(logTabHeader, logTab, false);

        tabContainer.addClassName("sae-tab-container");
        getSystemLayout().add(tabs, tabContainer);
        getSystemLayout().addClassName("system-layout-sae");
        getSystemLayout().setWidth("69.5%");
        getSystemLayout().setMaxWidth("69.5%");
    }

    private class SaeStatusTabFirstRow extends HorizontalLayout {
        public SaeStatusTabFirstRow() {
            add();
        }
    }

    private class SaeStatusTabFirstColumn extends VerticalLayout {
        public SaeStatusTabFirstColumn() {
            TextField turnOnFeeder1 = new TextField("Статус фідера 1");
            turnOnFeeder1.setReadOnly(true);
            turnOnFeeder1.addClassName("feeder1");
            turnOnFeeder1.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            turnOnFeeder1.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, SaeFeederStatus.class));
            turnOnFeeder1.setValue(saeService.getSae().isEmpty() ?
                    SaeFeederStatus.UNDEFINED.getValueUa() :
                    (saeService.getSae().get().getFeeder1Status() != null ?
                            saeService.getSae().get().getFeeder1Status().getValueUa() : SaeFeederStatus.UNDEFINED.getValueUa()));
            saeBinder.forField(turnOnFeeder1)
                    .bindReadOnly(sae -> (sae.getFeeder1Status() != null) ?
                            sae.getFeeder1Status().getValueUa() : "Помилка формату");

            TextField turnOnFeeder2 = new TextField("Статус фідера 2");
            turnOnFeeder2.setReadOnly(true);
            turnOnFeeder2.addClassName("feeder2");
            turnOnFeeder2.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            turnOnFeeder2.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, SaeFeederStatus.class));
            turnOnFeeder2.setValue(saeService.getSae().isEmpty() ?
                    SaeFeederStatus.UNDEFINED.getValueUa() :
                    (saeService.getSae().get().getFeeder2Status() != null ?
                            saeService.getSae().get().getFeeder2Status().getValueUa() : SaeFeederStatus.UNDEFINED.getValueUa()));
            saeBinder.forField(turnOnFeeder2)
                    .bindReadOnly(sae -> (sae.getFeeder2Status() != null) ?
                            sae.getFeeder2Status().getValueUa() : "Помилка формату");

            TextField turnOnFeeder3 = new TextField("Статус фідера 3");
            turnOnFeeder3.setReadOnly(true);
            turnOnFeeder3.addClassName("feeder3");
            turnOnFeeder3.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            turnOnFeeder3.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, SaeFeederStatus.class));
            turnOnFeeder3.setValue(saeService.getSae().isEmpty() ?
                    SaeFeederStatus.UNDEFINED.getValueUa() :
                    (saeService.getSae().get().getFeeder3Status() != null ?
                            saeService.getSae().get().getFeeder3Status().getValueUa() : SaeFeederStatus.UNDEFINED.getValueUa()));

            saeBinder.forField(turnOnFeeder3)
                    .bindReadOnly(sae -> (sae.getFeeder3Status() != null) ?
                            sae.getFeeder3Status().getValueUa() : "Помилка формату");

            TextField turnOnFeeder4 = new TextField("Статус фідера 4");
            turnOnFeeder4.setReadOnly(true);
            turnOnFeeder4.addClassName("feeder4");
            turnOnFeeder4.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            turnOnFeeder4.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, SaeFeederStatus.class));
            turnOnFeeder4.setValue(saeService.getSae().isEmpty() ?
                    SaeFeederStatus.UNDEFINED.getValueUa() :
                    (saeService.getSae().get().getFeeder4Status() != null ?
                            saeService.getSae().get().getFeeder4Status().getValueUa() : SaeFeederStatus.UNDEFINED.getValueUa()));
            saeBinder.forField(turnOnFeeder4)
                    .bindReadOnly(sae -> (sae.getFeeder4Status() != null) ?
                            sae.getFeeder4Status().getValueUa() : "Помилка формату");

            TextField turnOnFeeder5 = new TextField("Статус фідера 5");
            turnOnFeeder5.setReadOnly(true);
            turnOnFeeder5.addClassName("feeder5");
            turnOnFeeder5.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            turnOnFeeder5.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, SaeFeederStatus.class));
            turnOnFeeder5.setValue(saeService.getSae().isEmpty() ?
                    SaeFeederStatus.UNDEFINED.getValueUa() :
                    (saeService.getSae().get().getFeeder5Status() != null ?
                            saeService.getSae().get().getFeeder5Status().getValueUa() : SaeFeederStatus.UNDEFINED.getValueUa()));
            saeBinder.forField(turnOnFeeder5)
                    .bindReadOnly(sae -> (sae.getFeeder5Status() != null) ?
                            sae.getFeeder5Status().getValueUa() : "Помилка формату");

            TextField turnOnFeeder6 = new TextField("Статус фідера 6");
            turnOnFeeder6.setReadOnly(true);
            turnOnFeeder6.addClassName("feeder6");
            turnOnFeeder6.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            turnOnFeeder6.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, SaeFeederStatus.class));
            turnOnFeeder6.setValue(saeService.getSae().isEmpty() ?
                    SaeFeederStatus.UNDEFINED.getValueUa() :
                    (saeService.getSae().get().getFeeder6Status() != null ?
                            saeService.getSae().get().getFeeder6Status().getValueUa() : SaeFeederStatus.UNDEFINED.getValueUa()));
            saeBinder.forField(turnOnFeeder6)
                    .bindReadOnly(sae -> (sae.getFeeder6Status() != null) ?
                            sae.getFeeder6Status().getValueUa() : "Помилка формату");

            TextField feederNppa1Status = new TextField("Статус фідера 1 НППА");
            feederNppa1Status.setReadOnly(true);
            feederNppa1Status.addClassName("feeder-nppa-su");
            feederNppa1Status.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            feederNppa1Status.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, SaeFeederStatus.class));
            feederNppa1Status.setValue(saeService.getSae().isEmpty() ?
                    SaeFeederStatus.UNDEFINED.getValueUa() :
                    (saeService.getSae().get().getFeederNppa1Status() != null ?
                            saeService.getSae().get().getFeederNppa1Status().getValueUa() : SaeFeederStatus.UNDEFINED.getValueUa()));
            saeBinder.forField(feederNppa1Status)
                    .bindReadOnly(sae -> (sae.getFeederNppa1Status() != null) ?
                            sae.getFeederNppa1Status().getValueUa() : "Помилка формату");

            add(turnOnFeeder1, turnOnFeeder2, turnOnFeeder3, turnOnFeeder4, turnOnFeeder5, turnOnFeeder6, feederNppa1Status);
        }
    }

    private class SaeStatusTabSecondColumn extends VerticalLayout {
        public SaeStatusTabSecondColumn() {

            TextField launchAJSAE = new TextField("Запуск АДЖ");
            launchAJSAE.setReadOnly(true);
            launchAJSAE.addClassName("launch-aj-sae");
            launchAJSAE.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            launchAJSAE.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, ADJstatus.class));
            launchAJSAE.setValue(saeService.getSae().isEmpty() ?
                    ADJstatus.NOT_ACTIVE.getValueUa() :
                    (saeService.getSae().get().getAdjStart() != null ?
                            saeService.getSae().get().getAdjStart().getValueUa() : ADJstatus.NOT_ACTIVE.getValueUa()));
            saeBinder.forField(launchAJSAE)
                    .bindReadOnly(sae -> sae.getAdjStart().getValueUa() != null ?
                            sae.getAdjStart().getValueUa() : "Помилка формату");

            TextField suppressionAJSAE = new TextField("Глушення АДЖ");
            suppressionAJSAE.setReadOnly(true);
            suppressionAJSAE.addClassName("suppression-aj-sae");
            suppressionAJSAE.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            suppressionAJSAE.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, ADJstatus.class));
            suppressionAJSAE.setValue(saeService.getSae().isEmpty() ?
                    ADJstatus.NOT_ACTIVE.getValueUa() :
                    (saeService.getSae().get().getAdjStop() != null ?
                            saeService.getSae().get().getAdjStop().getValueUa() : ADJstatus.NOT_ACTIVE.getValueUa()));
            saeBinder.forField(suppressionAJSAE)
                    .bindReadOnly(sae -> sae.getAdjStop() != null ?
                            sae.getAdjStop().getValueUa() : "Помилка формату");

            TextField blockingAJSAE = new TextField("Блокування АДЖ");
            blockingAJSAE.setReadOnly(true);
            blockingAJSAE.addClassName("blocking-aj-sae");
            blockingAJSAE.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            blockingAJSAE.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, ADJstatus.class));
            blockingAJSAE.setValue(saeService.getSae().isEmpty() ?
                    ADJstatus.NOT_ACTIVE.getValueUa() :
                    (saeService.getSae().get().getAdjLock() != null ?
                            saeService.getSae().get().getAdjLock().getValueUa() : ADJstatus.NOT_ACTIVE.getValueUa()));
            saeBinder.forField(blockingAJSAE)
                    .bindReadOnly(sae -> sae.getAdjLock() != null ?
                            sae.getAdjLock().getValueUa() : "Помилка формату");

            TextField unblockingAJSAE = new TextField("Розблокування АДЖ");
            unblockingAJSAE.setReadOnly(true);
            unblockingAJSAE.addClassName("unblocking-aj-sae");
            unblockingAJSAE.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            unblockingAJSAE.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, ADJstatus.class));
            unblockingAJSAE.setValue(saeService.getSae().isEmpty() ?
                    ADJstatus.NOT_ACTIVE.getValueUa() :
                    (saeService.getSae().get().getAdjUnlock() != null ?
                            saeService.getSae().get().getAdjUnlock().getValueUa() : ADJstatus.NOT_ACTIVE.getValueUa()));
            saeBinder.forField(unblockingAJSAE)
                    .bindReadOnly(sae -> sae.getAdjUnlock() != null ?
                            sae.getAdjUnlock().getValueUa() : "Помилка формату");

            add(launchAJSAE, suppressionAJSAE, blockingAJSAE, unblockingAJSAE);
        }

    }

    private class SaeStatusTabThirdColumn extends VerticalLayout {
        public SaeStatusTabThirdColumn() {
            TextField chargingHDSSAE = new TextField("Зарядка ХДС");
            chargingHDSSAE.setReadOnly(true);
            chargingHDSSAE.addClassName("charging-hds-sae");
            chargingHDSSAE.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            chargingHDSSAE.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, HDSstatus.class));
            chargingHDSSAE.setValue(saeService.getSae().isEmpty() ?
                    HDSstatus.NOT_ACTIVE.getValueUa() :
                    (saeService.getSae().get().getHdsStatus() != null ?
                            saeService.getSae().get().getHdsStatus().getValueUa() : HDSstatus.NOT_ACTIVE.getValueUa()));
            saeBinder.forField(chargingHDSSAE)
                    .bindReadOnly(sae -> sae.getHdsStatus() != null ?
                            sae.getHdsStatus().getValueUa() : "Помилка формату");

            TextField voltageHDSSAElover22V = new TextField("ХДС САЕ U > 22,5 В");
            voltageHDSSAElover22V.setReadOnly(true);
            voltageHDSSAElover22V.addClassName("voltage-hds-sae-lover-22v");
            voltageHDSSAElover22V.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            voltageHDSSAElover22V.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, VoltageStatus.class));
            voltageHDSSAElover22V.setValue(saeService.getSae().isEmpty() ?
                    VoltageStatus.NOT_ACTIVE.getValueUa() :
                    (saeService.getSae().get().getVoltageStatus() != null ?
                            saeService.getSae().get().getVoltageStatus().getValueUa() : VoltageStatus.NOT_ACTIVE.getValueUa()));
            saeBinder.forField(voltageHDSSAElover22V)
                    .bindReadOnly(sae -> sae.getVoltageStatus() != null ?
                            sae.getVoltageStatus().getValueUa() : "Помилка формату");

            add(voltageHDSSAElover22V, chargingHDSSAE);
        }
    }

    private class SaeStatusTabFourthColumn extends VerticalLayout {
        public SaeStatusTabFourthColumn() {
            TextField saeSPL = new TextField("САЕ СПУ");
            saeSPL.setReadOnly(true);
            saeSPL.addClassName("sae-spl");
            saeSPL.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            saeSPL.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, SaeStatus.class));
            saeSPL.setValue(saeService.getSae().isEmpty() ?
                    SaeStatus.NOT_CONNECTED.getValueUa() :
                    (saeService.getSae().get().getSaeStatus() != null ?
                            saeService.getSae().get().getSaeStatus().getValueUa() : SaeStatus.NOT_CONNECTED.getValueUa()));
            saeBinder.forField(saeSPL)
                    .bindReadOnly(sae -> sae.getSaeStatus() != null ?
                            sae.getSaeStatus().getValueUa() : "Помилка формату");

            TextField voltageExternalSource = new TextField("Напруга Зов. Дж.");
            voltageExternalSource.setReadOnly(true);
            voltageExternalSource.addClassName("voltage-external-source");
            voltageExternalSource.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            voltageExternalSource.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, VoltageStatus.class));
            voltageExternalSource.setValue(saeService.getSae().isEmpty() ?
                    VoltageStatus.NOT_ACTIVE.getValueUa() :
                    (saeService.getSae().get().getExternalPowerSourceVoltage() != null ?
                            saeService.getSae().get().getExternalPowerSourceVoltage().getValueUa() : VoltageStatus.NOT_ACTIVE.getValueUa()));
            saeBinder.forField(voltageExternalSource)
                    .bindReadOnly(sae -> sae.getExternalPowerSourceVoltage() != null ?
                            sae.getExternalPowerSourceVoltage().getValueUa() : "Помилка формату");

            TextField voltageBS = new TextField("Напруга БМ");
            voltageBS.setReadOnly(true);
            voltageBS.addClassName("voltage-bs");
            voltageBS.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            voltageBS.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, VoltageStatus.class));
            voltageBS.setValue(saeService.getSae().isEmpty() ?
                    VoltageStatus.NOT_ACTIVE.getValueUa() :
                    (saeService.getSae().get().getBSVoltage() != null ?
                            saeService.getSae().get().getBSVoltage().getValueUa() : VoltageStatus.NOT_ACTIVE.getValueUa()));
            saeBinder.forField(voltageBS)
                    .bindReadOnly(sae -> sae.getBSVoltage().getValueUa() != null ?
                            sae.getBSVoltage().getValueUa() : "Помилка формату");


            add(saeSPL, voltageExternalSource, voltageBS);
        }
    }

    private class SaeStatusTabSecondRow extends HorizontalLayout {
        public SaeStatusTabSecondRow() {

            TextField supplyFromExternalPowerSource = new TextField("Живлення від 28,5В");
            supplyFromExternalPowerSource.setReadOnly(true);
            supplyFromExternalPowerSource.addClassName("supply-from-28v");
            supplyFromExternalPowerSource.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            supplyFromExternalPowerSource.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, SaePowerSourceStatus.class));
            supplyFromExternalPowerSource.setValue(saeService.getSae().isEmpty() ?
                    VoltageStatus.NOT_ACTIVE.getValueUa() :
                    (saeService.getSae().get().getEnergizedByExternalPowerSource() != null ?
                            saeService.getSae().get().getEnergizedByExternalPowerSource().getValueUa() : VoltageStatus.NOT_ACTIVE.getValueUa()));
            saeBinder.forField(supplyFromExternalPowerSource)
                    .bindReadOnly(sae -> sae.getEnergizedByAdj().getValueUa() != null ?
                            sae.getEnergizedByExternalPowerSource().getValueUa() : "Помилка формату");

            TextField supplyFromAJSAE = new TextField("Живлення від АДЖ");
            supplyFromAJSAE.setReadOnly(true);
            supplyFromAJSAE.addClassName("supply-from-aj-sae");
            supplyFromAJSAE.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            supplyFromAJSAE.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, SaePowerSourceStatus.class));
            supplyFromAJSAE.setValue(saeService.getSae().isEmpty() ?
                    SaePowerSourceStatus.ERROR.getValueUa() :
                    (saeService.getSae().get().getEnergizedByAdj() != null ?
                            saeService.getSae().get().getEnergizedByAdj().getValueUa() : SaePowerSourceStatus.ERROR.getValueUa()));
            saeBinder.forField(supplyFromAJSAE)
                    .bindReadOnly(sae -> sae.getEnergizedByAdj() != null ?
                            sae.getEnergizedByAdj().getValueUa() : "Помилка формату");

            TextField supplyFromHDSSAE = new TextField("Живлення від ХДС");
            supplyFromHDSSAE.setReadOnly(true);
            supplyFromHDSSAE.addClassName("supply-from-hds-sae");
            supplyFromHDSSAE.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            supplyFromHDSSAE.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, SaePowerSourceStatus.class));
            supplyFromHDSSAE.setValue(saeService.getSae().isEmpty() ?
                    SaePowerSourceStatus.ERROR.getValueUa() :
                    (saeService.getSae().get().getEnergizedByHds() != null ?
                            saeService.getSae().get().getEnergizedByHds().getValueUa() : SaePowerSourceStatus.ERROR.getValueUa()));
            saeBinder.forField(supplyFromHDSSAE)
                    .bindReadOnly(sae -> sae.getEnergizedByHds() != null ?
                            sae.getEnergizedByHds().getValueUa() : "Помилка формату");

            TextField urgentMalfunction = new TextField("Несправність");
            urgentMalfunction.setReadOnly(true);
            urgentMalfunction.addClassName("urgent-malfunction");
            urgentMalfunction.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            urgentMalfunction.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, SaeStatus.class));
            urgentMalfunction.setValue(saeService.getSae().isEmpty() ?
                    SaeStatus.UNDEFINED.getValueUa() :
                    ((saeService.getSae().get().getSaeStatus() != null &&
                      (saeService.getSae().get().getSaeStatus().equals(SaeStatus.WARNING) ||
                       saeService.getSae().get().getSaeStatus().equals(SaeStatus.ERROR))
                    ) ? saeService.getSae().get().getSaeStatus().getValueUa() : SaeStatus.UNDEFINED.getValueUa()));
            saeBinder.forField(urgentMalfunction)
                    .bindReadOnly(sae -> {
                        if (sae.getSaeStatus() != null) {
                            if (sae.getSaeStatus().equals(SaeStatus.WARNING) ||
                                sae.getSaeStatus().equals(SaeStatus.ERROR)) {
                                return sae.getSaeStatus().getValueUa();
                            }
                        }
                        return SaeStatus.UNDEFINED.getValueUa();
                    });

            add(supplyFromExternalPowerSource, supplyFromAJSAE, supplyFromHDSSAE, urgentMalfunction);
        }

    }

    private class SaeCommandsTab extends VerticalLayout {
        public SaeCommandsTab() {
            VerticalLayout tabPanel = new VerticalLayout();
            tabPanel.setWidth("100%");
            tabPanel.setMaxWidth("100%");
            tabPanel.setHeightFull();

            //commands
            HorizontalLayout commandsLayoutH = new HorizontalLayout();
            commandsLayoutH.addClassName("commands-layout-h-sae");
            commandsLayoutH.setWidth("100%");
            commandsLayoutH.setMinWidth("100%");
            VerticalLayout commandsLayout = new VerticalLayout();
            commandsLayout.addClassName("commands-layout-v-sae");
            commandsLayout.setWidth("100%");
            commandsLayout.setMinWidth("100%");
            commandsLayoutH.add(commandsLayout);
            tabPanel.add(commandsLayoutH);

            Span commandsCaption = new Span("Перелік команд");
            commandsCaption.addClassName("commands-caption");
            commandsLayout.add(commandsCaption);

            setupSaeCommandsGrid(commandsLayout);

            add(tabPanel);
        }

        private void setupSaeCommandsGrid(VerticalLayout tabPanel) {

            List<SaeCommand> commandsList = saeService.getAvailableCommands();

            Grid<SaeCommand> commandsGrid = new Grid<>(SaeCommand.class, false);

            Grid.Column<SaeCommand> caption = commandsGrid
                    .addColumn(SaeCommand::getCaption).setHeader(new Html("<b>Команда</b>")).setTextAlign(ColumnTextAlign.START)
                    .setFlexGrow(0).setWidth("80%");

            Grid.Column<SaeCommand> sentence = commandsGrid
                    .addColumn(SaeCommand::getCommand).setHeader("Відправити").setTextAlign(ColumnTextAlign.END);
            sentence.setVisible(false);

            Grid.Column<SaeCommand> sendColumn = commandsGrid.addComponentColumn(saeCommand -> {

                Button sendBt = new Button("Відправити");
                sendBt.addClassName("send-sentence-button");
                sendBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY, ButtonVariant.LUMO_SMALL);
                sendBt.getStyle().set("background-color", "#4F4F4F");

                sendBt.addClickListener(event -> {
                    Dialog dialog = new Dialog();
                    dialog.addClassName("dialog-sae");

                    dialog.setHeaderTitle(saeCommand.getCaption() + "?");
                    dialog.add("Ви впевнені, що бажаєте " + saeCommand.getCaption() + "?");

                    Button confirmBt = new Button(sentence.getHeaderText(), (e) -> {
                        SaeCommand generatedCommand = SaeCommand.builder()
                                .command(saeCommand.getCommand())
                                .caption(saeCommand.getCaption())
                                .commandState(saeCommand.getCommandState())
                                .adjacentSystem(saeCommand.getAdjacentSystem())
                                .generationTime(LocalDateTime.now())
                                .originator(userService.getUserName())
                                .build();

                        saeService.setSaeCommand(generatedCommand);
                        dialog.close();
                    });
                    confirmBt.addClassName("confirm-button-msu-dialog");

                    confirmBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY,
                            ButtonVariant.LUMO_ERROR);
                    confirmBt.getStyle().set("margin-right", "auto");
                    dialog.getFooter().add(confirmBt);

                    Button cancelButton = new Button("Відміна", (e) -> dialog.close());
                    cancelButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
                    cancelButton.addClassName("cancel-button-sae-dialog");

                    dialog.getFooter().add(cancelButton);

                    dialog.open();
                });

                return sendBt;
            }).setHeader(new Html("<b>Відправити</b>"));

            commandsGrid.addClassName("commands-grid-sae");
            commandsGrid.addThemeVariants(GridVariant.MATERIAL_COLUMN_DIVIDERS, GridVariant.LUMO_COLUMN_BORDERS,
                    GridVariant.LUMO_ROW_STRIPES);
            commandsGrid.setItems(commandsList);
            tabPanel.add(commandsGrid);
            tabPanel.setPadding(false);
        }
    }
    private SaeEvent previousEvent;

    @Override
    public void receiveBroadcast(Object incomingEvent) {
        if (!(incomingEvent instanceof SaeEvent eventUnderProcessing)) {
            log.debug("unexpected event at " + this.getClassName());
            return;
        }

        if (previousEvent != null && previousEvent.equals(incomingEvent)) {
            return;
        }
        if (eventUnderProcessing.getSae() == null) {
            log.error("received event without Sae entity " + eventUnderProcessing + " " + this);
            return;
        }

        getUI().get().access(() -> {
            saeBinder.readBean(eventUnderProcessing.getSae());
        });
        previousEvent = eventUnderProcessing;
    }
}
