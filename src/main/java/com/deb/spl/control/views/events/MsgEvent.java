package com.deb.spl.control.views.events;

import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.asku.MsgType;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@EqualsAndHashCode
@Builder
public class MsgEvent {
    @NotNull
    MsgType msgType;
    @NotNull
    AdjacentSystemType adjacentSystemType;
    @NotBlank
    String payload;
    Object source;


}
