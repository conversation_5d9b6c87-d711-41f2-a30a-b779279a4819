package com.deb.spl.control.views.rocket;

import com.deb.spl.control.data.asku.Rocket;
import com.deb.spl.control.service.Broadcaster;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.views.adjacentSystems.utils.AnimationColor;
import com.deb.spl.control.views.events.RocketEvent;
import com.vaadin.flow.component.AttachEvent;
import com.vaadin.flow.component.DetachEvent;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.data.binder.Binder;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;

@Slf4j
public class RocketSensorsInfo extends VerticalLayout implements Broadcaster.BroadcastListener {
    private final Binder<Rocket> rocketBinder = new Binder<>(Rocket.class);
    private final AskuService askuService;

    private HorizontalLayout sensorsLayout;

    @Override
    protected void onAttach(AttachEvent attachEvent) {
        super.onAttach(attachEvent);
        Broadcaster.register(this, RocketEvent.class);
    }

    @Override
    protected void onDetach(DetachEvent detachEvent) {
        super.onDetach(detachEvent);
        Broadcaster.unregister(this);
    }

    public RocketSensorsInfo(AskuService askuService,
                             String textFieldClassName,
                             boolean isLeft) {
        this.askuService = askuService;
        sensorsLayout = new HorizontalLayout();
        sensorsLayout.addClassName("sensors-layout");

        TextField basuSensorValueTf = new TextField("Температура БАСУ");
        basuSensorValueTf.setReadOnly(true);
        basuSensorValueTf.addClassName(textFieldClassName);
        basuSensorValueTf.addValueChangeListener(value -> {
            try {
                if (value == null) {
                    basuSensorValueTf.removeClassName("TextField-" + AnimationColor.RED.getText());
                    basuSensorValueTf.removeClassName("TextField-" + AnimationColor.GREEN.getText());
                    basuSensorValueTf.removeClassName("TextField-" + AnimationColor.YELLOW.getText());
                }

                double temperature = Double.parseDouble(value.getValue());
                basuSensorValueTf.removeClassName("TextField-" + AnimationColor.RED.getText());
                basuSensorValueTf.removeClassName("TextField-" + AnimationColor.YELLOW.getText());
                basuSensorValueTf.removeClassName("TextField-" + AnimationColor.GREEN.getText());
                switch (this.askuService.matchMeasureLimit(temperature)) {
                    case HI -> basuSensorValueTf.addClassName("TextField-" + AnimationColor.YELLOW.getText());
                    case HI_HI -> basuSensorValueTf.addClassName("TextField-" + AnimationColor.RED.getText());
                }
            } catch (NumberFormatException e) {
                log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
            }
        });

        rocketBinder.forField(basuSensorValueTf)
                .bindReadOnly(event -> {
                    if (event == null) {
                        return "";
                    }
                    return String.valueOf(event.getSensorTemperature());
                });

        if (this.askuService.getRocketCopy(isLeft).isPresent() && getUI().isPresent()) {
            getUI().get().access(() ->
                    rocketBinder.readBean(this.askuService.getRocketCopy(isLeft).get()));
        }

        sensorsLayout.add(basuSensorValueTf);
        add(sensorsLayout);
    }

    public VerticalLayout getLayout() {
        return this;
    }


    @Override
    public void receiveBroadcast(Object event) {

    }
}
