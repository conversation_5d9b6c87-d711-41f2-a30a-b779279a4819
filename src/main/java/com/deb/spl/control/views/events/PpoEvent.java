package com.deb.spl.control.views.events;

import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.ppo.Ppo;
import lombok.*;

import java.util.Optional;

@NoArgsConstructor
@Getter
@Setter
@Builder
public class PpoEvent implements AdjacentSystemEvent{
    Ppo ppo;
    Object source;
    AdjacentSystemUpdateEventType eventType;

    /**
     * @param ppo changed object
     * @param source event source
     * @param eventType @{@link AdjacentSystemUpdateEventType}
     */
    public PpoEvent(Ppo ppo, Object source, AdjacentSystemUpdateEventType eventType) {
        this.ppo = ppo;
        this.source = source;
        this.eventType = eventType;
    }

    @Override
    public AdjacentSystemType getSystemType() {
        return ppo.getAdjacentSystemType();
    }

    @Override
    public Optional<AdjacentSystem> getEntity() {
        return Optional.ofNullable(ppo);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PpoEvent ppoEvent)) return false;

        if (getPpo() != null ? !getPpo().equals(ppoEvent.getPpo()) : ppoEvent.getPpo() != null) return false;
        if (getSource() != null ? !getSource().equals(ppoEvent.getSource()) : ppoEvent.getSource() != null)
            return false;
        return getEventType() == ppoEvent.getEventType();
    }

    @Override
    public int hashCode() {
        int result = getPpo() != null ? getPpo().hashCode() : 0;
        result = 31 * result + (getSource() != null ? getSource().hashCode() : 0);
        result = 31 * result + (getEventType() != null ? getEventType().hashCode() : 0);
        return result;
    }
}
