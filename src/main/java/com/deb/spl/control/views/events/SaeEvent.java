package com.deb.spl.control.views.events;

import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.sae.Sae;
import lombok.*;

import java.util.Optional;

@NoArgsConstructor
@Getter
@Setter
@EqualsAndHashCode
@Builder
public class SaeEvent implements AdjacentSystemEvent{
    Sae sae;
    Object source;
    AdjacentSystemUpdateEventType eventType;

    /**
     * @param sae changed object
     * @param source event source
     * @param eventType @{@link AdjacentSystemUpdateEventType}
     */
    public SaeEvent(Sae sae, Object source, AdjacentSystemUpdateEventType eventType) {
        this.sae = sae;
        this.source = source;
        this.eventType = eventType;
    }

    public SaeEvent(SaeEvent prototype) {
        this.sae=prototype.getSae();
        this.source=prototype.getSource();
        this.eventType=prototype.getEventType();
    }

    @Override
    public AdjacentSystemType getSystemType() {
        return sae.getAdjacentSystemType();
    }

    @Override
    public Optional<AdjacentSystem> getEntity() {
        return Optional.ofNullable(sae);
    }
}
