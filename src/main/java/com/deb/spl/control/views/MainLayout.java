package com.deb.spl.control.views;


import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.asku.*;
import com.deb.spl.control.data.ccv.VehicleResource;
import com.deb.spl.control.service.Broadcaster;
import com.deb.spl.control.service.CcvCommunicationService;
import com.deb.spl.control.service.DateTimeService;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.utils.Pdf.TmpFileUtils;
import com.deb.spl.control.views.adjacentSystems.SystemsHome;
import com.deb.spl.control.views.adjacentSystems.utils.AnimationColor;
import com.deb.spl.control.views.adjacentSystems.utils.ConfigurationUtils;
import com.deb.spl.control.views.adjacentSystems.utils.CountdownOTR;
import com.deb.spl.control.views.automotion.PdpView;
import com.deb.spl.control.views.events.*;
import com.deb.spl.control.views.log.LogMainView;
import com.deb.spl.control.views.readiness.ReadinessView;
import com.deb.spl.control.views.redirects.Mmhs;
import com.vaadin.flow.component.*;
import com.vaadin.flow.component.applayout.AppLayout;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.html.*;
import com.vaadin.flow.component.icon.Icon;
import com.vaadin.flow.component.icon.VaadinIcon;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.notification.NotificationVariant;
import com.vaadin.flow.component.orderedlayout.FlexComponent;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.component.textfield.TextFieldVariant;
import com.vaadin.flow.data.binder.Binder;
import com.vaadin.flow.theme.lumo.LumoUtility.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.vaadin.olli.FileDownloadWrapper;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Locale;

/**
 * The main view is a top-level placeholder for other views.
 */
@Slf4j
public class MainLayout extends AppLayout implements Broadcaster.BroadcastListener {

    private TextField сountDownOTR1Tf;
    private TextField сountDownOTR2Tf;
    private static final Binder<LocalDateTime> dateTimeBinder = new Binder<>(LocalDateTime.class);
    private static final Binder<AskuInfo> askuInfoBinder = new Binder<>(AskuInfo.class);
    private static final Binder<VehicleResource> ccvBinder = new Binder<>(VehicleResource.class);
    private final AskuService askuService;
    private final CcvCommunicationService ccvCommunicationService;
    private final DateTimeService dateTimeService;

    private final int commitMessageDisplayTime;
    private final int warningMessageDisplayTime;
    private final int errorMessageDisplayTime;
    private final int defaultMessageDisplayTime;

    @Override
    protected void onAttach(AttachEvent attachEvent) {
        super.onAttach(attachEvent);
        Broadcaster.register(this, DateTimeUpdateEvent.class);
        Broadcaster.register(this, MsgEvent.class);
        Broadcaster.register(this, AskuInfoEvent.class);
        Broadcaster.register(this, FileCreatedEvent.class);
        Broadcaster.register(this, CountdownEvent.class);
        Broadcaster.register(this, CcvVehicleEvent.class);
    }

    @Override
    protected void onDetach(DetachEvent detachEvent) {
        super.onDetach(detachEvent);
        Broadcaster.unregister(this);
    }

    public MainLayout(AskuService askuService,
                      DateTimeService dateTimeService,
                      CcvCommunicationService ccvCommunicationService,
                      @Value("#{${events.messages.commit.display-time-sec:15}*1000}")
                      int commitMessageDisplayTime,
                      @Value("#{${events.messages.warning.display-time-sec:50}*1000}")
                      int warningMessageDisplayTime,
                      @Value("#{${events.messages.error.display-time-sec:0}*1000}")
                      int errorMessageDisplayTime,
                      @Value("#{${events.messages.default.display-time-sec:50}*1000}")
                      int defaultMessageDisplayTime) {
        this.askuService = askuService;
        this.dateTimeService = dateTimeService;
        this.ccvCommunicationService = ccvCommunicationService;

        this.commitMessageDisplayTime = commitMessageDisplayTime > 0 ? commitMessageDisplayTime : 0;
        this.warningMessageDisplayTime = warningMessageDisplayTime > 0 ? warningMessageDisplayTime : 0;
        this.errorMessageDisplayTime = errorMessageDisplayTime > 0 ? errorMessageDisplayTime : 0;
        this.defaultMessageDisplayTime = defaultMessageDisplayTime;

        addToNavbar(createHeaderContent());
        getStyle().set("text-align", "center");
    }

    private Component createHeaderContent() {
        addClassName("main-header");
        Header header = new Header();
        header.addClassNames(BoxSizing.BORDER, Display.FLEX, FlexDirection.COLUMN);

        Div layout = new Div();
        layout.addClassName("div-header");

        //sdo image
        Image sdoLogo = new Image("images/sdo-logo-white.png", "SDO Logo");
        sdoLogo.addClassNames("sdo-logo");

        layout.add(sdoLogo);

        TextField currentDateTf = new TextField();
        currentDateTf.addClassName("current-date");

        // Create a DateTimeFormatter with a Ukrainian Locale
        DateTimeFormatter ukrainianFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy\tE", new Locale("uk"));

        currentDateTf.setValue(LocalDateTime.now().format(ukrainianFormatter));

        currentDateTf.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        Span datePrefix = new Span("Дата: ");
        datePrefix.addClassName("dataPrefix");
        currentDateTf.setPrefixComponent(datePrefix);
        currentDateTf.setReadOnly(true);

        dateTimeBinder.forField(currentDateTf).bindReadOnly(val -> {
            if (val == null) {
                return "Помилка конвертації";
            }
            return val.format(ukrainianFormatter); // Use the Ukrainian formatter
        });

        TextField currentTime = new TextField();
        currentTime.addClassName("current-time");
        currentTime.setValue(dateTimeService.isUSE_BINS_TIME() ?
                dateTimeService.getDateTime()
                        .orElse(LocalDateTime.now())
                        .format(DateTimeFormatter.ofPattern("HH:mm:ss"))
                : LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss")));

        currentTime.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        Span timePrefix = new Span("Час:");
        timePrefix.addClassName("timePrefix");
        currentTime.setPrefixComponent(timePrefix);
        currentTime.setReadOnly(true);
        dateTimeBinder.forField(currentTime).bindReadOnly(val -> {
            if (val == null) {
                return "Помилка конвертації";
            }
            return val.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        });

        TextField readinessTF = new TextField();
        readinessTF.addClassName("current-readiness");
        readinessTF.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        readinessTF.setValue(askuService.getSplReadiness().isEmpty() ? Readiness.UNDEFINED.getValueUa() :
                askuService.getSplReadiness().get().getValueUa());
        askuInfoBinder.forField(readinessTF)
                .bindReadOnly(askuInfo -> askuInfo.getSplReadiness().isEmpty() ? Readiness.UNDEFINED.getValueUa() :
                        askuInfo.getSplReadiness().get().getValueUa());

        сountDownOTR1Tf = new TextField();
        Span labelCoundownOTR1 = new Span("Таймер пуску ОТР1");
        labelCoundownOTR1.addClassName("label-countdown-otr1");
        сountDownOTR1Tf.setReadOnly(true);
        сountDownOTR1Tf.addClassName("countdown-otr1-tf");

        int otr1CountDown = 0;
        if (askuService.getRocketCopy(true).isPresent() &&
            askuService.getRocketCopy(true).isPresent() && askuService.getRocketCopy(true).get().getInitialData() != null) {
            otr1CountDown = CountdownOTR.getCountdownOTR1Seconds();
        }

        CountdownOTR.getCountdownOTR1Seconds();
        if (otr1CountDown != CountdownOTR.getCountdownOTR1Seconds()) {
            сountDownOTR1Tf.setValue(CountdownOTR.formatCountDownTime(CountdownOTR.getCountdownOTR1Seconds()));
        } else {
            сountDownOTR1Tf.setValue(CountdownOTR.formatCountDownTime(otr1CountDown));
        }
        сountDownOTR2Tf = new TextField();
        сountDownOTR2Tf.setReadOnly(true);
        Span labelCoundownOTR2 = new Span("Таймер пуску ОТР2");
        labelCoundownOTR2.addClassName("label-countdown-otr2");
        сountDownOTR2Tf.addClassName("countdown-otr2-tf");

        int otr2CountDown = 0;
        if (askuService.getRocketCopy(false).isPresent()
            && askuService.getRocketCopy(false).isPresent() && askuService.getRocketCopy(false).get().getInitialData() != null) {
            otr2CountDown = CountdownOTR.getCountdownOTR2Seconds();
        }

        CountdownOTR.getCountdownOTR2Seconds();
        if (otr1CountDown != CountdownOTR.getCountdownOTR2Seconds()) {
            сountDownOTR2Tf.setValue(CountdownOTR.formatCountDownTime(CountdownOTR.getCountdownOTR2Seconds()));
        } else {
            сountDownOTR2Tf.setValue(CountdownOTR.formatCountDownTime(otr2CountDown));
        }


        TextField ccvName = new TextField();
        ccvName.setReadOnly(true);
        ccvName.addClassName("master-ccv-connection");
        ccvName.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        ccvName.addClassNames(Margin.Vertical.SMALL, Margin.End.AUTO);
        ccvName.addClassNames(TextAlignment.RIGHT);
        ccvBinder.forField(ccvName)
                .bindReadOnly(vehicleResource -> {
                    try {
                        if (vehicleResource == null) {
                            return "Немає зв'язку";
                        }
                        return vehicleResource.isHasConnection() ? "Норма" : "Немає зв'язку";
                    } catch (Exception e) {
                        log.error(Arrays.toString(e.getStackTrace()));
                        return "Немає зв'язку";
                    }
                });

        ccvName.addValueChangeListener(valueChangeEvent -> {
            if (!valueChangeEvent.getOldValue().equals(valueChangeEvent.getValue()))
                ConfigurationUtils.setBooleanToTextAnimation(
                        valueChangeEvent,
                        "Норма",
                        "Немає зв'язку",
                        AnimationColor.GREEN,
                        AnimationColor.RED);
        });

        ccvName.setValue(ccvCommunicationService.isHasConnection() ? "Норма" : "Немає зв'язку");

        Span ccvNameTitle = new Span("Зв'язок з КМУ");
        ccvNameTitle.addClassName("ccv-name-title");

        layout.add(currentDateTf, currentTime, readinessTF, labelCoundownOTR1, сountDownOTR1Tf, labelCoundownOTR2,
                сountDownOTR2Tf, ccvNameTitle, ccvName);

        Nav nav = new Nav();
        nav.addClassNames("itemTabNav");
        // Wrap the links in a list; improves accessibility
        UnorderedList list = new UnorderedList();
        list.addClassNames("itemTabList");
        nav.add(list);

        for (MenuItemInfo menuItem : createMenuItems()) {
            list.add(menuItem);
        }

        header.add(layout, nav);
        return header;
    }


    private ComboBox<Readiness> configureReadinessCombo() {
        ComboBox<Readiness> readinessCB = new ComboBox<>();
        readinessCB.addClassName("current-readiness");
        readinessCB.setItems(Arrays.asList(Readiness.values()));
        readinessCB.setItemLabelGenerator(Readiness::getValueUa);
        askuInfoBinder.forField(readinessCB)
                .bind(askuInfo -> {
                    if (askuInfo.getSplReadiness().isEmpty()) {
                        return Readiness.UNDEFINED;
                    }
                    return askuInfo.getSplReadiness().get();
                }, AskuInfo::setSplReadiness);
        readinessCB.setValue(askuService.getSplReadiness().isEmpty() ? Readiness.UNDEFINED : askuService.getSplReadiness().get());

        readinessCB.addValueChangeListener(event -> {
            if (event == null) {
                return;
            }
            boolean accepted = false;
            try {
                accepted = askuService.requestSplReadinessChange(event.getValue());
            } catch (Exception e) {
                log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                accepted = false;
            }
            if (!accepted) {
                if (getUI().isPresent()) {
                    getUI().get().access(() -> readinessCB.setValue(askuService.getSplReadiness().isEmpty() ? Readiness.UNDEFINED : askuService.getSplReadiness().get()));
                }
                Notification notification = new Notification();
                notification.addThemeVariants(NotificationVariant.LUMO_ERROR);
                Div statusText = new Div(new Text("Помилка при зміні режиму роботи СПУ"));
                notification.setPosition(Notification.Position.TOP_END);

                Button okOnlyBt = new Button("Прийняти", e -> {
                    notification.close();
                });

                notification.add(statusText);
                HorizontalLayout body = new HorizontalLayout(okOnlyBt);
                body.setWidthFull();
                body.setAlignItems(FlexComponent.Alignment.END);
                notification.add(body);
                notification.open();

                Notification.show("Запит на зміну готовності не був прийнятий");
            }
        });

        return readinessCB;
    }

    private MenuItemInfo[] createMenuItems() {
        MenuItemInfo systems = new MenuItemInfo("Статус систем", new Icon(VaadinIcon.CARET_RIGHT), SystemsHome.class);

        MenuItemInfo logs = new MenuItemInfo("Журнал", new Icon(VaadinIcon.TWIN_COL_SELECT), LogMainView.class);
        MenuItemInfo launch = new MenuItemInfo("ПДП", new Icon(VaadinIcon.ROCKET), PdpView.class);

        MenuItemInfo readiness = new MenuItemInfo("Зміна готовності", new Icon(VaadinIcon.ARROWS_CROSS), ReadinessView.class);

        MenuItemInfo mmhs = new MenuItemInfo("ПЗ СПУ", new Icon(VaadinIcon.DESKTOP), Mmhs.class);

        return new MenuItemInfo[]{systems, launch, readiness, logs, mmhs};
    }

    @Override
    public void receiveBroadcast(Object incomingEvent) {
        if (incomingEvent == null) {
            log.warn("Expected  " + AskuInfoEvent.class.getSimpleName() + " " + DateTimeUpdateEvent.class.getSimpleName() + " but found null " + this);
            return;
        }

        if (incomingEvent instanceof DateTimeUpdateEvent) {
            processDateTimeEvent((DateTimeUpdateEvent) incomingEvent);
        } else if (incomingEvent instanceof AskuInfoEvent) {
            processAskuInfoEvent((AskuInfoEvent) incomingEvent);
        } else if (incomingEvent instanceof FileCreatedEvent event) {
            processFileEvent(event);
        } else if (incomingEvent instanceof MsgEvent) {
            processMsgEvent((MsgEvent) incomingEvent);
        } else if (incomingEvent instanceof CountdownEvent event) {
            processCountdownEvent(event);
        } else if (incomingEvent instanceof CcvVehicleEvent event) {
            processCcvEvent(event);
        } else {
            log.debug("unexpected event at " + this.getClassName());
        }
    }

    private AskuInfoEvent previousAskuInfoEvent;

    private void processCountdownEvent(CountdownEvent incomingEvent) {
        if (incomingEvent != null && getUI().isEmpty()) {
            return;
        }
        UI ui = getUI().get();

        assert incomingEvent != null;
        if (incomingEvent.getEventType().equals(CountdownEventType.UPDATE)) {
            if (incomingEvent.getLaunchType().equals(LaunchType.FIRST)) {
                ui.access(() -> {
                    сountDownOTR1Tf.setValue(String.format("%02d:%02d",
                            incomingEvent.getTimerValue() / 60,
                            incomingEvent.getTimerValue() % 60));
                });
            } else if (incomingEvent.getLaunchType().equals(LaunchType.SECOND)) {
                ui.access(() -> {
                    сountDownOTR2Tf.setValue(String.format("%02d:%02d",
                            incomingEvent.getTimerValue() / 60,
                            incomingEvent.getTimerValue() % 60));
                });
            }
        }
    }

    private void processAskuInfoEvent(AskuInfoEvent incomingEvent) {
        if (previousAskuInfoEvent != null && previousAskuInfoEvent.equals(incomingEvent)) {
            return;
        }
        if (incomingEvent.getAskuInfo() == null || getUI().isEmpty()) {
            return;
        }

        getUI().get().access(() -> askuInfoBinder.readBean(incomingEvent.getAskuInfo()));
        previousAskuInfoEvent = incomingEvent;
    }

    private DateTimeUpdateEvent previousDateTimeUpdateEvent;

    private void processDateTimeEvent(DateTimeUpdateEvent incomingEvent) {
        if (incomingEvent.getDateTime() == null || getUI().isEmpty()) {
            return;
        }
        getUI().get().access(() -> dateTimeBinder.readBean(incomingEvent.getDateTime()));
        previousDateTimeUpdateEvent = incomingEvent;
    }

    private void processFileEvent(FileCreatedEvent incomingEvent) {
        getUI().get().access(() -> {
            Notification notification = new Notification();
            notification.addClassName("plc-msg-notification");

            Button closeButton;

            notification.setDuration(-1);
            closeButton = new Button(new Icon("lumo", "cross"));
            closeButton.addThemeVariants(ButtonVariant.LUMO_SMALL);
            closeButton.addClassName("close-button-notify-info");

            Div caption = incomingEvent.getMsgType() == null ? new Div(new Text("")) :
                    new Div(new Text(incomingEvent.getMsgType().getValueUa()));
            caption.getStyle().set("font-weight", "bold");
            caption.addClassName("caption-message");

            VerticalLayout filesUploadLayout = new VerticalLayout();
            if (incomingEvent.getFilesInfo() != null && incomingEvent.getFilesInfo().size() > 0) {
                for (FileCreatedEvent.FileInfo fileInfo : incomingEvent.getFilesInfo()) {
                    FileDownloadWrapper downloadWrapper = null;
                    Div mainText = new Div();
                    if (fileInfo.fileName() != null && TmpFileUtils.getTopMostFileByPath(fileInfo.pathToFile()).isPresent()) {
                        downloadWrapper = new FileDownloadWrapper(fileInfo.fileName(),
                                TmpFileUtils.getTopMostFileByPath(fileInfo.pathToFile()).get());
                        Button getDataBt = new Button("Get data");
                        getDataBt.addClickListener(event -> Notification.show("clicked!!!!11111!!!!"));
                        downloadWrapper.wrapComponent(getDataBt);
                        downloadWrapper.setText(fileInfo.fileName());

                        float fileSize = fileInfo.size() != 0 ? fileInfo.size() / 1024 / 1024 : 0.00F;
                        mainText.add(downloadWrapper, new Text(String.format("\t %.3f MB", fileSize)));
                        filesUploadLayout.add(mainText);
                    }

                }
                closeButton.getElement().setAttribute("aria-label", "Close");
                closeButton.addClickListener(event -> {
                    notification.close();
                });
            }

            HorizontalLayout header = new HorizontalLayout(caption, closeButton);
            header.addClassNames("header-crossed");
            header.setAlignItems(FlexComponent.Alignment.END);
            Div payload = new Div(new Text(incomingEvent.getPayload()));
            VerticalLayout combinedLayout = new VerticalLayout(header, payload,
                    filesUploadLayout);


            notification.add(combinedLayout);
            notification.open();
        });

    }

    private void processMsgEvent(MsgEvent incomingEvent) {
        if (getUI().isEmpty()) {
            return;
        }

        getUI().get().access(() -> {
            Notification notification = new Notification();
            notification.addClassName("plc-msg-notification");

            Button closeButton;
            switch (incomingEvent.getMsgType()) {
                case ERROR -> {
                    notification.addThemeVariants(NotificationVariant.LUMO_ERROR);
                    notification.setDuration(errorMessageDisplayTime);
                    notification.addClassName("notification-error");
                    closeButton = new Button("Прочитано");
                    closeButton.addClassName("close-button-notify-error");
                }
                case COMMIT -> {
                    notification.addThemeVariants(NotificationVariant.LUMO_SUCCESS);
                    notification.setDuration(commitMessageDisplayTime);
                    notification.addClassName("notification-commit");
                    closeButton = new Button("Прочитано");
                    closeButton.addClassName("close-button-notify-commit");
                }
                case WARNING -> {
                    notification.addThemeVariants(NotificationVariant.LUMO_CONTRAST);
                    notification.setDuration(warningMessageDisplayTime);
                    notification.addClassName("notification-warning");
                    closeButton = new Button("Прочитано");
                    closeButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY_INLINE);
                    closeButton.addClassName("close-button-notify-warning");
                }
                default -> {
                    notification.setDuration(defaultMessageDisplayTime);
                    closeButton = new Button(new Icon("lumo", "cross"));
                    closeButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY_INLINE);
                    closeButton.addClassName("close-button-notify-info");
                }
            }

            Div caption = incomingEvent.getMsgType() == null ? new Div(new Text("")) :
                    new Div(new Text(incomingEvent.getMsgType().getValueUa()));
            caption.getStyle().set("font-weight", "bold");
            caption.addClassName("caption-message");
            Div mainText = new Div();
            mainText.add(new Text(incomingEvent.getPayload()));

            Div systemInfoText = new Div();
            if (incomingEvent.getAdjacentSystemType() != null && incomingEvent.getAdjacentSystemType() != AdjacentSystemType.UNCLASSIFIED &&
                incomingEvent.getAdjacentSystemType() != AdjacentSystemType.UNDEFINED) {
                systemInfoText.add(new Text(" Система:" + incomingEvent.getAdjacentSystemType().getUa()));
            }

            closeButton.getElement().setAttribute("aria-label", "Close");
            closeButton.addClickListener(event -> {
                notification.close();
            });

            VerticalLayout layout = new VerticalLayout();
            if (incomingEvent.getMsgType() == MsgType.ERROR) {
                layout = new VerticalLayout(caption, mainText, systemInfoText, closeButton);
            } else {
                HorizontalLayout header = new HorizontalLayout(caption, closeButton);
                header.addClassNames("header-crossed");
                header.setAlignItems(FlexComponent.Alignment.END);
                layout = new VerticalLayout(new HorizontalLayout(caption, closeButton), mainText, systemInfoText);
            }

            notification.add(layout);
            notification.open();
        });

    }

    private void processCcvEvent(CcvVehicleEvent event) {
        if (event == null) {
            return;
        }
        if (event.getVehicleResource().isPresent()) {
            ccvBinder.readBean(event.getVehicleResource().get());
        }
    }

}
