package com.deb.spl.control.views.adjacentSystems;

import com.deb.spl.control.data.PropertyType;
import com.deb.spl.control.data.asku.RocketMapper;
import com.deb.spl.control.data.asku.TpcState;
import com.deb.spl.control.data.sae.CommandState;
import com.deb.spl.control.data.suto.*;
import com.deb.spl.control.repository.suto.SutoHistoryRepository;
import com.deb.spl.control.service.*;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.service.asku.PlcService;
import com.deb.spl.control.views.MainLayout;
import com.deb.spl.control.views.events.SutoEvent;
import com.vaadin.flow.component.AttachEvent;
import com.vaadin.flow.component.DetachEvent;
import com.vaadin.flow.component.Html;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.dialog.Dialog;
import com.vaadin.flow.component.grid.ColumnTextAlign;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.grid.GridVariant;
import com.vaadin.flow.component.html.Span;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.component.textfield.TextFieldVariant;
import com.vaadin.flow.data.binder.Binder;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import com.vaadin.flow.theme.lumo.LumoUtility;
import lombok.extern.slf4j.Slf4j;
import org.vaadin.tabs.PagedTabs;

import javax.validation.constraints.NotNull;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static com.deb.spl.control.data.suto.OutriggerName.*;

@Slf4j
@PageTitle("Сатус систем | СУТО")
@Route(value = "adjacent-systems/suto", layout = MainLayout.class)
public class SutoView extends AdjacentSystemView implements Broadcaster.BroadcastListener {

    private final Binder<Suto> sutoBinder = new Binder<>(Suto.class);
    private final SutoService sutoService;
    private final UserService userService;
    private final AskuService askuService;
    private final SutoHistoryRepository historyRepository;
    private Grid<SutoProperty> propertiesGrid;

    @Override
    protected void onAttach(AttachEvent attachEvent) {
        super.onAttach(attachEvent);
        Broadcaster.register(this, SutoEvent.class);
    }

    @Override
    protected void onDetach(DetachEvent detachEvent) {
        super.onDetach(detachEvent);
        Broadcaster.unregister(this);
    }

    public SutoView(MsuService msuService,
                    PpoService ppoService,
                    SutoService sutoService,
                    BinsService binsService,
                    SaeService saeService,
                    PlcService plcService,
                    AskuService askuService,
                    UserService userService,
                    SutoHistoryRepository historyRepository,
                    RocketMapper rocketMapper,
                    NppaService nppaService,
                    CcvCommunicationService ccvCommunicationService) {
        super(msuService,
                ppoService,
                sutoService,
                binsService,
                saeService,
                askuService,
                plcService,
                "Система управління технологічним обладнанням",
                rocketMapper,
                nppaService,
                ccvCommunicationService);
        this.sutoService = sutoService;
        this.userService = userService;
        this.historyRepository = historyRepository;
        this.askuService = askuService;
        VerticalLayout statusTab = new VerticalLayout();
        SutoCommandsTab sutoCommandsTab = new SutoCommandsTab();
        VerticalLayout commandTabSuto = new VerticalLayout();

        commandTabSuto.setWidthFull();
        commandTabSuto.addClassName("command-tab-suto");
        commandTabSuto.setWidth("100%");
        commandTabSuto.setMaxWidth("100%");
        commandTabSuto.add(sutoCommandsTab);//commands class

        sutoCommandsTab.addClassName("suto-commands-tab");

        VerticalLayout logTabHolder = new LogTab(historyRepository, "suto-log-tab");

        VerticalLayout tabContainer = new VerticalLayout();
        PagedTabs tabs = new PagedTabs(tabContainer);
        tabs.getContent();

        String statusHeader = "Статус";
        String commandTabHeader = "Команди";
        String logTabHeader = "Журнал";

        VerticalLayout sutoStatus = new SutoStatus();

        HorizontalLayout sutoStatusTabFirstColumn = new SutoStatusTabFirstColumn();
        HorizontalLayout sutoStatusTabSecondColumn = new SutoStatusTabSecondColumn();
        HorizontalLayout sutoStatusTabSecond = new SutoStatusTabSecond();
        HorizontalLayout sutoStatusTabThird = new SutoStatusTabThird();

        sutoStatusTabFirstColumn.addClassName("suto-status-tab-first-column");
        sutoStatusTabSecondColumn.addClassName("suto-status-tab-second-column");
        sutoStatusTabSecond.addClassName("suto-status-tab-second");
        sutoStatusTabThird.addClassName("suto-status-tab-third");
        sutoStatus.addClassNames(LumoUtility.Display.FLEX);
        sutoStatus.addClassNames(LumoUtility.AlignItems.CENTER);

        sutoStatus.addClassName("suto-status");
        sutoStatus.setHeight("645px");
        sutoStatus.add(sutoStatusTabSecond, sutoStatusTabThird, sutoStatusTabFirstColumn, sutoStatusTabSecondColumn);

        setupSutoPropertiesGrid(sutoStatus);
        sutoStatus.addClassName("setup-suto-properties-grid");
        statusTab.add(sutoStatus);
        statusTab.addClassName("suto-status-tab");

        //commands class
        tabs.add(statusHeader, statusTab, false);
        tabs.add(commandTabHeader, commandTabSuto, false);
        tabs.add(logTabHeader, logTabHolder, false);

        getSystemLayout().add(tabs, tabContainer);
        getSystemLayout().addClassName("system-layout-suto");
        getSystemLayout().setWidth("69.5%");
        getSystemLayout().setMaxWidth("69.5%");
    }

    private static class SutoStatus extends VerticalLayout {

        public SutoStatus() {
            add();
        }

    }

    private void setupOutriggerAnimation(@NotNull TextField textField, @NotNull OutriggerName outriggerName) {
        textField.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, OutriggerEmergencyCode.class));
        sutoBinder.forField(textField)
                .bindReadOnly(suto -> {
                    if (suto == null) {
                        return OutriggerEmergencyCode.SENSORS_MALFUNCTION.getValueUa();
                    }

                    try {
                        Method getOutriggerMalfunctionCodeMethod = Suto.class.getMethod(outriggerName.methodName);
                        OutriggerEmergencyCode code = (OutriggerEmergencyCode) getOutriggerMalfunctionCodeMethod.invoke(suto);

                        return code.getValueUa();
                    } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
                        log.error(Arrays.toString(e.getStackTrace()));
                        return OutriggerEmergencyCode.SENSORS_MALFUNCTION.getValueUa();
                    }
                });
    }

    private class SutoStatusTabFirstColumn extends HorizontalLayout {

        public SutoStatusTabFirstColumn() {
            TextField pillarFL = new TextField("Опора передня ліва");
            pillarFL.setReadOnly(true);
            pillarFL.addClassName("pillar-FL");
            pillarFL.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            pillarFL.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, OutriggerEmergencyCode.class));
            setupOutriggerAnimation(pillarFL, leftFront);

            TextField pillarFR = new TextField("Опора передня права");
            pillarFR.setReadOnly(true);
            pillarFR.addClassName("pillar-FR");
            pillarFR.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            setupOutriggerAnimation(pillarFR, rightFront);

            add(pillarFL, pillarFR);
        }

    }

    private class SutoStatusTabSecondColumn extends HorizontalLayout {

        public SutoStatusTabSecondColumn() {
            TextField pillarBL = new TextField("Опора задня ліва");
            pillarBL.setReadOnly(true);
            pillarBL.addClassName("pillar-BL");
            pillarBL.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            setupOutriggerAnimation(pillarBL, leftRear);

            TextField pillarBR = new TextField("Опора задня права");
            pillarBR.setReadOnly(true);
            pillarBR.addClassName("pillar-BR");
            pillarBR.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            setupOutriggerAnimation(pillarBR, rightRear);

            add(pillarBL, pillarBR);
        }

    }

    private class SutoStatusTabSecond extends HorizontalLayout {

        public SutoStatusTabSecond() {

            TextField rollSuto = new TextField("Крен");
            rollSuto.setReadOnly(true);
            rollSuto.addClassName("roll-SUTO");
            rollSuto.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            sutoBinder.forField(rollSuto)
                    .bindReadOnly(s -> {
                        if (s == null || s.getStats() == null) {
                            return "";
                        }
                        return String.valueOf(s.getStats().getRoll());
                    });

            TextField pitchSuto = new TextField("Тангаж");
            pitchSuto.setReadOnly(true);
            pitchSuto.addClassName("pitch-SUTO");
            pitchSuto.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            sutoBinder.forField(pitchSuto)
                    .bindReadOnly(s -> {
                        if (s == null || s.getStats() == null) {
                            return "";
                        }
                        return String.valueOf(s.getStats().getPitch());
                    });

            TextField arrowSuto = new TextField("Положення штоку циліндра");
            arrowSuto.setReadOnly(true);
            arrowSuto.addClassName("arrow-SUTO");
            arrowSuto.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            sutoBinder.forField(arrowSuto)
                    .bindReadOnly(s -> {
                        if (s == null || s.getStats() == null) {
                            return "";
                        }
                        return String.valueOf(s.getStats().getArmLiftStrokePosition());
                    });
            TextField temperatureRR = new TextField("Температура РР");
            temperatureRR.setReadOnly(true);
            temperatureRR.addClassName("temperature-RR");
            temperatureRR.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            sutoBinder.forField(temperatureRR)
                    .bindReadOnly(s -> {
                        if (s == null || s.getStats() == null) {
                            return "";
                        }
                        return String.valueOf(s.getStats().getTemperatureRR());
                    });
            TextField levelWorkingFluid = new TextField("Рівень РР");
            levelWorkingFluid.setReadOnly(true);
            levelWorkingFluid.addClassName("level-working-fluid");
            levelWorkingFluid.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            sutoBinder.forField(levelWorkingFluid)
                    .bindReadOnly(s -> {
                        if (s == null || s.getStats() == null) {
                            return "";
                        }
                        return String.valueOf(s.getStats().getWorkingFluidLevel());
                    });
            add(rollSuto, pitchSuto, arrowSuto, temperatureRR, levelWorkingFluid);
        }

    }

    private class SutoStatusTabThird extends HorizontalLayout {

        public SutoStatusTabThird() {
            TextField preassure = new TextField("Тиск");
            preassure.setReadOnly(true);
            preassure.addClassName("pressure-impulse-section");
            preassure.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            sutoBinder.forField(preassure)
                    .bindReadOnly(s -> {
                        if (s == null || s.getStats() == null) {
                            return "";
                        }
                        return String.valueOf(s.getStats().getPressureInImpulseSection());
                    });

            TextField mainPumpSpeed = new TextField("Оберти насосу");
            mainPumpSpeed.setReadOnly(true);
            mainPumpSpeed.addClassName("main-pump-speed");
            mainPumpSpeed.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            sutoBinder.forField(mainPumpSpeed)
                    .bindReadOnly(s -> {
                        if (s == null || s.getStats() == null) {
                            return "";
                        }
                        return String.valueOf(s.getStats().getMainPumpRPM());
                    });

            TextField numberLevelingCycles = new TextField("Циклів горизонтування");
            numberLevelingCycles.setReadOnly(true);
            numberLevelingCycles.addClassName("number-leveling-cycles");
            numberLevelingCycles.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            sutoBinder.forField(numberLevelingCycles)
                    .bindReadOnly(s -> {
                        if (s == null || s.getStats() == null) {
                            return "";
                        }
                        return String.valueOf(s.getStats().getLevelingCyclesCount());
                    });

            TextField runtimeCounter = new TextField("Лічильник напрацювань");
            runtimeCounter.setReadOnly(true);
            runtimeCounter.addClassName("runtime-counter");
            runtimeCounter.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            sutoBinder.forField(runtimeCounter)
                    .bindReadOnly(s -> {
                        if (s == null || s.getStats() == null) {
                            return "";
                        }
                        return String.valueOf(s.getStats().getOverallOperatingTime());
                    });

            TextField numberArrowLifts = new TextField("Підйомів стріли");
            numberArrowLifts.setReadOnly(true);
            numberArrowLifts.addClassName("number-arrow-lifts");
            numberArrowLifts.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            sutoBinder.forField(numberArrowLifts)
                    .bindReadOnly(s -> {
                        if (s == null || s.getStats() == null) {
                            return "";
                        }
                        return String.valueOf(s.getStats().getOverallArmLiftingsCount());
                    });
            add(preassure, mainPumpSpeed, numberLevelingCycles, runtimeCounter, numberArrowLifts);
        }
    }

    private void setupSutoPropertiesGrid(VerticalLayout tabPanel) {

        List<SutoProperty> propertiesList = sutoService.getAvailableProperties();

        propertiesGrid = new Grid<>(SutoProperty.class, false);
        propertiesGrid.addThemeVariants(GridVariant.LUMO_WRAP_CELL_CONTENT);

        Grid.Column<SutoProperty> id = propertiesGrid
                .addColumn(SutoProperty::getId).setHeader("id").setTextAlign(ColumnTextAlign.START)
                .setFlexGrow(0).setWidth("5%");

        Grid.Column<SutoProperty> caption = propertiesGrid
                .addColumn(SutoProperty::getCaption).setHeader("Параметр").setTextAlign(ColumnTextAlign.START)
                .setFlexGrow(0).setWidth("82%");

        Grid.Column<SutoProperty> value = propertiesGrid
                .addColumn(item -> {
                    if (item == null || item.getState() == null) {
                        setClassName("grid-cell-none");
                        return "";
                    }
                    if (item.getPropertyType().equals(PropertyType.MALFUNCTION)) {
                        return item.getState().equals(CommandState.ON) ? "Не норма" : "Норма";
                    } else {
                        return item.getState().equals(CommandState.ON) ? "Так" : "Ні";
                    }
                }).setHeader("Стан опори")
                .setTextAlign(ColumnTextAlign.CENTER)
                .setFlexGrow(0)
                .setWidth("13%")
                .setClassNameGenerator(item -> {
                    if (item == null || item.getState() == null) {
                        return "grid-cell-none";
                    }
                    if (item.getPropertyType().equals(PropertyType.MALFUNCTION)) {
                        return item.getState().equals(CommandState.ON) ? "grid-cell-red" : "grid-cell-green";
                    } else {
                        return item.getState().equals(CommandState.ON) ? "grid-cell-blue" : "grid-cell-gray";
                    }
                });

        propertiesGrid.addClassName("properties-grid");
        propertiesGrid.setHeight("385px");
        propertiesGrid.addThemeVariants(GridVariant.MATERIAL_COLUMN_DIVIDERS, GridVariant.LUMO_COLUMN_BORDERS, GridVariant.LUMO_ROW_STRIPES);
        propertiesGrid.setItems(query -> sutoService.getPropertiesStream(query.getPage(), query.getPageSize()));
        tabPanel.add(propertiesGrid);
    }

    private class SutoCommandsTab extends VerticalLayout {
        public SutoCommandsTab() {
            VerticalLayout tabPanel = new VerticalLayout();
            tabPanel.setWidth("100%");
            tabPanel.setMaxWidth("100%");
            tabPanel.addClassName("tab-panel-ppo");

            //settings array
            List<TpcState> tpcStates = Arrays.stream(TpcState.values()).toList();

            ComboBox<TpcState> leftTpcCombo = new ComboBox<>("Лівий ТПК");
            leftTpcCombo.setItemLabelGenerator(TpcState::getValueUa);
            leftTpcCombo.setItems(tpcStates);
            leftTpcCombo.setValue(askuService.getLeftTpcLoadState().orElse(TpcState.UNKNOWN));

            ComboBox<TpcState> rightTpcCombo = new ComboBox<>("Правий ТПК");
            rightTpcCombo.setItemLabelGenerator(TpcState::getValueUa);
            rightTpcCombo.setItems(tpcStates);
            rightTpcCombo.setValue(askuService.getRightTpcLoadState().orElse(TpcState.UNKNOWN));

            List<ChassisState> chassisStates = Arrays.stream(ChassisState.values()).toList();
            ComboBox<ChassisState> chassisStateCb = new ComboBox<>("Стан шассі");
            chassisStateCb.setItems(chassisStates);
            chassisStateCb.setItemLabelGenerator(ChassisState::getValueUa);
            chassisStateCb.setValue(sutoService.getSutoSettings().orElse(SutoSettings.builder()
                            .chassisState(ChassisState.UNKNOWN)
                            .build())
                    .getChassisState());
            sutoBinder.forField(chassisStateCb)
                    .bind(val -> {
                        if (val == null || val.getSutoSettings() == null) {
                            return ChassisState.UNKNOWN;
                        }
                        return val.getSutoSettings().getChassisState() != null ?
                                val.getSutoSettings().getChassisState() :
                                ChassisState.UNKNOWN;
                    }, (bean, val) -> {
                    });

            Button updateTpcBt = new Button("Оновити", (e) -> {
                Dialog dialog = new Dialog();
                dialog.addClassName("dialog-update-suto-settings");

                dialog.setHeaderTitle("Оновлення налаштувань СУТО");
                dialog.add("Ви впевнені, що бажаєте оновити налаштування СУТО?");
                HorizontalLayout hz = new HorizontalLayout();
                hz.add("Лівий ТПК: " + leftTpcCombo.getValue().getValueUa() + "\n" +
                       "Правий ТПК: " + rightTpcCombo.getValue().getValueUa() + "\n" +
                       "Стан шассі: " + chassisStateCb.getValue().getValueUa());
                dialog.add(hz);

                Button confirmBt = new Button("Так", (confirmEvent) -> {

                    //update validate and update settings
                    sutoService.updateSutoSettings(leftTpcCombo.getValue(), rightTpcCombo.getValue()
                            , chassisStateCb.getValue());

                    dialog.close();
                });
                confirmBt.addClassName("confirm-button-stuo-dialog");

                confirmBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY,
                        ButtonVariant.LUMO_ERROR);
                confirmBt.getStyle().set("margin-right", "auto");
                dialog.getFooter().add(confirmBt);

                Button cancelButton = new Button("Відміна", (cancelEvent) -> dialog.close());
                cancelButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
                cancelButton.addClassName("cancel-button-ppo-dialog");

                dialog.getFooter().add(cancelButton);

                dialog.open();

            });

            updateTpcBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY, ButtonVariant.LUMO_SUCCESS);
            updateTpcBt.addClassName("update-tpc-bt");

            HorizontalLayout tpcs = new HorizontalLayout();
            tpcs.addClassName("tpcs");
            tpcs.setMinWidth("100%");
            tpcs.addClassName(LumoUtility.Display.FLEX);
            tpcs.addClassName(LumoUtility.AlignItems.CENTER);
            tpcs.add(leftTpcCombo, rightTpcCombo, chassisStateCb, updateTpcBt);

            add(tpcs);

            //commands
            HorizontalLayout commandsLayoutH = new HorizontalLayout();
            commandsLayoutH.addClassName("commands-layout-h");
            commandsLayoutH.setWidthFull();
            commandsLayoutH.setWidth("100%");
            commandsLayoutH.setMinWidth("100%");
            VerticalLayout commandsLayout = new VerticalLayout();
            commandsLayout.addClassName("commands-layout-v");
            commandsLayout.setWidth("100%");
            commandsLayout.setMinWidth("100%");
            commandsLayoutH.add(commandsLayout);
            tabPanel.add(commandsLayoutH);

            Span commandsCaption = new Span("Перелік команд");
            commandsCaption.addClassName("commands-caption");
            commandsLayout.add(commandsCaption);

            setupSutoCommandsGrid(commandsLayout);

            add(tabPanel);
        }

        private void setupSutoCommandsGrid(VerticalLayout tabPanel) {

            List<SutoCommand> commandsList = sutoService.getAvailableCommands();

            Grid<SutoCommand> commandsGrid = new Grid<>(SutoCommand.class, false);
            commandsGrid.addThemeVariants(GridVariant.LUMO_WRAP_CELL_CONTENT);

            Grid.Column<SutoCommand> caption = commandsGrid
                    .addColumn(SutoCommand::getCaption).setHeader(new Html("<b>Команда</b>")).setTextAlign(ColumnTextAlign.START)
                    .setFlexGrow(0).setWidth("80%");
            commandsGrid.addThemeVariants(GridVariant.LUMO_COMPACT);

            Grid.Column<SutoCommand> sentence = commandsGrid
                    .addColumn(SutoCommand::getCommand).setHeader("Відправити").setTextAlign(ColumnTextAlign.END);

            sentence.setVisible(false);

            Grid.Column<SutoCommand> sendColumn = commandsGrid.addComponentColumn(command -> {

                Button sendBt = new Button("Відправити");
                sendBt.addClassName("send-sentence-button");
                sendBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY, ButtonVariant.LUMO_SMALL);
                sendBt.getStyle().set("background-color", "#4F4F4F");

                sendBt.addClickListener(event -> {
                    Dialog dialog = new Dialog();
                    dialog.addClassName("dialog-suto");
                    dialog.setHeaderTitle(command.getCaption() + "?");
                    dialog.add("Ви впевнені, що бажаєте " + command.getCaption() + "?");

                    Button confirmBt = new Button(sentence.getHeaderText(), (e) -> {
                        SutoCommand generatedCommand = SutoCommand.builder()
                                .command(command.getCommand())
                                .caption(command.getCaption())
                                .commandState(command.getCommandState())
                                .adjacentSystem(command.getAdjacentSystem())
                                .generationTime(LocalDateTime.now())
                                .originator(userService.getUserName())
                                .build();

                        sutoService.setCommand(generatedCommand);
                        dialog.close();
                    });
                    confirmBt.addClassName("confirm-button-msu-dialog");

                    confirmBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY,
                            ButtonVariant.LUMO_ERROR);
                    confirmBt.getStyle().set("margin-right", "auto");
                    dialog.getFooter().add(confirmBt);

                    Button cancelButton = new Button("Відміна", (e) -> dialog.close());
                    cancelButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
                    cancelButton.addClassName("cancel-button-ppo-dialog");

                    dialog.getFooter().add(cancelButton);

                    dialog.open();
                });

                return sendBt;
            }).setHeader(new Html("<b>Відправити</b>"));

            commandsGrid.addClassName("commands-grid-suto");
            commandsGrid.addThemeVariants(GridVariant.MATERIAL_COLUMN_DIVIDERS, GridVariant.LUMO_COLUMN_BORDERS,
                    GridVariant.LUMO_ROW_STRIPES);
            commandsGrid.setItems(commandsList);
            tabPanel.add(commandsGrid);
            tabPanel.setPadding(false);
        }
    }

    private VerticalLayout setupStatus() {
        VerticalLayout tabPanel = new VerticalLayout();
        tabPanel.setWidth("882px");
        tabPanel.setMaxWidth("882px");
        tabPanel.addClassName("tab-panel");

//      status
        HorizontalLayout statusLayoutH = new HorizontalLayout();
        statusLayoutH.addClassName("status-layout-h");
        statusLayoutH.setWidthFull();

        VerticalLayout statusLayout = new VerticalLayout();
        statusLayout.addClassName("status-layout-v");
        statusLayoutH.add(statusLayout);
        tabPanel.add(statusLayoutH);

        Span logCaption = new Span("Статус системи");
        logCaption.addClassName("log-caption");
        statusLayout.add(logCaption);

        return tabPanel;
    }

    private SutoEvent previousEvent;

    @Override
    public void receiveBroadcast(Object incomingEvent) {
        if (!(incomingEvent instanceof SutoEvent eventUnderProcessing)) {
            log.debug("unexpected event at " + this.getClassName());
            return;
        }

        if (previousEvent != null && previousEvent.equals(incomingEvent)) {
            return;
        }
        if (eventUnderProcessing.getSuto() == null) {
            log.error("received event without SUTO entity " + eventUnderProcessing + " " + this);
            return;
        }

        getUI().get().access(() -> {
            propertiesGrid.getDataProvider().refreshAll();
            sutoBinder.readBean(eventUnderProcessing.getSuto());
        });
        previousEvent = eventUnderProcessing;
    }
}



