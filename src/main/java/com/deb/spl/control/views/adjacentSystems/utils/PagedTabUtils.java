package com.deb.spl.control.views.adjacentSystems.utils;

import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import org.vaadin.tabs.PagedTabs;

public class PagedTabUtils {

    public static void addTab(VerticalLayout tabLayout, PagedTabs tabs, String tabLayoutClassName, String tabClassName, String tabCaption) {
        VerticalLayout idTabLayout = new VerticalLayout();
        idTabLayout.setWidthFull();
        idTabLayout.addClassName(tabClassName);
        idTabLayout.setWidth("100%");
        idTabLayout.setMaxWidth("100%");
        idTabLayout.add(tabLayout);//commands class
        tabLayout.addClassName(tabLayoutClassName);
        tabs.add(tabCaption, idTabLayout, false);
    }
}
