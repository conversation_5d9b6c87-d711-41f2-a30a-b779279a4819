package com.deb.spl.control.views.rocket;

import com.deb.spl.control.controller.BadRequestException;
import com.deb.spl.control.controller.NotFoundException;
import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;
import com.deb.spl.control.data.asku.*;
import com.deb.spl.control.service.Broadcaster;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.utils.AskuUtils;
import com.deb.spl.control.utils.RocketUtils;
import com.deb.spl.control.views.adjacentSystems.utils.AnimationColor;
import com.deb.spl.control.views.adjacentSystems.utils.ConfigurationUtils;
import com.deb.spl.control.views.events.AdjacentSystemUpdateEventType;
import com.deb.spl.control.views.events.AskuInfoEvent;
import com.deb.spl.control.views.events.RocketEvent;
import com.vaadin.flow.component.AbstractField;
import com.vaadin.flow.component.AttachEvent;
import com.vaadin.flow.component.DetachEvent;
import com.vaadin.flow.component.HasValue;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.component.textfield.TextFieldVariant;
import com.vaadin.flow.data.binder.Binder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class RocketInitialDataLayout extends VerticalLayout implements Broadcaster.BroadcastListener {
    private final Binder<Rocket> rocketDataBinder = new Binder<>(Rocket.class);
    private final RocketMapper rocketMapper;
    private final Map<String, HasValue> readModeChangerList = new HashMap<>();
    private final AskuService askuService;
    private Rocket rocket;
    @Getter
    private boolean formEditModeLocked;
    private final HorizontalLayout otrData;
    private TextField orderValidUntilTf;
    private TextField launchPlannedTimeTf;

    @Override
    protected void onAttach(AttachEvent attachEvent) {
        super.onAttach(attachEvent);
        Broadcaster.register(this, RocketEvent.class);
    }

    @Override
    protected void onDetach(DetachEvent detachEvent) {
        super.onDetach(detachEvent);
        Broadcaster.unregister(this);
    }

    private final boolean isLeftRocket;

    public RocketInitialDataLayout(@NotNull boolean isLeftRocket,
                                   @NotNull boolean isEditable,
                                   @NotNull AskuService askuService,
                                   @NotNull RocketMapper rocketMapper,
                                   boolean isPdpView,
                                   String initialDataLatitudeCssName,
                                   String initialDataAltitudeCssName,
                                   String initialDataLongitudeCssName,
                                   String initialDataInclinationAngleCssName,
                                   String readinessCssName,
                                   String timeStampTfCssName,
                                   String otrDataCssName,
                                   String changeInitialDataBtCssName,
                                   String launchPlannedTime,
                                   String orderValidUntil) {
        this.askuService = askuService;
        this.rocketMapper = rocketMapper;
        this.isLeftRocket = isLeftRocket;

        this.formEditModeLocked = true;

        /*HorizontalLayout*/
        otrData = initializeOtrInitialDataLayout(isEditable, isLeftRocket, isPdpView, initialDataLatitudeCssName,
                initialDataAltitudeCssName, initialDataLongitudeCssName,
                initialDataInclinationAngleCssName, readinessCssName,
                timeStampTfCssName, changeInitialDataBtCssName, launchPlannedTime, orderValidUntil);

        otrData.addClassName(otrDataCssName);
        add(otrData);

        this.rocket = rocketMapper.clone(askuService.getRocketCopy(isLeftRocket)
                .orElse(Rocket.builder().build()));

        if (rocket.getInitialData() == null) {
            rocket.setInitialData(LaunchInitialData.builder()
                    .latitudeRad("0.0")
                    .longitudeRad("0.0")
                    .altitude(0.0)
                    .inclinationAngle(-0.0)
                    .trajectory(TrajectoryType.UNKNOWN)
                    .readiness(Readiness.UNDEFINED)
                    .proDetected(false)
                    .missileOperatingMode(MissileOperatingMode.UNKNOWN)
                    .build());
        }

        this.rocketDataBinder.setBean(rocket);
    }

    private HorizontalLayout initializeOtrInitialDataLayout(boolean isEditable, boolean isLeftRocket, boolean isPdpView,
                                                            String initialDataLatitudeCssName,
                                                            String initialDataAltitudeCssName,
                                                            String initialDataLongitudeCssName,
                                                            String initialDataInclinationAngleCssName,
                                                            String readinessCssName,
                                                            String timeStampTfCssName,
                                                            String changeInitialDataBtCssName,
                                                            String launchPlannedTime,
                                                            String orderValidUntil) {
        HorizontalLayout layout = new HorizontalLayout();

        setReadOnly(true);

        TextField tlSignature = configureTf("tl-signature",
                "TL код",
                "Введіть TL код",
                "tlSignature",
                true,
                false, "");
        tlSignature.setVisible(false);
        rocketDataBinder.forField(tlSignature)
                .bind(rocket -> {
                    if (rocket == null) {
                        return "";
                    }
                    LaunchInitialData val = rocket.getInitialData();
                    if (val == null) {
                        return "";
                    }
                    return val.getTlCode();
                }, (rocket, val) -> {
                    if (rocket.getInitialData() != null) {
                        rocket.getInitialData().setTlCode(val);
                    }
                });
        TextField sdf = new TextField();

        TextField initialDataLatitude = configureTf(initialDataLatitudeCssName,
                "Широта точки прицілювання",
                "Введіть широту в рад",
                "initialDataLatitude",
                true,
                false,
                "- Некоректний формат. Дозволені тільки цифри та крапка" +
                ".  Наприклад 0.1234567890123456");
        initialDataLatitude.setAllowedCharPattern("[0-9.-]");
        initialDataLatitude.setPattern("|^-?\\d+(\\.\\d+)?$");
        initialDataLatitude.setErrorMessage("тільки цифри та крапка");
        initialDataLatitude.setErrorMessage("тільки цифри та крапка");
        rocketDataBinder.forField(initialDataLatitude)
                .bind(rocket -> {
                    if (rocket == null) {
                        return "";
                    }
                    LaunchInitialData val = rocket.getInitialData();
                    if (val == null) {
                        return "";
                    }
                    return String.valueOf(val.getLatitudeRad());
                }, (rocket, val) -> {
                    if (rocket.getInitialData() != null) {
                        rocket.getInitialData().setLatitudeRad(val);
                    }
                });

        TextField initialDataAltitude = configureTf(initialDataAltitudeCssName,
                "Висота ТП",
                "Введіть висоту в радіанах",
                "initialDataAltitude",
                true,
                false, "- Некоректний формат висоти. Дозволені тільки цифри та крапка" +
                       ".  Наприклад " + 101.23);
        initialDataAltitude.setPattern("|^[+-]?\\d+(\\.\\d+)?([eE][+-]?\\d+)?$");
        initialDataAltitude.setAllowedCharPattern("|[0-9.-]");

        rocketDataBinder.forField(initialDataAltitude)
                .bind(rocket -> {
                    if (rocket == null) {
                        return "";
                    }
                    LaunchInitialData val = rocket.getInitialData();
                    if (val == null) {
                        return "";
                    }
                    return String.valueOf(val.getAltitude());
                }, (rocket, val) -> {
                    try {
                        if (rocket.getInitialData() != null && !val.isBlank()) {
                            rocket.getInitialData().setAltitude(Double.parseDouble(val));
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                    }
                });


        TextField initialDataLongitude = configureTf(initialDataLongitudeCssName,
                "Довгота точки прицілювання",
                "Введіть довготу в радіанах",
                "initialDataLongitude",
                true,
                false, "- Некоректний формат. Дозволені тільки цифри та крапка" +
                       ".  Наприклад 0.1234567890123456");
        initialDataLongitude.setPattern("|^-?\\d+(\\.\\d+)?$");
        initialDataLongitude.setAllowedCharPattern("|[0-9.-]");
        rocketDataBinder.forField(initialDataLongitude)
                .bind(rocket -> {
                    if (rocket == null) {
                        return "";
                    }
                    LaunchInitialData val = rocket.getInitialData();
                    if (val == null) {
                        return "";
                    }
                    return String.valueOf(val.getLongitudeRad());
                }, (rocket, val) -> {
                    try {
                        if (rocket.getInitialData() != null && !val.isBlank()) {
                            rocket.getInitialData().setLongitudeRad(val);
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                    }
                });


        TextField initialDataInclinationAngle = configureTf(initialDataInclinationAngleCssName,
                "Кут нахилу",
                "Введіть кут",
                "initialDataInclinationAngle",
                true,
                false, "");
        initialDataInclinationAngle.setAllowedCharPattern("|[0-9.-]");
        initialDataInclinationAngle.addValueChangeListener(event -> {
            if (this.isFormEditModeLocked()) {
                return;
            }
            initialDataInclinationAngle.setInvalid(!(event.getValue() == null || event.getValue().isBlank()
                                                     || RocketUtils.isValidAngle(rocket.getWarheadType().orElse(WarheadType.UNDEFINED), event.getValue())));
            if (initialDataInclinationAngle.isInvalid()) {
                initialDataInclinationAngle.scrollIntoView();
                Notification notification = ConfigurationUtils.getErrorNotification(
                        "Помилка валідації даних ВД на пуск ракети №" + (isLeftRocket ? "1 " : "2 "),
                        " Некоректний формат куту нахилу. Значення повинно бути числом з крапкою " +
                        "у діапазоні -68.00 до -90.00 для МФБЧ та -90.0±2 для КБЧ,.  Наприклад -89.0",
                        60000,
                        true);
                notification.setClassName("initial-data-validation-msg-notification");
                notification.setPosition(Notification.Position.MIDDLE);
                notification.open();
            }
        });
        rocketDataBinder.forField(initialDataInclinationAngle)
                .bind(rocket -> {
                    if (rocket == null) {
                        return "";
                    }
                    LaunchInitialData val = rocket.getInitialData();
                    if (val == null) {
                        return "";
                    }
                    return String.valueOf(val.getInclinationAngle());
                }, (rocket, val) -> {
                    try {
                        if (rocket.getInitialData() != null && !val.isBlank()) {
                            rocket.getInitialData().setInclinationAngle(Double.parseDouble(val));
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                    }
                });

        ComboBox<MissileOperatingMode> initialDataMissileOperatingMode = configureCb("initial-data-missile-operating-mode",
                "Ознака режиму роботи",
                Arrays.asList(MissileOperatingMode.values()),
                "initialDataMissileOperatingMode",
                true,
                false);
        rocketDataBinder.forField(initialDataMissileOperatingMode)
                .bind(rocket -> {
                    if (rocket == null) {
                        return MissileOperatingMode.UNKNOWN;
                    }
                    LaunchInitialData val = rocket.getInitialData();
                    if (val == null) {
                        return MissileOperatingMode.UNKNOWN;
                    }
                    return val.getMissileOperatingMode();
                }, (rocket, val) -> {
                    try {
                        if (rocket.getInitialData() != null) {
                            rocket.getInitialData().setMissileOperatingMode(val);
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                    }
                });

        ComboBox<Boolean> initialDataIsProDetected = new ComboBox<>("Ознака наявності ПРО");
        initialDataIsProDetected.addClassName("initial-data-is-pro-detected");
        initialDataIsProDetected.setItems(List.of(true, false));
        initialDataIsProDetected.setItemLabelGenerator(item -> item ? "Присутнє ПРО" : "Відсутнє ПРО");
        initialDataIsProDetected.setReadOnly(true);
        initialDataIsProDetected.setRequired(false);
        initialDataIsProDetected.setRequiredIndicatorVisible(true);
        readModeChangerList.put("initialDataIsProDetected", initialDataIsProDetected);
        rocketDataBinder.forField(initialDataIsProDetected)
                .bind(rocket -> {
                    if (rocket == null) {
                        return false;
                    }
                    LaunchInitialData val = rocket.getInitialData();
                    if (val == null) {
                        return false;
                    }
                    return val.isProDetected();
                }, (rocket, val) -> {
                    try {
                        if (rocket.getInitialData() != null) {
                            rocket.getInitialData().setProDetected(val);
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                    }
                });

        TextField loadTemperature = configureTf("initial-data-load-temperature",
                "Температура ЗМД",
                "",
                "loadTemperature",
                false,
                false, "- Некоректний формат. Дозволені тільки цифри та крапка. Наприклад 25.5");

        rocketDataBinder.forField(loadTemperature)
                .bindReadOnly(rocket -> {
                    if (rocket == null) {
                        return "";
                    }
                    return String.valueOf(rocket.getSensorTemperature());
                });

        ComboBox<Readiness> readiness = configureCb(readinessCssName,
                "Ознака типу готовності",
                Arrays.asList(Readiness.values()),
                "readiness",
                true,
                false);
        rocketDataBinder.forField(readiness)
                .bind(rocket -> {
                    if (rocket == null) {
                        return Readiness.UNDEFINED;
                    }
                    LaunchInitialData val = rocket.getInitialData();
                    if (val == null) {
                        return Readiness.UNDEFINED;
                    }
                    return val.getReadiness();
                }, (rocket, val) -> {
                    try {
                        if (rocket.getInitialData() != null) {
                            rocket.getInitialData().setReadiness(val);
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                    }
                });


        ComboBox<TrajectoryType> trajectory = configureCb("initial-data-trajectory",
                "Ознака типу траекторії",
                Arrays.asList(TrajectoryType.values()),
                "trajectory",
                true,
                false);
        rocketDataBinder.forField(trajectory)
                .bind(rocket -> {
                    if (rocket == null) {
                        return TrajectoryType.UNKNOWN;
                    }
                    LaunchInitialData val = rocket.getInitialData();
                    if (val == null) {
                        return TrajectoryType.UNKNOWN;
                    }
                    return val.getTrajectory();
                }, (rocket, val) -> {
                    try {
                        if (rocket.getInitialData() != null) {
                            rocket.getInitialData().setTrajectory(val);
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                    }
                });

        TextField timeStampTf = configureTf(timeStampTfCssName,
                "Час завантаження",
                "",
                "timeStampTf",
                false,
                false, "");

        rocketDataBinder.forField(timeStampTf)
                .bindReadOnly(val -> {
                    if (val == null) {
                        return "";
                    }
                    return val.getInitialDataTS() != null ?
                            val.getInitialDataTS().format(DateTimeFormatter.ofPattern("dd/MM/yyyy\tHH:mm:ss")) :
                            "";
                });

        Button changeInitialDataBt = new Button("Завантажити в ПЛК");
        changeInitialDataBt.addClassName(changeInitialDataBtCssName);

        changeInitialDataBt.addClickListener(e -> {
            boolean isValid = true;

            String errorMessage = "";
            if (rocketDataBinder.getBean() == null) {
                isValid = false;
                errorMessage = "Вихідних даних не знайдено. Для продовження оновіть сторінку, " +
                               "введіть та ретельно перевірте вихідні дані";
            } else if (!rocketDataBinder.isValid()) {
                isValid = false;
                errorMessage = " Вихідні дані не валідні. Для продовження оновіть сторінку, " +
                               "введіть та ретельно перевірте вихідні дані.";
            } else if (!initialDataIsValid()) {
                isValid = false;
                errorMessage = "Не всі обов'язкові поля заповнені. Для продовження внесіть усі дані " +
                               "(опис джерела ВД можна ігнорувати) та повторіть спробу";
            }

            try {
                Rocket actual = null;
                Rocket beanVal = null;
                if (isValid) {
                    actual = rocketMapper.clone(askuService
                            .findRocketByPlantNumber(rocket.getPlantMissile().orElse(""))
                            .orElseThrow(NotFoundException::new));
                    beanVal = rocketDataBinder.getBean();
                    beanVal.setInitialDataTS(null);

                    if (!actual.getFormData().equals(beanVal.getFormData())) {
                        log.error("Формулярні дані ракети було змінено під час редагування. " +
                                  "Для корегування ВД оновіть сторінку та почніть вносити зміни повторно." +
                                  " Корегувалися дані " + beanVal.getRocketFormData() +
                                  " Актуальні дані " + actual.getFormData().toString());
                        errorMessage = "Формулярні дані ракети було змінено під час редагування. " +
                                       "Для корегування ВД оновіть сторінку та почніть вносити зміни повторно." +
                                       " Корегувалися дані " + beanVal.getRocketFormData() +
                                       " Актуальні дані " + actual.getFormData().toString();
                        isValid = false;
                    }
                }
                if (!isValid) {
                    log.error(errorMessage + " ракета " + (isLeftRocket ? "№1 (ліва) " : "№2 (права) ") +
                              (rocketDataBinder.getBean() != null ? rocketDataBinder.getBean().toString() : "не існує"));

                    Notification notification = ConfigurationUtils.getErrorNotification(
                            "Помилка валідації даних ВД на пуск",
                            errorMessage,
                            60000,
                            true
                    );
                    notification.setPosition(Notification.Position.MIDDLE);
                    notification.setClassName("initial-data-validation-msg-notification");
                    notification.open();
                } else if (beanVal != null) {
                    askuService.saveRocketWithInitialData(beanVal.getInitialDataSource(),
                            beanVal.getInitialDataSourceDescription(),
                            rocket.getPlantMissile().orElseThrow(NotFoundException::new),
                            beanVal.getInitialData());

                    if (!formEditModeLocked) {
                        setReadOnly(!formEditModeLocked);
                        formEditModeLocked = !formEditModeLocked;
                    }
                }
                // TODO: 14.08.2023 check TS generation
            } catch (NotFoundException | BadRequestException exception) {
                ConfigurationUtils.getErrorNotification("Помилка при збереженні ВД ", exception.getMessage(),
                        0, true).open();
            }
        });

        TextField isScheduledTf = configureTf("is-launch-scheduled",
                "Пуск за часом",
                "",
                "isScheduledTf",
                false,
                false,
                "");
        rocketDataBinder.forField(isScheduledTf)
                .bindReadOnly(val -> {
                    if (val == null || val.getInitialData() == null) {
                        return "";
                    }
                    return val.getInitialData().isScheduled() ? "Так" : "Ні";

                });

        launchPlannedTimeTf = configureTf(launchPlannedTime,
                "Плановий час пуску",
                "",
                "launchPlannedTimeTf",
                false,
                false,
                "");
        rocketDataBinder.forField(launchPlannedTimeTf)
                .bindReadOnly(val -> {
                    try {
                        if (val == null || val.getInitialData() == null || val.getInitialData().getStartTime() == null) {
                            return "";
                        }
                        return val.getInitialData().getStartTime()
                                .format(DateTimeFormatter.ofPattern("dd/MM/yyyy\tHH:mm:ss"));
                    } catch (Exception e) {
                        log.error(Arrays.toString(e.getStackTrace()));
                        return "";
                    }
                });
        launchPlannedTimeTf.addValueChangeListener(this::animateOrderValidity);

        orderValidUntilTf = configureTf(orderValidUntil,
                "Наказ дійсний до",
                "",
                "orderValidUntilTf",
                false,
                false,
                "");

        rocketDataBinder.forField(orderValidUntilTf)
                .bindReadOnly(val -> {
                    try {
                            if (val == null || val.getInitialData() == null ||
                            val.getInitialData().getOrderInfo() == null ||
                            val.getInitialData().getOrderInfo().getValidUntil() == null) {
                            return "";
                        }
                        return val.getInitialData().getOrderInfo().getValidUntil()
                                .format(DateTimeFormatter.ofPattern("dd/MM/yyyy\tHH:mm:ss"));

                    } catch (Exception e) {
                        log.error(Arrays.toString(e.getStackTrace()));
                        return "";
                    }
                });

        orderValidUntilTf.addValueChangeListener(this::animateOrderValidity);

        if (!isPdpView) {
            if (isEditable) {
                layout.add(tlSignature,
                        initialDataLatitude,
                        initialDataAltitude,
                        initialDataLongitude,
                        initialDataInclinationAngle,
                        initialDataMissileOperatingMode,
                        loadTemperature,
                        initialDataIsProDetected,
                        readiness,
                        trajectory,
                        timeStampTf,
                        isScheduledTf,
                        launchPlannedTimeTf,
                        orderValidUntilTf,
                        changeInitialDataBt
                );
            } else {
                layout.add(tlSignature,
                        initialDataLatitude,
                        initialDataAltitude,
                        initialDataLongitude,
                        initialDataInclinationAngle,
                        initialDataMissileOperatingMode,
                        loadTemperature,
                        initialDataIsProDetected,
                        readiness,
                        trajectory,
                        timeStampTf,
                        isScheduledTf,
                        launchPlannedTimeTf,
                        orderValidUntilTf);
            }
        } else {
            layout.add(tlSignature,
                    initialDataLatitude,
                    initialDataAltitude,
                    initialDataLongitude,
                    initialDataInclinationAngle,
                    readiness,
                    timeStampTf,
                    launchPlannedTimeTf,
                    orderValidUntilTf
            );
        }
        loadData();

        return layout;
    }

    private void animateOrderValidity(AbstractField.ComponentValueChangeEvent<TextField, String> event) {
        if (event == null || event.getValue().isBlank()) {
            return;
        }
        animateOrderValidity(event.getSource(), event.getValue());
    }

    private void animateOrderValidity(TextField textField, String value) {
        if (textField == null) {
            return;
        }

        try {
            if (!value.isBlank()) {
                boolean orderIsValid = LocalDateTime.parse(value, DateTimeFormatter.ofPattern("dd/MM/yyyy\tHH:mm:ss")).
                        isAfter(LocalDateTime.now());

                if (orderIsValid) {
                    textField.removeClassName("TextField-" + AnimationColor.RED.getText());
                    textField.addClassName("TextField-" + AnimationColor.GREEN.getText());
                } else {
                    textField.removeClassName("TextField-" + AnimationColor.GREEN.getText());
                    textField.addClassName("TextField-" + AnimationColor.RED.getText());
                }
            } else {
                textField.removeClassName("TextField-" + AnimationColor.RED.getText());
                textField.removeClassName("TextField-" + AnimationColor.GREEN.getText());
            }
        } catch (Exception e) {
            log.error(Arrays.toString(e.getStackTrace()));
            textField.removeClassName("TextField-" + AnimationColor.GREEN.getText());
            textField.addClassName("TextField-" + AnimationColor.RED.getText());
        }
    }

    private boolean initialDataIsValid() {
        if (!rocketDataBinder.isValid()) {
            return false;
        }

        return rocketDataBinder.getFields().noneMatch(f -> {
            if (f instanceof TextField tf) {
                if (tf.getLabel().equalsIgnoreCase("Інформація про джерело наказу") ||
                    tf.getLabel().equalsIgnoreCase("Час завантаження")) {
                    return false;
                }
            }
            return f.isEmpty();
        });
    }

    private void loadData() {
        rocket = null;

        try {
            if (askuService != null && askuService.getAskuEntity() != null) {
                if (isLeftRocket && askuService.getAskuEntity().getLeftRocket() != null) {
                    rocket = rocketMapper.clone(askuService.getAskuEntity().getLeftRocket());
                }
                if (!isLeftRocket && askuService.getAskuEntity().getRightRocket() != null) {
                    rocket = rocketMapper.clone(askuService.getAskuEntity().getRightRocket());
                }

            }
        } catch (NullPointerException e) {
            log.error(e.getMessage() + " - " + Arrays.toString(e.getStackTrace()));
        }
        if (rocket != null) {
            updateDataAndView(rocket, rocket.getInitialDataTS());
        }
    }

    private Rocket prevRocket = AskuUtils.getDefaultRocket();
    private LocalDateTime initialDataTs = LocalDateTime.MIN;


    @Override
    public void receiveBroadcast(Object incomingEvent) {
        if (!(incomingEvent instanceof RocketEvent newEvent)) {
            log.debug("expected " + AskuInfoEvent.class.getSimpleName() + "but received unexpected event at " + this.getClassName());
            return;
        }

        if (newEvent.isLeft() != isLeftRocket) {
            return;
        }

        if (newEvent.getEventType().equals(AdjacentSystemUpdateEventType.UPDATE_VIEW_SILENTLY) &&
            newEvent.getRocket() != null) {
            if (newEvent.getRocket().getSensorTemperature() != rocket.getSensorTemperature()) {
                updateDataAndView(newEvent.getRocket(), newEvent.getTimeStamp());
            }

            if (newEvent.getRocket().getInitialData() != null) {
                updateDataAndView(newEvent.getRocket(), newEvent.getTimeStamp());

                if(askuService.fireOrderOrLunchTimeExpired(isLeftRocket)){
                    animateOrderValidity(launchPlannedTimeTf,
                            (newEvent.getRocket().getInitialData().getStartTime() != null) ?
                                    newEvent.getRocket().getInitialData().getStartTime().format(DateTimeFormatter.ofPattern("dd/MM/yyyy\tHH:mm:ss")) :
                                    "");
                }

                animateOrderValidity(orderValidUntilTf, (newEvent.getRocket().getInitialData().getOrderInfo() != null &&
                                                           newEvent.getRocket().getInitialData().getOrderInfo().getValidUntil() != null) ?
                        newEvent.getRocket().getInitialData().getOrderInfo().getValidUntil().format(DateTimeFormatter.ofPattern("dd/MM/yyyy\tHH:mm:ss")) :
                        "");


            }

            return;
        }

        if (newEvent.getEventType().equals(AdjacentSystemUpdateEventType.REMOVE_ENTITY) &&
            newEvent.getRocket() != null) {
            removeRocket(rocket);
            return;
        }

        if (!(prevRocket.equals(newEvent.getRocket()) ||
              !initialDataTs.withNano(0).isEqual(newEvent.getTimeStamp().withNano(0)))) {
            return;
        }
        updateDataAndView(newEvent.getRocket(), newEvent.getTimeStamp());
    }

    private void removeRocket(@NotNull Rocket removedRocket) {
        if (rocket == null || !rocket.equals(removedRocket)) {
            return;
        }

        prevRocket = AskuUtils.getDefaultRocket();

        if (getUI().isPresent()) {
            getUI().get().access(() -> {
                rocketDataBinder.removeBean();
                rocketDataBinder.getFields().forEach(f -> {
                    if (f instanceof ComboBox<?> cb) {
                        cb.setValue(null);
                    } else if (f instanceof TextField tf) {
                        tf.setValue("");
                    }
                });
            });
        } else {
            rocketDataBinder.removeBean();
        }

        initialDataTs = LocalDateTime.MIN;
    }

    private void updateDataAndView(Rocket newRocket, LocalDateTime timeStamp) {

        if (newRocket == null || newRocket.getInitialData() == null || newRocket.equals(AskuUtils.getDefaultRocket())) {
            log.debug("initialData doesn't exists in rocket " + isLeftRocket);
            //clean initial data

            if (rocket != null) {
                if (getUI().isPresent()) {
                    getUI().get().access(() -> rocketDataBinder.removeBean());
                } else {
                    rocketDataBinder.removeBean();
                }
            }
        }

        if (rocket != null) {
            prevRocket = rocket;
        }
        rocket = rocketMapper.clone(newRocket);
        if (getUI().isPresent()) {
            getUI().get().access(() -> {
                rocketDataBinder.setBean(rocket);
            });
        } else {
            rocketDataBinder.setBean(rocket);
        }
        initialDataTs = timeStamp != null ? timeStamp.withNano(0) : LocalDateTime.MIN;
    }


    public VerticalLayout buildView() {
        return this;
    }


    public void setReadOnly(boolean readOnly) {
        for (HasValue field : readModeChangerList.values()) {
            field.setReadOnly(readOnly);
        }
    }

    private TextField configureTf(@NotNull String cssClassName,
                                  @NotNull String label,
                                  @NotNull String placeHolder,
                                  @NotBlank String fieldName,
                                  @NotNull boolean canChangeReadOnlyMode,
                                  boolean isRequired,
                                  String errorMessage) {
        TextField textField = new TextField();
        textField.addClassName(cssClassName);
        textField.setLabel(label);
        textField.setReadOnly(true);
        textField.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        textField.setPlaceholder(placeHolder);

        if (!errorMessage.isBlank()) {
            textField.addValueChangeListener(event -> {
                if (textField.isInvalid()) {
                    Notification notification = ConfigurationUtils.getErrorNotification(
                            "Помилка валідації даних ВД на пуск",
                            textField.getLabel() + " " + errorMessage,
                            60000,
                            true
                    );
                    notification.setPosition(Notification.Position.MIDDLE);
                    notification.setClassName("initial-data-validation-msg-notification");
                    notification.open();
                }
            });
        }

        if (isRequired) {
            textField.setRequired(true);
            textField.setRequiredIndicatorVisible(true);
        }

        if (canChangeReadOnlyMode) {
            readModeChangerList.put(fieldName, textField);
        }

        return textField;
    }

    private <T extends AdjacentSystemWithDescriptionUa> ComboBox<T> configureCb(@NotNull String cssClassName,
                                                                                @NotNull String label,
                                                                                @NotNull List<T> items,
                                                                                @NotNull @NotBlank String fieldName,
                                                                                @NotNull boolean canChangeReadOnlyMode,
                                                                                boolean isRequired) {
        ComboBox<T> comboBox = new ComboBox<T>(label);
        comboBox.addClassName(cssClassName);
        comboBox.setItems(items);
        comboBox.setItemLabelGenerator(T::getValueUa);
        comboBox.setReadOnly(true);

        if (isRequired) {
            comboBox.setRequired(true);
            comboBox.setRequiredIndicatorVisible(true);
        }

        if (canChangeReadOnlyMode) {
            readModeChangerList.put(fieldName, comboBox);
        }

        return comboBox;
    }
}
