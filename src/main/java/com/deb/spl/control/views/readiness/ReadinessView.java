package com.deb.spl.control.views.readiness;

import com.deb.spl.control.controller.NotFoundException;
import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemCommand;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.Bins;
import com.deb.spl.control.data.asku.AskuInfo;
import com.deb.spl.control.data.asku.Readiness;
import com.deb.spl.control.data.asku.RocketMapper;
import com.deb.spl.control.data.nppa.*;
import com.deb.spl.control.data.sae.*;
import com.deb.spl.control.data.suto.Suto;
import com.deb.spl.control.repository.nppa.NppaCommandRepository;
import com.deb.spl.control.service.*;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.service.asku.PlcService;
import com.deb.spl.control.views.MainLayout;
import com.deb.spl.control.views.adjacentSystems.SystemsInfo;
import com.deb.spl.control.views.adjacentSystems.nppa.NppaView;
import com.deb.spl.control.views.adjacentSystems.utils.ConfigurationUtils;
import com.deb.spl.control.views.events.*;
import com.vaadin.flow.component.AttachEvent;
import com.vaadin.flow.component.DetachEvent;
import com.vaadin.flow.component.Html;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.dialog.Dialog;
import com.vaadin.flow.component.grid.ColumnTextAlign;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.grid.GridVariant;
import com.vaadin.flow.component.html.Span;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextArea;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.component.textfield.TextFieldVariant;
import com.vaadin.flow.data.binder.Binder;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@PageTitle("Зміна готовності")
@Route(value = "readiness", layout = MainLayout.class)
public class ReadinessView extends VerticalLayout implements Broadcaster.BroadcastListener {
    private final MsuService msuService;
    private final BinsService binsService;
    private Readiness askuReadiness;
    private final UserService userService;
    private final PpoService ppoService;
    private final SutoService sutoService;
    final SaeService saeService;
    final NppaService nppaService;
    private final PlcService plcService;
    private final AskuService askuService;
    private final RocketMapper rocketMapper;
    private final Binder<AdjacentSystem> msuBinder = new Binder<>(AdjacentSystem.class);
    private final Binder<Bins> binsBinder = new Binder<>(Bins.class);
    private final Binder<AdjacentSystem> ppoBinder = new Binder<>(AdjacentSystem.class);
    private final Binder<Suto> sutoBinder = new Binder<>(Suto.class);
    private final Binder<Sae> saeBinder = new Binder<>(Sae.class);
    private final Binder<AdjacentSystem> plcBinder = new Binder<>(AdjacentSystem.class);
    private final Binder<AskuInfo> askuInfoBinder = new Binder<>(AskuInfo.class);
    private final Binder<Byn> bynBinder = new Binder<>();
    private final Binder<Ncok> ncokBinder = new Binder<>(Ncok.class);
    private final SystemsInfo systemsInfo;
    private Grid<AdjacentSystemCommand> workflowCommandsGrid;
    private final List<AdjacentSystemCommand> workflowCommands;

    private final List<Readiness> askuBgTransitionsAvailableForCancellation;

    @Override
    protected void onAttach(AttachEvent attachEvent) {
        super.onAttach(attachEvent);

        List<Class> events = List.of(
                PpoEvent.class,
                SutoEvent.class,
                BinsEvent.class,
                MsuEvent.class,
                SaeEvent.class,
                PlcEvent.class,
                AskuInfoEvent.class,
                NppaEvent.class,
                MsgEvent.class);
        Broadcaster.register(this, events.toArray(new Class[0]));

    }

    @Override
    protected void onDetach(DetachEvent detachEvent) {
        super.onDetach(detachEvent);

        Broadcaster.unregister(this);
    }

    public ReadinessView(MsuService msuService,
                         BinsService binsService,
                         PpoService ppoService,
                         SutoService sutoService,
                         SaeService saeService,
                         PlcService plcService,
                         AskuService askuService,
                         NppaService nppaService, UserService userService,
                         RocketMapper rocketMapper,
                         CcvCommunicationService ccvCommunicationService,
                         NppaCommandRepository nppaCommandRepository) {
        this.msuService = msuService;
        this.binsService = binsService;
        this.ppoService = ppoService;
        this.sutoService = sutoService;
        this.saeService = saeService;
        this.plcService = plcService;
        this.askuService = askuService;
        this.nppaService = nppaService;
        this.rocketMapper = rocketMapper;
        this.userService = userService;

        systemsInfo = new SystemsInfo(this.msuService,
                this.binsService,
                this.ppoService,
                this.sutoService,
                this.saeService,
                this.askuService,
                plcService,
                this.nppaService,
                rocketMapper,
                ccvCommunicationService);

        askuReadiness = askuService.getSplReadiness().orElse(Readiness.UNDEFINED);
        List<NppaCommand> nppaWorkflowCommands = nppaService.getAvailableNcokWorkflowAutomationCommands();

        List<SaeCommand> saeCommands = saeService.getAvailablePdpCommands();

        cancelBgToBgToggle = nppaService.getAvailableNcokWorkflowCommands().stream().
                filter(e -> e.getCommand().equals("CancelBgToBgToggle")).findFirst().orElseThrow(NotFoundException::new);
        NppaCommandDAO cancelBgToBgToggleDAO = nppaCommandRepository.findByCommandName("CancelBgToBgToggle");
        askuBgTransitionsAvailableForCancellation = cancelBgToBgToggleDAO != null ? cancelBgToBgToggleDAO.getAvailableAtReadiness()
                : new ArrayList<>();

        cancelAutoTestModeCommand = nppaService.getAvailableNcokWorkflowCommands()/*getAvailableNcokTestModeCommands()*/.stream().
                filter(e -> e.getCommand().equals("CancelAutoTestMode")).findFirst().orElseThrow(NotFoundException::new);
        workflowCommands = new ArrayList<>(nppaWorkflowCommands);
        workflowCommands.addAll(saeCommands);
        initView();

    }

    private List<? extends AdjacentSystemCommand> combineCommands(List<? extends AdjacentSystemCommand>... commandLists) {
        List<AdjacentSystemCommand> combined = new ArrayList<>();
        for (List<? extends AdjacentSystemCommand> list : commandLists) {
            combined.addAll(list);
        }

        return combined;
    }

    private void initView() {


        HorizontalLayout readinessLayout = new HorizontalLayout();
        readinessLayout.addClassName("readiness-layout");
        readinessLayout.setWidth("69%");
        readinessLayout.setMaxWidth("69%");
        readinessLayout.setMaxHeight("861px");
        HorizontalLayout firstRowReadiness = new HorizontalLayout();
        ComboBox<Readiness> readinessComboBox = configureReadinessCombo();

        VerticalLayout firstColumnReadiness = new VerticalLayout();
        VerticalLayout firstColumnFirstRowReadiness = new VerticalLayout();

        firstColumnFirstRowReadiness.add(readinessComboBox);

        VerticalLayout commands = new VerticalLayout();
        workflowCommandsGrid = new Grid<>(AdjacentSystemCommand.class, false);
        workflowCommandsGrid.addThemeVariants(GridVariant.LUMO_WRAP_CELL_CONTENT);
        setupNppaCommandsGrid(commands, workflowCommandsGrid, workflowCommands,
                userService, nppaService, saeService, "readiness-command-grid");

        if (askuService.getSplReadiness().orElse(Readiness.UNDEFINED) == Readiness.BG_4 ||
            askuService.getSplReadiness().orElse(Readiness.UNDEFINED) == Readiness.AUTO_TEST_IN_PROGRESS) {
            firstColumnReadiness.add(firstColumnFirstRowReadiness, commands);
        } else firstColumnReadiness.add(firstColumnFirstRowReadiness);


        ThirdColumnStatus thirdColumnStatus = new ThirdColumnStatus();
        thirdColumnStatus.addClassName("third-column-readiness");

        firstRowReadiness.add(firstColumnReadiness);
        firstRowReadiness.addClassName("first-row-readiness");

        readinessLayout.add(firstRowReadiness, thirdColumnStatus);
        addClassName("readiness-layout-out");
        add(systemsInfo, readinessLayout);

        setupBgCommandButtonState(sendBtRowReadiness, askuService.getSplReadiness().orElse(Readiness.UNDEFINED));
    }

    private VerticalLayout firstColumnSecondRowReadiness = new VerticalLayout();

    static void setupNppaCommandsGrid(@NotNull VerticalLayout tabPanel,
                                      @NotNull Grid<AdjacentSystemCommand> commandsGrid,
                                      @NotNull List<AdjacentSystemCommand> commandsList,
                                      @NotNull UserService userService,
                                      @NotNull NppaService nppaService,
                                      @NotNull SaeService saeService,
                                      @NotBlank String gridClassName) {

        Grid.Column<? extends AdjacentSystemCommand> caption = commandsGrid
                .addColumn(AdjacentSystemCommand::getCaption).setHeader(new Html("<b>Команда</b>")).setTextAlign(ColumnTextAlign.START)
                .setFlexGrow(1).setAutoWidth(true);

        Grid.Column<? extends AdjacentSystemCommand> sentence = commandsGrid
                .addColumn(AdjacentSystemCommand::getCommand).setHeader("Відправити").setTextAlign(ColumnTextAlign.END);

        sentence.setVisible(false);

        Grid.Column<? extends AdjacentSystemCommand> sendColumn = commandsGrid.addComponentColumn(command -> {

            Button sendBt = new Button("Відправити");
            sendBt.addClassName("send-sentence-button");
            sendBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY, ButtonVariant.LUMO_SMALL);

            sendBt.getStyle().set("background-color", command.getId() == 20008 ? "#dc0101" : "#4F4F4F");

            sendBt.addClickListener(event -> {
                AdjacentSystemCommand generatedCommand = command instanceof NppaCommand ?
                        NppaCommand.builder()
                                .command(command.getCommand())
                                .caption(command.getCaption())
                                .commandState(command.getCommandState())
                                .adjacentSystem(command.getAdjacentSystem())
                                .generationTime(LocalDateTime.now())
                                .originator(userService.getUserName())
                                .build() :

                        SaeCommand.builder()
                                .command(command.getCommand())
                                .caption(command.getCaption())
                                .commandState(command.getCommandState())
                                .adjacentSystem(command.getAdjacentSystem())
                                .generationTime(LocalDateTime.now())
                                .originator(userService.getUserName())
                                .build();

                Dialog dialog = new Dialog();


                CommandValidationResult commandValidationResult = (generatedCommand instanceof NppaCommand) ?
                        nppaService.validateCommand((NppaCommand) generatedCommand) :
                        new CommandValidationResult(true, "", "");

                if (commandValidationResult.isValid()) {
                    dialog.addClassName("dialog-nppa");

                    dialog.setHeaderTitle("Видача команди");
                    dialog.add("Ви впевнені, що бажаєте відправити команду " + command.getCaption() + "?");

                    Button confirmBt = new Button(sentence.getHeaderText(), (e) -> {
                        if (command instanceof SaeCommand) {
                            saeService.setSaeCommand((SaeCommand) generatedCommand);
                        }

                        if (command instanceof NppaCommand) {
                            nppaService.setNppaCommand((NppaCommand) generatedCommand);
                        }

                        dialog.close();
                    });
                    confirmBt.addClassName("confirm-button-nppa-dialog");

                    confirmBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY,
                            ButtonVariant.LUMO_ERROR);
                    confirmBt.getStyle().set("margin-right", "auto");
                    dialog.getFooter().add(confirmBt);
                } else {
                    String errorMsg = commandValidationResult.errorMessageUa();
                    log.error(errorMsg + " " + NppaView.class);

                    ConfigurationUtils.getErrorNotification(errorMsg, "",
                            0, true).open();

                    dialog.addClassName("dialog-nppa");

                    dialog.setHeaderTitle("Помилка");
                    dialog.add(commandValidationResult.errorMessageUa());
                    return;
                }
                Button cancelButton = new Button("Відміна", (e) -> dialog.close());
                cancelButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
                cancelButton.addClassName("cancel-button-nppa-dialog");

                dialog.getFooter().add(cancelButton);


                dialog.open();
            });

            return sendBt;
        }).setHeader(new Html("<b>Відправити</b>")).setFlexGrow(0).setWidth("150px");


        commandsGrid.addClassName(gridClassName);
        commandsGrid.addThemeVariants(GridVariant.MATERIAL_COLUMN_DIVIDERS, GridVariant.LUMO_COLUMN_BORDERS,
                GridVariant.LUMO_ROW_STRIPES);
        commandsGrid.setItems(commandsList);
        tabPanel.add(commandsGrid);
        tabPanel.setPadding(false);
    }

    private VerticalLayout sendBtRowReadiness;

    private ComboBox<Readiness> configureReadinessCombo() {
        final String SEND_COMMAND_CAPTION = "Відправити";

        ComboBox<Readiness> readinessCB = new ComboBox<>();
        readinessCB.addClassName("readiness-combobox");

        firstColumnSecondRowReadiness = new VerticalLayout();
        firstColumnSecondRowReadiness.addClassName("first-column-second-row-readiness");
        sendBtRowReadiness = new VerticalLayout();
        sendBtRowReadiness.addClassName("send-bt-row-readiness");
        //cb config
        if (!askuService.getSplReadiness().isEmpty()) {
            if (Readiness.BG_4 == askuService.getSplReadiness().get()) {
                readinessCB.setItems(List.of(Readiness.BG_3));
                readinessCB.setItemLabelGenerator(Readiness::getValueUa);
                readinessCB.setPlaceholder("Вибрати готовність");

            } else if (Readiness.BG_3 == askuService.getSplReadiness().get()) {
                readinessCB.setItems(Arrays.asList(Readiness.BG_4, Readiness.BG_2B, Readiness.BG_1));
                readinessCB.setItemLabelGenerator(Readiness::getValueUa);
                readinessCB.setPlaceholder("Вибрати готовність");
            } else if (Readiness.BG_2A == askuService.getSplReadiness().get()) {
                readinessCB.setItems(Arrays.asList(Readiness.BG_4, Readiness.BG_3, Readiness.BG_1));
                readinessCB.setItemLabelGenerator(Readiness::getValueUa);
                readinessCB.setPlaceholder("Вибрати готовність");
            } else if (Readiness.BG_2B == askuService.getSplReadiness().get()) {
                readinessCB.setItems(Arrays.asList(Readiness.BG_4, Readiness.BG_3));
                readinessCB.setItemLabelGenerator(Readiness::getValueUa);
                readinessCB.setPlaceholder("Вибрати готовність");
            } else if (Readiness.BG_1 == askuService.getSplReadiness().get()) {
                readinessCB.setItems(Arrays.asList(Readiness.BG_4, Readiness.BG_3/*, Readiness.BG_2A*/));
                readinessCB.setItemLabelGenerator(Readiness::getValueUa);
                readinessCB.setPlaceholder("Вибрати готовність");
            } else if (Readiness.UNDEFINED == askuService.getSplReadiness().get()) {
                readinessCB.setItems(Arrays.asList(Readiness.values()));
                readinessCB.setItemLabelGenerator(Readiness::getValueUa);
                readinessCB.setPlaceholder("Вибрати готовність");
            } else if (Readiness.BG_4_TO_BG_3 == askuService.getSplReadiness().get() ||
                       Readiness.BG_3_TO_BG_4 == askuService.getSplReadiness().get() ||
                       Readiness.BG_3_TO_BG_2A == askuService.getSplReadiness().get() ||
                       Readiness.BG_3_TO_BG_2B == askuService.getSplReadiness().get() ||
                       Readiness.BG_3_TO_BG_1 == askuService.getSplReadiness().get() ||
                       Readiness.BG_2A_TO_BG_1 == askuService.getSplReadiness().get() ||
                       Readiness.BG_2A_TO_BG_3 == askuService.getSplReadiness().get() ||
                       Readiness.BG_2A_TO_BG_4 == askuService.getSplReadiness().get() ||
                       Readiness.BG_2B_TO_BG_4 == askuService.getSplReadiness().get() ||
                       Readiness.BG_2B_TO_BG_3 == askuService.getSplReadiness().get() ||
                       Readiness.BG_1_TO_BG_3 == askuService.getSplReadiness().get() ||
                       Readiness.BG_1_TO_BG_4 == askuService.getSplReadiness().get() ||
                       Readiness.BG_1_TO_BG_2A == askuService.getSplReadiness().get()) {
                readinessCB.setReadOnly(true);

                readinessCB.setItems(Readiness.TRANSITION);
                readinessCB.setValue(Readiness.TRANSITION);
            }
        }

        readinessCB.addValueChangeListener(event -> {
            if (event == null) {
                return;
            }
            readinessCB.setValue(event.getValue());

            if (event.getValue().name() == "BG_4") {
                TextArea tipsForBG = new TextArea();
                tipsForBG.setReadOnly(true);
                tipsForBG.addClassName("readines-tips-text-area");
                tipsForBG.setLabel("Технічна готовність №4");
                tipsForBG.setValue("Стан систем для переходу в Бойову Готовність №3:\n" +
                                   "Статус фідера 1 - Включений;\n" +
                                   "Статус фідера 2 - Включений;\n" +
                                   "Статус фідера 3 - Включений;\n" +
                                   "Ініціалізація даних про ТПК та ОТР;\n" +
                                   "Статус фідера 1 НППА СУ - Відключений;\n" +
                                   "Статус фідера 6 - Включений;\n" +
                                   "СУТО в ПП - Норма;\n" +
                                   "УКННС - Норма;\n" +
                                   "БВН - Відключено;\n" +
                                   "НЦОК - Відключено;\n" +
                                   "Зв’язок - наявний.\u2028");
                if (askuService.getSplReadiness().get() == Readiness.BG_3) {
                    NppaCommand sendBtReadiness = nppaService.getAvailableNcokWorkflowCommands().stream().
                            filter(e -> e.getCommand().equals("Bg3toBg4Toggle")).findFirst().orElseThrow(NotFoundException::new);
                    Button sendBtNewReadiness = setupGenerateSendCommandButton(sendBtReadiness, ButtonVariant.LUMO_PRIMARY, SEND_COMMAND_CAPTION);
                    if (getUI().isPresent()) {
                        getUI().get().access(() -> {
                            sendBtRowReadiness.removeAll();
                            sendBtRowReadiness.add(sendBtNewReadiness);
                        });
                    }
                }
                if (askuService.getSplReadiness().get() == Readiness.BG_2A) {
                    NppaCommand sendBtReadiness = nppaService.getAvailableNcokWorkflowCommands().stream().
                            filter(e -> e.getCommand().equals("Bg2atoBg4Toggle")).findFirst().orElseThrow(NotFoundException::new);
                    Button sendBtNewReadiness = setupGenerateSendCommandButton(sendBtReadiness, ButtonVariant.LUMO_PRIMARY, SEND_COMMAND_CAPTION);
                    if (getUI().isPresent()) {
                        getUI().get().access(() -> {
                            sendBtRowReadiness.removeAll();
                            sendBtRowReadiness.add(sendBtNewReadiness);
                        });
                    }
                }
                if (askuService.getSplReadiness().get() == Readiness.BG_2B) {
                    NppaCommand sendBtReadiness = nppaService.getAvailableNcokWorkflowCommands().stream().
                            filter(e -> e.getCommand().equals("Bg2btoBg4Toggle")).findFirst().orElseThrow(NotFoundException::new);
                    Button sendBtNewReadiness = setupGenerateSendCommandButton(sendBtReadiness, ButtonVariant.LUMO_PRIMARY, SEND_COMMAND_CAPTION);
                    if (getUI().isPresent()) {
                        getUI().get().access(() -> {
                            sendBtRowReadiness.removeAll();
                            sendBtRowReadiness.add(sendBtNewReadiness);
                        });
                    }
                }
                if (askuService.getSplReadiness().get() == Readiness.BG_1) {
                    NppaCommand sendBtReadiness = nppaService.getAvailableNcokCombatModeCommands().stream().
                            filter(e -> e.getCommand().equals("Bg1toBg4Toggle")).findFirst().orElseThrow(NotFoundException::new);
                    Button sendBtNewReadiness = setupGenerateSendCommandButton(sendBtReadiness, ButtonVariant.LUMO_PRIMARY, SEND_COMMAND_CAPTION);
                    if (getUI().isPresent()) {
                        getUI().get().access(() -> {
                            sendBtRowReadiness.removeAll();
                            sendBtRowReadiness.add(sendBtNewReadiness);
                        });
                    }
                }

                if (getUI().isPresent()) {
                    getUI().get().access(() -> {
                        firstColumnSecondRowReadiness.removeAll();
                        firstColumnSecondRowReadiness.add(tipsForBG);
                    });
                }
            } else if (event.getValue().name() == "BG_3") {
                TextArea tipsForBG = new TextArea();
                tipsForBG.setReadOnly(true);
                tipsForBG.addClassName("readines-tips-text-area");
                tipsForBG.setLabel("Бойова Готовність №3");
                tipsForBG.setValue("Стан систем для Бойової Готовності №3:\n" +
                                   "Статус фідера 1 - Включений;\n" +
                                   "Статус фідера 2 - Включений;\n" +
                                   "Статус фідера 3 - Включений;\n" +
                                   "ОТР1 та ОТР2 перевірені та можуть мати такі статуси :\n" +
                                   "      - ОТР1 та ОТР2 - Норма;\u2028   \n" +
                                   "      - ОТР1 - Норма, ОТР2 - Не норма;\n" +
                                   "      - ОТР1 - Не норма, ОТР2 - Норма.\n" +
//                                   "Статус фідера 1 НППА СУ - Включений;\n" +
                                   "Статус фідера 6 - Включений;\n" +
                                   "СУТО в ПП - Норма; " +
                                   "УКННС - Норма;\n" +
//                                   "БВН - Норма; " +
//                                   "НЦОК - Відключено;\n" +
                                   "Зв’язок - наявний;\n" +
                                   "Координати СПУ присутні.\n" + "\u2028");
                if (askuService.getSplReadiness().get() == Readiness.BG_4) {
                    NppaCommand sendBtReadiness = nppaService.getAvailableNcokWorkflowCommands().stream().
                            filter(e -> e.getCommand().equals("Bg4toBg3Toggle")).findFirst().orElseThrow(NotFoundException::new);
                    Button sendBtNewReadiness = setupGenerateSendCommandButton(sendBtReadiness, ButtonVariant.LUMO_PRIMARY, SEND_COMMAND_CAPTION);
                    if (getUI().isPresent()) {
                        getUI().get().access(() -> {
                            sendBtRowReadiness.removeAll();
                            sendBtRowReadiness.add(sendBtNewReadiness);
                        });
                    }
                } else if (askuService.getSplReadiness().get() == Readiness.BG_2A) {
                    NppaCommand sendBtReadiness = nppaService.getAvailableNcokCombatModeCommands().stream().
                            filter(e -> e.getCommand().equals("Bg2atoBg3Toggle")).findFirst().orElseThrow(NotFoundException::new);
                    Button sendBtNewReadiness = setupGenerateSendCommandButton(sendBtReadiness, ButtonVariant.LUMO_PRIMARY, SEND_COMMAND_CAPTION);
                    if (getUI().isPresent()) {
                        getUI().get().access(() -> {
                            sendBtRowReadiness.removeAll();
                            sendBtRowReadiness.add(sendBtNewReadiness);
                        });
                    }
                } else if (askuService.getSplReadiness().get() == Readiness.BG_2B) {
                    NppaCommand sendBtReadiness = nppaService.getAvailableNcokCombatModeCommands().stream().
                            filter(e -> e.getCommand().equals("Bg2btoBg3Toggle")).findFirst().orElseThrow(NotFoundException::new);
                    Button sendBtNewReadiness = setupGenerateSendCommandButton(sendBtReadiness, ButtonVariant.LUMO_PRIMARY, SEND_COMMAND_CAPTION);
                    if (getUI().isPresent()) {
                        getUI().get().access(() -> {
                            sendBtRowReadiness.removeAll();
                            sendBtRowReadiness.add(sendBtNewReadiness);
                        });
                    }
                } else if (askuService.getSplReadiness().get() == Readiness.BG_1) {
                    NppaCommand sendBtReadiness = nppaService.getAvailableNcokCombatModeCommands().stream().
                            filter(e -> e.getCommand().equals("Bg1toBg3Toggle")).findFirst().orElseThrow(NotFoundException::new);
                    Button sendBtNewReadiness = setupGenerateSendCommandButton(sendBtReadiness, ButtonVariant.LUMO_PRIMARY, SEND_COMMAND_CAPTION);
                    if (getUI().isPresent()) {
                        getUI().get().access(() -> {
                            sendBtRowReadiness.removeAll();
                            sendBtRowReadiness.add(sendBtNewReadiness);
                        });
                    }
                }
                if (getUI().isPresent()) {
                    getUI().get().access(() -> {
                        firstColumnSecondRowReadiness.removeAll();
                        firstColumnSecondRowReadiness.add(tipsForBG);
                    });
                }
            } else if (event.getValue().name() == "BG_2A") {
                TextArea tipsForBG = new TextArea();
                tipsForBG.setReadOnly(true);
                tipsForBG.addClassName("readines-tips-text-area");
                tipsForBG.setLabel("Бойова Готовність №2a");
                tipsForBG.setValue("Стан систем для Бойової Готовності №2а:\n" +
                                   "Статус фідера 1 - Включений;\n" +
                                   "Статус фідера 2 - Включений;\n" +
                                   "Статус фідера 3 - Включений;\n" +
                                   "ОТР1 та ОТР2 перевірені та можуть мати такі статуси :\n" +
                                   "      - ОТР1 та ОТР2 - Норма;\u2028  \n " +
                                   "     - ОТР1 - Норма, ОТР2 - Не норма;\n" +
                                   "      - ОТР1 - Не норма, ОТР2 - Норма.\n" +
                                   "Статус фідера 1 НППА СУ - Включений;\n" +
                                   "Статус фідера 6 - Включений;\n" +
                                   "СУТО в ГП - Норма;\n" +
                                   "УКННС - Норма;\n" +
                                   "БВН - Норма;\n" +
                                   "НЦОК - Відключено;\n" +
                                   "Зв’язок - наявний;\n" +
                                   "Координати СПУ присутні;\n" +
                                   "Вихідні дані для ОТР присутні.\u2028");
                if (askuService.getSplReadiness().get() == Readiness.BG_3) {
                    NppaCommand sendBtReadiness = nppaService.getAvailableNcokCombatModeCommands().stream().
                            filter(e -> e.getCommand().equals("Bg3toBg2aToggle")).findFirst().orElseThrow(NotFoundException::new);
                    Button sendBtNewReadiness = setupGenerateSendCommandButton(sendBtReadiness, ButtonVariant.LUMO_PRIMARY, SEND_COMMAND_CAPTION);
                    if (getUI().isPresent()) {
                        getUI().get().access(() -> {
                            sendBtRowReadiness.removeAll();
                            sendBtRowReadiness.add(sendBtNewReadiness);
                        });
                    }
                } else if (askuService.getSplReadiness().get() == Readiness.BG_1) {
                    NppaCommand sendBtReadiness = nppaService.getAvailableNcokCombatModeCommands().stream().
                            filter(e -> e.getCommand().equals("Bg1toBg2aToggle")).findFirst().orElseThrow(NotFoundException::new);
                    Button sendBtNewReadiness = setupGenerateSendCommandButton(sendBtReadiness, ButtonVariant.LUMO_PRIMARY, SEND_COMMAND_CAPTION);
                    if (getUI().isPresent()) {
                        getUI().get().access(() -> {
                            sendBtRowReadiness.removeAll();
                            sendBtRowReadiness.add(sendBtNewReadiness);
                        });
                    }
                }
                if (getUI().isPresent()) {
                    getUI().get().access(() -> {
                        firstColumnSecondRowReadiness.removeAll();
                        firstColumnSecondRowReadiness.add(tipsForBG);
                    });
                }
            } else if (event.getValue().name() == "BG_2B") {
                TextArea tipsForBG = new TextArea();
                tipsForBG.setReadOnly(true);
                tipsForBG.addClassName("readines-tips-text-area");
                tipsForBG.setLabel("Бойова Готовність №2");
                tipsForBG.setValue("Стан систем для Бойової Готовності №2:\n" +
                                   "Статус фідера 1 - Включений;\n" +
                                   "Статус фідера 2 - Включений;\n" +
                                   "Статус фідера 3 - Включений;\n" +
                                   "ОТР1 та ОТР2 перевірені та можуть мати такі статуси :\n" +
                                   "      - ОТР1 та ОТР2 - Норма;\u2028    \n" +
                                   "      - ОТР1 - Норма, ОТР2 - Не норма;\n" +
                                   "      - ОТР1 - Не норма, ОТР2 - Норма.\n" +
                                   "Статус фідера 1 НППА СУ - Включений;\n" +
                                   "Статус фідера 6 - Включений;\n" +
                                   "СУТО в ГП - Норма;\n" +
                                   "УКННС - Норма;\n" +
                                   "БВН - Норма;\n" +
                                   "НЦОК - Відключено;\n" +
                                   "Зв’язок - наявний;\n" +
                                   "Координати СПУ присутні;\n" +
                                   "Вихідні дані для ОТР присутні.\u2028");
                if (askuService.getSplReadiness().get() == Readiness.BG_3) {
                    NppaCommand sendBtReadiness = nppaService.getAvailableNcokCombatModeCommands().stream().
                            filter(e -> e.getCommand().equals("Bg3toBg2bToggle")).findFirst().orElseThrow(NotFoundException::new);
                    Button sendBtNewReadiness = setupGenerateSendCommandButton(sendBtReadiness, ButtonVariant.LUMO_PRIMARY, SEND_COMMAND_CAPTION);
                    if (getUI().isPresent()) {
                        getUI().get().access(() -> {
                            sendBtRowReadiness.removeAll();
                            sendBtRowReadiness.add(sendBtNewReadiness);
                        });
                    }
                }
                if (getUI().isPresent()) {
                    getUI().get().access(() -> {
                        firstColumnSecondRowReadiness.removeAll();
                        firstColumnSecondRowReadiness.add(tipsForBG);
                    });
                }
            } else if (event.getValue().name() == "BG_1") {
                TextArea tipsForBG = new TextArea();
                tipsForBG.setReadOnly(true);
                tipsForBG.addClassName("readines-tips-text-area");
                tipsForBG.setLabel("Бойова Готовність №1");
                tipsForBG.setValue("Стан систем для Бойової Готовності №1:\n" +
                                   "Статус фідера 1 - Включений;\n" +
                                   "Статус фідера 2 - Включений;\n" +
                                   "Статус фідера 3 - Включений;\n" +
                                   "ОТР1 та ОТР2 перевірені та можуть мати такі статуси :\n" +
                                   "      - ОТР1 та ОТР2 - Норма;\u2028   \n" +
                                   "      - ОТР1 - Норма, ОТР2 - Не норма;\n" +
                                   "      - ОТР1 - Не норма, ОТР2 - Норма.\n" +
                                   "Статус фідера 1 НППА СУ - Включений;\n" +
                                   "Статус фідера 6 - Включений;\n" +
                                   "СУТО в ГП - Норма;\n" +
                                   "УКННС - Норма;\n" +
                                   "БВН - Норма;\n" +
                                   "НЦОК - Норма;\n" +
                                   "Зв’язок - наявний;\n" +
                                   "Координати СПУ присутні;\n" +
                                   "Вихідні дані для ОТР присутні.\u2028");
                if (askuService.getSplReadiness().get() == Readiness.BG_3) {
                    NppaCommand sendBtReadiness = nppaService.getAvailableNcokCombatModeCommands().stream().
                            filter(e -> e.getCommand().equals("Bg3ToBg1SwitchWithOtr1Otr2SwOn")).findFirst().orElseThrow(NotFoundException::new);
                    Button sendBtNewReadiness = setupGenerateSendCommandButton(sendBtReadiness, ButtonVariant.LUMO_PRIMARY, SEND_COMMAND_CAPTION);
                    if (getUI().isPresent()) {
                        getUI().get().access(() -> {
                            sendBtRowReadiness.removeAll();
                            sendBtRowReadiness.add(sendBtNewReadiness);
                        });
                    }
                } else if (askuService.getSplReadiness().get() == Readiness.BG_2A) {
                    NppaCommand sendBtReadiness = nppaService.getAvailableNcokCombatModeCommands().stream().
                            filter(e -> e.getCommand().equals("Bg2aToBg1SwitchWithOtr1Otr2SwOn")).findFirst().orElseThrow(NotFoundException::new);
                    Button sendBtNewReadiness = setupGenerateSendCommandButton(sendBtReadiness, ButtonVariant.LUMO_PRIMARY, SEND_COMMAND_CAPTION);
                    if (getUI().isPresent()) {
                        getUI().get().access(() -> {
                            sendBtRowReadiness.removeAll();
                            sendBtRowReadiness.add(sendBtNewReadiness);
                        });
                    }

                }
                if (getUI().isPresent()) {
                    getUI().get().access(() -> {
                        firstColumnSecondRowReadiness.removeAll();
                        firstColumnSecondRowReadiness.add(tipsForBG);
                    });
                }
            }

//            boolean accepted = false;
//            try {
//                accepted = askuService.requestSplReadinessChange(event.getValue());
//            } catch (Exception e) {
//                log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
//                accepted = false;
//            }

        });
        add(firstColumnSecondRowReadiness, sendBtRowReadiness);
        return readinessCB;
    }

    private Button setupGenerateSendCommandButton(NppaCommand nppaCommand, ButtonVariant buttonColor, String caption) {
        Button sendBt = new Button(caption);
        sendBt.addClassName("send-sentence-button-command");
        sendBt.addThemeVariants(ButtonVariant.LUMO_SMALL, buttonColor);
        sendBt.getStyle().set("background-color", buttonColor.equals(ButtonVariant.LUMO_ERROR) ? "#dc0101" : "#4F4F4F");

        sendBt.addClickListener(event -> {
            NppaCommand generatedCommand = NppaCommand.builder()
                    .command(nppaCommand.getCommand())
                    .caption(nppaCommand.getCaption())
                    .commandState(nppaCommand.getCommandState())
                    .adjacentSystem(nppaCommand.getAdjacentSystem())
                    .generationTime(LocalDateTime.now())
                    .originator(userService.getUserName())
                    .build();

            Dialog dialog = new Dialog();


            CommandValidationResult commandValidationResult = nppaService.validateCommand(generatedCommand);

            if (commandValidationResult.isValid()) {
                dialog.addClassName("dialog-nppa");

                dialog.setHeaderTitle("Видача команди");
                dialog.add("Ви впевнені, що бажаєте відправити команду " + nppaCommand.getCaption() + "?");

                Button confirmBt = new Button("Відправити", (e) -> {
                    nppaService.setNppaCommand(generatedCommand);
                    dialog.close();
                    sendBtRowReadiness.remove(); // TODO: 1/3/2025 change caption to Отменить переход
                    // change color to red
                    // and set command for cancellation
                    // TODO: 1/3/2025 or setup new button for cancellation with event listener for TRANSITION и AUTO_TEST_IN_PROGRESS
                });
                confirmBt.addClassName("confirm-button-nppa-dialog");

                confirmBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY,
                        ButtonVariant.LUMO_ERROR);
                confirmBt.getStyle().set("margin-right", "auto");
                dialog.getFooter().add(confirmBt);
            } else {
                String errorMsg = commandValidationResult.errorMessageUa();
                log.error(errorMsg + " " + NppaView.class);

                ConfigurationUtils.getErrorNotification(errorMsg, "",
                        0, true).open();

                dialog.addClassName("dialog-nppa");

                dialog.setHeaderTitle("Помилка");
                dialog.add(commandValidationResult.errorMessageUa());
                return;
            }
            Button cancelButton = new Button("Відміна", (e) -> dialog.close());
            cancelButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
            cancelButton.addClassName("cancel-button-nppa-dialog");

            dialog.getFooter().add(cancelButton);


            dialog.open();

        });
        return sendBt;

    }

    private final NppaCommand cancelBgToBgToggle;
    private final NppaCommand cancelAutoTestModeCommand;

    private void initializeReadinessChangeCancellationBt(VerticalLayout layout, NppaCommand command) {
        Button button = setupGenerateSendCommandButton(command, ButtonVariant.LUMO_ERROR, "Відмінити");
        layout.add(button);

    }

    private void setupBgCommandButtonState(VerticalLayout layout, Readiness currentReadiness) {

        if (!askuBgTransitionsAvailableForCancellation.contains(currentReadiness)) {
            return;
        }
        Button button = setupGenerateSendCommandButton(cancelBgToBgToggle, ButtonVariant.LUMO_ERROR, "Відмінити");
        layout.removeAll();
        layout.add(button);
    }

    private class ThirdColumnStatus extends HorizontalLayout {

        public ThirdColumnStatus() {
            Span statusCellASKU = new Span("Статус відсіку АСКУ");
            statusCellASKU.addClassName("status-cell-ASKU");


            VerticalLayout firstColumnStatus = new VerticalLayout();

            TextField checkOtrLeft = new TextField("Перевірка ОТР1");
            checkOtrLeft.setReadOnly(true);
            checkOtrLeft.addClassName("check-otr-left");
            checkOtrLeft.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            ncokBinder.forField(checkOtrLeft)
                    .bindReadOnly(ncok -> {
                        try {
                            if (ncok != null) {
                                return ncok.getOtr1TestResult().getValueUa();
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                        }
                        return AdjacentSystemStatus.UNDEFINED.getValueUa();
                    });
            checkOtrLeft.addValueChangeListener(valueChangeEvent -> ConfigurationUtils.setTextAnimation(valueChangeEvent, BaseProperty.class));
            checkOtrLeft.setValue((nppaService.getNcok().isPresent() && nppaService.getNcok().get().otr1TestResult() != null) ?
                    nppaService.getNcok().get().otr1TestResult().getValueUa() : BaseProperty.UNDEFINED.getValueUa());

            TextField checkOtrRight = new TextField("Перевірка ОТР2");
            checkOtrRight.setReadOnly(true);
            checkOtrRight.addClassName("check-otr-right");
            checkOtrRight.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            ncokBinder.forField(checkOtrRight)
                    .bindReadOnly(ncok -> {
                        try {
                            if (ncok != null) {
                                return ncok.getOtr2TestResult().getValueUa();
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                        }
                        return AdjacentSystemStatus.UNDEFINED.getValueUa();
                    });
            checkOtrRight.addValueChangeListener(valueChangeEvent -> ConfigurationUtils.setTextAnimation(valueChangeEvent, BaseProperty.class));
            checkOtrRight.setValue((nppaService.getNcok().isPresent() && nppaService.getNcok().get().otr2TestResult() != null) ?
                    nppaService.getNcok().get().otr2TestResult().getValueUa() : BaseProperty.UNDEFINED.getValueUa());

            TextField outriggersHorizontalPosition = new TextField("СУТО в ПП");
            outriggersHorizontalPosition.setReadOnly(true);
            outriggersHorizontalPosition.addClassName("outriggers-horizontal-position-readiness");
            outriggersHorizontalPosition.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            sutoBinder.forField(outriggersHorizontalPosition)
                    .bindReadOnly(s -> {
                        if (s == null || s.getProperties() == null) {
                            return "";
                        }
                        if (sutoService.getSutoPropertyByName("isOutriggersInMobileState").get().getState().equals(CommandState.ON)) {
                            outriggersHorizontalPosition.addClassName("grid-cell-blue");
                            return "Так";
                        } else {
                            outriggersHorizontalPosition.addClassName("grid-cell-gray");
                            return "Ні";
                        }
                    });

//readiness change
            TextField outriggersDP = new TextField("СУТО в ГП");
            outriggersDP.setReadOnly(true);
            outriggersDP.addClassName("outriggers-dp");
            outriggersDP.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            sutoBinder.forField(outriggersDP)
                    .bindReadOnly(s -> {
                        if (s == null || s.getProperties() == null) {
                            return "";
                        }
                        if (sutoService.getSutoPropertyByName("isChassisHorizontal").get().getState().equals(CommandState.ON)) {
                            outriggersDP.addClassName("grid-cell-blue");
                            return "Так";
                        } else {
                            outriggersDP.addClassName("grid-cell-gray");
                            return "Ні";
                        }
                    });

            TextField testNppa = new TextField("Перевірка НППА");
            testNppa.setReadOnly(true);
            testNppa.addClassName("check-nppa-readiness");
            testNppa.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            ncokBinder.forField(testNppa)
                    .bindReadOnly(ncok -> {
                        try {
                            if (ncok != null) {
                                return ncok.getNppaTestResult().getValueUa();
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "Невідомо";
                        }
                        return AdjacentSystemStatus.UNDEFINED.getValueUa();
                    });
            testNppa.addValueChangeListener(valueChangeEvent -> ConfigurationUtils.setTextAnimation(valueChangeEvent, BaseProperty.class));
            testNppa.setValue((nppaService.getNcok().isPresent() && nppaService.getNcok().get().nppaTestResult() != null) ?
                    nppaService.getNcok().get().nppaTestResult().getValueUa() : BaseProperty.UNDEFINED.getValueUa());

            firstColumnStatus.add(checkOtrLeft, checkOtrRight, outriggersHorizontalPosition, outriggersDP,testNppa);

            VerticalLayout secondColumnStatus = new VerticalLayout();

            TextField feeder1Status = new TextField("Статус фідера 1");
            feeder1Status.setReadOnly(true);
            feeder1Status.addClassName("feeder1-readiness");
            feeder1Status.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            feeder1Status.addValueChangeListener(valueChangeEvent -> ConfigurationUtils.setTextAnimation(valueChangeEvent, SaeFeederStatus.class));
            feeder1Status.setValue(saeService.getSae().isEmpty() ?
                    SaeFeederStatus.UNDEFINED.getValueUa() :
                    (saeService.getSae().get().getFeeder1Status() != null ?
                            saeService.getSae().get().getFeeder1Status().getValueUa() : SaeFeederStatus.UNDEFINED.getValueUa()));
            saeBinder.forField(feeder1Status)
                    .bindReadOnly(sae -> (sae.getFeeder1Status() != null) ?
                            sae.getFeeder1Status().getValueUa() : "Помилка формату");


            TextField turnOnFeeder2 = new TextField("Статус фідера 2");
            turnOnFeeder2.setReadOnly(true);
            turnOnFeeder2.addClassName("feeder2-readiness");
            turnOnFeeder2.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            turnOnFeeder2.addValueChangeListener(valueChangeEvent -> ConfigurationUtils.setTextAnimation(valueChangeEvent, SaeFeederStatus.class));
            turnOnFeeder2.setValue(saeService.getSae().isEmpty() ?
                    SaeFeederStatus.UNDEFINED.getValueUa() :
                    (saeService.getSae().get().getFeeder2Status() != null ?
                            saeService.getSae().get().getFeeder2Status().getValueUa() : SaeFeederStatus.UNDEFINED.getValueUa()));
            saeBinder.forField(turnOnFeeder2)
                    .bindReadOnly(sae -> (sae.getFeeder2Status() != null) ?
                            sae.getFeeder2Status().getValueUa() : "Помилка формату");

            TextField turnOnFeeder3 = new TextField("Статус фідера 3");
            turnOnFeeder3.setReadOnly(true);
            turnOnFeeder3.addClassName("feeder3-readiness");
            turnOnFeeder3.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            turnOnFeeder3.addValueChangeListener(valueChangeEvent -> ConfigurationUtils.setTextAnimation(valueChangeEvent, SaeFeederStatus.class));
            turnOnFeeder3.setValue(saeService.getSae().isEmpty() ?
                    SaeFeederStatus.UNDEFINED.getValueUa() :
                    (saeService.getSae().get().getFeeder3Status() != null ?
                            saeService.getSae().get().getFeeder3Status().getValueUa() : SaeFeederStatus.UNDEFINED.getValueUa()));

            saeBinder.forField(turnOnFeeder3)
                    .bindReadOnly(sae -> (sae.getFeeder3Status() != null) ?
                            sae.getFeeder3Status().getValueUa() : "Помилка формату");

            TextField turnOnFeeder4 = new TextField("Статус фідера 4");
            turnOnFeeder4.setReadOnly(true);
            turnOnFeeder4.addClassName("feeder4-readiness");
            turnOnFeeder4.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            turnOnFeeder4.addValueChangeListener(valueChangeEvent -> ConfigurationUtils.setTextAnimation(valueChangeEvent, SaeFeederStatus.class));
            turnOnFeeder4.setValue(saeService.getSae().isEmpty() ?
                    SaeFeederStatus.UNDEFINED.getValueUa() :
                    (saeService.getSae().get().getFeeder4Status() != null ?
                            saeService.getSae().get().getFeeder4Status().getValueUa() : SaeFeederStatus.UNDEFINED.getValueUa()));
            saeBinder.forField(turnOnFeeder4)
                    .bindReadOnly(sae -> (sae.getFeeder4Status() != null) ?
                            sae.getFeeder4Status().getValueUa() : "Помилка формату");

            TextField turnOnFeeder5 = new TextField("Статус фідера 5");
            turnOnFeeder5.setReadOnly(true);
            turnOnFeeder5.addClassName("feeder5-readiness");
            turnOnFeeder5.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            turnOnFeeder5.addValueChangeListener(valueChangeEvent -> ConfigurationUtils.setTextAnimation(valueChangeEvent, SaeFeederStatus.class));
            turnOnFeeder5.setValue(saeService.getSae().isEmpty() ?
                    SaeFeederStatus.UNDEFINED.getValueUa() :
                    (saeService.getSae().get().getFeeder5Status() != null ?
                            saeService.getSae().get().getFeeder5Status().getValueUa() : SaeFeederStatus.UNDEFINED.getValueUa()));
            saeBinder.forField(turnOnFeeder5)
                    .bindReadOnly(sae -> (sae.getFeeder5Status() != null) ?
                            sae.getFeeder5Status().getValueUa() : "Помилка формату");

            TextField turnOnFeeder6 = new TextField("Статус фідера 6");
            turnOnFeeder6.setReadOnly(true);
            turnOnFeeder6.addClassName("feeder6-readiness");
            turnOnFeeder6.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            turnOnFeeder6.addValueChangeListener(valueChangeEvent -> ConfigurationUtils.setTextAnimation(valueChangeEvent, SaeFeederStatus.class));
            turnOnFeeder6.setValue(saeService.getSae().isEmpty() ?
                    SaeFeederStatus.UNDEFINED.getValueUa() :
                    (saeService.getSae().get().getFeeder6Status() != null ?
                            saeService.getSae().get().getFeeder6Status().getValueUa() : SaeFeederStatus.UNDEFINED.getValueUa()));
            saeBinder.forField(turnOnFeeder6)
                    .bindReadOnly(sae -> (sae.getFeeder6Status() != null) ?
                            sae.getFeeder6Status().getValueUa() : "Помилка формату");


            TextField feederNppa1Status = new TextField("Статус фідера 1 НППА");
            feederNppa1Status.setReadOnly(true);
            feederNppa1Status.addClassName("feeder-nppa-su-readiness");
            feederNppa1Status.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            feederNppa1Status.addValueChangeListener(valueChangeEvent -> ConfigurationUtils.setTextAnimation(valueChangeEvent, SaeFeederStatus.class));
            feederNppa1Status.setValue(saeService.getSae().isEmpty() ?
                    SaeFeederStatus.UNDEFINED.getValueUa() :
                    (saeService.getSae().get().getFeederNppa1Status() != null ?
                            saeService.getSae().get().getFeederNppa1Status().getValueUa() : SaeFeederStatus.UNDEFINED.getValueUa()));
            saeBinder.forField(feederNppa1Status)
                    .bindReadOnly(sae -> (sae.getFeederNppa1Status() != null) ?
                            sae.getFeederNppa1Status().getValueUa() : "Помилка формату");

            TextField blockingAJSAE = new TextField("Блокування АДЖ");
            blockingAJSAE.setReadOnly(true);
            blockingAJSAE.addClassName("blocking-aj-sae-readiness");
            blockingAJSAE.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            blockingAJSAE.addValueChangeListener(valueChangeEvent -> ConfigurationUtils.setTextAnimation(valueChangeEvent, ADJstatus.class));
            blockingAJSAE.setValue(saeService.getSae().isEmpty() ?
                    ADJstatus.NOT_ACTIVE.getValueUa() :
                    (saeService.getSae().get().getAdjLock() != null ?
                            saeService.getSae().get().getAdjLock().getValueUa() : ADJstatus.NOT_ACTIVE.getValueUa()));
            saeBinder.forField(blockingAJSAE)
                    .bindReadOnly(sae -> sae.getAdjLock() != null ?
                            sae.getAdjLock().getValueUa() : "Помилка формату");
            secondColumnStatus.addClassName("second-column-status");
            secondColumnStatus.add(feeder1Status, turnOnFeeder2, turnOnFeeder3, turnOnFeeder4, turnOnFeeder5, turnOnFeeder6, feederNppa1Status, blockingAJSAE);

            add(firstColumnStatus, secondColumnStatus);
        }
    }

    private void processAskuInfoEvent(AskuInfoEvent incomingEvent) {
        if (askuReadiness != null && askuReadiness.equals(incomingEvent)) {
            return;
        }
        if (incomingEvent.getAskuInfo() == null || getUI().isEmpty()) {
            return;
        }

        if (incomingEvent.getAskuInfo().getSplReadiness().isPresent() &&
            incomingEvent.getAskuInfo().getSplReadiness().get() != askuReadiness) {
            getUI().get().access(() -> {
                askuInfoBinder.readBean(incomingEvent.getAskuInfo());
                askuReadiness = incomingEvent.getAskuInfo().getSplReadiness().get();

                if (askuBgTransitionsAvailableForCancellation.contains(askuReadiness)) {
                    setupBgCommandButtonState(sendBtRowReadiness, askuReadiness);
                } else if (!askuReadiness.equals(Readiness.AUTO_TEST_IN_PROGRESS)) {
                    removeAll();
                    initView();
                }
            });
        }
    }

    @Override
    public void receiveBroadcast(Object incomingEvent) {


        if (!(incomingEvent instanceof AdjacentSystemEvent event)) {
            return;
        }

        if (event.getEntity().isEmpty()) {
            log.error("received event without entity " + event + " " + this);
            return;
        }
        if (incomingEvent instanceof AskuInfoEvent) {
            processAskuInfoEvent((AskuInfoEvent) incomingEvent);
        }


        switch (event.getSystemType()) {
            case PPO -> updateEvent(PpoEvent.class, event, ppoBinder);
            case SUTO -> handleSutoEvent(SutoEvent.class, event, sutoBinder);
            case SAE -> updateEvent(SaeEvent.class, event, saeBinder);
            case BINS -> handleBinsEvent(BinsEvent.class, event, binsBinder);
            case MSU -> updateEvent(MsuEvent.class, event, msuBinder);
            case PLC -> updateEvent(PlcEvent.class, event, plcBinder);
            case ASKU -> handleAskuEvent(event);
            case NCOK, BYN, NPPA -> handleNppaEvent(event);
        }
    }

    private void handleBinsEvent(Class eventClass, AdjacentSystemEvent event, Binder binder) {

        updateEvent(eventClass, event, binder);
    }

    private void handleSutoEvent(Class eventClass, AdjacentSystemEvent event, Binder binder) {
        if (!(event instanceof SutoEvent eventUnderProcessing)) {
            log.debug("unexpected event at " + this.getClassName());
            return;
        }
        getUI().get().access(() -> {
            sutoBinder.readBean(eventUnderProcessing.getSuto());
        });
        updateEvent(eventClass, event, binder);
    }


    private void handleNppaEvent(@NotNull AdjacentSystemEvent event) {
        if (event.getEntity().isEmpty()) {
            return;
        }

        if (event.getEntity().get() instanceof Nppa updatedNppa) {
            getUI().get().access(() -> {
                bynBinder.readBean(updatedNppa.getByn());
                ncokBinder.readBean(updatedNppa.getNcok());
            });
        }

    }

    private void handleAskuEvent(@NotNull AdjacentSystemEvent event) {
        if (event.getEntity().isEmpty()) {
            return;
        }
        if (!(event.getEntity().get() instanceof AskuInfo)) {
            return;
        }

        updateEvent(AskuInfoEvent.class, event, askuInfoBinder);
    }


    private boolean updateEvent(Class eventClass, AdjacentSystemEvent event, Binder binder) {

        if (getUI().isPresent()) {
            getUI().get().access(() -> binder.readBean(event.getEntity().get()));
        }

        return true;
    }

}