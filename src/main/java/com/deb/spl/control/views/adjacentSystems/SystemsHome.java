package com.deb.spl.control.views.adjacentSystems;

import com.deb.spl.control.data.asku.RocketMapper;
import com.deb.spl.control.service.*;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.service.asku.PlcService;
import com.deb.spl.control.views.MainLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import org.springframework.beans.factory.annotation.Value;

@PageTitle("Сатус систем")
@Route(value = "adjacent-systems", layout = MainLayout.class)
public class SystemsHome extends AdjacentSystemView {
    @Value("${home.url}")
    String homePage;

    public SystemsHome(MsuService msuService,
                       PpoService ppoService,
                       SutoService sutoService,
                       BinsService binsService,
                       SaeService saeService,
                       AskuService askuService,
                       PlcService plcService,
                       RocketMapper rocketMapper,
                       NppaService nppaService,
                       CcvCommunicationService ccvCommunicationService) {
        super(msuService,
                ppoService,
                sutoService,
                binsService,
                saeService,
                askuService,
                plcService,
                "Оберіть систему",
                rocketMapper,
                nppaService,
                ccvCommunicationService);
        getSystemLayout().addClassName("system-layout-bins");
        getSystemLayout().setWidth("69.5%");
        getSystemLayout().setMaxWidth("69.5%");
    }
}
