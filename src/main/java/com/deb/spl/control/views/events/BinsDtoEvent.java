package com.deb.spl.control.views.events;

import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.BinsDto;
import com.deb.spl.control.utils.BinsToDtoConverter;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Optional;

@NoArgsConstructor
@Getter
public class BinsDtoEvent implements AdjacentSystemEvent {
    BinsDto dto;
    Object source;
    AdjacentSystemUpdateEventType eventType;

    /**
     * @param dto       changed object
     * @param source    event source
     * @param eventType @{@link AdjacentSystemUpdateEventType}
     */
    public BinsDtoEvent(BinsDto dto, Object source, AdjacentSystemUpdateEventType eventType) {
        this.dto = dto;
        this.source = source;
        this.eventType = eventType;
    }

    @Override
    public AdjacentSystemType getSystemType() {
        return AdjacentSystemType.BINS;
    }

    @Override
    public Optional<AdjacentSystem> getEntity() {
        return dto != null ? Optional.of(BinsToDtoConverter.fromDto(dto)) : Optional.empty();
    }
}
