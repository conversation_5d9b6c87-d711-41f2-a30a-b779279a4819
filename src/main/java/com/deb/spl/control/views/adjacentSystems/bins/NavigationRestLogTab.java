package com.deb.spl.control.views.adjacentSystems.bins;

import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.bins.RestRequestDAO;
import com.deb.spl.control.repository.bins.NavigationRestRequestRepository;
import com.deb.spl.control.views.adjacentSystems.utils.LogExtendedInfoDialogBuilder;
import com.vaadin.flow.component.AttachEvent;
import com.vaadin.flow.component.ComponentEventListener;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.dialog.Dialog;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.grid.GridVariant;
import com.vaadin.flow.component.icon.Icon;
import com.vaadin.flow.component.icon.VaadinIcon;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.data.value.ValueChangeMode;
import com.vaadin.flow.shared.Registration;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

import javax.validation.constraints.NotNull;
import java.time.format.DateTimeFormatter;

public class NavigationRestLogTab extends VerticalLayout {
    private final Grid<RestRequestDAO> grid;
    private TextField filterTextField;

    private final NavigationRestRequestRepository repository;
    private final AdjacentSystemType adjacentSystemTypeFilter;

    @Override
    public Registration addAttachListener(ComponentEventListener<AttachEvent> listener) {
        return super.addAttachListener(listener);
    }

    public NavigationRestLogTab(@NotNull NavigationRestRequestRepository repository, AdjacentSystemType adjacentSystemTypeFilter, String className) {
        this.repository = repository;
        this.adjacentSystemTypeFilter = adjacentSystemTypeFilter;
        addClassName("navigation-rest-log-tab");

        initFilterField();

        grid = new Grid<>(RestRequestDAO.class, false);
        grid.addClassName(className != null ? className : "log-grid");
        grid.addThemeVariants(GridVariant.LUMO_WRAP_CELL_CONTENT);

        grid.addColumn(RestRequestDAO::getUrl)
                .setResizable(true)
                .setHeader("URL")
                .setFlexGrow(0);
        grid.addColumn(RestRequestDAO::getRequestQueryString)
                .setResizable(true)
                .setHeader("Request query string")
                .setWidth("100px")
                .setFlexGrow(0);
        grid.addColumn(RestRequestDAO::getCachedPayload)
                .setResizable(true)
                .setHeader("Payload")
                .setFlexGrow(0);
        grid.addColumn(RestRequestDAO::getDirection)
                .setResizable(true)
                .setHeader("напрямок")
                .setFlexGrow(0);
        grid.addColumn(item -> item.getAdjacentSystem().getUa())
                .setResizable(true)
                .setHeader("Система")
                .setFlexGrow(0);
        grid.addColumn(item -> item.getCreatedAt() != null ? item.getCreatedAt().format(DateTimeFormatter.ofPattern("dd/MM/yyyy\t HH:mm:ss")) : "")
                .setResizable(true)
                .setHeader("Час створення");

        grid.addItemDoubleClickListener(event -> {
            Dialog itemInfoDialog = LogExtendedInfoDialogBuilder.getDialog(event.getItem());
            itemInfoDialog.open();
        });

        setPadding(false);
        setSizeFull();
        add(grid);
    }

    public void setupDataBinding() {
        grid.setItems(query -> repository.findAllByAdjacentSystem(
                                PageRequest.of(query.getPage(), query.getPageSize(), Sort.by("createdAt").descending())
                                , adjacentSystemTypeFilter
                        )
                        .stream()
        );
    }

    private void initFilterField() {
        HorizontalLayout horizontalLayout = new HorizontalLayout();
        addClassName("grid-filter-layout");

        filterTextField = new TextField();
        filterTextField.setPlaceholder("Пошук");
        filterTextField.setPrefixComponent(new Icon("lumo", "search"));

        filterTextField.setValueChangeMode(ValueChangeMode.LAZY);
        filterTextField.addValueChangeListener(e -> listRecordFilterByOriginalSentence(e.getValue()));

        Button refresh = new Button(VaadinIcon.REFRESH.create(),
                ev -> grid.getDataProvider().refreshAll());

        horizontalLayout.add(filterTextField, refresh);
        add(horizontalLayout);
    }

    private void listRecordFilterByOriginalSentence(String filterString) {
        String likeFilter = "%" + filterString + "%";
        grid.setItems(query ->
                repository.findByCachedPayloadLikeIgnoreCase(
                                likeFilter,
                                PageRequest.of(query.getPage(), query.getPageSize(), Sort.by("createdAt").descending()))
                        .stream());
    }

}

