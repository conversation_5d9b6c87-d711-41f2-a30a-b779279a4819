package com.deb.spl.control.views.adjacentSystems;

import com.deb.spl.control.repository.HistoryRepository;
import com.deb.spl.control.views.adjacentSystems.utils.LogExtendedInfoDialogBuilder;
import com.deb.spl.control.data.AdjacentSystemCommand;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.dialog.Dialog;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.grid.GridVariant;
import com.vaadin.flow.component.icon.Icon;
import com.vaadin.flow.component.icon.VaadinIcon;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.data.value.ValueChangeMode;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

import javax.validation.constraints.NotNull;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;

public class LogTab extends VerticalLayout {
    private final Grid<AdjacentSystemCommand> grid;
    private TextField filterTextField;

    private final HistoryRepository repository;


    public LogTab(@NotNull HistoryRepository repository, String className) {
        this.repository = repository;
        addClassName("adj-rest-log-tab");

        initFilterField();

        grid = new Grid<>(AdjacentSystemCommand.class, false);
        grid.addClassName(className != null ? className : "log-grid");
        grid.addThemeVariants(GridVariant.LUMO_WRAP_CELL_CONTENT);

        grid.addColumn(AdjacentSystemCommand::getId)
                .setHeader("id")
                .setWidth("75px")
                .setFlexGrow(0);
        grid.addColumn(AdjacentSystemCommand::getCommand)
                .setHeader("Команда")
                .setFlexGrow(2);
        grid.addColumn(AdjacentSystemCommand::getCaption)
                .setHeader("Опис")
                .setFlexGrow(2);
        grid.addColumn(item -> item.getExecutionTime() != null ? item.getExecutionTime().format(DateTimeFormatter.ofPattern("dd/MM/yyyy\t HH:mm:ss")) : "")
                .setHeader("Час виконання");
        grid.addColumn(item -> item.getGenerationTime() != null ? item.getGenerationTime().format(DateTimeFormatter.ofPattern("dd/MM/yyyy\t HH:mm:ss")) : "")
                .setHeader("Час створення");
        grid.setItems(query -> repository.findAll(
                        PageRequest.of(query.getPage(), query.getPageSize(), Sort.by("generationTime").descending())
                )
                .stream()
                .sorted(Comparator.comparing(AdjacentSystemCommand::getGenerationTime).reversed()));

        grid.addItemDoubleClickListener(event -> {
                    Dialog itemInfoDialog = LogExtendedInfoDialogBuilder.getDialog(event.getItem());
                    itemInfoDialog.open();
                }
        );
        setSizeFull();
        add(grid);
    }

    private void initFilterField() {
        HorizontalLayout horizontalLayout = new HorizontalLayout();
        addClassName("grid-filter-layout");

        filterTextField = new TextField();
        filterTextField.setPlaceholder("Пошук");
        filterTextField.setPrefixComponent(new Icon("lumo", "search"));

        filterTextField.setValueChangeMode(ValueChangeMode.LAZY);
        filterTextField.addValueChangeListener(e -> listRecordFilterByOriginalSentence(e.getValue()));

        Button refresh = new Button(VaadinIcon.REFRESH.create(),
                ev -> grid.getDataProvider().refreshAll());

        horizontalLayout.add(filterTextField, refresh);
        add(horizontalLayout);
    }

    private void listRecordFilterByOriginalSentence(String filterString) {
        String likeFilter = "%" + filterString + "%";
        grid.setItems(query ->
                repository.findByCommandLikeIgnoreCase(
                                likeFilter,
                                PageRequest.of(query.getPage(), query.getPageSize(), Sort.by("generationTime").descending()))
                        .stream());
    }
}
