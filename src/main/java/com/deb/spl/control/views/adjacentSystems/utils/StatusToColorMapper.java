package com.deb.spl.control.views.adjacentSystems.utils;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;
import com.deb.spl.control.data.VoltageStatus;
import com.deb.spl.control.data.asku.TpcState;
import com.deb.spl.control.data.nppa.BasSnsStatus;
import com.deb.spl.control.data.nppa.BaseProperty;
import com.deb.spl.control.data.nppa.NppaOperatingMode;
import com.deb.spl.control.data.ppo.PpoBayStatus;
import com.deb.spl.control.data.ppo.PpoUnitStatus;
import com.deb.spl.control.data.sae.*;
import com.deb.spl.control.data.suto.OutriggerEmergencyCode;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import javax.validation.constraints.NotBlank;

@Slf4j
public class StatusToColorMapper {
    public static AnimationColor fromElementsStatuses(@NotBlank String value,
                                                      @NotNull Class<AdjacentSystemWithDescriptionUa> aClass) {
        if (AdjacentSystemStatus.class.isAssignableFrom(aClass) || BaseProperty.class.isAssignableFrom(aClass)) {
            return fromAdjacentSystemStatuses(AdjacentSystemStatus.fromValueUa(value));
        }

        if (BasSnsStatus.class.isAssignableFrom(aClass)) {
            return fromBasSnsStatusStatuses(BasSnsStatus.fromValueUa(value));
        }

        if (SaeFeederStatus.class.isAssignableFrom(aClass)) {
            return fromSaeElementsStatuses(SaeFeederStatus.fromValueUa(value));
        }

        if (ADJstatus.class.isAssignableFrom(aClass)) {
            return fromSaeElementsStatuses(ADJstatus.fromValueUa(value));
        }

        if (HDSstatus.class.isAssignableFrom(aClass)) {
            return fromSaeElementsStatuses(HDSstatus.fromValueUa(value));
        }

        if (VoltageStatus.class.isAssignableFrom(aClass)) {
            return fromSaeElementsStatuses(VoltageStatus.fromValueUa(value));
        }

        if (SaePowerSourceStatus.class.isAssignableFrom(aClass)) {
            return fromSaeElementsStatuses(SaePowerSourceStatus.fromValueUa(value));
        }

        if (SaeStatus.class.isAssignableFrom(aClass)) {
            return fromSaeElementsStatuses(SaeStatus.fromValueUa(value));
        }

        if (PpoBayStatus.class.isAssignableFrom(aClass)) {
            return fromPpoBayStatus(PpoBayStatus.fromValueUa(value));
        }

        if (PpoUnitStatus.class.isAssignableFrom(aClass)) {
            return fromPpoUnitStatus(PpoUnitStatus.fromValueUa(value));
        }

        if (OutriggerEmergencyCode.class.isAssignableFrom(aClass)) {
            return fromOutriggerMalfunctionCode(OutriggerEmergencyCode.fromValueUa(value));
        }

        if (TpcState.class.isAssignableFrom(aClass)) {
            return fromTpcStatus(TpcState.fromValueUa(value));
        }

        if (NppaOperatingMode.class.isAssignableFrom(aClass)) {
            return fromNppaOperatingMode(NppaOperatingMode.fromValueUa(value));
        }

        log.warn("can find applicable mapper for th value " + value + " for class " + aClass);

        return AnimationColor.UNDEFINED;
    }


    private static AnimationColor fromOutriggerMalfunctionCode(OutriggerEmergencyCode outriggerEmergencyCode) {

        return outriggerEmergencyCode == OutriggerEmergencyCode.NONE ? AnimationColor.GREEN : AnimationColor.RED;
    }

    public static AnimationColor fromSaeElementsStatuses(SaeFeederStatus feederStatus) {
        AnimationColor animationColor;
        switch (feederStatus) {
            case ON -> animationColor = AnimationColor.GREEN;
            case ERROR -> animationColor = AnimationColor.RED;
            default -> animationColor = AnimationColor.UNDEFINED;
        }

        return animationColor;
    }

    public static AnimationColor fromSaeElementsStatuses(ADJstatus status) {
        AnimationColor animationColor;
        switch (status) {
            case OK -> animationColor = AnimationColor.GREEN;
            case ERROR -> animationColor = AnimationColor.RED;
            default -> animationColor = AnimationColor.UNDEFINED;
        }

        return animationColor;
    }

    public static AnimationColor fromSaeElementsStatuses(@NotNull HDSstatus status) {
        AnimationColor animationColor;
        switch (status) {
            case OK -> animationColor = AnimationColor.GREEN;
            case ERROR -> animationColor = AnimationColor.RED;
            default -> animationColor = AnimationColor.UNDEFINED;
        }

        return animationColor;
    }

    public static AnimationColor fromSaeElementsStatuses(VoltageStatus status) {
        AnimationColor animationColor;
        switch (status) {
            case OK -> animationColor = AnimationColor.GREEN;
            case ERROR -> animationColor = AnimationColor.RED;
            default -> animationColor = AnimationColor.UNDEFINED;
        }

        return animationColor;
    }

    public static AnimationColor fromSaeElementsStatuses(SaePowerSourceStatus status) {
        AnimationColor animationColor;
        switch (status) {
            case OK -> animationColor = AnimationColor.GREEN;
            case ERROR -> animationColor = AnimationColor.RED;
            default -> animationColor = AnimationColor.UNDEFINED;
        }

        return animationColor;
    }

    public static AnimationColor fromSaeElementsStatuses(SaeStatus status) {
        AnimationColor animationColor;
        switch (status) {
            case OK -> animationColor = AnimationColor.GREEN;
            case WARNING -> animationColor = AnimationColor.YELLOW;
            case ERROR -> animationColor = AnimationColor.RED;
            default -> animationColor = AnimationColor.UNDEFINED;
        }

        return animationColor;
    }

    public static AnimationColor fromAdjacentSystemStatuses(AdjacentSystemStatus status) {
        AnimationColor animationColor;
        switch (status) {
            case OK -> animationColor = AnimationColor.GREEN;
            case WARNING -> animationColor = AnimationColor.YELLOW;
            case ERROR -> animationColor = AnimationColor.RED;
            case RT_DATA -> animationColor = AnimationColor.WHITE;
            default -> animationColor = AnimationColor.UNDEFINED;
        }

        return animationColor;
    }

    public static AnimationColor fromBasSnsStatusStatuses(BasSnsStatus status) {
        AnimationColor animationColor;
        switch (status) {
            case OK -> animationColor = AnimationColor.GREEN;
            case WARNING -> animationColor = AnimationColor.YELLOW;
            case ERROR -> animationColor = AnimationColor.RED;
            default -> animationColor = AnimationColor.UNDEFINED;
        }

        return animationColor;
    }

    private static AnimationColor fromPpoBayStatus(PpoBayStatus status) {
        AnimationColor animationColor;
        switch (status) {
            case OK -> animationColor = AnimationColor.GREEN;
            case WARNING -> animationColor = AnimationColor.YELLOW;
            case ERROR -> animationColor = AnimationColor.RED;
            default -> animationColor = AnimationColor.UNDEFINED;
        }

        return animationColor;
    }

    private static AnimationColor fromPpoUnitStatus(PpoUnitStatus status) {
        AnimationColor animationColor;
        switch (status) {
            case ON -> animationColor = AnimationColor.GREEN;
            case ERROR -> animationColor = AnimationColor.RED;
            default -> animationColor = AnimationColor.UNDEFINED;
        }

        return animationColor;
    }

    public static AnimationColor fromTpcStatus(TpcState status) {
        AnimationColor animationColor;
        switch (status) {
            case TPC_WITH_ROCKET -> animationColor = AnimationColor.GREEN;
            case TPC_ONLY -> animationColor = AnimationColor.YELLOW;
            case NONE -> animationColor = AnimationColor.GREY;
            case ERROR -> animationColor = AnimationColor.RED;
            default -> animationColor = AnimationColor.UNDEFINED;
        }

        return animationColor;
    }

    private static AnimationColor fromNppaOperatingMode(NppaOperatingMode status) {
        AnimationColor animationColor;
        switch (status) {
            case COMBAT -> animationColor = AnimationColor.GREEN;
            case TESTING -> animationColor = AnimationColor.GREY;
            default -> animationColor = AnimationColor.YELLOW;
        }

        return animationColor;
    }
}
