package com.deb.spl.control.views.adjacentSystems.bins;

import com.deb.spl.control.repository.SplNavigationHistoryRepository;
import com.deb.spl.control.views.adjacentSystems.utils.LogExtendedInfoDialogBuilder;
import com.deb.spl.control.data.bins.SplNavigationRecord;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.dialog.Dialog;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.grid.GridVariant;
import com.vaadin.flow.component.icon.Icon;
import com.vaadin.flow.component.icon.VaadinIcon;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.data.value.ValueChangeMode;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

import javax.validation.constraints.NotNull;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;

public class SplNavigationLogTab extends VerticalLayout {


    private final Grid<SplNavigationRecord> grid;
    private TextField filterTextField;

    private final SplNavigationHistoryRepository repository;

    public SplNavigationLogTab(@NotNull SplNavigationHistoryRepository repository, String className) {
        this.repository = repository;
        addClassName("adj-rest-log-tab");

        initFilterField();

        grid = new Grid<>(SplNavigationRecord.class, false);
        grid.addClassName(className != null ? className : "log-grid");
        grid.addThemeVariants(GridVariant.LUMO_WRAP_CELL_CONTENT);

        grid.addColumn(SplNavigationRecord::getId)
                .setHeader("id")
                .setWidth("75px")
                .setResizable(true)
                .setFlexGrow(0);
        grid.addColumn(SplNavigationRecord::getOriginalSentence)
                .setResizable(true)
                .setHeader("Речення")
                .setFlexGrow(2);
        grid.addColumn(item -> item.getGenerationDate() != null ? item.getGenerationDate().format(DateTimeFormatter.ofPattern("dd/MM/yyyy\t HH:mm:ss")) : "")
                .setResizable(true)
                .setHeader("Час створення");
        grid.addColumn(SplNavigationRecord::getDirection)
                .setResizable(true)
                .setHeader("")
                .setFlexGrow(2);
        grid.setItems(query -> repository.findAll(
                        PageRequest.of(query.getPage(), query.getPageSize(), Sort.by("generationDate").descending())
                )
                .stream()
                /*.map(p -> (SplNavigationRecord) p)
                .sorted(Comparator.comparing(SplNavigationRecord::getGenerationDate).reversed())*/);
        grid.addItemDoubleClickListener(event -> {
            Dialog itemInfoDialog= LogExtendedInfoDialogBuilder.getDialog(event.getItem());
            itemInfoDialog.open();
        });

        setSizeFull();
        add(grid);
    }

    private void initFilterField() {
        HorizontalLayout horizontalLayout = new HorizontalLayout();
        addClassName("grid-filter-layout");

        filterTextField = new TextField();
        filterTextField.setPlaceholder("Пошук");
        filterTextField.setPrefixComponent(new Icon("lumo", "search"));

        filterTextField.setValueChangeMode(ValueChangeMode.LAZY);
        filterTextField.addValueChangeListener(e -> listRecordFilterByOriginalSentence(e.getValue()));

        Button refresh = new Button(VaadinIcon.REFRESH.create(),
                ev -> grid.getDataProvider().refreshAll());

        horizontalLayout.add(filterTextField, refresh);
        add(horizontalLayout);
    }

    private void listRecordFilterByOriginalSentence(String filterString) {
        String likeFilter = "%" + filterString + "%";
        grid.setItems(query ->
                repository.findByOriginalSentenceLikeIgnoreCase(
                                likeFilter,
                                PageRequest.of(query.getPage(), query.getPageSize(), Sort.by("generationDate").descending()))
                        .stream()
                        /*.map(p -> (SplNavigationRecord) p)*/);
    }
}
