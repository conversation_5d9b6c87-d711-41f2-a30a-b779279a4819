package com.deb.spl.control.views.adjacentSystems.nppa;

import com.deb.spl.control.service.Broadcaster;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.views.events.RocketEvent;
import com.vaadin.flow.component.AttachEvent;
import com.vaadin.flow.component.DetachEvent;
import com.vaadin.flow.component.Html;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.dialog.Dialog;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.grid.GridVariant;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextArea;
import com.vaadin.flow.component.textfield.TextField;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class TlKeysTab extends VerticalLayout implements Broadcaster.BroadcastListener {
    private final AskuService askuService;

    private List<RocketEntry> keys;
    private Grid<RocketEntry> grid;

    public TlKeysTab(AskuService askuService) {
        this.askuService = askuService;
        keys = new ArrayList<>();
        keys = listTlKeys();

        this.addClassName("tl-keys-plc-main-layout");
        grid = new Grid<>(RocketEntry.class, false);
        grid.addColumn(entry -> {
            if (entry == null || entry.plantMissile == null) {
                return "невідомо";
            }
            try {
                return entry.plantMissile;
            } catch (Exception e) {
                return "невідомо";
            }
        }).setHeader(new Html("<b>№</b>")).setWidth("10%");

        grid.addColumn(entry -> {
            if (entry == null || entry.tlKey == null) {
                return "невідомо";
            }
            try {
                return entry.tlKey;
            } catch (Exception e) {
                return "невідомо";
            }
        }).setHeader(new Html("<b>Ключ</b>")).setWidth("90%");
        grid.setItems(keys);
        grid.addThemeVariants(GridVariant.MATERIAL_COLUMN_DIVIDERS, GridVariant.LUMO_COLUMN_BORDERS,
                GridVariant.LUMO_ROW_STRIPES);
        grid.addClassName("tl-keys-plc-grid");

        grid.addItemDoubleClickListener(item -> {
            Dialog dialog = new Dialog();
            dialog.addClassName("dialog-tl-plc-keys");
            dialog.setHeaderTitle("Перегаляд TL ключів");
            VerticalLayout bodyLayout = new VerticalLayout();

            TextField plantMissileTf = new TextField("Номер ракети");
            plantMissileTf.setValue(item.getItem().plantMissile);
            plantMissileTf.setEnabled(false);
            plantMissileTf.setClassName("plant-missile-tf");
            bodyLayout.add(plantMissileTf);

            TextArea keyValueTf = new TextArea("Ключ");
            keyValueTf.setClassName("tl-key-tf");
            keyValueTf.setValue(item.getItem().tlKey());
            bodyLayout.add(keyValueTf);
            dialog.add(bodyLayout);
            Button confirmBt = new Button("Закрити", (e) -> {
                dialog.close();
            });
            confirmBt.addClassName("confirm-button-dialog-tl-plc-keys");

            dialog.getFooter().add(confirmBt);

            dialog.open();
        });

        VerticalLayout vertical = new VerticalLayout();
        vertical.addClassName("tl-keys-plc-vertical-layout");
        vertical.add(grid);
        this.add(vertical);

        HorizontalLayout buttonsLayout = new HorizontalLayout();
        buttonsLayout.addClassName("tl-keys-plc-buttons");
    }

    private List<RocketEntry> listTlKeys() {
        List<RocketEntry> tlKeys = new ArrayList<>();
        if (askuService.getAskuEntity() == null) {
            return tlKeys;
        }

        if (askuService.getAskuEntity().isLeftRocketKnown()) {
            for (String key : askuService.getAskuEntity().getLeftRocket().getStoredTlKeys()) {
                tlKeys.add(new RocketEntry(
                        askuService.getAskuEntity().getLeftRocket().getPlantMissile().orElse("ліва ракета"),
                        key));
            }
        }

        if (askuService.getAskuEntity().isRightRocketKnown()) {
            for (String key : askuService.getAskuEntity().getRightRocket().getStoredTlKeys()) {
                tlKeys.add(new RocketEntry(
                        askuService.getAskuEntity().getRightRocket().getPlantMissile().orElse("права ракета"),
                        key));
            }
        }

        return tlKeys;
    }

    @Override
    public void receiveBroadcast(Object incomingEvent) {
        if (!(incomingEvent instanceof RocketEvent)) {
            return;
        }
        keys = listTlKeys();
        if (getUI().isPresent()) {
            getUI().get().access(() -> grid.setItems(keys));
        }
    }

    @Override
    protected void onAttach(AttachEvent attachEvent) {
        super.onAttach(attachEvent);
        Broadcaster.register(this, RocketEvent.class);
    }

    @Override
    protected void onDetach(DetachEvent detachEvent) {
        super.onDetach(detachEvent);
        Broadcaster.unregister(this);
    }

    private record RocketEntry(String plantMissile, String tlKey) {
    }
}
