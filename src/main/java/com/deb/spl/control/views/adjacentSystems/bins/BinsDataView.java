package com.deb.spl.control.views.adjacentSystems.bins;


import com.deb.spl.control.service.BinsService;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.Bins;
import com.deb.spl.control.data.BinsDto;
import com.deb.spl.control.service.Broadcaster;
import com.deb.spl.control.views.adjacentSystems.utils.ConfigurationUtils;
import com.deb.spl.control.views.events.AdjacentSystemUpdateEventType;
import com.deb.spl.control.views.events.BinsDtoEvent;
import com.deb.spl.control.views.events.BinsEvent;
import com.vaadin.flow.component.AttachEvent;
import com.vaadin.flow.component.DetachEvent;
import com.vaadin.flow.component.html.Span;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.data.binder.Binder;
import com.vaadin.flow.data.binder.Result;
import com.vaadin.flow.data.binder.ValueContext;
import com.vaadin.flow.data.converter.Converter;
import com.vaadin.flow.data.converter.StringToBooleanConverter;
import com.vaadin.flow.data.converter.StringToDoubleConverter;
import lombok.extern.slf4j.Slf4j;
import net.sf.marineapi.nmea.util.BinsCalibrationStatus;
import net.sf.marineapi.nmea.util.BinsGpsStatus;
import net.sf.marineapi.nmea.util.BinsNavigationTaskStatus;

import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;
import java.util.WeakHashMap;

@Slf4j
public class BinsDataView extends VerticalLayout implements Broadcaster.BroadcastListener {
    public static final String BINS_ERROR_MSG = "Навігаційні дані не достовірні або виникла помилка в роботі УКННС. " +
                                                "Зачекайте 12 хв. та оновіть сторінку.";

    public static final String BINS_NO_CONNECTION_MSG = "Немає зв'язку з УКННС. " +
                                                "Зачекайте 10 хв. та оновіть сторінку.";


    private final BinsService binsService;

    private Binder<Bins> binsBinder;
    private Binder<BinsDto> dtoBinder;
    private Binder<Boolean> connectionStatusBinder;

    TextField utcTime;
    TextField latitude;
    TextField latHemisphere;
    TextField longitude;
    TextField lonHemisphere;

    TextField altitude;
    TextField speed;
    TextField course;
    TextField correctedCourse;
    TextField utcDate;

    TextField latitudePrecision;
    TextField longitudePrecision;
    TextField altitudePrecision;
    TextField navigationTaskStatus;
    TextField connectionStatus;
    TextField binsGpsStatus;

    TextField headingAzimuth;
    TextField tiltToStarboard;
    TextField trimHeadIsBottom;
    TextField binsCalibrationStatus;

    TextField ggaText;
    TextField rmcText;
    TextField hprText;
    TextField pmpText;
    private static boolean refreshingPause;

    @Override
    protected void onAttach(AttachEvent attachEvent) {
        super.onAttach(attachEvent);
        Broadcaster.register(this, BinsEvent.class);
        Broadcaster.register(this, BinsDtoEvent.class);
    }

    @Override
    protected void onDetach(DetachEvent detachEvent) {
        super.onDetach(detachEvent);
        Broadcaster.unregister(this);
    }

    public BinsDataView(BinsService binsService) {
        this.binsService = binsService;
        binsBinder = new Binder<>(Bins.class);
        dtoBinder = new Binder<>(BinsDto.class);
        connectionStatusBinder = new Binder<>(Boolean.class);

        configure();
    }

    private void configure() {
        addClassName("bins-data-layout");
        setWidth("100%");


        latitude = new TextField("Широта", "llll.ll");
        latitude.setWidth("12em");
        binsBinder.forField(latitude)
                .bindReadOnly(bins -> new DecimalFormat("00.00000000").format(bins.getPosition().getLatitude()));

        latHemisphere = new TextField("Півкуля", "N/S");
        latHemisphere.setWidth("5em");
        binsBinder.forField(latHemisphere)
                .bindReadOnly(bins -> String.valueOf(bins.getPosition().getLatitudeHemisphere().toChar()));

        longitude = new TextField("Довгота", "yyyyy.yy");
        binsBinder.forField(longitude)
                .bindReadOnly(bins -> new DecimalFormat("00.00000000").format(bins.getPosition().getLongitude()));

        lonHemisphere = new TextField("Півкуля", "E/W");
        lonHemisphere.setWidth("5em");
        binsBinder.forField(lonHemisphere)
                .bindReadOnly(bins -> String.valueOf(bins.getPosition().getLongitudeHemisphere().toChar()));

        altitude = new TextField("Висота", "x.x");
        binsBinder.forField(altitude)
                .bindReadOnly(bins -> new DecimalFormat("###,###,##0.0").format(bins.getPosition().getAltitude()));


        speed = new TextField("Швидксіть, м", "x.x");
        speed.setWidth("8em");
        binsBinder.forField(speed)
                .withConverter(new StringToDoubleConverter("Помилка формату"))
                .bindReadOnly(Bins::getSpeed);

        course = new TextField("Курс, °", "x.x");
        course.setWidth("5em");
        binsBinder.forField(course)
                .withConverter(new StringToDoubleConverter("Помилка формату"))
                .bindReadOnly(Bins::getCourse);

        correctedCourse = new TextField("Курс відкоригований, °", "x.x");
        correctedCourse.setWidth("12em");
        correctedCourse.addClassName("corrected-course");
        binsBinder.forField(correctedCourse)
                .withConverter(new StringToDoubleConverter("Помилка формату"))
                .bindReadOnly(Bins::getCorrectedCourse);
        correctedCourse.setVisible(false);


        utcDate = new TextField("Дата UTC", "xxxxxx");
        utcDate.setWidth("12em");
        binsBinder.forField(utcDate)
                .bindReadOnly(bins -> bins.getGpsDate().toString());

        utcTime = new TextField("Час UTC", "hhmmss.ss");
        utcTime.setWidth("12em");
        binsBinder.forField(utcTime)
                .bindReadOnly(entity -> entity.getGpsTime().toString());

        add(new Span("Навігаційні дані"));

        HorizontalLayout coordTimeLayout = new HorizontalLayout();
        coordTimeLayout.addClassName("coord-time");
        coordTimeLayout.add(utcTime, utcDate, course, correctedCourse, speed);
        add(coordTimeLayout);

        HorizontalLayout coordLayout = new HorizontalLayout();
        coordLayout.addClassName("coord-part");
        coordLayout.add(latitude, latHemisphere, longitude, lonHemisphere, altitude);
        add(coordLayout);

        latitudePrecision = new TextField("Широта", "x.x");
        binsBinder.forField(latitudePrecision)
                .withConverter(new StringToDoubleConverter("Помилка формату"))
                .bindReadOnly(Bins::getLatitudePrecision);

        longitudePrecision = new TextField("Довгота", "x.x");
        binsBinder.forField(longitudePrecision)
                .withConverter(new StringToDoubleConverter("Помилка формату"))
                .bindReadOnly(Bins::getLongitudePrecision);

        altitudePrecision = new TextField("Висота");
        binsBinder.forField(altitudePrecision)
                .withConverter(new StringToDoubleConverter("Помилка формату"))
                .bindReadOnly(Bins::getAltitudePrecision);

        connectionStatus = new TextField("Зв'язок");
        connectionStatusBinder.forField(connectionStatus)
                .withConverter(new StringToBooleanConverter("Помилка формату", "Підключено", "Роз'єднано"))
                .bindReadOnly(Boolean::booleanValue);
        connectionStatus.addValueChangeListener(valueChangeEvent -> {
            if (valueChangeEvent == null) {
                connectionStatus.setClassName(connectionStatus.getClassName() + "-" + "unknown");
            }
            AdjacentSystemStatus status = AdjacentSystemStatus.fromValueUa(valueChangeEvent.getValue());

            connectionStatus.setClassName(connectionStatus.getClassName() + "-" + status.name().toLowerCase());
        });

        navigationTaskStatus = new TextField("Статус");
        binsBinder.forField(navigationTaskStatus)
                .withConverter(new Converter<String, BinsNavigationTaskStatus>() {
                    @Override
                    public Result<BinsNavigationTaskStatus> convertToModel(String s, ValueContext valueContext) {
                        return null;
                    }

                    @Override
                    public String convertToPresentation(BinsNavigationTaskStatus binsNavigationTaskStatus, ValueContext valueContext) {
                        if (binsNavigationTaskStatus == null) {
                            return "";
                        }

                        Map<BinsNavigationTaskStatus, String> comparisonMap = new WeakHashMap<>();
                        comparisonMap.put(BinsNavigationTaskStatus.NOT_RESOLVED, "Не вирішена");
                        comparisonMap.put(BinsNavigationTaskStatus.RESOLVED, "Вирішена");
                        comparisonMap.put(BinsNavigationTaskStatus.RESOLVING_IN_PROGRESS, "Вирішується");
                        comparisonMap.put(BinsNavigationTaskStatus.AUTOMATIC_CALIBRATION_IN_PROGRESS, "Калібрується");

                        return Objects.requireNonNullElse(comparisonMap.get(binsNavigationTaskStatus), "");
                    }
                })
                .bindReadOnly(bins -> bins.getNavigationTaskStatus());
        binsGpsStatus = new TextField("Статус GPS");

        Map<BinsGpsStatus, String> binsGpsStatusComparisonMap = new WeakHashMap<>();
        binsGpsStatusComparisonMap.put(BinsGpsStatus.NON_RELIABLE, "Дані не достовірні");
        binsGpsStatusComparisonMap.put(BinsGpsStatus.RELIABLE, "Дані достовірні");
        binsGpsStatusComparisonMap.put(BinsGpsStatus.PROVIDED_MANUALLY, "Отримані без GPS");
        binsBinder.forField(binsGpsStatus)
                .withConverter(new Converter<String, BinsGpsStatus>() {
                    @Override
                    public Result<BinsGpsStatus> convertToModel(String s, ValueContext valueContext) {
                        return null;
                    }

                    @Override
                    public String convertToPresentation(BinsGpsStatus binsGpsStatus, ValueContext valueContext) {
                        if (binsGpsStatus == null) {
                            return "";
                        }
                        return Objects.requireNonNullElse(binsGpsStatusComparisonMap.get(binsGpsStatus), "");
                    }
                })
                .bindReadOnly(Bins::getBinsGpsStatus);
        HorizontalLayout coordStatusLayout = new HorizontalLayout();
        coordStatusLayout.addClassName("coord-status");
        coordStatusLayout.add(connectionStatus, navigationTaskStatus, binsGpsStatus);
        add(coordStatusLayout);

        HorizontalLayout precisionLayout =
                new HorizontalLayout(latitudePrecision, longitudePrecision, altitudePrecision);
        add(new Span("Точність"));
        precisionLayout.setAlignItems(Alignment.BASELINE);
        precisionLayout.addClassName("precision-layout");
        add(precisionLayout);

        headingAzimuth = new TextField("Курс");
        binsBinder.forField(headingAzimuth)
                .withConverter(new StringToDoubleConverter("Помилка формату"))
                .bindReadOnly(Bins::getHeadingAzimuth);

        tiltToStarboard = new TextField("Крен");
        binsBinder.forField(tiltToStarboard)
                .withConverter(new StringToDoubleConverter("Помилка формату"))
                .bindReadOnly(Bins::getTiltToStarboard);

        trimHeadIsBottom = new TextField("Диферент");
        binsBinder.forField(trimHeadIsBottom)
                .withConverter(new StringToDoubleConverter("Помилка формату"))
                .bindReadOnly(Bins::getTrimHeadIsBottom);

        binsCalibrationStatus = new TextField("Статус УКННС");
        Map<BinsCalibrationStatus, String> comparisonMapBinsCalibrationStatus = new WeakHashMap<>();
        comparisonMapBinsCalibrationStatus.put(BinsCalibrationStatus.RELIABLE,
                "Дані достовірні");
        comparisonMapBinsCalibrationStatus.put(BinsCalibrationStatus.RESOLVE_IS_CALCULATED_WITHOUT_PROOF_OF_RELIABLE,
                "Дані не достовірні");
        comparisonMapBinsCalibrationStatus.put(BinsCalibrationStatus.AUTOMATIC_CALIBRATION_IN_PROGRESS,
                "Автоматичене калібрування");
        comparisonMapBinsCalibrationStatus.put(BinsCalibrationStatus.RESOLVE_NOT_CALCULATED,
                "Немає рішення");
        binsBinder.forField(binsCalibrationStatus)
                .withConverter(new Converter<String, BinsCalibrationStatus>() {
                    @Override
                    public Result<BinsCalibrationStatus> convertToModel(String s, ValueContext valueContext) {
                        return null;
                    }

                    @Override
                    public String convertToPresentation(BinsCalibrationStatus binsCalibrationStatus, ValueContext valueContext) {
                        if (binsCalibrationStatus == null) {
                            return "";
                        }
                        return Objects.requireNonNullElse(comparisonMapBinsCalibrationStatus.get(binsCalibrationStatus), "");
                    }
                })
                .bindReadOnly(Bins::getBinsCalibrationStatus);


        add(new Span("Дані вимірювання поточних кутів"));

        HorizontalLayout coordDataLayout = new HorizontalLayout();
        coordDataLayout.addClassName("coord-data");
        coordDataLayout.add(headingAzimuth, tiltToStarboard, trimHeadIsBottom, binsCalibrationStatus);
        add(coordDataLayout);

        add(new Span("NMEA речення"));
        ggaText = new TextField("GGA");
        ggaText.addClassName("gga-text");

        dtoBinder.forField(ggaText).bindReadOnly(e ->
                Objects.requireNonNullElse(e.getGga(), ""));
        add(ggaText);

        rmcText = new TextField("RMC");
        rmcText.addClassName("rmc-text");
        dtoBinder.forField(rmcText).bindReadOnly(e -> Objects.requireNonNullElse(e.getRmc(), ""));
        add(rmcText);

        hprText = new TextField("HPR");
        hprText.addClassName("hpr-text");
        dtoBinder.forField(hprText).bindReadOnly(e -> Objects.requireNonNullElse(e.getHpr(), ""));
        add(hprText);

        pmpText = new TextField("PMP");
        pmpText.addClassName("pmp-text");
        dtoBinder.forField(pmpText).bindReadOnly(e -> Objects.requireNonNullElse(e.getPmp(), ""));
        add(pmpText);

    }


    private AdjacentSystemUpdateEventType previousBinsUpdateEvent;
    private Bins previousBins;
    private LocalDateTime binsErrorShowed = LocalDateTime.MIN;
    private final long ERROR_MESSAGE_DELAY_SEC = 600L;

    @Override
    public void receiveBroadcast(Object incomingEvent) {
        if (incomingEvent instanceof BinsDtoEvent) {
            BinsDtoEvent event = (BinsDtoEvent) incomingEvent;
            if (event == null) {
                return;
            }
            if (event.getDto() != null) {
                getUI().get().access(() -> dtoBinder.readBean(event.getDto()));
            }
        }

        if (!(incomingEvent instanceof BinsEvent)) {
            log.debug("unexpected event at " + this.getClassName());
            return;
        }
        BinsEvent event = (BinsEvent) incomingEvent;
        if (event == null) {
            return;
        }

        if (previousBinsUpdateEvent == incomingEvent) {
            return;
        }
        this.previousBins = ((BinsEvent) incomingEvent).getBins();
        previousBinsUpdateEvent = event.getEventType();


        try {
            if (getUI().isEmpty()) {
                return;
            }

            getUI().get().access(() -> {
                binsBinder.readBean(event.getBins());

                if(event.getEventType() == AdjacentSystemUpdateEventType.UNDEFINED){
                    if (LocalDateTime.now().isBefore(binsErrorShowed.plusSeconds(ERROR_MESSAGE_DELAY_SEC))) {
                        return;
                    }

                    int autoCloseDelay=(int) ERROR_MESSAGE_DELAY_SEC*1000;
                    ConfigurationUtils.getErrorNotification(BINS_NO_CONNECTION_MSG, "",
                            autoCloseDelay, true).open();
                    binsErrorShowed=LocalDateTime.now();
                }

                if (event.getEventType() == AdjacentSystemUpdateEventType.ERROR_OCCURRED) {

                    if (LocalDateTime.now().isBefore(binsErrorShowed.plusSeconds(ERROR_MESSAGE_DELAY_SEC))) {
                        return;
                    }
                    binsErrorShowed = LocalDateTime.now();

                    String errorMsg = BINS_ERROR_MSG;
                    log.error(errorMsg + " " + this.toString());

                    int autoCloseDelay=(int) ERROR_MESSAGE_DELAY_SEC*1000;
                    ConfigurationUtils.getErrorNotification(errorMsg, "",
                            autoCloseDelay, true).open();
                    binsErrorShowed=LocalDateTime.now();
                }

                connectionStatusBinder.readBean(binsService.isConnected());
            });
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }
}
