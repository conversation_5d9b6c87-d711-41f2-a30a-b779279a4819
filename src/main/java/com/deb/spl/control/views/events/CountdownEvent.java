package com.deb.spl.control.views.events;

import com.deb.spl.control.data.asku.CountdownEventType;
import com.deb.spl.control.data.asku.LaunchType;
import lombok.*;

@NoArgsConstructor
@Getter
@Setter
@EqualsAndHashCode
@Builder
public class CountdownEvent implements SystemEvent{

    LaunchType launchType;
    CountdownEventType eventType;
    int timerValue;

    public CountdownEvent(LaunchType launchType, CountdownEventType eventType, int timerValue) {
        this.launchType = launchType;
        this.eventType = eventType;
        this.timerValue = timerValue;
    }

}
