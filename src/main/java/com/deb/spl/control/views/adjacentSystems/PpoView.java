package com.deb.spl.control.views.adjacentSystems;

import com.deb.spl.control.data.asku.RocketMapper;
import com.deb.spl.control.data.ppo.*;
import com.deb.spl.control.repository.ppo.PpoHistoryRepository;
import com.deb.spl.control.service.*;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.service.asku.PlcService;
import com.deb.spl.control.views.MainLayout;
import com.deb.spl.control.views.events.PpoEvent;
import com.vaadin.flow.component.AttachEvent;
import com.vaadin.flow.component.DetachEvent;
import com.vaadin.flow.component.Html;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.dialog.Dialog;
import com.vaadin.flow.component.grid.ColumnTextAlign;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.grid.GridVariant;
import com.vaadin.flow.component.html.Span;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.component.textfield.TextFieldVariant;
import com.vaadin.flow.data.binder.Binder;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import lombok.extern.slf4j.Slf4j;
import org.vaadin.tabs.PagedTabs;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Slf4j
@PageTitle("Сатус систем | ППО")
@Route(value = "adjacent-systems/ppo", layout = MainLayout.class)
public class PpoView extends AdjacentSystemView implements Broadcaster.BroadcastListener {

    private final Binder<Ppo> ppoBinder = new Binder<>(Ppo.class);
    private final PpoService ppoService;
    private final UserService userService;
    private final PpoHistoryRepository historyRepository;

    @Override
    protected void onAttach(AttachEvent attachEvent) {
        super.onAttach(attachEvent);
        Broadcaster.register(this, PpoEvent.class);
    }

    @Override
    protected void onDetach(DetachEvent detachEvent) {
        super.onDetach(detachEvent);
        Broadcaster.unregister(this);
    }

    public PpoView(MsuService msuService,
                   PpoService ppoService,
                   SutoService sutoService,
                   BinsService binsService,
                   SaeService saeService,
                   AskuService askuService,
                   UserService userService,
                   PlcService plcService,
                   PpoHistoryRepository historyRepository,
                   RocketMapper rocketMapper,
                   NppaService nppaService,
                   CcvCommunicationService ccvCommunicationService) {
        super(msuService,
                ppoService,
                sutoService,
                binsService,
                saeService,
                askuService,
                plcService,
                "Система Протипожежного обладнання",
                rocketMapper,
                nppaService,
                ccvCommunicationService);
        this.ppoService = ppoService;
        this.userService = userService;
        this.historyRepository = historyRepository;

        PpoCommandsTab ppoCommandsTab = new PpoCommandsTab();
        VerticalLayout commandTabPpo = new VerticalLayout();

        commandTabPpo.setWidthFull();
        commandTabPpo.addClassName("command-tab-ppo");
        commandTabPpo.setWidth("100%");
        commandTabPpo.setMaxWidth("100%");
        commandTabPpo.add(ppoCommandsTab);//commands class

        ppoCommandsTab.addClassName("ppo-commands-tab");

        VerticalLayout logTab = new LogTab(historyRepository, "ppo-log-tab");

        VerticalLayout tabContainer = new VerticalLayout();
        PagedTabs tabs = new PagedTabs(tabContainer);
        tabs.getContent();

        String commandTabHeader = "Команди";
        String logTabHeader = "Журнал";

        HorizontalLayout ppoStatus = new HorizontalLayout();
        add(ppoStatus);

        HorizontalLayout ppoStatusTabFirstColumn = configurePpoStatusTabFirstColumn();
        HorizontalLayout ppoStatusTabSecondColumn = configurePpoStatusTabSecondColumn();
        HorizontalLayout ppoStatusTabThirdColumn = configurePpoStatusTabThirdColumn();
        HorizontalLayout ppoStatusTabFourthColumn = configurePpoStatusTabFourthColumn();

        HorizontalLayout ppoStatusTabFifthColumn = configurePpoStatusTabFifthColumn();
        HorizontalLayout ppoStatusTabSixthColumn = configurePpoStatusTabSixthColumn();
        HorizontalLayout ppoStatusTabSeventhColumn = configurePpoStatusTabSeventhColumn();
        HorizontalLayout ppoStatusTabEighthColumn = configurePpoStatusTabEighthColumn();

        VerticalLayout ppoStatusTabFirst = new VerticalLayout();
        add(ppoStatusTabFirst);
        VerticalLayout ppoStatusTabSecond = new VerticalLayout();
        add(ppoStatusTabSecond);
        VerticalLayout ppoStatusTabThird = new VerticalLayout();
        add(ppoStatusTabThird);
        VerticalLayout ppoStatusTabFourth = new VerticalLayout();
        add(ppoStatusTabFourth);

        ppoStatusTabFirst.setWidth("396px");
        ppoStatusTabFirst.setMaxWidth("396px");
        ppoStatusTabFirst.addClassName("ppo-status-tab-first");
        ppoStatusTabSecond.setWidth("396px");
        ppoStatusTabSecond.setMaxWidth("396px");
        ppoStatusTabSecond.addClassName("ppo-status-tab-second");
        ppoStatusTabThird.setWidth("396px");
        ppoStatusTabThird.setMaxWidth("396px");
        ppoStatusTabThird.addClassName("ppo-status-tab-third");
        ppoStatusTabFourth.setWidth("396px");
        ppoStatusTabFourth.setMaxWidth("396px");
        ppoStatusTabFourth.addClassName("ppo-status-tab-fourth");
        ppoStatusTabFirstColumn.addClassName("ppo-status-tab-first-column");
        ppoStatusTabSecondColumn.addClassName("ppo-status-tab-second-column");
        ppoStatusTabThirdColumn.addClassName("ppo-status-tab-third-column");
        ppoStatusTabFourthColumn.addClassName("ppo-status-tab-fourth-column");
        ppoStatusTabFifthColumn.addClassName("ppo-status-tab-fifth-column");
        ppoStatusTabSixthColumn.addClassName("ppo-status-tab-sixth-column");
        ppoStatusTabSeventhColumn.addClassName("ppo-status-tab-seventh-column");
        ppoStatusTabEighthColumn.addClassName("ppo-status-tab-eighth-column");

        ppoStatusTabFirst.add(ppoStatusTabFirstColumn, ppoStatusTabSecondColumn);
        ppoStatusTabSecond.add(ppoStatusTabThirdColumn, ppoStatusTabFourthColumn);
        ppoStatusTabThird.add(ppoStatusTabFifthColumn, ppoStatusTabSixthColumn);
        ppoStatusTabFourth.add(ppoStatusTabSeventhColumn, ppoStatusTabEighthColumn);

        ppoStatus.addClassName("ppo-status");
        ppoStatus.setHeight("360px");
        ppoStatus.add(ppoStatusTabFirst, ppoStatusTabSecond, ppoStatusTabThird, ppoStatusTabFourth);
        commandTabPpo.add(ppoStatus);

        //commands class
        tabs.add(commandTabHeader, commandTabPpo, false);
        tabs.add(logTabHeader, logTab, false);

        getSystemLayout().add(tabs, tabContainer);
        getSystemLayout().addClassName("system-layout-ppo");
        getSystemLayout().setWidth("69.5%");
        getSystemLayout().setMaxWidth("69.5%");
    }

    private HorizontalLayout configurePpoStatusTabFirstColumn() {
        HorizontalLayout layout = new HorizontalLayout();
        Span statusCellASKU = new Span("Статус відсіку АСКУ");
        statusCellASKU.addClassName("status-cell-ASKU");

        TextField statusOfCellASKU = new TextField();
        statusOfCellASKU.setReadOnly(true);
        statusOfCellASKU.addClassName("status-of-cell-ASKU");
        statusOfCellASKU.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureBayStatusAnimation(statusOfCellASKU, PpoBayType.ASKU);

        layout.add(statusCellASKU, statusOfCellASKU);
        return layout;
    }

    private PpoBayStatus updateBayStateView(@NotNull PpoBay bay) {
        if (bay.isFirePresent()) {
            return PpoBayStatus.ERROR;
        }

        return bay.isMalfunctionPresent() ? PpoBayStatus.WARNING : PpoBayStatus.OK;
    }

    private void configureBayStatusAnimation(@NotNull TextField bayTf,
                                             @NotNull PpoBayType bayType) {
        bayTf.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, PpoBayStatus.class));
        ppoBinder.forField(bayTf)
                .bindReadOnly(ppo -> {
                    PpoBay bay = ppo.getBays().get(bayType);

                    return bay == null ? PpoBayStatus.UNDEFINED.getValueUa()
                            : updateBayStateView(bay).getValueUa();
                });
    }

    private void configureUnitAnimation(@NotNull TextField unitTf,
                                        @NotNull PpoBayType bayType,
                                        @NotBlank String ppoUnitAlias,
                                        @NotNull Class aClass,
                                        @NotNull Binder<Ppo> binder) {
        unitTf.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, aClass));
        binder.forField(unitTf)
                .bindReadOnly(ppo -> {
                    if (ppo == null) {
                        return PpoUnitStatus.UNDEFINED.getValueUa();
                    }
                    PpoBay bay = ppo.getBays().get(bayType);
                    if (bay == null) {
                        return PpoUnitStatus.UNDEFINED.getValueUa();
                    }
                    Optional<PpoUnit> unit = bay.getUnitByAlias(ppoUnitAlias);
                    if (unit.isEmpty()) {
                        log.error("Cant find ppo unit with alias " + ppoUnitAlias + "in bay " + bay);
                    }
                    return unit.isEmpty() ? PpoUnitStatus.UNDEFINED.getValueUa() : unit.get().getUnitStatus().getValueUa();
                });
    }

    private HorizontalLayout configurePpoStatusTabSecondColumn() {
        HorizontalLayout layout = new HorizontalLayout();

        TextField balloonASKU1 = new TextField("Балон №1");
        balloonASKU1.setReadOnly(true);
        balloonASKU1.addClassName("balloon-ASKU-1");
        balloonASKU1.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureUnitAnimation(balloonASKU1,
                PpoBayType.ASKU,
                "BALLOON_ASKU_1",
                PpoUnitStatus.class,
                ppoBinder);

        TextField balloonASKU2 = new TextField("Балон №2");
        balloonASKU2.setReadOnly(true);
        balloonASKU2.addClassName("balloon-ASKU-2");
        balloonASKU2.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureUnitAnimation(
                balloonASKU2,
                PpoBayType.ASKU,
                "BALLOON_ASKU_2",
                PpoUnitStatus.class,
                ppoBinder);

        TextField opticalSensorASKU1 = new TextField("Оптичний датчик №1");
        opticalSensorASKU1.setReadOnly(true);
        opticalSensorASKU1.addClassName("optical-sensor-ASKU-1");
        opticalSensorASKU1.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureUnitAnimation(
                opticalSensorASKU1,
                PpoBayType.ASKU,
                "OPTICAL_SENSOR_ASKU_1",
                PpoUnitStatus.class,
                ppoBinder);

        TextField opticalSensorASKU2 = new TextField("Оптичний датчик №2");
        opticalSensorASKU2.setReadOnly(true);
        opticalSensorASKU2.addClassName("optical-sensor-ASKU-2");
        opticalSensorASKU2.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureUnitAnimation(
                opticalSensorASKU2,
                PpoBayType.ASKU,
                "OPTICAL_SENSOR_ASKU_2",
                PpoUnitStatus.class,
                ppoBinder);

        TextField opticalSensorASKU3 = new TextField("Оптичний датчик №3");
        opticalSensorASKU3.setReadOnly(true);
        opticalSensorASKU3.addClassName("optical-sensor-ASKU-3");
        opticalSensorASKU3.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureUnitAnimation(
                opticalSensorASKU3,
                PpoBayType.ASKU,
                "OPTICAL_SENSOR_ASKU_3",
                PpoUnitStatus.class,
                ppoBinder);

        TextField opticalSensorASKU4 = new TextField("Оптичний датчик №4");
        opticalSensorASKU4.setReadOnly(true);
        opticalSensorASKU4.addClassName("optical-sensor-ASKU-4");
        opticalSensorASKU4.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureUnitAnimation(
                opticalSensorASKU4,
                PpoBayType.ASKU,
                "OPTICAL_SENSOR_ASKU_4",
                PpoUnitStatus.class,
                ppoBinder);

        layout.add(balloonASKU1, balloonASKU2, opticalSensorASKU1, opticalSensorASKU2, opticalSensorASKU3, opticalSensorASKU4);
        return layout;
    }

    private HorizontalLayout configurePpoStatusTabThirdColumn() {

        Span statusCellNPPA = new Span("Статус відсіку НППА");
        statusCellNPPA.addClassName("status-cell-NPPA");

        TextField statusOfCellNPPA = new TextField();
        statusOfCellNPPA.setReadOnly(true);
        statusOfCellNPPA.addClassName("status-of-cell-NPPA");
        statusOfCellNPPA.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureBayStatusAnimation(statusOfCellNPPA, PpoBayType.NPPA);

        return new HorizontalLayout(statusCellNPPA, statusOfCellNPPA);
    }

    private HorizontalLayout configurePpoStatusTabFourthColumn() {
        TextField balloonNPPA1 = new TextField("Балон №1");
        balloonNPPA1.setReadOnly(true);
        balloonNPPA1.addClassName("balloon-NPPA-1");
        balloonNPPA1.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureUnitAnimation(
                balloonNPPA1,
                PpoBayType.NPPA,
                "BALLOON_NPPA_1",
                PpoUnitStatus.class,
                ppoBinder);

        TextField balloonNPPA2 = new TextField("Балон №2");
        balloonNPPA2.setReadOnly(true);
        balloonNPPA2.addClassName("balloon-NPPA-2");
        balloonNPPA2.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureUnitAnimation(
                balloonNPPA2,
                PpoBayType.NPPA,
                "BALLOON_NPPA_2",
                PpoUnitStatus.class,
                ppoBinder);

        TextField opticalSensorNPPA1 = new TextField("Оптичний датчик №1");
        opticalSensorNPPA1.setReadOnly(true);
        opticalSensorNPPA1.addClassName("optical-sensor-NPPA-1");
        opticalSensorNPPA1.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureUnitAnimation(
                opticalSensorNPPA1,
                PpoBayType.NPPA,
                "OPTICAL_SENSOR_NPPA_1",
                PpoUnitStatus.class,
                ppoBinder);

        TextField opticalSensorNPPA2 = new TextField("Оптичний датчик №2");
        opticalSensorNPPA2.setReadOnly(true);
        opticalSensorNPPA2.addClassName("optical-sensor-NPPA-2");
        opticalSensorNPPA2.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureUnitAnimation(
                opticalSensorNPPA2,
                PpoBayType.NPPA,
                "OPTICAL_SENSOR_NPPA_2",
                PpoUnitStatus.class,
                ppoBinder);

        TextField opticalSensorNPPA3 = new TextField("Оптичний датчик №3");
        opticalSensorNPPA3.setReadOnly(true);
        opticalSensorNPPA3.addClassName("optical-sensor-NPPA-3");
        opticalSensorNPPA3.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureUnitAnimation(
                opticalSensorNPPA3,
                PpoBayType.NPPA,
                "OPTICAL_SENSOR_NPPA_3",
                PpoUnitStatus.class,
                ppoBinder);

        return new HorizontalLayout(balloonNPPA1, balloonNPPA2, opticalSensorNPPA1, opticalSensorNPPA2,
                opticalSensorNPPA3);
    }

    private HorizontalLayout configurePpoStatusTabFifthColumn() {
        Span statusCellDEA = new Span("Статус відсіку ДЕА");
        statusCellDEA.addClassName("status-cell-DEA");

        TextField statusOfCellDEA = new TextField();
        statusOfCellDEA.setReadOnly(true);
        statusOfCellDEA.addClassName("status-of-cell-DEA");
        statusOfCellDEA.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureBayStatusAnimation(statusOfCellDEA, PpoBayType.DEA);

        return new HorizontalLayout(statusCellDEA, statusOfCellDEA);
    }


    private HorizontalLayout configurePpoStatusTabSixthColumn() {
        TextField balloonDEA1 = new TextField("Балон №1");
        balloonDEA1.setReadOnly(true);
        balloonDEA1.addClassName("balloon-DEA-1");
        balloonDEA1.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        balloonDEA1.addValueChangeListener(valueChangeEvent -> setTextAnimation(valueChangeEvent, PpoBayType.class));
        configureUnitAnimation(
                balloonDEA1,
                PpoBayType.DEA,
                "BALLOON_DEA_1",
                PpoUnitStatus.class,
                ppoBinder);

        TextField balloonDEA2 = new TextField("Балон №2");
        balloonDEA2.setReadOnly(true);
        balloonDEA2.addClassName("balloon-DEA-2");
        balloonDEA2.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureUnitAnimation(
                balloonDEA2,
                PpoBayType.DEA,
                "BALLOON_DEA_2",
                PpoUnitStatus.class,
                ppoBinder);

        TextField thermalSensorDEA1 = new TextField("Термодатчик №1");
        thermalSensorDEA1.setReadOnly(true);
        thermalSensorDEA1.addClassName("thermal-sensor-DEA-1");
        thermalSensorDEA1.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureUnitAnimation(
                thermalSensorDEA1,
                PpoBayType.DEA,
                "THERMAL_SENSOR_DEA_1",
                PpoUnitStatus.class,
                ppoBinder);

        TextField thermalSensorDEA2 = new TextField("Термодатчик №2");
        thermalSensorDEA2.setReadOnly(true);
        thermalSensorDEA2.addClassName("thermal-sensor-DEA-2");
        thermalSensorDEA2.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureUnitAnimation(
                thermalSensorDEA2,
                PpoBayType.DEA,
                "THERMAL_SENSOR_DEA_2",
                PpoUnitStatus.class,
                ppoBinder);

        TextField thermalSensorDEA3 = new TextField("Термодатчик №3");
        thermalSensorDEA3.setReadOnly(true);
        thermalSensorDEA3.addClassName("thermal-sensor-DEA-3");
        thermalSensorDEA3.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureUnitAnimation(
                thermalSensorDEA3,
                PpoBayType.DEA,
                "THERMAL_SENSOR_DEA_3",
                PpoUnitStatus.class,
                ppoBinder);

        return new HorizontalLayout(balloonDEA1, balloonDEA2, thermalSensorDEA1, thermalSensorDEA2, thermalSensorDEA3);
    }


    private HorizontalLayout configurePpoStatusTabSeventhColumn() {
        Span statusCellTO = new Span("Статус відсіку ТО");
        statusCellTO.addClassName("status-cell-TO");

        TextField statusOfCellTO = new TextField();
        statusOfCellTO.setReadOnly(true);
        statusOfCellTO.addClassName("status-of-cell-TO");
        statusOfCellTO.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureBayStatusAnimation(statusOfCellTO, PpoBayType.TO);

        return new HorizontalLayout(statusCellTO, statusOfCellTO);
    }

    private HorizontalLayout configurePpoStatusTabEighthColumn() {
        TextField balloonTO1 = new TextField("Балон №1");
        balloonTO1.setReadOnly(true);
        balloonTO1.addClassName("balloon-TO-1");
        balloonTO1.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureUnitAnimation(
                balloonTO1,
                PpoBayType.TO,
                "BALLOON_TO_1",
                PpoUnitStatus.class,
                ppoBinder);

        TextField balloonTO2 = new TextField("Балон №2");
        balloonTO2.setReadOnly(true);
        balloonTO2.addClassName("balloon-TO-2");
        balloonTO2.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureUnitAnimation(
                balloonTO2,
                PpoBayType.TO,
                "BALLOON_TO_2",
                PpoUnitStatus.class,
                ppoBinder);

        TextField thermalSensorTO1 = new TextField("Термодатчик №1");
        thermalSensorTO1.setReadOnly(true);
        thermalSensorTO1.addClassName("thermal-sensor-TO-1");
        thermalSensorTO1.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureUnitAnimation(
                thermalSensorTO1,
                PpoBayType.TO,
                "THERMAL_SENSOR_TO_1",
                PpoUnitStatus.class,
                ppoBinder);

        TextField thermalSensorTO2 = new TextField("Термодатчик №2");
        thermalSensorTO2.setReadOnly(true);
        thermalSensorTO2.addClassName("thermal-sensor-TO-2");
        thermalSensorTO2.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureUnitAnimation(
                thermalSensorTO2,
                PpoBayType.TO,
                "THERMAL_SENSOR_TO_2",
                PpoUnitStatus.class,
                ppoBinder);

        TextField thermalSensorTO3 = new TextField("Термодатчик №3");
        thermalSensorTO3.setReadOnly(true);
        thermalSensorTO3.addClassName("thermal-sensor-TO-3");
        thermalSensorTO3.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        configureUnitAnimation(
                thermalSensorTO3,
                PpoBayType.TO,
                "THERMAL_SENSOR_TO_3",
                PpoUnitStatus.class,
                ppoBinder);

        return new HorizontalLayout(balloonTO1, balloonTO2, thermalSensorTO1, thermalSensorTO2, thermalSensorTO3);
    }


    private class PpoCommandsTab extends VerticalLayout {
        private PpoCommandsTab() {
            VerticalLayout tabPanel = new VerticalLayout();
            tabPanel.setWidth("100%");
            tabPanel.setMaxWidth("100%");
            tabPanel.addClassName("tab-panel-ppo");

            //commands
            HorizontalLayout commandsLayoutH = new HorizontalLayout();
            commandsLayoutH.addClassName("commands-layout-h");
            commandsLayoutH.setWidth("100%");
            commandsLayoutH.setMinWidth("100%");
            commandsLayoutH.setWidthFull();
            VerticalLayout commandsLayout = new VerticalLayout();
            commandsLayout.addClassName("commands-layout-v");
            commandsLayout.setWidth("100%");
            commandsLayout.setMinWidth("100%");
            commandsLayoutH.add(commandsLayout);
            tabPanel.add(commandsLayoutH);

            setupPpoCommandsGrid(commandsLayout);

            add(tabPanel);
        }

        private void setupPpoCommandsGrid(VerticalLayout tabPanel) {
            List<PpoCommand> commandsList = ppoService.getAvailablePpoCommands();

            Grid<PpoCommand> commandsGrid = new Grid<>(PpoCommand.class, false);
            commandsGrid.addThemeVariants(GridVariant.LUMO_WRAP_CELL_CONTENT);

            Grid.Column<PpoCommand> caption = commandsGrid
                    .addColumn(PpoCommand::getCaption).setHeader(new Html("<b>Команда</b>")).setTextAlign(ColumnTextAlign.START)
                    .setFlexGrow(0).setWidth("80%");

            Grid.Column<PpoCommand> sentence = commandsGrid
                    .addColumn(PpoCommand::getCommand).setHeader("Відправити").setTextAlign(ColumnTextAlign.END);

            sentence.setVisible(false);

            Grid.Column<PpoCommand> sendColumn = commandsGrid.addComponentColumn(ppoCommand -> {

                Button sendBt = new Button("Відправити");
                sendBt.addClassName("send-sentence-button");
                sendBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY, ButtonVariant.LUMO_SMALL);
                sendBt.getStyle().set("background-color", "#4F4F4F");

                sendBt.addClickListener(event -> {
                    Dialog dialog = new Dialog();
                    dialog.addClassName("dialog-ppo");

                    dialog.setHeaderTitle(ppoCommand.getCaption() + "?");
                    dialog.add("Ви впевнені, що бажаєте " + ppoCommand.getCaption() + "?");

                    Button confirmBt = new Button(sentence.getHeaderText(), (e) -> {
                        PpoCommand generatedCommand = PpoCommand.builder()
                                .command(ppoCommand.getCommand())
                                .caption(ppoCommand.getCaption())
                                .commandState(ppoCommand.getCommandState())
                                .adjacentSystem(ppoCommand.getAdjacentSystem())
                                .generationTime(LocalDateTime.now())
                                .originator(userService.getUserName())
                                .build();

                        ppoService.setPpoCommand(generatedCommand);
                        dialog.close();
                    });

                    confirmBt.addClassName("confirm-button-msu-dialog");

                    confirmBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY,
                            ButtonVariant.LUMO_ERROR);
                    confirmBt.getStyle().set("margin-right", "auto");
                    dialog.getFooter().add(confirmBt);

                    Button cancelButton = new Button("Відміна", (e) -> dialog.close());
                    cancelButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
                    cancelButton.addClassName("cancel-button-ppo-dialog");

                    dialog.getFooter().add(cancelButton);

                    dialog.open();
                });

                return sendBt;
            }).setHeader(new Html("<b>Відправити</b>"));

            commandsGrid.addClassName("commands-grid-ppo");

            commandsGrid.setItems(commandsList);
            commandsGrid.addThemeVariants(GridVariant.MATERIAL_COLUMN_DIVIDERS, GridVariant.LUMO_COLUMN_BORDERS,
                    GridVariant.LUMO_ROW_STRIPES);
            tabPanel.add(commandsGrid);
            tabPanel.setPadding(false);
        }
    }

    private PpoEvent previousEvent;

    @Override
    public void receiveBroadcast(Object incomingEvent) {
        if (!(incomingEvent instanceof PpoEvent eventUnderProcessing)) {
            log.debug("unexpected event at " + this.getClassName());
            return;
        }

        if (previousEvent != null && previousEvent.equals(incomingEvent)) {
            return;
        }

        if (eventUnderProcessing.getPpo() == null) {
            log.error("received event without PPO entity " + eventUnderProcessing + " " + this);
            return;
        }

        getUI().get().access(() -> ppoBinder.readBean(eventUnderProcessing.getPpo()));
        previousEvent = eventUnderProcessing;
    }
}



