package com.deb.spl.control.views.events;

import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.asku.MsgType;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@EqualsAndHashCode
@Builder
@Getter
public class FileCreatedEvent {
    final MsgType msgType = MsgType.INFO;
    @NotNull
    AdjacentSystemType adjacentSystemType;
    @NotBlank
    String payload;
    private List<FileInfo> filesInfo;
    Object source;

    public MsgType getMsgType() {
        return msgType;
    }


    public Object getSource() {
        return source;
    }

    public void setSource(Object source) {
        this.source = source;
    }


    public FileCreatedEvent() {
        this.filesInfo = new ArrayList<>();
    }

    public FileCreatedEvent(AdjacentSystemType adjacentSystemType, String payload, List<FileInfo> filesInfo, Object source) {
        this.adjacentSystemType = adjacentSystemType;
        this.payload = payload;
        this.filesInfo = filesInfo;
        this.source = source;
    }

    public void add(@NotBlank
                    String pathToFile,
                    String fileName,
                    LocalDateTime createdAt,
                    float size) {
        this.filesInfo.add(new FileInfo(pathToFile, fileName, createdAt, size));
    }

    public boolean isPresent(String fileName) {
        return filesInfo.stream().anyMatch(e -> e.fileName.equals(fileName));
    }

    public String getPathToFile(String fileName) {
        Optional<FileInfo> info = filesInfo.stream().filter(e -> e.fileName.equals(fileName)).findFirst();
        return info.isPresent() ?
                info.get().pathToFile() :
                "";
    }

    public Optional<LocalDateTime> getCreatedAt(String fileName) {
        Optional<FileInfo> info = filesInfo.stream().filter(e -> e.fileName.equals(fileName)).findFirst();

        return info.map(fileInfo -> fileInfo.createdAt);
    }

    public float getFileSize(String fileName) {
        Optional<FileInfo> info = filesInfo.stream().filter(e -> e.fileName.equals(fileName)).findFirst();

        return info.map(fileInfo -> fileInfo.size).orElse(-1F);
    }

    public int getSize() {
        return filesInfo.size();
    }

    @Builder
    public record FileInfo(@NotBlank
                           String pathToFile,
                           String fileName,
                           LocalDateTime createdAt,
                           float size) {
    }
}
