package com.deb.spl.control.views.log;

import com.deb.spl.control.repository.LogRepository;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;
import com.deb.spl.control.data.LogBean;
import com.deb.spl.control.service.Broadcaster;
import com.deb.spl.control.service.DateTimeService;
import com.deb.spl.control.utils.LogBeanService;
import com.deb.spl.control.utils.Pdf.ExportDataType;
import com.deb.spl.control.utils.Pdf.TmpFileUtils;
import com.deb.spl.control.utils.ReportGenerator;
import com.deb.spl.control.utils.ReportSettings;
import com.deb.spl.control.utils.logs.EnumFilterUtils;
import com.deb.spl.control.views.MainLayout;
import com.deb.spl.control.views.adjacentSystems.utils.AnimationColor;
import com.deb.spl.control.views.adjacentSystems.utils.LogExtendedInfoDialogBuilder;
import com.deb.spl.control.views.adjacentSystems.utils.StatusToColorMapper;
import com.deb.spl.control.views.events.FileCreatedEvent;
import com.vaadin.flow.component.Key;
import com.vaadin.flow.component.KeyModifier;
import com.vaadin.flow.component.UI;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.checkbox.Checkbox;
import com.vaadin.flow.component.combobox.MultiSelectComboBox;
import com.vaadin.flow.component.datepicker.DatePicker;
import com.vaadin.flow.component.datetimepicker.DateTimePicker;
import com.vaadin.flow.component.dependency.CssImport;
import com.vaadin.flow.component.dialog.Dialog;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.icon.Icon;
import com.vaadin.flow.component.icon.VaadinIcon;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.data.value.ValueChangeMode;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.exception.SQLGrammarException;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.InvalidDataAccessResourceUsageException;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;
import org.springframework.util.concurrent.SettableListenableFuture;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@PageTitle("Журнал")
@Route(value = "log_main_view", layout = MainLayout.class)
@CssImport(value = "./styles/dynamic-grid-row-background-color.css", themeFor = "vaadin-grid")
public class LogMainView extends VerticalLayout {
    private final AdjacentSystemStatus DEFAULT_SYSTEM_STATUS = AdjacentSystemStatus.ANY;
    private final LogRepository logRepository;
    private final LogBeanService logService;
    private final DateTimeService dateTimeService;
    private final ReportGenerator reportGenerator;
    private final List<String> tableHeaders;
    private final List<Float> rowWiths;
    private Button exportLogButton;
    private Button cancelExportButton;

    Grid<LogBean> grid = new Grid<>(LogBean.class, false);

    private DateTimeFormatter ukrainianFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy\tE  HH:mm:ss.SSS", new Locale("uk"));

    public LogMainView(@Qualifier("OpenPdfAsyncReportGenerator") ReportGenerator reportGenerator,
                       LogRepository logRepository,
                       LogBeanService logService,
                       DateTimeService dateTimeService,
                       @Value("${report.enabled}") boolean reportIsEnabled,
                       @Value("#{'${report.rows.header.captions}'.split(',')}") List<String> headerCaptions,
                       @Value("#{'${report.rows.withs}'.split(',')}") List<Float> rowWiths) {
        this.logRepository = logRepository;
        this.logService = logService;
        this.reportGenerator = reportGenerator;
        this.dateTimeService = dateTimeService;
        this.reportIsEnabled = reportIsEnabled;
        this.rowWiths = new ArrayList<>(rowWiths);
        for (int i = 0; i < headerCaptions.size(); i++) {
            try {
                headerCaptions.set(i, new String(headerCaptions.get(i).getBytes("ISO-8859-1"), StandardCharsets.UTF_8));
            } catch (UnsupportedEncodingException e) {
                log.error(String.valueOf(e));
            }
        }
        this.tableHeaders = new ArrayList<>(headerCaptions);

        initFilterField();
        startDatePicker.setValue(LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(1));

        add(grid);
        addClassName("log-layout-all");
        grid.addClassName("grid-layout-log");
        grid.addColumn(LogBean::getId).setHeader("ID");
        grid.addColumn(e -> e.getSystem().getUa()).setHeader("Система");
        grid.addColumn(e -> (e.getUpdatedAt()).format(ukrainianFormatter)).setHeader("Оновлено").setSortable(true);
        grid.addColumn(LogBean::getStatus).setHeader("Ознака").setSortable(false);
        grid.addColumn(LogBean::getInfo).setHeader("Опис").setSortable(false);
        grid.setPageSize(100);
        grid.addItemDoubleClickListener(event -> {
            Dialog itemInfoDialog = LogExtendedInfoDialogBuilder.getDialog(event.getItem());
            itemInfoDialog.open();
        });

        grid.setItems(query -> logService.selectFiltered(PageRequest.of(query.getPage(), query.getPageSize()),
                filterTextField.getValue(),
                (systemFilterCb.getSelectedItems().size() == 0) ? List.of(AdjacentSystemType.UNDEFINED) : (systemFilterCb.isSelected(AdjacentSystemType.UNCLASSIFIED) ? List.of(AdjacentSystemType.UNCLASSIFIED) : systemFilterCb.getSelectedItems().stream().toList()),
                (statusCb.getSelectedItems().size() == 0 ? List.of(AdjacentSystemStatus.UNDEFINED) : statusCb.isSelected(AdjacentSystemStatus.ANY) ? List.of(AdjacentSystemStatus.ANY) : statusCb.getSelectedItems().stream().toList()),
                startDatePicker.getValue(),
                endDatePicker.getValue()).stream());


        grid.setClassNameGenerator(item -> {
            if (item == null || item.getStatus() == null) {
                return AnimationColor.UNDEFINED.getText();
            }
            Class aClass = AdjacentSystemStatus.class;
            if (!AdjacentSystemWithDescriptionUa.class.isAssignableFrom(aClass)) {
                return AnimationColor.UNDEFINED.getText();
            }
            return StatusToColorMapper.fromElementsStatuses(item.getStatus().value, aClass).getText();
        });
    }


    private TextField filterTextField;
    private MultiSelectComboBox<AdjacentSystemType> systemFilterCb;
    private MultiSelectComboBox<AdjacentSystemStatus> statusCb;
    private DateTimePicker startDatePicker;
    private DateTimePicker endDatePicker;
    private final boolean reportIsEnabled;

    private void initFilterField() {
        HorizontalLayout filterHz = new HorizontalLayout();
        filterHz.setClassName("grid-filter-layout");

        HorizontalLayout filterDtWithButtonsHz = new HorizontalLayout();
        filterDtWithButtonsHz.setClassName("grid-filters-buttons-layout");

        filterTextField = new TextField();
        filterTextField.setPlaceholder("Пошук");
        filterTextField.addClassName("filter-text-field");
        filterTextField.setPrefixComponent(new Icon("lumo", "search"));
        filterTextField.setValueChangeMode(ValueChangeMode.LAZY);

        systemFilterCb = new MultiSelectComboBox<>();
        systemFilterCb.addClassName("system-filter-cb");
        systemFilterCb.setItemLabelGenerator(AdjacentSystemType::getUa);
        systemFilterCb.setItems(EnumFilterUtils.getConsiderableSystemsFilter());
        systemFilterCb.setValue(AdjacentSystemType.UNCLASSIFIED);
        systemFilterCb.addValueChangeListener(event -> {
            if (event == null || event.getValue() == null || event.getValue().size() < 1) {
                return;
            }
            if (!event.getOldValue().contains(AdjacentSystemType.UNDEFINED) && event.getValue().contains(AdjacentSystemType.UNDEFINED)) {
                systemFilterCb.setValue(AdjacentSystemType.UNDEFINED);
            } else if (!event.getOldValue().contains(AdjacentSystemType.UNCLASSIFIED) && event.getValue().contains(AdjacentSystemType.UNCLASSIFIED)) {
                systemFilterCb.setValue(AdjacentSystemType.UNCLASSIFIED);
            } else if (systemFilterCb.getValue().size() > 1 && (systemFilterCb.getValue().contains(AdjacentSystemType.UNCLASSIFIED) || systemFilterCb.getValue().contains(AdjacentSystemType.UNDEFINED))) {
                systemFilterCb.setValue(systemFilterCb.getValue().stream().filter(e -> !(e.equals(AdjacentSystemType.UNCLASSIFIED) || e.equals(AdjacentSystemType.UNDEFINED))).toList());
            }
        });

        statusCb = new MultiSelectComboBox<>();
        statusCb.addClassName("status-cb");
        statusCb.setItemLabelGenerator(AdjacentSystemStatus::getValueUa);
        statusCb.setItems(EnumFilterUtils.getConsiderableSystemStatusesFilter());
        statusCb.setValue(AdjacentSystemStatus.ANY);
        statusCb.addValueChangeListener(event -> {
            if (event == null || event.getValue() == null || event.getValue().size() < 1) {
                return;
            }

            if (!event.getOldValue().contains(AdjacentSystemStatus.ANY) && event.getValue().contains(AdjacentSystemStatus.ANY)) {
                statusCb.setValue(AdjacentSystemStatus.ANY);
            } else if (event.getValue().size() > 1 && event.getValue().contains(AdjacentSystemStatus.ANY)) {
                statusCb.setValue(statusCb.getValue().stream().filter(e -> !e.equals(AdjacentSystemStatus.ANY)).toList());
            }
        });

        startDatePicker = getDateI18nUa("Початок", true);

        endDatePicker = getDateI18nUa("Кінець", false);


        Button refresh = new Button(VaadinIcon.REFRESH.create(), ev -> {
            if (getUI().isPresent()) {
                getUI().get().access(() -> grid.getDataProvider().refreshAll());
            }
        });
        refresh.setText("Оновити");
        refresh.addClickShortcut(Key.ENTER, KeyModifier.CONTROL);
        refresh.addClassName("refresh-button");


        Button clearBt = new Button(VaadinIcon.FILTER.create(), event -> {
            if (getUI().isPresent()) {
                getUI().get().access(() -> {
                    filterTextField.clear();
                    systemFilterCb.setValue(AdjacentSystemType.UNCLASSIFIED);
                    statusCb.setValue(AdjacentSystemStatus.ANY);
                    startDatePicker.clear();
                    endDatePicker.clear();
                });
            }

        });
        clearBt.setText("Зкинути фільтри");
        clearBt.addClassName("clear-button");
        clearBt.setWidth("200px");
        filterHz.add(filterTextField, systemFilterCb, statusCb);
        exportLogButton = initExportToPdf(reportGenerator.getNumberOfReportsToProcess() == 0);
        cancelExportButton = initCancelPdf(reportGenerator.getNumberOfReportsToProcess() != 0);

        if (reportIsEnabled) {
            VerticalLayout exportActionsButtonsLayout = new VerticalLayout(refresh, exportLogButton, cancelExportButton);
            exportActionsButtonsLayout.addClassName("refresh-export-button");

            filterDtWithButtonsHz.add(startDatePicker, endDatePicker, clearBt, exportActionsButtonsLayout);

        } else {
            filterDtWithButtonsHz.add(startDatePicker, endDatePicker, clearBt, refresh);
        }
        add(filterHz, filterDtWithButtonsHz);
    }

    private Button initExportToPdf(boolean enabled) {
        Button exportBt = new Button("Єкспортувати", new Icon(VaadinIcon.FILE_TEXT));
        exportBt.addThemeVariants(ButtonVariant.LUMO_SUCCESS, ButtonVariant.LUMO_SMALL);
        exportBt.setClassName("export-pdf-button");
        exportBt.addClickListener((event) -> createReportOptionsDialog(exportBt));
        exportBt.setVisible(enabled);
        return exportBt;
    }

    private Button initCancelPdf(boolean enabled) {
        Button button = new Button("Скасувати", new Icon(VaadinIcon.CLOSE_SMALL));
        button.setVisible(enabled);
        button.addThemeVariants(ButtonVariant.LUMO_SUCCESS, ButtonVariant.LUMO_SMALL);
        button.setClassName("cancel-export-pdf-button");

        return button;
    }

    ListenableFuture<String> createReport(String fileName, List<LogBean> data) {
        try {
            List<AdjacentSystemType> systemsFilter = (systemFilterCb.getSelectedItems().size() == 0) ? List.of(AdjacentSystemType.UNDEFINED) :
                    (systemFilterCb.isSelected(AdjacentSystemType.UNCLASSIFIED) ? List.of(AdjacentSystemType.UNCLASSIFIED) :
                            systemFilterCb.getSelectedItems().stream().toList());
            List<AdjacentSystemStatus> systemStatusesFilter = statusCb.getSelectedItems().size() == 0 ? List.of(AdjacentSystemStatus.UNDEFINED) :
                    statusCb.isSelected(AdjacentSystemStatus.ANY) ? List.of(AdjacentSystemStatus.ANY) : statusCb.getSelectedItems().stream().toList();
            ReportSettings reportSettings = new ReportSettings(filterTextField.getValue(),
                    systemsFilter,
                    systemStatusesFilter,
                    startDatePicker.getValue(),
                    endDatePicker.getValue());

            ListenableFuture<String> future = reportGenerator.exportToPdf(fileName,
                    data,
                    tableHeaders,
                    rowWiths);

            return future;
        } catch (Exception e) {
            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
            return AsyncResult.forValue("failed to create pdf");
        }

    }

    private DateTimePicker getDateI18nUa(String label, boolean initAsStartOfDay) {
        DateTimePicker dateTimePicker = new DateTimePicker(label);
        dateTimePicker.setLocale(new Locale("en", "GB"));
        DatePicker.DatePickerI18n uaI18n = new DatePicker.DatePickerI18n();
        uaI18n.setMonthNames(List.of("Січень", "Лютий", "Березень", "Квітень", "Травень", "Червень", "Липень", "Серпень", "Вересень", "Жовтень", "Листопад", "Грудень"));
        uaI18n.setWeekdays(List.of("Неділя", "Понеділок", "Вівторок", "Середа", "Четвер", "П'ятниця", "Субота"));
        uaI18n.setWeekdaysShort(List.of("Нд", "Пн", "Вт", "Ср", "Чт", "Пт", "Сб"));
        uaI18n.setToday("Сьгодні");
        uaI18n.setCancel("Відміна");
        uaI18n.setDateFormat("dd/MM/yyyy");

        dateTimePicker.setDatePickerI18n(uaI18n);

        dateTimePicker.setStep(Duration.ofMinutes(1));
        dateTimePicker.setHelperText("Формат: дд/ММ/рррр гг:хх");
        if (initAsStartOfDay) {
            dateTimePicker.getElement().executeJs("this.getElementsByTagName(\"vaadin-date-time-picker-date-picker\")[0]" +
                                                  ".addEventListener('change', function(){this.getElementsByTagName(\"vaadin-date-time-picker-time-picker\")[0].value='00:00';}" +
                                                  ".bind(this));");
        } else {
            dateTimePicker.getElement().executeJs("this.getElementsByTagName(\"vaadin-date-time-picker-date-picker\")[0]" +
                                                  ".addEventListener('change', function(){this.getElementsByTagName(\"vaadin-date-time-picker-time-picker\")[0].value='23:59';}" +
                                                  ".bind(this));");
        }

        return dateTimePicker;
    }

    private Dialog createReportOptionsDialog(Button exportBt) {
        Dialog dialog = new Dialog();
        dialog.addClassName("log-export-options-dialog");

        dialog.setHeaderTitle("Експорт даних");
        dialog.add("Оберіть параметри експорту");
        VerticalLayout exportOptionLayout = new VerticalLayout();
        Checkbox exportSystemStatesRequestsOption = new Checkbox("Експортувати історію параметрів систем?", true);
        Checkbox exportCommandsOption = new Checkbox("Експортувати історію команд?");
        Checkbox exportNavigationServiceNmea = new Checkbox("Експортувати історію роботи УКННС та Метеостанції?");
        Checkbox exportNavigationHttpServiceRequestsOption = new Checkbox("Експортувати журнали HTTP запитів УКННС та Метеостанції?");
        exportOptionLayout.add(exportSystemStatesRequestsOption, exportCommandsOption, exportNavigationServiceNmea, exportNavigationHttpServiceRequestsOption);

        dialog.add(exportOptionLayout);

        Button confirmBt = new Button("Так", (confirmEvent) -> {
            UI ui = getUI().get();

            ui.access(() -> {
                exportBt.setVisible(false);
                cancelExportButton.setVisible(true);
            });

            LocalDateTime startDate = startDatePicker.getValue() != null ? startDatePicker.getValue() :
                    LocalDateTime.of(1917, 1, 1, 0, 0);
            LocalDateTime endDate = endDatePicker.getValue() != null ? endDatePicker.getValue() : LocalDateTime.now();

            futures = new ArrayList<>();

            if (exportNavigationServiceNmea.getValue()) {
                List<AdjacentSystemType> selectedSystems = List.of(AdjacentSystemType.BINS, AdjacentSystemType.MSU);
                for (AdjacentSystemType selectedSystem : selectedSystems) {
                    if (Thread.currentThread().isInterrupted()) {
                        log.error(" export was interrupted");
                        return;
                    }
                    String fileName = reportGenerator.getReportFileName(reportGenerator.getFileNameTsFormatter(),
                            selectedSystem.getEn(), ExportDataType.COMMAND);
                    ReportSettings reportSettings = new ReportSettings("*",
                            List.of(selectedSystem),
                            List.of(DEFAULT_SYSTEM_STATUS),
                            startDate,
                            endDate);
                    signUpExportTask(ui, exportLogButton, fileName, ExportType.NAVIGATION_SERVICE_NMEA, reportSettings);
                    log.debug("exportNavigationServiceNmea system:" + selectedSystem);
                }
            }

            //update validate and update settings
            if (exportCommandsOption.getValue()) {
                // TODO: 11/30/2023 export commands logs
                List<AdjacentSystemType> selectedSystems = (systemFilterCb.getSelectedItems().size() == 0) ?
                        List.of(AdjacentSystemType.UNDEFINED) : (systemFilterCb.isSelected(AdjacentSystemType.UNCLASSIFIED) ?
                        List.of(AdjacentSystemType.UNCLASSIFIED) : systemFilterCb.getSelectedItems().stream().toList());
                selectedSystems = logService.getSystemFiltersAsSystemType(selectedSystems);

                int reportsToExport = reportGenerator.countReportsToExport(exportSystemStatesRequestsOption.getValue(),
                        exportCommandsOption.getValue(),
                        exportNavigationServiceNmea.getValue(),
                        exportNavigationHttpServiceRequestsOption.getValue(),
                        selectedSystems);

                if (reportsToExport >= 0) {
                    reportGenerator.setNumberOfReportsToProcess(reportsToExport);
                }
                for (AdjacentSystemType selectedSystem : selectedSystems) {
                    if (Thread.currentThread().isInterrupted()) {
                        log.error(" export was interrupted");
                        return;
                    }
                    String fileName = reportGenerator.getReportFileName(reportGenerator.getFileNameTsFormatter(),
                            selectedSystem.getEn(), ExportDataType.COMMAND);
                    ReportSettings reportSettings = new ReportSettings("*",
                            List.of(selectedSystem),
                            List.of(DEFAULT_SYSTEM_STATUS),
                            startDate, endDate);
                    signUpExportTask(ui,
                            exportBt,
                            fileName,
                            ExportType.COMMAND,
                            reportSettings);
                }

            }
            if (exportNavigationHttpServiceRequestsOption.getValue()) {
                if (Thread.currentThread().isInterrupted()) {
                    log.error(" export was interrupted");
                    return;
                }
                String fileName = reportGenerator.getReportFileName(reportGenerator.getFileNameTsFormatter(),
                        "", ExportDataType.HTTP);
                ReportSettings reportSettings = new ReportSettings("*",
                        List.of(AdjacentSystemType.BINS, AdjacentSystemType.MSU),
                        List.of(DEFAULT_SYSTEM_STATUS),
                        startDate,
                        endDate);
                signUpExportTask(ui, exportBt, fileName, ExportType.HTTP, reportSettings);
            }
            if (exportSystemStatesRequestsOption.getValue()) {
                String fileName = reportGenerator.getReportFileName(reportGenerator.getFileNameTsFormatter(), ExportDataType.STATES);
                ReportSettings reportSettings = new ReportSettings(
                        filterTextField.getValue(),
                        (systemFilterCb.getSelectedItems().size() == 0) ? List.of(AdjacentSystemType.UNDEFINED) : (systemFilterCb.isSelected(AdjacentSystemType.UNCLASSIFIED) ? List.of(AdjacentSystemType.UNCLASSIFIED) : systemFilterCb.getSelectedItems().stream().toList()),
                        (statusCb.getSelectedItems().size() == 0 ? List.of(AdjacentSystemStatus.UNDEFINED) : statusCb.isSelected(AdjacentSystemStatus.ANY) ? List.of(AdjacentSystemStatus.ANY) : statusCb.getSelectedItems().stream().toList()),
                        startDatePicker.getValue(),
                        endDatePicker.getValue());
                signUpExportTask(ui, exportBt, fileName, ExportType.SYSTEM_STATUS, reportSettings);
            }

    dialog.close();
            ListenableFuture<List<String>> generatedFilesFuture = allOf(futures, ui);
            generatedFilesFuture.addCallback(success -> {
                ui.access(() -> {
                    cancelExportButton.setVisible(false);
                    exportLogButton.setVisible(true);
                    exportLogButton.setEnabled(true);
                    Notification.show("Callback from allOf.onSuccess");
                });

                if (success.size() > 0) {
                    final List<String> results = new ArrayList<>(success);
                    ui.access(() -> Notification.show("Processing Completed! size " + results.size()));

                    List<FileCreatedEvent.FileInfo> exportResults = new ArrayList<>();
                    for (String singleResult : results) {
                        if (TmpFileUtils.getTopMostFileByName(singleResult).isEmpty()) {
                            return;
                        }
                        File file = TmpFileUtils.getTopMostFileByName(singleResult).get();
                        exportResults.add(FileCreatedEvent.FileInfo.builder()
                                .fileName(file.getName())
                                .pathToFile(file.getPath())
                                .size(file.length())
                                .createdAt(TmpFileUtils.getCreationTs(file.toPath(), dateTimeService.getTimeZoneId()))
                                .build());
                    }

                    log.info("result size: " + results.size());
                    log.info("number of tasks: " + futures.size());
                    if (futures.size() == 0) {
                        log.info("WARNING!!!!!!!!!!!!!!!!!");
                    } else {
                        futures.clear();
                    }

                    Broadcaster.broadcast(FileCreatedEvent.builder()
                            .adjacentSystemType(AdjacentSystemType.ASKU)
                            .source(this)
                            .payload("Протокол готовий для завантаження")
                            .filesInfo(exportResults)
                            .build());

                } else {
                    ui.access(() -> Notification.show("No files were exported! size " + success.size()));
                }
            }, failure -> {
                ui.access(() -> {
                    Broadcaster.broadcast(FileCreatedEvent.builder()
                            .adjacentSystemType(AdjacentSystemType.ASKU)
                            .source(this)
                            .payload("Eкспорт протоколу перевано!")
                            .build());
                    log.debug("reports export was interrupted");

                    cancelExportButton.setVisible(false);
                    exportLogButton.setVisible(true);
                    exportLogButton.setEnabled(true);

                    Notification.show("Callback from allOf.onFailure");
                });
            });
        });

        confirmBt.addClassName("confirm-button-export-options-dialog");

        confirmBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY,
                ButtonVariant.LUMO_ERROR);
        confirmBt.getStyle().

                set("margin-right", "auto");
        dialog.getFooter().

                add(confirmBt);

        Button cancelButton = new Button("Відміна", (cancelEvent) -> dialog.close());
        cancelButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
        cancelButton.addClassName("cancel-button-export-options-dialog");

        dialog.getFooter().add(cancelButton);
        dialog.open();

        return dialog;
    }

    ListenableFuture<String> createReportWithData(LocalDateTime startDate,
                                                  LocalDateTime endDate,
                                                  ExportType exportType) {
        try {
            List<LogBean> data = new ArrayList<>();
            ListenableFuture<List<LogBean>> dataLodeRequest;
            String fileName = "";

            if (exportType.equals(ExportType.NAVIGATION_SERVICE_NMEA)) {
                List<AdjacentSystemType> selectedSystems = List.of(AdjacentSystemType.BINS, AdjacentSystemType.MSU);

                for (AdjacentSystemType selectedSystem : selectedSystems) {
                    data = logService.selectCommandLogBetweenDates(selectedSystem, startDate, endDate);
                    log.debug("exportNavigationServiceNmea system:" + selectedSystem);
                    if (data.size() > 0) {
                        fileName = reportGenerator.getReportFileName(reportGenerator.getFileNameTsFormatter(),
                                selectedSystem.getEn(), ExportDataType.COMMAND);
                        log.debug("creating a signUpExportTask system:" + selectedSystem + " fileName: " + fileName
                                  + "data size " + data.size());
                    } else {
                        log.debug("no record to export found in " + selectedSystem);
                    }
                }
            }

            if (data.size() < 1) {
                String msg = " Expected data.size() > 0";
                log.error(msg);
                throw new IllegalArgumentException(msg);
            }
            if (fileName.isBlank()) {
                String msg = "filename cant be blank";
                log.error(msg);
                throw new IllegalArgumentException(msg);
            }

            ListenableFuture<String> future = reportGenerator.exportToPdf(fileName,
                    data,
                    tableHeaders,
                    rowWiths);
            return future;
        } catch (Exception e) {
            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
            return AsyncResult.forValue("failed to create pdf");
        }
    }

    List<ListenableFuture<String>> futures = new ArrayList<>();

    private void signUpExportTask(UI ui, Button exportBt, String fileName, ExportType exportType/*List<LogBean> data*/, ReportSettings reportSettings) {
        List<LogBean> data = new ArrayList<>();
        try{
            switch (exportType) {
                case NAVIGATION_SERVICE_NMEA ->
                        data = logService.selectCommandLogBetweenDates(reportSettings.systemFilter().get(0), reportSettings.startDate(), reportSettings.endDate());
                case COMMAND ->
                        data = logService.selectCommandLogBetweenDates(reportSettings.systemFilter().get(0), reportSettings.startDate(), reportSettings.endDate());
                case HTTP -> data = logService.selectHttpLogs(reportSettings.startDate(), reportSettings.endDate());
                case SYSTEM_STATUS ->
                        data = logService.selectFiltered(reportSettings.filterTextField(), reportSettings.systemFilter(),
                                reportSettings.systemStatus(), reportSettings.startDate(), reportSettings.endDate());

            }
        }catch (SQLGrammarException | InvalidDataAccessResourceUsageException e){
            log.error(e.getMessage()+" "+Arrays.toString(e.getStackTrace()));
            return;
        }

        if (data.size() == 0) {
            log.info("no data to export " + exportType + " " + reportSettings.toString());
            return;
        }

        data = data.stream().limit(50).toList();// TODO: 1/8/2024 remove
        ListenableFuture<String> future = createReport(fileName, data);
        if (futures == null) {
            futures = new ArrayList<>();
        }
        futures.add(future);

        cancelExportButton.addClickListener((cancelEvent) -> {
            future.cancel(true);
        });
    }

    private <T> ListenableFuture<List<T>> allOf(final List<? extends ListenableFuture<? extends T>> futures, UI ui) {
        // we will return this ListenableFuture, and modify it from within callbacks on each input future
        final SettableListenableFuture<List<T>> groupFuture = new SettableListenableFuture<>();

        // use a defensive shallow copy of the futures list, to avoid errors that could be caused by
        // someone inserting/removing a future from `futures` list after they call this method
        final List<? extends ListenableFuture<? extends T>> futuresCopy = new ArrayList<>(futures);

        // Count the number of completed futures with an AtomicInt (to avoid race conditions)
        final AtomicInteger resultCount = new AtomicInteger(0);
        for (int i = 0; i < futuresCopy.size(); i++) {
            futuresCopy.get(i).addCallback(new ListenableFutureCallback<T>() {
                @Override
                public void onSuccess(final T result) {
                    int thisCount = resultCount.incrementAndGet();

                    // if this is the last result, build the ArrayList and complete the GroupFuture
                    if (thisCount == futuresCopy.size()) {
                        List<T> resultList = new ArrayList<T>(futuresCopy.size());
                        try {
                            for (ListenableFuture<? extends T> future : futuresCopy) {
                                resultList.add(future.get());
                            }
                            groupFuture.set(resultList);
                        } catch (Exception e) {
                            // this should never happen, but future.get() forces us to deal with this exception.
                            groupFuture.setException(e);
                        }
                    }
                }

                @Override
                public void onFailure(final Throwable throwable) {
                    groupFuture.setException(throwable);

                    // if one future fails, don't waste effort on the others
                    for (ListenableFuture future : futuresCopy) {
                        future.cancel(true);
                    }
                }
            });
        }

        return groupFuture;
    }

    private enum ExportType {
        COMMAND,
        SYSTEM_STATUS,
        NAVIGATION_SERVICE_NMEA,
        HTTP
    }
}
