package com.deb.spl.control.views;

import com.vaadin.flow.component.AttachEvent;
import com.vaadin.flow.component.applayout.AppLayout;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.router.Route;
import org.springframework.beans.factory.annotation.Value;

@Route(value = "", layout = AppLayout.class)
public class StartPage extends HorizontalLayout {
    @Value("${host.url}")
    String hostUrl;
    @Value("${server.port:8079}")
    String serverPort;
    @Value("${home.url}")
    String homePage;

    @Override
    public void onAttach(AttachEvent event) {
        if (getUI().isPresent()) {
            getUI().get().getPage().setLocation(homePage);
        }
    }
}
