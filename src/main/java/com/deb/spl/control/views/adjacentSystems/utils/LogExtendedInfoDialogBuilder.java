package com.deb.spl.control.views.adjacentSystems.utils;

import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.dialog.Dialog;
import com.vaadin.flow.component.html.Span;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;

public class LogExtendedInfoDialogBuilder {
    public static Dialog getDialog(HasVerticalInfoAsList item) {
        Dialog dialog = new Dialog();
        dialog.addClassName("show-full-info-dialog");

        dialog.setHeaderTitle("Інформація про подію");
        VerticalLayout textInfoLayout = new VerticalLayout();
        for (HasVerticalInfoAsList.Pair pair : item.toVerticalInfoListUa()) {
            StringBuilder rowString = new StringBuilder();
            for (int i = 1; i <= pair.key(); i++) {
                rowString.append("\t");
            }
            rowString.append(pair.value());
            Span sp = new Span(rowString.toString());
            sp.getStyle().set("white-space", "pre-line");
            sp.getStyle().set("padding-left", 20 * pair.key() + "px");
            if (pair.key() == 0) {
                sp.getStyle().set("font-style", "inherit");
            } else {
                sp.getStyle().set("font-style", "italic");
            }
            textInfoLayout.add(sp);
        }
            dialog.add(textInfoLayout);

        Button closeBt = new Button("Закрити", confirmEvent -> {
            dialog.close();
        });
        closeBt.addClassName("close-info-dialog");

        closeBt.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
        closeBt.setAutofocus(true);

        dialog.getFooter().add(closeBt);

        return dialog;
    }
}
