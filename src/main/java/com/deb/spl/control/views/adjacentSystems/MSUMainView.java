package com.deb.spl.control.views.adjacentSystems;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.asku.RocketMapper;
import com.deb.spl.control.data.msu.MeteoStationUnit;
import com.deb.spl.control.repository.bins.NavigationRestRequestRepository;
import com.deb.spl.control.repository.msu.MsuHistoryRepository;
import com.deb.spl.control.service.*;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.service.asku.PlcService;
import com.deb.spl.control.views.MainLayout;
import com.deb.spl.control.views.MenuItemInfo;
import com.deb.spl.control.views.adjacentSystems.bins.BinsView;
import com.deb.spl.control.views.adjacentSystems.bins.NavigationRestLogTab;
import com.deb.spl.control.views.adjacentSystems.bins.SplNavigationLogTab;
import com.deb.spl.control.views.adjacentSystems.nppa.NppaView;
import com.deb.spl.control.views.adjacentSystems.utils.ConfigurationUtils;
import com.deb.spl.control.views.events.AdjacentSystemUpdateEventType;
import com.deb.spl.control.views.events.MsuEvent;
import com.vaadin.flow.component.AttachEvent;
import com.vaadin.flow.component.DetachEvent;
import com.vaadin.flow.component.Html;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.dependency.CssImport;
import com.vaadin.flow.component.dialog.Dialog;
import com.vaadin.flow.component.grid.ColumnTextAlign;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.grid.GridVariant;
import com.vaadin.flow.component.html.Nav;
import com.vaadin.flow.component.html.Span;
import com.vaadin.flow.component.html.UnorderedList;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.component.textfield.TextFieldVariant;
import com.vaadin.flow.data.binder.Binder;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import com.vaadin.flow.theme.lumo.LumoUtility;
import lombok.extern.slf4j.Slf4j;
import net.sf.marineapi.nmea.parser.SDSParser;
import net.sf.marineapi.nmea.sentence.SDSSentence;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.vaadin.tabs.PagedTabs;

import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@PageTitle("Сатус систем | МСЮ")
@Route(value = "adjacent-systems/msu", layout = MainLayout.class)

@CssImport(value = "./recipe/vaadintextfield/text-field-RowDataState-background-color.css",
        themeFor = "vaadin-text-field")
@CssImport(value = "./recipe/vaadintextfield/text-field-RowDataState-background-color.css",
        themeFor = "vaadin-text-field")

public class MSUMainView extends HorizontalLayout implements Broadcaster.BroadcastListener {
    private final MsuService msuService;
    private final PpoService ppoService;
    private final SutoService sutoService;
    private final BinsService binsService;
    private final SaeService saeService;
    private final AskuService askuService;
    private final NppaService nppaService;


    private Binder<MeteoStationUnit> msuBinder;
    private final SystemsInfo systemsInfo;
    private final MsuHistoryRepository msuHistoryRepository;

    @Override
    protected void onAttach(AttachEvent attachEvent) {
        super.onAttach(attachEvent);
        Broadcaster.register(this, MsuEvent.class);
    }

    @Autowired
    public MSUMainView(MsuService msuService,
                       PpoService ppoService,
                       SutoService sutoService,
                       BinsService binsService,
                       SaeService saeService,
                       NppaService nppaService, PlcService plcService,
                       AskuService askuService,
                       MsuHistoryRepository msuHistoryRepository,
                       NavigationRestRequestRepository navigationRestRequestRepository,
                       RocketMapper rocketMapper,
                       CcvCommunicationService ccvCommunicationService,
                       @Value("${log.adjacent-system-view.enable}")
                       boolean showRestLog) {
        this.nppaService = nppaService;

        this.msuHistoryRepository = msuHistoryRepository;
        this.ppoService = ppoService;
        this.sutoService = sutoService;
        this.binsService = binsService;
        this.saeService = saeService;
        this.askuService = askuService;

        Span splNameSpan = new Span("Назва СПУ");
        splNameSpan.addClassName("spl-name-out");
        splNameSpan.setText("Система метеорологічного забезпечення");

        this.msuService = msuService;
        systemsInfo = new SystemsInfo(this.msuService,
                this.binsService,
                this.ppoService,
                this.sutoService,
                this.saeService,
                this.askuService,
                plcService,
                this.nppaService,
                rocketMapper,
                ccvCommunicationService);

        VerticalLayout systemLayout = new VerticalLayout();
        systemLayout.add(setupNavTab());
        systemLayout.add(splNameSpan);
        systemLayout.addClassName("system-layout-msu");
        systemLayout.setWidth("69.5%");

        systemLayout.setMaxHeight("100%");
        add(systemsInfo, systemLayout);

        VerticalLayout commandTab = new VerticalLayout();
        commandTab.addClassName("command-tab");
        commandTab.setWidthFull();
        commandTab.setWidth("100%");
        commandTab.setMinWidth("100%");
        commandTab.add(setupCommands());
        commandTab.add(setupStatus());

        VerticalLayout logTab = new VerticalLayout();
        logTab.addClassName("log-tab");
        logTab.add(new SplNavigationLogTab(msuHistoryRepository, "msu-log-tab")/*setupLog()*/);

        VerticalLayout tabContainer = new VerticalLayout();
        tabContainer.addClassName("tab-container");
        PagedTabs tabs = new PagedTabs(tabContainer);
        tabs.getContent().setWidthFull();

        String commandTabHeader = "Команди";
        String logTabHeader = "Журнал";

        tabs.add(commandTabHeader, commandTab, false);
        tabs.add(logTabHeader, logTab, false);
        if (showRestLog) {
            String restLogHeader = "Журнал запитів";

            NavigationRestLogTab restLogTab = new NavigationRestLogTab(navigationRestRequestRepository,
                    AdjacentSystemType.MSU,
                    "msu-rest-log-tab");
            VerticalLayout restLogHolder = new VerticalLayout();
            restLogHolder.addClassName("rest-log-tab");
            restLogHolder.add(restLogTab);

            tabs.add(restLogHeader, restLogHolder, false);
            tabs.addSelectedChangeListener(e -> {
                if (e.getLabel().equals(restLogHeader)) {
                    restLogTab.setupDataBinding();
                }
            });
        }

        systemLayout.add(tabs, tabContainer);
    }

    @Override
    protected void onDetach(DetachEvent detachEvent) {
        super.onDetach(detachEvent);
        Broadcaster.unregister(this);
    }

    private VerticalLayout setupNavTab() {
        Nav nav = new Nav();
        nav.addClassName("adjacent-systems-nav");


        UnorderedList list = new UnorderedList();
        list.addClassName("adjacent-systems-nav-list");
        list.addClassNames(LumoUtility.Display.BLOCK);
        HorizontalLayout hz = new HorizontalLayout();
        hz.add(nav);
        nav.add(list);

        for (MenuItemInfo menuItem : createMenuItems()) {
            list.add(menuItem);
        }

        VerticalLayout tabPanel = new VerticalLayout();
        tabPanel.addClassName("systems-tab-panel");
        tabPanel.setWidth("100%");

        tabPanel.add(nav);

        return tabPanel;
    }

    private VerticalLayout setupCommands() {
        VerticalLayout tabPanel = new VerticalLayout();
        tabPanel.setWidth("100%");
        tabPanel.setMaxWidth("100%");

        //commands
        HorizontalLayout commandsLayoutH = new HorizontalLayout();
        commandsLayoutH.addClassName("commands-layout-h");
        commandsLayoutH.setWidth("100%");
        commandsLayoutH.setMinWidth("100%");

        VerticalLayout commandsLayout = new VerticalLayout();
        commandsLayout.addClassName("commands-layout-v");
        commandsLayout.setWidth("100%");
        commandsLayout.setMinWidth("100%");
        commandsLayoutH.add(commandsLayout);
        tabPanel.add(commandsLayoutH);

        Span commandsCaption = new Span("Перелік команд");
        commandsCaption.addClassName("commands-caption");
        commandsLayout.add(commandsCaption);

        setupMsuCommandsGrid(commandsLayout);

        return tabPanel;
    }

    private VerticalLayout setupStatus() {
        VerticalLayout tabPanel = new VerticalLayout();
        tabPanel.setWidth("100%");
        tabPanel.setMaxWidth("100%");
        tabPanel.addClassName("tab-panel");

//      status
        HorizontalLayout statusLayoutH = new HorizontalLayout();
        statusLayoutH.addClassName("status-layout-h");
        statusLayoutH.setWidthFull();

        VerticalLayout statusLayout = new VerticalLayout();
        statusLayout.addClassName("status-layout-v");
        statusLayoutH.add(statusLayout);
        tabPanel.add(statusLayoutH);

        Span logCaption = new Span("Статус системи");
        logCaption.addClassName("log-caption");
        statusLayout.add(logCaption);

        setupMsuStateGrid(statusLayout);

        return tabPanel;
    }


    private void setupMsuStateGrid(VerticalLayout tabPanel) {
        msuBinder = new Binder<>(MeteoStationUnit.class);

        TextField isNormal = new TextField("Статус");
        isNormal.setReadOnly(true);
        isNormal.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        isNormal.addClassName("is-normal");

        msuBinder.forField(isNormal)
                .bindReadOnly(msu -> {
                    if (msu == null) {
                        return "";
                    }
                    return !msu.isConnected() ? AdjacentSystemStatus.NOT_CONNECTED.getValueUa() : (
                            msu.isNormal() ? AdjacentSystemStatus.OK.getValueUa() : AdjacentSystemStatus.ERROR.getValueUa());
                });

        TextField windDirection = new TextField("Напрямок вітру");
        windDirection.setReadOnly(true);
        windDirection.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        windDirection.addClassName("wind-direction");
        msuBinder.forField(windDirection)
                .bindReadOnly(msu -> msu == null ? "" : (
                        msu.isConnected() ? String.valueOf(msu.getWindDirection()) : ""));

        TextField windSpeed = new TextField("Швидкість вітру");
        windSpeed.setReadOnly(true);
        windSpeed.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        windSpeed.addClassName("wind-speed");
        msuBinder.forField(windSpeed)
                .bindReadOnly(msu -> msu == null ? "" : (
                        msu.isConnected() ? String.valueOf(msu.getWindSpeed()) : ""));

        isNormal.addValueChangeListener(event -> {
            if (event.getValue() == null) {
                isNormal.setClassName(isNormal.getClassName() + "-" + AdjacentSystemStatus.UNDEFINED);   //wrong name of class
            }
            AdjacentSystemStatus status = AdjacentSystemStatus.fromValueUa(event.getValue());
            isNormal.setClassName(isNormal.getClassName() + "-" + status.name().toLowerCase());
        });

        TextField airTemperature = new TextField("Температура");
        airTemperature.setReadOnly(true);
        airTemperature.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        airTemperature.addClassName("air-temperature");
        msuBinder.forField(airTemperature)
                .bindReadOnly(msu -> msu == null ? "" : (
                        msu.isConnected() ? String.valueOf(msu.getAirTemperature()) : ""));

        HorizontalLayout msuNullRow = new HorizontalLayout();
        msuNullRow.add(isNormal, windDirection, windSpeed, airTemperature);
        msuNullRow.addClassName("msu-null-row");
        tabPanel.add(msuNullRow);

        TextField voltage = new TextField("Напруга");
        voltage.setReadOnly(true);
        voltage.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        voltage.addClassName("voltage");
        msuBinder.forField(voltage)
                .bindReadOnly(msu -> msu == null ? "" : (
                        msu.isConnected() ? String.valueOf(msu.getVoltage()) : ""));

        TextField msuTemperature = new TextField("Температура МСЮ");
        msuTemperature.setReadOnly(true);
        msuTemperature.addClassName("msu-temperature");
        msuTemperature.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        msuBinder.forField(msuTemperature)
                .bindReadOnly(msu -> msu == null ? "" : (
                        msu.isConnected() ? String.valueOf(msu.getMsuTemperature()) : ""));

        TextField posture = new TextField("Положення");
        posture.setReadOnly(true);
        posture.addClassName("posture");
        posture.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);

        msuBinder.forField(posture).
                bindReadOnly(msu -> (msu == null) ? "" : (
                        (msu.isConnected() && msu.getPosture() != null) ? msu.getPosture().getStringValue() : ""));

        TextField heatingMode = new TextField("Обігрів");
        heatingMode.setReadOnly(true);
        heatingMode.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        heatingMode.addClassName("heating-mode");
        msuBinder.forField(heatingMode).
                bindReadOnly(msu -> (msu == null) ? "" : (
                        (msu.isConnected() && msu.getHeatingMode() != null) ? msu.getHeatingMode().getStringValue() : ""));

        HorizontalLayout msuFirstRow = new HorizontalLayout();
        msuFirstRow.add(voltage, msuTemperature, posture, heatingMode);
        msuFirstRow.addClassName("msu-first-row");

        tabPanel.add(msuFirstRow);

        TextField heatingModePermission = new TextField("Дозвіл обігріву");
        heatingModePermission.setReadOnly(true);
        heatingModePermission.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        heatingModePermission.addClassName("heating-mode-permission");
        msuBinder.forField(heatingModePermission).
                bindReadOnly(msu -> (msu == null) ? "" : (
                        (msu.isConnected() && msu.getHeatingModePermission() != null) ? msu.getHeatingModePermission().getStringValue() : ""));

        TextField temperatureSensorBlowing = new TextField("Обдув термосенсора");
        temperatureSensorBlowing.setReadOnly(true);
        temperatureSensorBlowing.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        temperatureSensorBlowing.addClassName("temperature-sensor-blowing");
        msuBinder.forField(temperatureSensorBlowing)
                .bindReadOnly(msu -> msu == null ? "" : (
                        (msu.isConnected() && msu.getTemperatureSensorMalfunction() != null) ?
                                msu.getTemperatureSensorMalfunction().getStringValue() : ""));

        HorizontalLayout msuSecondRow = new HorizontalLayout();
        msuSecondRow.add(heatingModePermission, temperatureSensorBlowing);
        msuSecondRow.addClassName("msu-second-row");

        tabPanel.add(msuSecondRow);

        TextField postureSwitchingMalfunction = new TextField("Помилка зміни полож.");
        postureSwitchingMalfunction.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        postureSwitchingMalfunction.addClassName("posture-switching-malfunction");
        postureSwitchingMalfunction.setReadOnly(true);
        msuBinder.forField(postureSwitchingMalfunction)
                .bindReadOnly(msu -> msu == null ? "" : (
                        (msu.isConnected() && msu.getPostureSwitchingMalfunction() != null) ?
                                msu.getPostureSwitchingMalfunction().getStringValue() : ""));

        TextField voltageMalfunction = new TextField("Помилка вольтажу");
        voltageMalfunction.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        voltageMalfunction.setReadOnly(true);
        voltageMalfunction.addClassName("voltage-malfunction");
        msuBinder.forField(voltageMalfunction)
                .bindReadOnly(msu -> msu == null ? "" : (
                        (msu.isConnected() && msu.getVoltageMalfunction() != null) ?
                                msu.getVoltageMalfunction().getStringValue() : ""));


        TextField dataExchangeMalfunction = new TextField("Помилка обміну даних");
        dataExchangeMalfunction.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        dataExchangeMalfunction.setReadOnly(true);
        dataExchangeMalfunction.addClassName("data-exchange-malfunction");
        msuBinder.forField(dataExchangeMalfunction)
                .bindReadOnly(msu -> msu == null ? "" : (
                        (msu.isConnected() && msu.getDataExchangeMalfunction() != null) ?
                                msu.getDataExchangeMalfunction().getStringValue() : ""));

        TextField electricalDriveMalfunction = new TextField("Несправність ЕП");
        electricalDriveMalfunction.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        electricalDriveMalfunction.setReadOnly(true);
        electricalDriveMalfunction.addClassName("electrical-drive-malfunction");
        msuBinder.forField(electricalDriveMalfunction)
                .bindReadOnly(msu -> msu == null ? "" : (
                        (msu.isConnected() && msu.getElectricalDriveMalfunction() != null) ?
                                msu.getElectricalDriveMalfunction().getStringValue() : ""));

        HorizontalLayout msuThirdRow = new HorizontalLayout();
        msuThirdRow.add(postureSwitchingMalfunction, voltageMalfunction, dataExchangeMalfunction
                , electricalDriveMalfunction);
        msuThirdRow.addClassName("msu-third-row");

        tabPanel.add(msuThirdRow);

        TextField temperatureSensorMalfunction = new TextField("Неспр. термосенсора");
        temperatureSensorMalfunction.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        temperatureSensorMalfunction.setReadOnly(true);
        temperatureSensorMalfunction.addClassName("temperature-sensor-malfunction");
        msuBinder.forField(temperatureSensorMalfunction)
                .bindReadOnly(msu -> msu == null ? "" : (
                        (msu.isConnected() && msu.getTemperatureSensorMalfunction() != null) ?
                                msu.getTemperatureSensorMalfunction().getStringValue() : ""));

        TextField endSwitchesMalfunction = new TextField("Неспр. кінцевого вимикача");
        endSwitchesMalfunction.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        endSwitchesMalfunction.setReadOnly(true);
        endSwitchesMalfunction.addClassName("end-switches-malfunction");
        msuBinder.forField(endSwitchesMalfunction)
                .bindReadOnly(msu -> msu == null ? "" : (
                        (msu.isConnected() && msu.getEndSwitchesMalfunction() != null) ?
                                msu.getEndSwitchesMalfunction().getStringValue() : ""));

        TextField windSpeedSensorMalfunction = new TextField("Неспр. сенсору шв. вітру");
        windSpeedSensorMalfunction.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        windSpeedSensorMalfunction.setReadOnly(true);
        windSpeedSensorMalfunction.addClassName("wind-speed-sensor-malfunction");
        msuBinder.forField(windSpeedSensorMalfunction)
                .bindReadOnly(msu -> msu == null ? "" : (
                        (msu.isConnected() && msu.getWindSpeedSensorMalfunction() != null) ?
                                msu.getWindSpeedSensorMalfunction().getStringValue() : ""));

        HorizontalLayout msuFourthRow = new HorizontalLayout();
        msuFourthRow.add(windSpeedSensorMalfunction, temperatureSensorMalfunction, endSwitchesMalfunction);
        msuFourthRow.addClassName("msu-fourth-row");

        tabPanel.add(msuFourthRow);
    }

    private void setupMsuCommandsGrid(VerticalLayout tabPanel) {

        List<CommandRecord> commandsList = Arrays.asList(
                new CommandRecord("Перевести в робоче положення", "$PMSDS,1,0,0,0*58"),
                new CommandRecord("Перевести в транспортне положення", "$PMSDS,0,1,0,0*58"),
                new CommandRecord("Дозволити обігрів", "$PMSDS,0,0,1,0*58"),
                new CommandRecord("Заборонити обігрів", "$PMSDS,0,0,0,1*58")
        );

        Grid<CommandRecord> commandsGrid = new Grid<>(CommandRecord.class, false);

        Grid.Column<CommandRecord> caption = commandsGrid
                .addColumn(CommandRecord::getCaption).setHeader(new Html("<b>Команда</b>")).setTextAlign(ColumnTextAlign.START)
                .setFlexGrow(0).setWidth("80%"); /*.setTextAlign(ColumnTextAlign.START)*/

        Grid.Column<CommandRecord> sentence = commandsGrid
                .addColumn(CommandRecord::getSentence).setHeader("Відправити").setTextAlign(ColumnTextAlign.END);
        sentence.setVisible(false);

        Grid.Column<CommandRecord> sendColumn = commandsGrid.addComponentColumn(commandRecord -> {
            Button sendBt = new Button("Відправити");
            sendBt.addClassName("send-sentence-button");
            sendBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY, ButtonVariant.LUMO_SMALL);
            sendBt.getStyle().set("background-color", "#4F4F4F");
            sendBt.addClickListener(event -> {
                Dialog dialog = new Dialog();
                dialog.addClassName("dialog-msu");

                dialog.setHeaderTitle(commandRecord.caption + "?");
                dialog.add("Ви впевнені, що бажаєте " + commandRecord.caption + "?");

                Button confirmBt = new Button(sentence.getHeaderText(), (e) -> {
                    String commandSentenceToSend = generateCommandSentence(commandRecord).toString();
                    System.out.println(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>" + commandSentenceToSend + "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<");
                    msuService.sendCommand(commandSentenceToSend);

                    dialog.close();
                });
                confirmBt.addClassName("confirm-button-msu-dialog");

                confirmBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY,
                        ButtonVariant.LUMO_ERROR);
                confirmBt.getStyle().set("margin-right", "auto");
                dialog.getFooter().add(confirmBt);

                Button cancelButton = new Button("Відміна", (e) -> dialog.close());
                cancelButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
                cancelButton.addClassName("cancel-button-msu-dialog");

                dialog.getFooter().add(cancelButton);

                dialog.open();
            });

            return sendBt;
        }).setHeader(new Html("<b>Відправити</b>"));

        commandsGrid.addClassName("commands-grid");
        commandsGrid.addThemeVariants(GridVariant.MATERIAL_COLUMN_DIVIDERS, GridVariant.LUMO_COLUMN_BORDERS,
                GridVariant.LUMO_ROW_STRIPES);
        commandsGrid.setItems(commandsList);
        tabPanel.add(commandsGrid);
        tabPanel.setPadding(false);
    }

    /**
     * @param record ANTENNA_ROD_PULLING_IN - складівается,
     *               ANTENNA_ROD_PULLING_OUT - розкладається,
     *               operating - разложена,
     *               TRANSPORT - сложена
     * @return
     */
    private SDSSentence generateCommandSentence(@NotNull CommandRecord record) {
        SDSSentence sdsSentence = new SDSParser(record.getSentence());

        return sdsSentence;
    }


    private MenuItemInfo[] createMenuItems() {
        return new MenuItemInfo[]{
                new MenuItemInfo("УКННС", "", BinsView.class),
                new MenuItemInfo("САЕ", "", SaeView.class),
                new MenuItemInfo("СУТО", "", SutoView.class),
                new MenuItemInfo("НППА", "", NppaView.class),
                new MenuItemInfo("ППО", "", PpoView.class),
                new MenuItemInfo("МЕТЕО", "", MSUMainView.class)
        };
    }

    private AtomicBoolean errorDisplayed = new AtomicBoolean(false);

    @Override
    public void receiveBroadcast(Object event) {
        if (!(event instanceof MsuEvent msuEvent)) {
            return;
        }
        if (msuEvent == null) {
            return;
        }

        getUI().get().access(() -> {
            msuBinder.readBean(msuEvent.getMsu());

            if (msuEvent.getEventType() == AdjacentSystemUpdateEventType.ERROR_OCCURRED && !errorDisplayed.get()) {
                String errorMsg = "Помилка в роботі Метеостанції";
                log.error(errorMsg + " " + this);

                ConfigurationUtils.getErrorNotification(errorMsg, "",
                        0, true).open();
                errorDisplayed.set(true);
            }
        });
    }

    public class CommandRecord {
        String caption;
        String sentence;

        public CommandRecord(String caption, String sentence) {
            this.caption = caption;
            this.sentence = sentence;
        }

        public String getCaption() {
            return caption;
        }

        public void setCaption(String caption) {
            this.caption = caption;
        }

        public String getSentence() {
            return sentence;
        }

        public void setSentence(String sentence) {
            this.sentence = sentence;
        }
    }

}
