package com.deb.spl.control.views.rocket;

import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;
import com.deb.spl.control.data.asku.*;
import com.deb.spl.control.service.Broadcaster;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.utils.AskuUtils;
import com.deb.spl.control.views.events.AdjacentSystemUpdateEventType;
import com.deb.spl.control.views.events.AskuInfoEvent;
import com.deb.spl.control.views.events.RocketEvent;
import com.vaadin.flow.component.AttachEvent;
import com.vaadin.flow.component.DetachEvent;
import com.vaadin.flow.component.HasValue;
import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.component.textfield.TextFieldVariant;
import com.vaadin.flow.data.binder.Binder;
import com.vaadin.flow.data.binder.Result;
import com.vaadin.flow.data.binder.ValueContext;
import com.vaadin.flow.data.converter.Converter;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
public class RocketFormDataView extends VerticalLayout implements Broadcaster.BroadcastListener {
    private final Binder<RocketFormData> rocketFormDataBinder = new Binder<>(RocketFormData.class);
    private final Map<String, HasValue> readModeChangerList = new HashMap<>();

    private final AskuService askuService;

    private RocketFormData formData;

    @Override
    protected void onAttach(AttachEvent attachEvent) {
        super.onAttach(attachEvent);
        Broadcaster.register(this, RocketEvent.class);
    }

    @Override
    protected void onDetach(DetachEvent detachEvent) {
        super.onDetach(detachEvent);
        Broadcaster.unregister(this);
    }

    private final boolean isLeftRocket;

    public RocketFormDataView(AskuService askuService, boolean isLeftRocket, String classNameForRFD) {
        this.askuService = askuService;
        this.isLeftRocket = isLeftRocket;

        addClassName(classNameForRFD);
        TextField createdAt = configureTf("created-at", "Час створення", "createdName", true);
        rocketFormDataBinder.forField(createdAt)
                .withConverter(new Converter<String, LocalDateTime>() {
                    @Override
                    public Result<LocalDateTime> convertToModel(String value, ValueContext context) {
                        try {
                            LocalDateTime dt = LocalDateTime.parse(value, DateTimeFormatter.ofPattern("HH:mm dd/MM/yyyy"));
                            return Result.ok(dt);
                        } catch (Exception e) {
                            return Result.error("can't convert");
                        }
                    }

                    @Override
                    public String convertToPresentation(LocalDateTime value, ValueContext context) {
                        if (value == null) {
                            value = LocalDateTime.now();
                        }
                        return value.format(DateTimeFormatter.ofPattern("HH:mm dd/MM/yyyy"));
                    }
                })
                .bind(RocketFormData::getCreatedAt, RocketFormData::setCreatedAt);

        TextField plantMissile = configureTf("plant-missile-form-data",
                "Заводський №",
                "plantMissile",
                true);
        rocketFormDataBinder.forField(plantMissile)
                .bindReadOnly(val -> {
                            if (val == null) {
                                return "";
                            }
                            return val.getPlantMissile();
                        }
                );

        ComboBox<WarheadType> warhead = configureCb("warhead-type",
                "Ознака БЧ",
                Arrays.asList(WarheadType.values()),
                "warhead",
                true);
        rocketFormDataBinder.forField(warhead)
                .bindReadOnly(RocketFormData::getWarhead);


        ComboBox<GsnType> gsnType = configureCb(
                "gsn-type",
                "ГСН",
                Arrays.asList(GsnType.values()),
                "gsnType",
                true);
        rocketFormDataBinder.forField(gsnType)
                .bindReadOnly(RocketFormData::getGsnType);

        ComboBox<AlpType> alpType = configureCb("alp-type",
                "АХП",
                Arrays.asList(AlpType.values()),
                "alpType",
                true);
        rocketFormDataBinder.forField(alpType)
                .bindReadOnly(RocketFormData::getAlpType);

        ComboBox<Boolean> isTelemetryIntegrated = new ComboBox<>("Телеметрія");
        isTelemetryIntegrated.addClassName("is-telemetry-integrated");
        isTelemetryIntegrated.setItems(List.of(true, false));
        isTelemetryIntegrated.setItemLabelGenerator(item -> item ? "Інтегрована" : "Відсутня");
        isTelemetryIntegrated.setReadOnly(true);
        readModeChangerList.put("isTelemetryIntegrated", isTelemetryIntegrated);
        rocketFormDataBinder.forField(isTelemetryIntegrated)
                .bindReadOnly(RocketFormData::isTelemetryIntegrated);

        ComboBox<OtrPurposeType> purposeType = configureCb("otr-purpose-type",
                "Призначення ОТР",
                Arrays.asList(OtrPurposeType.values()),
                "purposeType",
                true);
        rocketFormDataBinder.forField(purposeType)
                .bindReadOnly(RocketFormData::getPurposeType);

        add(createdAt, plantMissile, warhead, gsnType, alpType, isTelemetryIntegrated, purposeType);

        try {
            if (isLeftRocket) {
                if (askuService.getAskuEntity() != null && askuService.getAskuEntity().getLeftRocket() != null &&
                    askuService.getAskuEntity().getLeftRocket().getFormData() != null) {
                    formData = askuService.getAskuEntity().getLeftRocket().getFormData();
                } else {
                    formData = AskuUtils.getDefaultFormData();
                }
            } else {
                if (askuService.getAskuEntity() != null && askuService.getAskuEntity().getRightRocket() != null &&
                    askuService.getAskuEntity().getRightRocket().getFormData() != null) {
                    formData = askuService.getAskuEntity().getRightRocket().getFormData();
                } else {
                    formData = AskuUtils.getDefaultFormData();
                }
            }
        } catch (NullPointerException e) {
            formData = AskuUtils.getDefaultFormData();
        }

        try {
            rocketFormDataBinder.setBean(formData);
        } catch (Exception e) {
            log.error(e.getMessage() + " " + Arrays.asList(e.getStackTrace()));
        }
    }

    public VerticalLayout buildView() {
        return this;
    }

    public Optional<RocketFormData> getRocketFormData() {
        return Optional.ofNullable(formData);
    }

    public RocketFormData rocketFormData() {
        return formData;
    }


    public void setReadOnly(boolean readOnly) {
        for (HasValue field : readModeChangerList.values()) {
            field.setReadOnly(readOnly);
        }
    }

    private TextField configureTf(@NotNull String cssClassName, @NotNull String label, @NotBlank String fieldName,
                                  @NotNull boolean canChangeReadOnlyMode) {
        TextField textField = new TextField();
        textField.addClassName(cssClassName);
        textField.setLabel(label);
        textField.setReadOnly(true);
        textField.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        if (canChangeReadOnlyMode) {
            readModeChangerList.put(fieldName, textField);
        }

        return textField;
    }

    private <T extends AdjacentSystemWithDescriptionUa> ComboBox<T> configureCb(@NotNull String cssClassName,
                                                                                @NotNull String label,
                                                                                @NotNull List<T> items,
                                                                                @NotNull @NotBlank String fieldName,
                                                                                @NotNull boolean canChangeReadOnlyMode) {
        ComboBox<T> comboBox = new ComboBox<T>(label);
        comboBox.addClassName(cssClassName);
        comboBox.setItems(items);
        comboBox.setItemLabelGenerator(T::getValueUa);
        comboBox.setReadOnly(true);

        if (canChangeReadOnlyMode) {
            readModeChangerList.put(fieldName, comboBox);
        }

        return comboBox;
    }

    private final RocketEvent previousEvent = RocketEvent.builder().build();
    private LocalDateTime formDataTs = LocalDateTime.MIN;

    @Override
    public void receiveBroadcast(Object incomingEvent) {
        if (!(incomingEvent instanceof RocketEvent newEvent)) {
            log.debug("expected " + AskuInfoEvent.class.getSimpleName() + "but received unexpected event at " + this.getClassName());
            return;
        }

        if (newEvent.isLeft() != isLeftRocket) {
            return;
        }

        if (newEvent.getRocket() == null) {
            log.error("no rocket found in RocketEvent " + newEvent + " in " + this);
        }

        if (newEvent.getEventType().equals(AdjacentSystemUpdateEventType.REMOVE_ENTITY)) {
            removeRocket();
            formDataTs = newEvent.getTimeStamp() != null ? newEvent.getTimeStamp().withNano(0) :
                    LocalDateTime.MIN;
            return;
        }

        if (newEvent.getRocket().getRocketFormData().isEmpty()) {
            log.error("no rocketFormData found in RocketEvent " + newEvent + " in " + this);
        }

        if (!newEvent.getRocket().getRocketFormData().get().equals(formData) ||
                                                   !formDataTs.withNano(0).isEqual(newEvent.getTimeStamp().withNano(0))) {
            updateDataAndView(newEvent.getRocket().getFormData());

            formDataTs = newEvent.getTimeStamp() != null ? newEvent.getTimeStamp().withNano(0) :
                    LocalDateTime.MIN;
        }
    }

    private void updateDataAndView(@NotNull RocketFormData newFormData) {
        this.formData = newFormData;

        if (getUI().isPresent()) {
            getUI().get().access(() -> {
                if (rocketFormDataBinder.getBean() == null) {
                    rocketFormDataBinder.setBean(this.formData);
                } else {
                    rocketFormDataBinder.readBean(formData);
                }
            });
        } else {
            if (rocketFormDataBinder.getBean() == null) {
                rocketFormDataBinder.setBean(formData);
            } else {
                rocketFormDataBinder.readBean(formData);
            }
        }
    }

    private void removeRocket() {
        if (formData == null || formData.equals(AskuUtils.getDefaultFormData())) {
            return;
        }
        if (getUI().isPresent()) {
            getUI().get().access(() -> {
                rocketFormDataBinder.removeBean();
                rocketFormDataBinder.getFields().forEach(f -> {
                    if (f instanceof ComboBox<?> cb) {
                        cb.setValue(null);
                    } else if (f instanceof TextField tf) {
                        tf.setValue("");
                    }
                });
            });
        } else {
            rocketFormDataBinder.removeBean();
        }
    }
}
