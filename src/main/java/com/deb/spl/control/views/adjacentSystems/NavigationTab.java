package com.deb.spl.control.views.adjacentSystems;

import com.deb.spl.control.views.MenuItemInfo;
import com.deb.spl.control.views.adjacentSystems.bins.BinsView;
import com.deb.spl.control.views.adjacentSystems.nppa.NppaView;
import com.vaadin.flow.component.html.Nav;
import com.vaadin.flow.component.html.UnorderedList;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.theme.lumo.LumoUtility;

public class NavigationTab extends VerticalLayout {

    public NavigationTab() {
        Nav nav = new Nav();
        nav.addClassName("adjacent-systems-nav");

        UnorderedList list = new UnorderedList();
        list.addClassName("adjacent-systems-nav-list");
        list.addClassNames(LumoUtility.Display.BLOCK);
        HorizontalLayout hz = new HorizontalLayout();
        hz.add(nav);
        nav.add(list);

        for (MenuItemInfo menuItem : createMenuItems()) {
            list.add(menuItem);
        }

        addClassName("systems-tab-panel");
        setWidthFull();
        setMaxWidth("100%");
        add(nav);
    }

    private MenuItemInfo[] createMenuItems() {
        return new MenuItemInfo[]{
                new MenuItemInfo("УКННС", "", BinsView.class),
                new MenuItemInfo("САЕ", "", SaeView.class),
                new MenuItemInfo("СУТО", "", SutoView.class),
                new MenuItemInfo("НППА", "", NppaView.class),
                new MenuItemInfo("ППО", "", PpoView.class),
                new MenuItemInfo("МЕТЕО", "", MSUMainView.class)
        };
    }

}
