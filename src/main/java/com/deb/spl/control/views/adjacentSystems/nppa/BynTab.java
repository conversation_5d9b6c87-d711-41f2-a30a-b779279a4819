package com.deb.spl.control.views.adjacentSystems.nppa;

import com.deb.spl.control.repository.nppa.BynHistoryRepository;
import com.deb.spl.control.service.Broadcaster;
import com.deb.spl.control.service.NppaService;
import com.deb.spl.control.service.UserService;
import com.deb.spl.control.views.adjacentSystems.utils.AnimationColor;
import com.deb.spl.control.views.adjacentSystems.utils.ConfigurationUtils;
import com.deb.spl.control.views.adjacentSystems.utils.PagedTabUtils;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.nppa.BaseProperty;
import com.deb.spl.control.data.nppa.Byn;
import com.deb.spl.control.data.nppa.NppaCommand;
import com.deb.spl.control.data.nppa.NppaOperatingMode;
import com.deb.spl.control.data.ppo.PpoCommand;
import com.deb.spl.control.views.adjacentSystems.LogTab;
import com.deb.spl.control.views.events.NppaEvent;
import com.vaadin.flow.component.AttachEvent;
import com.vaadin.flow.component.DetachEvent;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.grid.ColumnTextAlign;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.grid.GridVariant;
import com.vaadin.flow.component.html.Span;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.component.textfield.TextFieldVariant;
import com.vaadin.flow.data.binder.Binder;
import lombok.extern.slf4j.Slf4j;
import org.vaadin.tabs.PagedTabs;

import java.util.Arrays;
import java.util.List;

@Slf4j
public class BynTab extends VerticalLayout implements Broadcaster.BroadcastListener {
    private final String unknownValue = "Невідомо";
    private final BynHistoryRepository bynHistoryRepository;
    private final NppaService nppaService;
    private final UserService userService;
    private final Binder<Byn> bynBinder = new Binder<>();

    private Grid<NppaCommand> bynModeCommandsGrid;
    private List<NppaCommand> bynModeCommands;

    @Override
    protected void onAttach(AttachEvent attachEvent) {
        super.onAttach(attachEvent);
        Broadcaster.register(this, NppaEvent.class);
    }

    @Override
    protected void onDetach(DetachEvent detachEvent) {
        super.onDetach(detachEvent);
        Broadcaster.unregister(this);
    }

    public BynTab(BynHistoryRepository bynRepository, NppaService nppaService, UserService userService) {
        this.bynHistoryRepository = bynRepository;
        this.nppaService = nppaService;
        this.userService = userService;

        VerticalLayout tabLayout = new VerticalLayout();
        PagedTabs tabs = new PagedTabs(tabLayout);
        tabs.getContent();

        workflowCommands = nppaService.getAvailableBynWorkflowCommands();

        PagedTabUtils.addTab(initBynStatusTab(), tabs,
                "byn-status-tab-layout",
                "byn-status-tab",
                "Статус");

        PagedTabUtils.addTab(initBynLogTab(), tabs,
                "byn-log-tab-layout",
                "byn-log-tab",
                "Журнал");

        HorizontalLayout topOfTab = new HorizontalLayout();

        TextField operatingModeTf = new TextField("Режим");
        operatingModeTf.setReadOnly(true);
        operatingModeTf.addClassName("operating_mode");
        operatingModeTf.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        bynBinder.forField(operatingModeTf)
                .bindReadOnly(byn -> {
                    try {
                        if (byn != null && byn.getOperatingMode() != null) {
                            return byn.getOperatingMode().getValueUa();
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                        return "Не обрано";
                    }
                    return NppaOperatingMode.NOT_SELECTED.getValueUa();
                });
        operatingModeTf.addValueChangeListener(valueChangeEvent ->
                ConfigurationUtils.setTextAnimation(valueChangeEvent, NppaOperatingMode.class));

        operatingModeTf.setValue((nppaService.getByn().isPresent() && nppaService.getByn().get().operatingMode() != null) ?
                nppaService.getByn().get().operatingMode().getValueUa() : NppaOperatingMode.NOT_SELECTED.getValueUa()
        );

        topOfTab.addClassName("top-of-tab-byn");
        topOfTab.add(tabs, new VerticalLayout(operatingModeTf/*, toggleButton*/));
        add(topOfTab, tabLayout);

    }

    private Grid<NppaCommand> workflowCommandsGrid;
    private final List<NppaCommand> workflowCommands;

    private VerticalLayout initBynStatusTab() {
        VerticalLayout statusTab = new VerticalLayout();

        BynStatus bynStatus = new BynStatus();

        BynStatusTabFirstColumn bynStatusTabFirstColumn = new BynStatusTabFirstColumn();
        BynStatusTabSecondColumn bynStatusTabSecondColumn = new BynStatusTabSecondColumn();
        bynStatusTabSecondColumn.addClassName("byn-status-tab-second-column");
        bynStatus.add(bynStatusTabFirstColumn, bynStatusTabSecondColumn);
        bynStatus.addClassName("byn-status-tab");

        statusTab.add(bynStatus);

        return statusTab;
    }

    private static class BynStatus extends HorizontalLayout {

        public BynStatus() {
            add();
        }

    }

    private class BynStatusTabFirstColumn extends VerticalLayout {

        public BynStatusTabFirstColumn() {

            TextField connectionByn = new TextField("Зв'язок з БВН");
            connectionByn.setReadOnly(true);
            connectionByn.addClassName("connection-byn");
            connectionByn.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            bynBinder.forField(connectionByn)
                    .bindReadOnly(byn -> {
                        try {
                            if (byn != null) {
                                return byn.isConnected() ? "Норма" : "Немає зв'язку";
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "Невідомо";
                        }
                        return "Невідомо";
                    });
            connectionByn.addValueChangeListener(valueChangeEvent ->
                    ConfigurationUtils.setBooleanToTextAnimation(
                            valueChangeEvent,
                            "Норма",
                            "Немає зв'язку",
                            AnimationColor.GREEN,
                            AnimationColor.RED)
            );
            connectionByn.setValue(nppaService.getByn().isPresent() ?
                    (nppaService.getByn().get().isConnected() ? "Норма" : "Немає зв'язку") : unknownValue);

            TextField tvByn = new TextField("ТВ БВН");
            tvByn.setReadOnly(true);
            tvByn.addClassName("tv-byn");
            tvByn.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            bynBinder.forField(tvByn)
                    .bindReadOnly(byn -> {
                        try {
                            if (byn != null && byn.getTvByn() != null) {
                                return byn.getTvByn().getValueUa();
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "Невідомо";
                        }
                        return AdjacentSystemStatus.UNDEFINED.getValueUa();
                    });
            tvByn.addValueChangeListener(valueChangeEvent -> ConfigurationUtils.setTextAnimation(valueChangeEvent, BaseProperty.class));
            tvByn.setValue(nppaService.getByn().isPresent() ?
                    nppaService.getByn().get().tvByn().getValueUa() : AdjacentSystemStatus.UNDEFINED.getValueUa());

            addClassName("byn-status-tab-first-column");
            add(connectionByn, tvByn/*, bynState*/);

            VerticalLayout commands = new VerticalLayout();
            workflowCommandsGrid = new Grid<>(NppaCommand.class, false);
            workflowCommandsGrid.addThemeVariants(GridVariant.LUMO_WRAP_CELL_CONTENT);
            NppaView.setupNppaCommandsGrid(commands, workflowCommandsGrid, workflowCommands,
                    userService, nppaService, "byn-command-grid");

            add(commands);
        }
    }

    private class BynStatusTabSecondColumn extends VerticalLayout {
        public BynStatusTabSecondColumn() {
            BynStatusFirstColumn bynStatusFirstColumn = new BynStatusFirstColumn();
            bynStatusFirstColumn.addClassName("byn-availability-of-power-column");
            Span availabilityOfPower = new Span("Наявність живлення / Контроль стикування");
            availabilityOfPower.addClassName("availability-of-power");

            add(availabilityOfPower, bynStatusFirstColumn);
        }
    }

    private class BynStatusFirstColumn extends HorizontalLayout {

        public BynStatusFirstColumn() {
            BynSupplyLabel bynSupplyLabel = new BynSupplyLabel();
            BynSupplyField bynSupplyField = new BynSupplyField();
            bynSupplyLabel.addClassName("byn-supply-label");
            bynSupplyField.addClassName("byn-supply-field");

            add(bynSupplyLabel, bynSupplyField);
        }
    }

    private class BynSupplyLabel extends VerticalLayout {
        public BynSupplyLabel() {

            Span ncokLabel = new Span("НЦОК");
            ncokLabel.addClassName("ncok-label");

            Span PgVNcokLabel = new Span("Pг вих НЦОК");
            PgVNcokLabel.addClassName("pg-v-ncok-label");

            Span BuveF2Label = new Span("БУВЕ - Ф2");
            BuveF2Label.addClassName("buve-f2-label");

            Span BuveF4Label = new Span("БУВЕ - Ф4");
            BuveF4Label.addClassName("buve-f4-label");

            Span BasuOtrLeftF3Label = new Span("БАСУ ОТР1 від - Ф3");
            BasuOtrLeftF3Label.addClassName("basu-otr-left-f3-label");

            Span BasuOtrRightF3Label = new Span("БАСУ ОТР2 від - Ф3");
            BasuOtrRightF3Label.addClassName("basu-otr-right-f3-label");

            Span availabilityF1Label = new Span("Наявність Ф1");
            availabilityF1Label.addClassName("availability-f1-label");

            Span availabilityF2Label = new Span("Наявність Ф2");
            availabilityF2Label.addClassName("availability-f2-label");

            Span availabilityF3Label = new Span("Наявність Ф3");
            availabilityF3Label.addClassName("availability-f3-label");

            Span availabilityF4Label = new Span("Наявність Ф4");
            availabilityF4Label.addClassName("availability-f4-label");

            Span availabilityF5Label = new Span("Наявність Ф5");
            availabilityF5Label.addClassName("availability-f5-label");

            Span dockingControlNPPALabel = new Span("Контроль стикування НППА");
            dockingControlNPPALabel.addClassName("docking-control-nppa-label");

            Span dockingControlBasuOtrLeftLabel = new Span("Контроль стикування БАСУ ОТР1");
            dockingControlBasuOtrLeftLabel.addClassName("docking-control-basu-otr-left-label");

            Span dockingControlBasuOtrRightLabel = new Span("Контроль стикування БАСУ ОТР2");
            dockingControlBasuOtrRightLabel.addClassName("docking-control-basu-otr-right-label");

            add(ncokLabel, PgVNcokLabel, BuveF2Label, BuveF4Label, BasuOtrLeftF3Label, BasuOtrRightF3Label, availabilityF1Label,
                    availabilityF1Label, availabilityF2Label, availabilityF3Label, availabilityF4Label, availabilityF5Label,
                    dockingControlNPPALabel, dockingControlBasuOtrLeftLabel, dockingControlBasuOtrRightLabel);
        }
    }

    private class BynSupplyField extends VerticalLayout {
        public BynSupplyField() {
            TextField ncokField = new TextField();
            ncokField.setReadOnly(true);
            ncokField.addClassName("byn-field");
            ncokField.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            bynBinder.forField(ncokField)
                    .bindReadOnly(byn -> {
                        try {
                            if (byn != null) {
                                return byn.isNcok() ? "Так" : "Ні";
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "---";
                        }
                        return "---";
                    });
            ncokField.addValueChangeListener(valueChangeEvent ->
                    ConfigurationUtils.setBooleanToTextAnimation(
                            valueChangeEvent,
                            "Ні",
                            "Так",
                            AnimationColor.RED,
                            AnimationColor.GREEN)
            );
            ncokField.setValue(nppaService.getByn().isPresent() ?
                    (nppaService.getByn().get().isNcok() ? "Так" : "Ні") : "---");

            TextField rgVNcokField = new TextField();
            rgVNcokField.setReadOnly(true);
            rgVNcokField.addClassName("byn-field");
            rgVNcokField.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            bynBinder.forField(rgVNcokField)
                    .bindReadOnly(byn -> {
                        try {
                            if (byn != null) {
                                return byn.isRgOutNcok() ? "Так" : "Ні";
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "---";
                        }
                        return "---";
                    });
            rgVNcokField.addValueChangeListener(valueChangeEvent ->
                    ConfigurationUtils.setBooleanToTextAnimation(
                            valueChangeEvent,
                            "Ні",
                            "Так",
                            AnimationColor.RED,
                            AnimationColor.GREEN)
            );
            rgVNcokField.setValue(nppaService.getByn().isPresent() ?
                    (nppaService.getByn().get().isRgOutNcok() ? "Так" : "Ні") : "---");

            TextField buveF2Field = new TextField();
            buveF2Field.setReadOnly(true);
            buveF2Field.addClassName("byn-field");
            buveF2Field.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            bynBinder.forField(buveF2Field)
                    .bindReadOnly(byn -> {
                        try {
                            if (byn != null) {
                                return byn.isBuveF2() ? "Так" : "Ні";
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "---";
                        }
                        return "---";
                    });
            buveF2Field.addValueChangeListener(valueChangeEvent ->
                    ConfigurationUtils.setBooleanToTextAnimation(
                            valueChangeEvent,
                            "Ні",
                            "Так",
                            AnimationColor.RED,
                            AnimationColor.GREEN)
            );
            buveF2Field.setValue(nppaService.getByn().isPresent() ?
                    (nppaService.getByn().get().isBuveF2() ? "Так" : "Ні") : "---");

            TextField buveF4Field = new TextField();
            buveF4Field.setReadOnly(true);
            buveF4Field.addClassName("byn-field");
            buveF4Field.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            bynBinder.forField(buveF4Field)
                    .bindReadOnly(byn -> {
                        try {
                            if (byn != null) {
                                return byn.isBuveF4() ? "Так" : "Ні";
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "---";
                        }
                        return "---";
                    });
            buveF4Field.addValueChangeListener(valueChangeEvent ->
                    ConfigurationUtils.setBooleanToTextAnimation(
                            valueChangeEvent,
                            "Ні",
                            "Так",
                            AnimationColor.RED,
                            AnimationColor.GREEN)
            );
            buveF4Field.setValue(nppaService.getByn().isPresent() ?
                    (nppaService.getByn().get().isBuveF4() ? "Так" : "Ні") : "---");

            TextField basuOtrLeftF3Field = new TextField();
            basuOtrLeftF3Field.setReadOnly(true);
            basuOtrLeftF3Field.addClassName("byn-field");
            basuOtrLeftF3Field.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            bynBinder.forField(basuOtrLeftF3Field)
                    .bindReadOnly(byn -> {
                        try {
                            if (byn != null) {
                                return byn.isBasuOtr1F3() ? "Так" : "Ні";
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "---";
                        }
                        return "---";
                    });
            basuOtrLeftF3Field.addValueChangeListener(valueChangeEvent ->
                    ConfigurationUtils.setBooleanToTextAnimation(
                            valueChangeEvent,
                            "Ні",
                            "Так",
                            AnimationColor.RED,
                            AnimationColor.GREEN)
            );
            basuOtrLeftF3Field.setValue(nppaService.getByn().isPresent() ?
                    (nppaService.getByn().get().isBasuOtr1F3() ? "Так" : "Ні") : "---");

            TextField basuOtrRightF3Field = new TextField();
            basuOtrRightF3Field.setReadOnly(true);
            basuOtrRightF3Field.addClassName("byn-field");
            basuOtrRightF3Field.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            bynBinder.forField(basuOtrRightF3Field)
                    .bindReadOnly(byn -> {
                        try {
                            if (byn != null) {
                                return byn.isBasuOtr2F3() ? "Так" : "Ні";
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "---";
                        }
                        return "---";
                    });
            basuOtrRightF3Field.addValueChangeListener(valueChangeEvent ->
                    ConfigurationUtils.setBooleanToTextAnimation(
                            valueChangeEvent,
                            "Ні",
                            "Так",
                            AnimationColor.RED,
                            AnimationColor.GREEN)
            );
            basuOtrRightF3Field.setValue(nppaService.getByn().isPresent() ?
                    (nppaService.getByn().get().isBasuOtr2F3() ? "Так" : "Ні") : "---");

            TextField availabilityF1Field = new TextField();
            availabilityF1Field.setReadOnly(true);
            availabilityF1Field.addClassName("byn-field");
            availabilityF1Field.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            bynBinder.forField(availabilityF1Field)
                    .bindReadOnly(byn -> {
                        try {
                            if (byn != null) {
                                return byn.isF1() ? "Так" : "Ні";
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "---";
                        }
                        return "---";
                    });
            availabilityF1Field.addValueChangeListener(valueChangeEvent ->
                    ConfigurationUtils.setBooleanToTextAnimation(
                            valueChangeEvent,
                            "Ні",
                            "Так",
                            AnimationColor.RED,
                            AnimationColor.GREEN)
            );
            availabilityF1Field.setValue(nppaService.getByn().isPresent() ?
                    (nppaService.getByn().get().isF1() ? "Так" : "Ні") : "---");

            TextField availabilityF2Field = new TextField();
            availabilityF2Field.setReadOnly(true);
            availabilityF2Field.addClassName("byn-field");
            availabilityF2Field.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            bynBinder.forField(availabilityF2Field)
                    .bindReadOnly(byn -> {
                        try {
                            if (byn != null) {
                                return byn.isF2() ? "Так" : "Ні";
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "---";
                        }
                        return "---";
                    });
            availabilityF2Field.addValueChangeListener(valueChangeEvent ->
                    ConfigurationUtils.setBooleanToTextAnimation(
                            valueChangeEvent,
                            "Ні",
                            "Так",
                            AnimationColor.RED,
                            AnimationColor.GREEN)
            );
            availabilityF2Field.setValue(nppaService.getByn().isPresent() ?
                    (nppaService.getByn().get().isF2() ? "Так" : "Ні") : "---");

            TextField availabilityF3Field = new TextField();
            availabilityF3Field.setReadOnly(true);
            availabilityF3Field.addClassName("byn-field");
            availabilityF3Field.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            bynBinder.forField(availabilityF3Field)
                    .bindReadOnly(byn -> {
                        try {
                            if (byn != null) {
                                return byn.isF3() ? "Так" : "Ні";
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "---";
                        }
                        return "---";
                    });
            availabilityF3Field.addValueChangeListener(valueChangeEvent ->
                    ConfigurationUtils.setBooleanToTextAnimation(
                            valueChangeEvent,
                            "Ні",
                            "Так",
                            AnimationColor.RED,
                            AnimationColor.GREEN)
            );
            availabilityF3Field.setValue(nppaService.getByn().isPresent() ?
                    (nppaService.getByn().get().isF3() ? "Так" : "Ні") : "---");

            TextField availabilityF4Field = new TextField();
            availabilityF4Field.setReadOnly(true);
            availabilityF4Field.addClassName("byn-field");
            availabilityF4Field.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            bynBinder.forField(availabilityF4Field)
                    .bindReadOnly(byn -> {
                        try {
                            if (byn != null) {
                                return byn.isF4() ? "Так" : "Ні";
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "---";
                        }
                        return "---";
                    });
            availabilityF4Field.addValueChangeListener(valueChangeEvent ->
                    ConfigurationUtils.setBooleanToTextAnimation(
                            valueChangeEvent,
                            "Ні",
                            "Так",
                            AnimationColor.RED,
                            AnimationColor.GREEN)
            );
            availabilityF4Field.setValue(nppaService.getByn().isPresent() ?
                    (nppaService.getByn().get().isF4() ? "Так" : "Ні") : "---");

            TextField availabilityF5Field = new TextField();
            availabilityF5Field.setReadOnly(true);
            availabilityF5Field.addClassName("byn-field");
            availabilityF5Field.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            bynBinder.forField(availabilityF5Field)
                    .bindReadOnly(byn -> {
                        try {
                            if (byn != null) {
                                return byn.isF5() ? "Так" : "Ні";
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "---";
                        }
                        return "---";
                    });
            availabilityF5Field.addValueChangeListener(valueChangeEvent ->
                    ConfigurationUtils.setBooleanToTextAnimation(
                            valueChangeEvent,
                            "Ні",
                            "Так",
                            AnimationColor.RED,
                            AnimationColor.GREEN)
            );
            availabilityF5Field.setValue(nppaService.getByn().isPresent() ?
                    (nppaService.getByn().get().isF5() ? "Так" : "Ні") : "---");

            TextField nppaConnectionTF = new TextField();
            nppaConnectionTF.setReadOnly(true);
            nppaConnectionTF.addClassName("byn-field");
            nppaConnectionTF.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            bynBinder.forField(nppaConnectionTF)
                    .bindReadOnly(byn -> {
                        try {
                            if (byn != null) {
                                return byn.isNppaConnected() ? "Так" : "Ні";
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "---";
                        }
                        return "---";
                    });
            nppaConnectionTF.addValueChangeListener(valueChangeEvent ->
                    ConfigurationUtils.setBooleanToTextAnimation(
                            valueChangeEvent,
                            "Ні",
                            "Так",
                            AnimationColor.RED,
                            AnimationColor.GREEN)
            );
            nppaConnectionTF.setValue(nppaService.getByn().isPresent() ?
                    (nppaService.getByn().get().isNppaConnected() ? "Так" : "Ні") : "---");

            TextField basuOtrLeftConnectionTF = new TextField();
            basuOtrLeftConnectionTF.setReadOnly(true);
            basuOtrLeftConnectionTF.addClassName("byn-field");
            basuOtrLeftConnectionTF.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            bynBinder.forField(basuOtrLeftConnectionTF)
                    .bindReadOnly(byn -> {
                        try {
                            if (byn != null) {
                                return byn.isBasuOtr1Connected() ? "Так" : "Ні";
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "---";
                        }
                        return "---";
                    });
            basuOtrLeftConnectionTF.addValueChangeListener(valueChangeEvent ->
                    ConfigurationUtils.setBooleanToTextAnimation(
                            valueChangeEvent,
                            "Ні",
                            "Так",
                            AnimationColor.RED,
                            AnimationColor.GREEN)
            );
            basuOtrLeftConnectionTF.setValue(nppaService.getByn().isPresent() ?
                    (nppaService.getByn().get().isBasuOtr1Connected() ? "Так" : "Ні") : "---");

            TextField basuOtrRightConnectionTF = new TextField();
            basuOtrRightConnectionTF.setReadOnly(true);
            basuOtrRightConnectionTF.addClassName("byn-field");
            basuOtrRightConnectionTF.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            bynBinder.forField(basuOtrRightConnectionTF)
                    .bindReadOnly(byn -> {
                        try {
                            if (byn != null) {
                                return byn.isBasuOtr2Connected() ? "Так" : "Ні";
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "---";
                        }
                        return "---";
                    });
            basuOtrRightConnectionTF.addValueChangeListener(valueChangeEvent ->
                    ConfigurationUtils.setBooleanToTextAnimation(
                            valueChangeEvent,
                            "Ні",
                            "Так",
                            AnimationColor.RED,
                            AnimationColor.GREEN)
            );
            basuOtrRightConnectionTF.setValue(nppaService.getByn().isPresent() ?
                    (nppaService.getByn().get().isBasuOtr2Connected() ? "Так" : "Ні") : "---");

            add(ncokField, rgVNcokField, buveF2Field, buveF4Field, basuOtrLeftF3Field, basuOtrRightF3Field, availabilityF1Field,
                    availabilityF1Field, availabilityF2Field, availabilityF3Field, availabilityF4Field, availabilityF5Field,
                    nppaConnectionTF, basuOtrLeftConnectionTF, basuOtrRightConnectionTF);

        }
    }

    private class BynCommandsTab extends VerticalLayout {
        public BynCommandsTab() {
            VerticalLayout tabPanel = new VerticalLayout();
            tabPanel.setWidth("845px");
            tabPanel.setMaxWidth("845px");
            tabPanel.addClassName("tab-panel-byn");

            //commands
            HorizontalLayout commandsLayoutH = new HorizontalLayout();
            commandsLayoutH.addClassName("commands-layout-h");
            commandsLayoutH.setWidthFull();
            VerticalLayout commandsLayout = new VerticalLayout();
            commandsLayout.addClassName("commands-layout-v");
            commandsLayout.setWidth("825px");
            commandsLayoutH.add(commandsLayout);
            tabPanel.add(commandsLayoutH);

            Span commandsCaption = new Span("Перелік команд");
            commandsCaption.addClassName("commands-caption");
            commandsLayout.add(commandsCaption);

            setupBynCommandsGrid(commandsLayout);

            add(tabPanel);
        }

        private void setupBynCommandsGrid(VerticalLayout tabPanel) {
            Grid<PpoCommand> commandsGrid = new Grid<>(PpoCommand.class, false);
            commandsGrid.addThemeVariants(GridVariant.LUMO_WRAP_CELL_CONTENT);

            Grid.Column<PpoCommand> caption = commandsGrid
                    .addColumn(PpoCommand::getCaption).setHeader("Команда").setTextAlign(ColumnTextAlign.START)
                    .setFlexGrow(0).setWidth("280x");

            Grid.Column<PpoCommand> sentence = commandsGrid
                    .addColumn(PpoCommand::getCommand).setHeader("Відправити").setTextAlign(ColumnTextAlign.END);

            sentence.setVisible(false);

            Grid.Column<PpoCommand> sendColumn = commandsGrid.addComponentColumn(ppoCommand -> {

                Button sendBt = new Button("Відправити");
                sendBt.addClassName("send-sentence-button");
                sendBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY, ButtonVariant.LUMO_SMALL);
                sendBt.getStyle().set("background-color", "#4F4F4F");

                return sendBt;
            }).setHeader("Відправити");

            tabPanel.add(commandsGrid);
            tabPanel.setPadding(false);
        }
    }

    private VerticalLayout initBynLogTab() {
        VerticalLayout logTab = new LogTab(bynHistoryRepository, "byn-log-tab");

        return logTab;
    }

    private NppaEvent previousNppaEvent;

    @Override
    public void receiveBroadcast(Object incomingEvent) {
        if (!(incomingEvent instanceof NppaEvent eventUnderProcessing)) {
            log.debug("expected " + NppaEvent.class.getSimpleName() + "but received unexpected event at " + this.getClassName());
            return;
        }
        if (previousNppaEvent != null && previousNppaEvent.equals(incomingEvent)) {
            return;
        }
        if (eventUnderProcessing.getNppa() == null) {
            log.error("received event without Nppa entity " + eventUnderProcessing + " " + this);
            return;
        }
        if (eventUnderProcessing.getNppa().getByn() == null) {
            log.error("received NPPA event without Byn entity " + eventUnderProcessing + " " + this);
            return;
        }

        getUI().get().access(() -> bynBinder.readBean(eventUnderProcessing.getNppa().getByn()));

        previousNppaEvent = eventUnderProcessing;
    }
}
