package com.deb.spl.control.views.automotion;


import com.deb.spl.control.views.adjacentSystems.utils.AnimationColor;
import com.vaadin.flow.component.AbstractSinglePropertyField;
import com.vaadin.flow.component.HasStyle;
import com.vaadin.flow.component.Tag;
import com.vaadin.flow.component.html.Image;
import com.vaadin.flow.component.html.Span;
import lombok.Getter;


@Getter
@Tag("div")
public class Badge extends AbstractSinglePropertyField<Badge, String> implements HasStyle {
    private static final long serialVersionUID = 123l;
    private Image image;
    private Span text;
//
//    private final AnimationColor highlightedColor;
//    private final AnimationColor disabledColor;
    private final String baseClassName;

    public AnimationColor getAnimationColor() {
        String className = image.getClassName().replace(baseClassName, "").trim();
        return AnimationColor.valueOf(className);
    }

    public Badge(Image image,
                 String baseClassName,
                 AnimationColor value,
                 AnimationColor highlightedColor,
                 AnimationColor disabledColor) {
        super("value", value.getText(), false);

        this.getElement().appendChild(image.getElement());
        this.image = image;
        this.baseClassName = baseClassName;
//        this.highlightedColor = highlightedColor;
//        this.disabledColor = disabledColor;

        image.setClassName(baseClassName + "-" + disabledColor);


      /*  this.text = new Span();
        text.setText(baseClassName + "-" + disabledColor);
        this.getElement().appendChild(text.getElement());*/
    }

    @Override
    public void setValue(String value) {
        super.setValue(value);
//        text.setText(String.valueOf(getValue()));
        image.setClassName(baseClassName + "-" + value);
        /*text.setText(baseClassName + "-" + value);*/
    }
}
