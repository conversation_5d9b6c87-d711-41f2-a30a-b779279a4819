package com.deb.spl.control.views.events;

import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.suto.Suto;
import lombok.*;

import java.util.Optional;

@NoArgsConstructor
@Getter
@Setter
@EqualsAndHashCode
@Builder
public class SutoEvent implements AdjacentSystemEvent {
    Suto suto;
    Object source;
    AdjacentSystemUpdateEventType eventType;

    /**
     * @param suto      changed object
     * @param source    event source
     * @param eventType @{@link AdjacentSystemUpdateEventType}
     */
    public SutoEvent(Suto suto, Object source, AdjacentSystemUpdateEventType eventType) {
        this.suto = suto;
        this.source = source;
        this.eventType = eventType;
    }

    @Override
    public AdjacentSystemType getSystemType() {
        return suto.getAdjacentSystemType();
    }

    @Override
    public Optional<AdjacentSystem> getEntity() {
        return Optional.ofNullable(suto);
    }
}
