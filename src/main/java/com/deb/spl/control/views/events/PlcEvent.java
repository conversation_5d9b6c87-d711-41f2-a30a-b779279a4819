package com.deb.spl.control.views.events;

import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.asku.Plc;
import lombok.*;

import java.util.Optional;

@NoArgsConstructor
@Getter
@Setter
@EqualsAndHashCode
@Builder
public class PlcEvent implements AdjacentSystemEvent{
    Plc plc;
    Object source;
    AdjacentSystemUpdateEventType eventType;

    /**
     * @param plc changed object
     * @param source event source
     * @param eventType @{@link AdjacentSystemUpdateEventType}
     */
    public PlcEvent(Plc plc, Object source, AdjacentSystemUpdateEventType eventType) {
        this.plc = plc;
        this.source = source;
        this.eventType = eventType;
    }

    public PlcEvent(PlcEvent prototype) {
        this.plc=prototype.getPlc();
        this.source=prototype.getSource();
        this.eventType=prototype.getEventType();
    }

    @Override
    public AdjacentSystemType getSystemType() {
        return plc.getAdjacentSystemType();
    }

    @Override
    public Optional<AdjacentSystem> getEntity() {
        return Optional.ofNullable(plc);
    }
}
