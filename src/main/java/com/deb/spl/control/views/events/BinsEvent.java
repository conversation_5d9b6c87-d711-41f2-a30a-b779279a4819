package com.deb.spl.control.views.events;

import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.Bins;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Optional;

@NoArgsConstructor
@Getter
public class BinsEvent implements AdjacentSystemEvent {
    Bins bins;
    Object source;
    AdjacentSystemUpdateEventType eventType;

    /**
     * @param bins      changed object
     * @param source    event source
     * @param eventType @{@link AdjacentSystemUpdateEventType}
     */
    public BinsEvent(Bins bins, Object source, AdjacentSystemUpdateEventType eventType) {
        this.bins = bins;
        this.source = source;
        this.eventType = eventType;
    }

    @Override
    public AdjacentSystemType getSystemType() {
        return bins.getAdjacentSystemType();
    }

    @Override
    public Optional<AdjacentSystem> getEntity() {
        return Optional.ofNullable(bins);
    }


}
