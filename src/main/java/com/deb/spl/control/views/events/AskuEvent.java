package com.deb.spl.control.views.events;

import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.asku.Asku;
import com.deb.spl.control.data.asku.Readiness;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Optional;

@NoArgsConstructor
@Getter
@Setter
@Builder
public class AskuEvent implements AdjacentSystemEvent{
    Asku asku;
    Object source;
    AdjacentSystemUpdateEventType eventType;

    /**
     * @param asku changed object
     * @param source event source
     * @param eventType @{@link AdjacentSystemUpdateEventType}
     */
    public AskuEvent(Asku asku, Object source, AdjacentSystemUpdateEventType eventType) {
        this.asku = asku;
        this.source = source;
        this.eventType = eventType;
    }

    @Override
    public AdjacentSystemType getSystemType() {
        return asku.getAdjacentSystemType();
    }

    @Override
    public Optional<AdjacentSystem> getEntity() {
        return Optional.ofNullable(asku);
    }


    public Optional<Readiness> getSplReadiness(){
        if(asku==null){
            return Optional.empty();
        }

        return Optional.ofNullable(asku.getSplReadiness());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof AskuEvent askuEvent)) return false;

        if (getAsku() != null ? !getAsku().equals(askuEvent.getAsku()) : askuEvent.getAsku() != null) return false;
        if (getSource() != null ? !getSource().equals(askuEvent.getSource()) : askuEvent.getSource() != null)
            return false;
        return getEventType() == askuEvent.getEventType();
    }

    @Override
    public int hashCode() {
        int result = getAsku() != null ? getAsku().hashCode() : 0;
        result = 31 * result + (getSource() != null ? getSource().hashCode() : 0);
        result = 31 * result + (getEventType() != null ? getEventType().hashCode() : 0);
        return result;
    }
}
