package com.deb.spl.control.views.events;

import lombok.*;

import java.time.LocalDateTime;

@NoArgsConstructor
@Getter
@Setter
@EqualsAndHashCode
@Builder
public class DateTimeUpdateEvent implements SystemEvent{
    LocalDateTime dateTime;
    Object source;
    AdjacentSystemUpdateEventType eventType;

    /**
     * @param dateTime changed object
     * @param source event source
     * @param eventType @{@link AdjacentSystemUpdateEventType}
     */
    public DateTimeUpdateEvent(LocalDateTime dateTime , Object source, AdjacentSystemUpdateEventType eventType) {
        this.dateTime = dateTime;
        this.source = source;
        this.eventType = eventType;
    }

}
