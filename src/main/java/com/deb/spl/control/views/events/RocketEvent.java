package com.deb.spl.control.views.events;

import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.asku.Rocket;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Optional;

@Getter
@Setter
@Builder
public class RocketEvent implements AdjacentSystemEvent {
    private final Rocket rocket;
    @Builder.Default
    private LocalDateTime timeStamp=LocalDateTime.MIN;
    private boolean isLeft;
    private Object source;
    @Builder.Default
    private AdjacentSystemUpdateEventType eventType=AdjacentSystemUpdateEventType.UNDEFINED;

    public RocketEvent(@NotNull Rocket rocket,
                       LocalDateTime timeStamp,
                       boolean isLeft,
                       Object source,
                       AdjacentSystemUpdateEventType eventType) {
        this.rocket = rocket;
        this.timeStamp = timeStamp != null ? timeStamp : LocalDateTime.MIN;
        this.isLeft = isLeft;
        this.source = source;
        this.eventType = eventType != null ? eventType : AdjacentSystemUpdateEventType.UNDEFINED;
    }

    public RocketEvent(@NotNull Rocket rocket, LocalDateTime timeStamp, Object source, boolean isLeft) {
        this(rocket, timeStamp, isLeft, source, AdjacentSystemUpdateEventType.UNDEFINED);
    }

    public RocketEvent(@NotNull Rocket rocket, Object source, boolean isLeft) {
        this(rocket, LocalDateTime.now(), isLeft, source, AdjacentSystemUpdateEventType.UNDEFINED);
    }

    @Override
    public AdjacentSystemType getSystemType() {
        return AdjacentSystemType.ASKU;
    }

    @Override
    public Optional<AdjacentSystem> getEntity() {
        return Optional.empty();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof RocketEvent that)) return false;

        if (getRocket() != null ? !getRocket().equals(that.getRocket()) : that.getRocket() != null)
            return false;
        if (!getTimeStamp().withNano(0).equals(that.getTimeStamp().withNano(0))) return false;
        if (getSource() != null ? !getSource().equals(that.getSource()) : that.getSource() != null) return false;
        return getEventType() == that.getEventType();
    }

    @Override
    public int hashCode() {
        int result = getRocket() != null ? getRocket().hashCode() : 0;
        result = 31 * result + getTimeStamp().hashCode();
        result = 31 * result + (getSource() != null ? getSource().hashCode() : 0);
        result = 31 * result + getEventType().hashCode();
        return result;
    }
}
