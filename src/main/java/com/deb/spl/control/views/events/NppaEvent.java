package com.deb.spl.control.views.events;

import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.nppa.Nppa;
import lombok.*;

import java.util.Optional;

@NoArgsConstructor
@Getter
@Setter
@EqualsAndHashCode
@Builder
public class NppaEvent implements AdjacentSystemEvent {
    Nppa nppa;
    Object source;
    AdjacentSystemUpdateEventType eventType;

    /**
     * @param nppa      changed object
     * @param source    event source
     * @param eventType @{@link AdjacentSystemUpdateEventType}
     */
    public NppaEvent(Nppa nppa, Object source, AdjacentSystemUpdateEventType eventType) {
        this.nppa = nppa;
        this.source = source;
        this.eventType = eventType;
    }

    @Override
    public AdjacentSystemType getSystemType() {
        return nppa.getAdjacentSystemType();
    }

    @Override
    public Optional<AdjacentSystem> getEntity() {
        return Optional.ofNullable(nppa);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof NppaEvent nppaEvent)) return false;

        if (getNppa() != null ? !getNppa().equals(nppaEvent.getNppa()) : nppaEvent.getNppa() != null) return false;
        if (getSource() != null ? !getSource().equals(nppaEvent.getSource()) : nppaEvent.getSource() != null)
            return false;
        return getEventType() == nppaEvent.getEventType();
    }

    @Override
    public int hashCode() {
        int result = getNppa() != null ? getNppa().hashCode() : 0;
        result = 31 * result + (getSource() != null ? getSource().hashCode() : 0);
        result = 31 * result + (getEventType() != null ? getEventType().hashCode() : 0);
        return result;
    }
}
