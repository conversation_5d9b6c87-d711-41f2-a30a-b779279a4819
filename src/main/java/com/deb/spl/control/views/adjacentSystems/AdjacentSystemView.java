package com.deb.spl.control.views.adjacentSystems;

import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;
import com.deb.spl.control.data.asku.RocketMapper;
import com.deb.spl.control.service.*;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.service.asku.PlcService;
import com.deb.spl.control.views.MainLayout;
import com.deb.spl.control.views.adjacentSystems.utils.AnimationColor;
import com.deb.spl.control.views.adjacentSystems.utils.StatusToColorMapper;
import com.vaadin.flow.component.AbstractField;
import com.vaadin.flow.component.html.Span;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.Arrays;

@PageTitle("Сатус систем")
@Route(value = "adjacent-systems", layout = MainLayout.class)
@Slf4j
public class AdjacentSystemView extends HorizontalLayout {

    @Getter
    private final SystemsInfo systemsInfo;
    @Getter
    VerticalLayout systemLayout;
    private final NavigationTab navTab;
    private final NppaService nppaService;

    public AdjacentSystemView(MsuService msuService,
                              PpoService ppoService,
                              SutoService sutoService,
                              BinsService binsService,
                              SaeService saeService,
                              AskuService askuService,
                              PlcService plcService,
                              String tabName,
                              RocketMapper rocketMapper,
                              NppaService nppaService,
                              CcvCommunicationService ccvCommunicationService) {
        this.nppaService = nppaService;

        Span splNameSpan = new Span("Назва");
        splNameSpan.addClassName("spl-name-out");
        splNameSpan.setText(tabName);

        this.systemsInfo = new SystemsInfo(msuService,
                binsService,
                ppoService,
                sutoService,
                saeService,
                askuService,
                plcService,
                this.nppaService,
                rocketMapper,
                ccvCommunicationService);
        this.navTab = new NavigationTab();
        systemLayout = new VerticalLayout();
        systemLayout.add(navTab);
        systemLayout.add(splNameSpan);
        add(systemsInfo, systemLayout);
    }

    void setTextAnimation(AbstractField.ComponentValueChangeEvent<TextField, String> valueChangeEvent, Class aClass) {
        if (!AdjacentSystemWithDescriptionUa.class.isAssignableFrom(aClass)) {
            return;
        }

        if (valueChangeEvent == null) {
            return;
        }

        TextField source = valueChangeEvent.getSource();
        AnimationColor color = StatusToColorMapper.fromElementsStatuses(
                valueChangeEvent.getValue(), aClass);

        try {
            source.setClassName(updateClassNameValue(source.getClassName(), source.getClass().getSimpleName(), color.getText()));
        } catch (Exception e) {
            log.error(e.getMessage() + "\t" + Arrays.toString(e.getStackTrace()));
        }
    }

    String updateClassNameValue(String classNameToUpdate,
                                @NotNull String baseName,
                                @NotNull String animationClassName) {
        if (classNameToUpdate == null || classNameToUpdate.isBlank()) {
            return (baseName != null && !baseName.isBlank()) ? baseName + "-" + animationClassName : animationClassName;
        }

        String pattern = baseName + "-(red|green|yellow|grey|undefined)";

        return StringUtils.trim(RegExUtils.removeAll(classNameToUpdate, pattern)) +
               " " + baseName + "-" + animationClassName;
    }
}
