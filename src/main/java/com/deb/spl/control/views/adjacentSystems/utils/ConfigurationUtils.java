package com.deb.spl.control.views.adjacentSystems.utils;

import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;
import com.deb.spl.control.data.asku.Rocket;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.views.automotion.Badge;
import com.deb.spl.control.views.events.RocketEvent;
import com.vaadin.flow.component.AbstractField.ComponentValueChangeEvent;
import com.vaadin.flow.component.Key;
import com.vaadin.flow.component.Text;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.html.Div;
import com.vaadin.flow.component.html.Image;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.notification.NotificationVariant;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.component.textfield.TextFieldVariant;
import com.vaadin.flow.data.binder.Binder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.Arrays;


@Slf4j
public class ConfigurationUtils {

    public static void setTextAnimation(ComponentValueChangeEvent<TextField, String> valueChangeEvent, Class aClass) {
        if (!AdjacentSystemWithDescriptionUa.class.isAssignableFrom(aClass)) {
            return;
        }
        if (valueChangeEvent == null) {
            return;
        }
        TextField source = valueChangeEvent.getSource();
        AnimationColor color = StatusToColorMapper.fromElementsStatuses(
                valueChangeEvent.getValue(), aClass);

        try {
            source.setClassName(updateClassNameValue(source.getClassName(), source.getClass().getSimpleName(), color.getText()));
        } catch (Exception e) {
            log.error(e.getMessage() + "\t" + Arrays.toString(e.getStackTrace()));
        }
    }

    public static String updateClassNameValue(String classNameToUpdate,
                                              @NotNull String baseName,
                                              @NotNull String animationClassName) {
        if (classNameToUpdate == null || classNameToUpdate.isBlank()) {
            return (baseName != null && !baseName.isBlank()) ? baseName + "-" + animationClassName : animationClassName;
        }
        String pattern = baseName + "-(red|green|yellow|grey|undefined)";

        return StringUtils.trim(RegExUtils.removeAll(classNameToUpdate, pattern)) +
               " " + baseName + "-" + animationClassName;
    }


    public static void setBooleanToTextAnimation(ComponentValueChangeEvent<TextField, String> valueChangeEvent,
                                                 @NotNull String falseSubstitution,
                                                 @NotNull String trueSubstitution,
                                                 @NotNull AnimationColor falseColor,
                                                 @NotNull AnimationColor trueColor) {
        TextField source = valueChangeEvent.getSource();
        AnimationColor color = AnimationColor.UNDEFINED;
        if (valueChangeEvent.getValue().equals(falseSubstitution)) {
            color = falseColor;
        } else if (valueChangeEvent.getValue().equals(trueSubstitution)) {
            color = trueColor;
        }

        try {
            source.setClassName(updateClassNameValue(source.getClassName(), source.getClass().getSimpleName(), color.getText()));
        } catch (Exception e) {
            log.error(e.getMessage() + "\t" + Arrays.toString(e.getStackTrace()));
        }
    }


    /**
     * @param captionText notification caption display text
     * @param payload     info to display in body
     * @param duration    delay before notification close. 0 or negative to disable closing
     * @param withButton  optional close button
     * @return
     */
    public static Notification getErrorNotification(@NotNull String captionText,
                                                    @NotNull String payload,
                                                    int duration,
                                                    boolean withButton) {
        return getErrorNotification(captionText, payload, duration, withButton, null);
    }

    public static Notification getErrorNotification(@NotNull String captionText,
                                                    @NotNull String payload,
                                                    int duration,
                                                    boolean withButton,
                                                    Button button) {
        Notification notification = new Notification();
        notification.addClassName("id-validation-msg-notification");
        notification.addThemeVariants(NotificationVariant.LUMO_ERROR);
        notification.setPosition(Notification.Position.MIDDLE);

        if (duration > 0) {
            notification.setDuration(duration);
        }

        Div caption = new Div(new Text(captionText));
        caption.getStyle().set("font-weight", "bold");
        Div mainText = new Div(new Text(payload));

        VerticalLayout layout = new VerticalLayout(caption, mainText);

        if (withButton) {
            notification.setDuration(notification.getDuration());

            Button closeButton = button != null ? button : new Button("Прочитано");
            closeButton.getElement().setAttribute("aria-label", "Close");
            closeButton.addClassName("close-button-msg");
            closeButton.addClickListener(event -> {
                notification.close();
            });

            closeButton.setAutofocus(true);
            closeButton.addClickShortcut(Key.SPACE);


            layout.add(closeButton);
        }

        notification.add(layout);

        return notification;
    }

    public static Badge setUpBadge(Image image,
                                   AnimationColor highlightedColor,
                                   AnimationColor disabledColor,
                                   String baseClassName,
                                   AnimationColor initialValue) {
        Badge badge = new Badge(image, baseClassName, initialValue, highlightedColor, disabledColor);
        badge.setValue(initialValue.getText());

        return badge;
    }

    public static Badge setUpBadge(String baseClassName, AnimationColor initialValue) {
        final Image baseImage = new Image("images/Vector1.svg", "check");
        return setUpBadge(baseImage,
                AnimationColor.GREEN,
                AnimationColor.GREY,
                baseClassName,
                initialValue);
    }

    public static HorizontalLayout buildRocketSensorsLayout(AskuService askuService,
                                                            String textFieldClassName,
                                                            Binder<RocketEvent> rocketBinder,
                                                            boolean isLeft,
                                                            String label) {
        return buildRocketSensorsLayout(askuService, textFieldClassName, rocketBinder, isLeft, label, "");
    }

    public static HorizontalLayout buildRocketSensorsLayout(AskuService askuService,
                                                            String textFieldClassName,
                                                            Binder<RocketEvent> rocketBinder,
                                                            boolean isLeft,
                                                            String label,
                                                            String initialValue) {
        HorizontalLayout layout = new HorizontalLayout();
        layout.addClassName("sensors-layout");

        TextField basuSensorValueTf = new TextField(label);
        basuSensorValueTf.setReadOnly(true);
        basuSensorValueTf.addClassName(textFieldClassName);
        basuSensorValueTf.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        basuSensorValueTf.addValueChangeListener(value -> {
            try {
                if (value == null) {
                    basuSensorValueTf.removeClassName("TextField-" + AnimationColor.RED.getText());
                    basuSensorValueTf.removeClassName("TextField-" + AnimationColor.GREEN.getText());
                    basuSensorValueTf.removeClassName("TextField-" + AnimationColor.YELLOW.getText());
                }

                double temperature = Double.parseDouble(value.getValue());
                basuSensorValueTf.removeClassName("TextField-" + AnimationColor.RED.getText());
                basuSensorValueTf.removeClassName("TextField-" + AnimationColor.YELLOW.getText());
                basuSensorValueTf.removeClassName("TextField-" + AnimationColor.GREEN.getText());
                switch (askuService.matchMeasureLimit(temperature)) {
                    case HI -> basuSensorValueTf.addClassName("TextField-" + AnimationColor.YELLOW.getText());
                    case HI_HI -> basuSensorValueTf.addClassName("TextField-" + AnimationColor.RED.getText());
                }
            } catch (NumberFormatException e) {
                log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
            }
        });

        rocketBinder.forField(basuSensorValueTf)
                .bindReadOnly(event -> {
                    if (event == null || event.getRocket() == null) {
                        return "";
                    }
                    if(event.isLeft() != isLeft){
                        return basuSensorValueTf.getValue();
                    }
                    return String.valueOf(event.getRocket().getSensorTemperature());
                });
        if (initialValue != null && !initialValue.isBlank()) {
            basuSensorValueTf.setValue(initialValue);
        }

        layout.add(basuSensorValueTf);
        return layout;
    }
}
