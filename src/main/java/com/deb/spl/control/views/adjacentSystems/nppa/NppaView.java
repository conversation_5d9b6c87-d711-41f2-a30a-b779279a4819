package com.deb.spl.control.views.adjacentSystems.nppa;


import com.deb.spl.control.data.asku.RocketMapper;
import com.deb.spl.control.data.nppa.CommandValidationResult;
import com.deb.spl.control.data.nppa.NppaCommand;
import com.deb.spl.control.repository.nppa.BynHistoryRepository;
import com.deb.spl.control.repository.nppa.NcokHistoryRepository;
import com.deb.spl.control.service.*;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.service.asku.PlcService;
import com.deb.spl.control.views.MainLayout;
import com.deb.spl.control.views.rocket.RocketFormDataView;
import com.deb.spl.control.views.rocket.RocketInitialDataLayout;
import com.deb.spl.control.views.adjacentSystems.AdjacentSystemView;
import com.deb.spl.control.views.adjacentSystems.utils.ConfigurationUtils;
import com.vaadin.flow.component.Html;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.dialog.Dialog;
import com.vaadin.flow.component.grid.ColumnTextAlign;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.grid.GridVariant;
import com.vaadin.flow.component.html.Span;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.vaadin.tabs.PagedTabs;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@PageTitle("Сатус систем | НППА")
@Route(value = "adjacent-systems/nppa", layout = MainLayout.class)
public class NppaView extends AdjacentSystemView {

    private final NppaService nppaService;
    private final RocketInitialDataLayout rocketInitialDataLayoutLeft;

    private final RocketInitialDataLayout rocketInitialDataLayoutRight;

    private final RocketFormDataView rocketFormDataViewLeft;

    private final RocketFormDataView rocketFormDataViewRight;

    public NppaView(MsuService msuService,
                    PpoService ppoService,
                    SutoService sutoService,
                    BinsService binsService,
                    SaeService saeService,
                    NppaService nppaService,
                    AskuService askuService,
                    PlcService plcService,
                    UserService userService,
                    NcokHistoryRepository ncokHistoryRepository,
                    BynHistoryRepository bynHistoryRepository,
                    RocketMapper rocketMapper,
                    CcvCommunicationService ccvCommunicationService,
                    @Value("${view.ncok-command-status-grid.enale}")
                    boolean showNcokCommandStatusGrid) {
        super(msuService,
                ppoService,
                sutoService,
                binsService,
                saeService,
                askuService,
                plcService, "Наземна перевірочно-пускова апаратура",
                rocketMapper,
                nppaService,
                ccvCommunicationService);
        this.nppaService = nppaService;

        VerticalLayout tabLayout = new VerticalLayout();
        PagedTabs tabs = new PagedTabs(tabLayout);
        tabs.getContent();
        tabLayout.addClassName("tab-layout-nppa");

        VerticalLayout ncokLayout = new NcokTab(ncokHistoryRepository, this.nppaService, userService,showNcokCommandStatusGrid);
        VerticalLayout ncokTabLayout = new VerticalLayout();
        ncokTabLayout.setWidthFull();
        ncokTabLayout.addClassName("command-tab-ncok");
        ncokTabLayout.setWidth("100%");
        ncokTabLayout.setMaxWidth("100%");
        ncokTabLayout.add(ncokLayout);//commands class
        ncokLayout.addClassName("ncok-tab");
        String ncokTabCaption = "НЦОК";

        tabs.add(ncokTabCaption, ncokTabLayout, false);

        VerticalLayout bynLayout = new BynTab(bynHistoryRepository, nppaService, userService);
        VerticalLayout bynTabLayout = new VerticalLayout();
        bynTabLayout.setWidthFull();
        bynTabLayout.addClassName("command-tab-byn");
        bynTabLayout.setWidth("100%");
        bynTabLayout.setMaxWidth("100%");
        bynTabLayout.add(bynLayout);//commands class
        bynLayout.addClassName("byn-tab");
        String bynTabCaption = "БВН";
        tabs.add(bynTabCaption, bynTabLayout, false);

        rocketInitialDataLayoutLeft = new RocketInitialDataLayout(true,
                true,
                askuService,
                rocketMapper,
                false,
                "initial-data-latitude",
                "initial-data-altitude",
                "initial-data-longitude",
                "initial-data-inclination-angle",
                "initial-data-readiness",
                "initial-data-time-stamp",
                "otr-vd-layout",
                "change-initial-data",
                "launch-planned-time",
                "order-valid-until");
        rocketInitialDataLayoutLeft.addClassName("rocket-initial-data-layout-left");
        VerticalLayout otrTabLeft = new VerticalLayout();
        otrTabLeft.add(new Span("Дані по цілі для ОТР №1"), rocketInitialDataLayoutLeft.buildView());
        rocketInitialDataLayoutRight = new RocketInitialDataLayout(false,
                true,
                askuService,
                rocketMapper,
                false,
                "initial-data-latitude",
                "initial-data-altitude",
                "initial-data-longitude",
                "initial-data-inclination-angle",
                "initial-data-readiness",
                "initial-data-time-stamp",
                "otr-vd-layout",
                "change-initial-data",
                "launch-planned-time",
                "order-valid-until");
        rocketInitialDataLayoutRight.addClassName("rocket-initial-data-layout-right");
        VerticalLayout otrTabRight = new VerticalLayout();
        otrTabRight.add(new Span("Дані по цілі для ОТР №2"), rocketInitialDataLayoutRight.buildView());
        HorizontalLayout otrTabLayout = new HorizontalLayout();
        otrTabLayout.setWidthFull();
        otrTabLayout.addClassName("command-tab-otr");
        otrTabLayout.setWidth("100%");
        otrTabLayout.setMaxWidth("100%");

        otrTabLayout.add(otrTabLeft, otrTabRight);//commands class*
        otrTabLeft.addClassName("otr-tab");
        otrTabRight.addClassName("otr-tab");
        String otrTabCaption = "ВД на Пуск";
        tabs.add(otrTabCaption, otrTabLayout, false);


        HorizontalLayout meansOfInjury = new HorizontalLayout();
        VerticalLayout idTabLayout = new VerticalLayout();
        idTabLayout.setWidthFull();
        idTabLayout.addClassName("command-tab-id");
        idTabLayout.setWidth("100%");
        idTabLayout.setMaxWidth("100%");
        idTabLayout.add(meansOfInjury);//commands class
        meansOfInjury.addClassName("means-of-injury");
        VerticalLayout formDataHeaderLeft = new VerticalLayout();
        VerticalLayout formDataHeaderRight = new VerticalLayout();
        String idTabCaption = "Формулярні дані";
        tabs.add(idTabCaption, idTabLayout, false);

        VerticalLayout tlKeysLayout = new TlKeysTab(askuService);
        VerticalLayout tlKeysTab = new VerticalLayout();
        tlKeysTab.setWidthFull();
        tlKeysTab.addClassName("command-tab-byn");
        tlKeysTab.setWidth("863px");
        tlKeysTab.setMaxWidth("863px");
        tlKeysTab.add(tlKeysLayout);//commands class

        String tlKeysTabCaption = "TL ключі";
        tabs.add(tlKeysTabCaption, tlKeysLayout, false);

        rocketFormDataViewLeft = new RocketFormDataView(askuService, true, "rocket-form-data-view");
        VerticalLayout formDataLeft = rocketFormDataViewLeft.buildView();
        Span formDataLaunchLeft = new Span("ОТР №1");
        rocketFormDataViewRight = new RocketFormDataView(askuService, false, "rocket-form-data-view");
        VerticalLayout formDataRight = rocketFormDataViewRight.buildView();
        Span formDataLaunchRight = new Span("ОТР №2");

        formDataHeaderLeft.add(formDataLaunchLeft, formDataLeft);
        formDataHeaderLeft.addClassName("form-data-header-left");
        formDataHeaderRight.add(formDataLaunchRight, formDataRight);
        formDataHeaderRight.addClassName("form-data-header-right");
        meansOfInjury.add(formDataHeaderLeft, formDataHeaderRight);

        getSystemLayout().add(tabs, tabLayout);
        getSystemLayout().addClassName("system-layout-nppa");
        getSystemLayout().setWidth("69.5%");
        getSystemLayout().setMaxWidth("69.5%");
    }

    static void setupNppaCommandsGrid(@NotNull VerticalLayout tabPanel,
                                      @NotNull Grid<NppaCommand> commandsGrid,
                                      @NotNull List<NppaCommand> commandsList,
                                      @NotNull UserService userService,
                                      @NotNull NppaService nppaService,
                                      @NotBlank String gridClassName) {

        Grid.Column<NppaCommand> caption = commandsGrid
                .addColumn(NppaCommand::getCaption).setHeader(new Html("<b>Команда</b>")).setTextAlign(ColumnTextAlign.START)
                .setFlexGrow(1).setAutoWidth(true);

        Grid.Column<NppaCommand> sentence = commandsGrid
                .addColumn(NppaCommand::getCommand).setHeader("Відправити").setTextAlign(ColumnTextAlign.END);

        sentence.setVisible(false);

        Grid.Column<NppaCommand> sendColumn = commandsGrid.addComponentColumn(nppaCommand -> {
            Button sendBt = new Button("Відправити");
            sendBt.addClassName("send-sentence-button");
            sendBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY, ButtonVariant.LUMO_SMALL);
            sendBt.getStyle().set("background-color", "#4F4F4F");

            sendBt.addClickListener(event -> {
                NppaCommand generatedCommand = NppaCommand.builder()
                        .command(nppaCommand.getCommand())
                        .caption(nppaCommand.getCaption())
                        .commandState(nppaCommand.getCommandState())
                        .adjacentSystem(nppaCommand.getAdjacentSystem())
                        .generationTime(LocalDateTime.now())
                        .originator(userService.getUserName())
                        .build();

                Dialog dialog = new Dialog();

                CommandValidationResult commandValidationResult = nppaService.validateCommand(generatedCommand);

                if (commandValidationResult.isValid()) {
                    dialog.addClassName("dialog-nppa");

                    dialog.setHeaderTitle("Видача команди");
                    dialog.add("Ви впевнені, що бажаєте відправити команду " + nppaCommand.getCaption() + "?");

                    Button confirmBt = new Button(sentence.getHeaderText(), (e) -> {
                        nppaService.setNppaCommand(generatedCommand);
                        dialog.close();
                    });
                    confirmBt.addClassName("confirm-button-nppa-dialog");

                    confirmBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY,
                            ButtonVariant.LUMO_ERROR);
                    confirmBt.getStyle().set("margin-right", "auto");
                    dialog.getFooter().add(confirmBt);
                } else {
                    String errorMsg = commandValidationResult.errorMessageUa();
                    log.error(errorMsg + " " + NppaView.class);

                    ConfigurationUtils.getErrorNotification(errorMsg, "",
                            0, true).open();

                    dialog.addClassName("dialog-nppa");

                    dialog.setHeaderTitle("Помилка");
                    dialog.add(commandValidationResult.errorMessageUa());
                    return;
                }
                Button cancelButton = new Button("Відміна", (e) -> dialog.close());
                cancelButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
                cancelButton.addClassName("cancel-button-nppa-dialog");

                dialog.getFooter().add(cancelButton);

                dialog.open();
            });

            return sendBt;
        }).setHeader(new Html("<b>Відправити</b>")).setFlexGrow(0).setWidth("150px");

        commandsGrid.addClassName(gridClassName);
        commandsGrid.addThemeVariants(GridVariant.MATERIAL_COLUMN_DIVIDERS, GridVariant.LUMO_COLUMN_BORDERS,
                GridVariant.LUMO_ROW_STRIPES);
        commandsGrid.setItems(commandsList);

        tabPanel.add(commandsGrid);
        tabPanel.setPadding(false);
    }

}
