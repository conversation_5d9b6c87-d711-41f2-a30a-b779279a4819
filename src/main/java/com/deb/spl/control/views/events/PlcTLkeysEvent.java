package com.deb.spl.control.views.events;

import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemType;
import lombok.*;
import org.apache.commons.lang3.NotImplementedException;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

@NoArgsConstructor
@Getter
@Setter
@EqualsAndHashCode
@Builder
public class PlcTLkeysEvent implements AdjacentSystemEvent {
    private HashMap<String, List<String>> keys;
    private long version;
    Object source;
    AdjacentSystemUpdateEventType eventType;
    private LocalDateTime updatedAt;

    /**
     * @param keys      changed object
     * @param source    event source
     * @param eventType @{@link AdjacentSystemUpdateEventType}
     * @Param keysVersion version of updated TlKeys for plc
     */
    public PlcTLkeysEvent(HashMap<String, List<String>> keys, long keysVersion,
                          Object source, AdjacentSystemUpdateEventType eventType, LocalDateTime updatedAt) {
        this.keys = keys;
        this.version = keysVersion;
        this.source = source;
        this.eventType = eventType;
        this.updatedAt = updatedAt != null ? updatedAt : LocalDateTime.now();

        this.updatedAt = this.updatedAt.withNano(0);
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt != null ? updatedAt : LocalDateTime.now();

        this.updatedAt = this.updatedAt.withNano(0);
    }

    /**
     * @param keys   changed object
     * @param source event source
     * @Param keysVersion version of updated TlKeys for plc
     */
    public PlcTLkeysEvent(HashMap<String, List<String>> keys, long keysVersion,
                          Object source) {
        this.keys = keys;
        this.version = keysVersion;
        this.source = source;
        this.eventType = AdjacentSystemUpdateEventType.DATA_UPDATE;
        this.updatedAt = LocalDateTime.now().withNano(0);
    }

    public PlcTLkeysEvent(HashMap<String, List<String>> keys, long keysVersion,
                          Object source, LocalDateTime updatedAt) {
        this.keys = keys;
        this.version = keysVersion;
        this.source = source;
        this.eventType = AdjacentSystemUpdateEventType.DATA_UPDATE;
        this.updatedAt = updatedAt != null ? updatedAt.withNano(0) :
                LocalDateTime.now().withNano(0);
    }


    public PlcTLkeysEvent(PlcTLkeysEvent prototype) {
        this.keys = prototype.getKeys();
        this.version = prototype.getVersion();
        this.source = prototype.getSource();
        this.eventType = prototype.getEventType();
        this.updatedAt = prototype.updatedAt;
    }

    @Override
    public AdjacentSystemType getSystemType() {
        return AdjacentSystemType.PLC;
    }

    @Override
    public Optional<AdjacentSystem> getEntity() {
        throw new NotImplementedException();
    }
}
