package com.deb.spl.control.views.events;

import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.asku.Tpc;
import lombok.*;

import java.util.Optional;

@NoArgsConstructor
@Getter
@Setter
@EqualsAndHashCode
@Builder
public class TpcEvent implements AdjacentSystemEvent{
    Tpc tpc;
    Object source;
    AdjacentSystemUpdateEventType eventType;

    /**
     * @param tpc changed object
     * @param source event source
     * @param eventType @{@link AdjacentSystemUpdateEventType}
     */
    public TpcEvent(Tpc tpc, Object source, AdjacentSystemUpdateEventType eventType) {
        this.tpc = tpc;
        this.source = source;
        this.eventType = eventType;
    }

    public TpcEvent(TpcEvent prototype) {
        this.tpc =prototype.getTpc();
        this.source=prototype.getSource();
        this.eventType=prototype.getEventType();
    }

    @Override
    public AdjacentSystemType getSystemType() {
        return tpc.getAdjacentSystemType();
    }

    @Override
    public Optional<AdjacentSystem> getEntity() {
        return Optional.ofNullable(tpc);
    }
}
