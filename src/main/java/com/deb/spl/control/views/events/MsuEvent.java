package com.deb.spl.control.views.events;

import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.msu.MeteoStationUnit;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Optional;

@NoArgsConstructor
@Getter
public class MsuEvent implements AdjacentSystemEvent{
    MeteoStationUnit msu;
    Object source;
    AdjacentSystemUpdateEventType eventType;

    /**
     * @param msu changed object
     * @param source event source
     * @param eventType @{@link AdjacentSystemUpdateEventType}
     */
    public MsuEvent(MeteoStationUnit msu, Object source, AdjacentSystemUpdateEventType eventType) {
        this.msu = msu;
        this.source = source;
        this.eventType = eventType;
    }

    @Override
    public AdjacentSystemType getSystemType() {
        return AdjacentSystemType.MSU;
    }

    @Override
    public Optional<AdjacentSystem> getEntity() {
        return Optional.ofNullable(msu);
    }
}
