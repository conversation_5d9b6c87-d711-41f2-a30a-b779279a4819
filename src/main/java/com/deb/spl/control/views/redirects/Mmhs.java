package com.deb.spl.control.views.redirects;

import com.deb.spl.control.views.MainLayout;
import com.vaadin.flow.component.AttachEvent;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import org.springframework.beans.factory.annotation.Value;

@PageTitle("Повідомлення")
@Route(value = "mmhs", layout = MainLayout.class)
public class Mmhs extends VerticalLayout {

    @Value("${mmhs.url}")
    String mmhsUrl;

    @Override
    public void onAttach(AttachEvent event) {
        if (getUI().isPresent()) {
            getUI().get().getPage().setLocation(mmhsUrl.toString());
        }
    }

    public Mmhs() {
    }

}
