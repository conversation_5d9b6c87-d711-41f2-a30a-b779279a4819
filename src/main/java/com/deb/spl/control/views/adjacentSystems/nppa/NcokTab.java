package com.deb.spl.control.views.adjacentSystems.nppa;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.nppa.BaseProperty;
import com.deb.spl.control.data.nppa.Ncok;
import com.deb.spl.control.data.nppa.NppaCommand;
import com.deb.spl.control.data.nppa.NppaOperatingMode;
import com.deb.spl.control.repository.nppa.NcokHistoryRepository;
import com.deb.spl.control.service.Broadcaster;
import com.deb.spl.control.service.Broadcaster.BroadcastListener;
import com.deb.spl.control.service.NppaService;
import com.deb.spl.control.service.UserService;
import com.deb.spl.control.views.adjacentSystems.LogTab;
import com.deb.spl.control.views.adjacentSystems.utils.AnimationColor;
import com.deb.spl.control.views.adjacentSystems.utils.ConfigurationUtils;
import com.deb.spl.control.views.adjacentSystems.utils.PagedTabUtils;
import com.deb.spl.control.views.events.NppaEvent;
import com.vaadin.flow.component.AttachEvent;
import com.vaadin.flow.component.DetachEvent;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.grid.GridVariant;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.component.textfield.TextFieldVariant;
import com.vaadin.flow.data.binder.Binder;
import lombok.extern.slf4j.Slf4j;
import org.vaadin.tabs.PagedTabs;

import java.util.Arrays;
import java.util.List;

@Slf4j
public class NcokTab extends VerticalLayout implements BroadcastListener {
    private final String unknownValue = "Невідомо";
    private final NcokHistoryRepository ncokHistoryRepository;
    private final NppaService nppaService;
    private final UserService userService;
    private final Binder<Ncok> ncokBinder = new Binder<Ncok>();

    private Grid<NppaCommand> ncokModeCommandsGrid;
    private List<NppaCommand> ncokModeCommands;
    private final boolean showNcokCommandStatusGrid;

    @Override
    protected void onAttach(AttachEvent attachEvent) {
        super.onAttach(attachEvent);
        Broadcaster.register(this, NppaEvent.class);
    }

    @Override
    protected void onDetach(DetachEvent detachEvent) {
        super.onDetach(detachEvent);
        Broadcaster.unregister(this);
    }

    public NcokTab(NcokHistoryRepository ncokRepository, NppaService nppaService, UserService userService, boolean showNcokCommandStatusGrid) {
        this.ncokHistoryRepository = ncokRepository;
        this.nppaService = nppaService;
        this.userService = userService;

        this.showNcokCommandStatusGrid = showNcokCommandStatusGrid;

        VerticalLayout tabLayout = new VerticalLayout();
        tabLayout.addClassName("tab-layout");
        PagedTabs tabs = new PagedTabs(tabLayout);
        tabs.getContent();

        workflowCommands = nppaService.getAvailableNcokWorkflowCommands();

        PagedTabUtils.addTab(initNcokStatusTab(), tabs,
                "ncok-status-tab-layout",
                "ncok-status-tab",
                "Статус");

        if (showNcokCommandStatusGrid) {
            PagedTabUtils.addTab(initNcokCommandsTab(), tabs,
                    "ncok-commands-tab-layout",
                    "ncok-commands-tab",
                    "Команди");
        }
        PagedTabUtils.addTab(initNcokLogTab(), tabs,
                "ncok-log-tab-layout",
                "ncok-log-tab",
                "Журнал");

        HorizontalLayout topOfTab = new HorizontalLayout();

        TextField operatingModeTf = new TextField("Режим");
        operatingModeTf.setReadOnly(true);
        operatingModeTf.addClassName("operating_mode");
        operatingModeTf.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        ncokBinder.forField(operatingModeTf)
                .bindReadOnly(ncok -> {
                    try {
                        if (ncok != null && ncok.getOperatingMode() != null) {
                            return ncok.getOperatingMode().getValueUa();
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                        return "Не обрано";
                    }
                    return NppaOperatingMode.NOT_SELECTED.getValueUa();
                });
        operatingModeTf.addValueChangeListener(valueChangeEvent ->
                ConfigurationUtils.setTextAnimation(valueChangeEvent, NppaOperatingMode.class));

        operatingModeTf.setValue((nppaService.getNcok().isPresent() && nppaService.getNcok().get().operatingMode() != null) ?
                nppaService.getNcok().get().operatingMode().getValueUa() : NppaOperatingMode.NOT_SELECTED.getValueUa()
        );

        topOfTab.addClassName("top-of-tab-ncok");
        topOfTab.add(tabs, new VerticalLayout(operatingModeTf));
        add(topOfTab, tabLayout);
    }

    private Grid<NppaCommand> workflowCommandsGrid;
    private final List<NppaCommand> workflowCommands;

    private VerticalLayout initNcokStatusTab() {

        VerticalLayout statusTab = new VerticalLayout();

        NcokStatus ncokStatus = new NcokStatus();

        NcokStatusTabFirstColumn ncokStatusTabFirstColumn = new NcokStatusTabFirstColumn();
        NcokStatusTabSecondColumn ncokStatusTabSecondColumn = new NcokStatusTabSecondColumn();
        NcokStatusTabThirdColumn ncokStatusTabThirdColumn = new NcokStatusTabThirdColumn();
        NcokStatusTabFourthColumn ncokStatusTabFourthColumn = new NcokStatusTabFourthColumn();
        ncokStatus.add(ncokStatusTabFirstColumn, ncokStatusTabSecondColumn, ncokStatusTabThirdColumn, ncokStatusTabFourthColumn);
        ncokStatus.addClassName("ncok-status-fields");

        statusTab.add(ncokStatus);

        workflowCommandsGrid = new Grid<>(NppaCommand.class, false);
        workflowCommandsGrid.addThemeVariants(GridVariant.LUMO_WRAP_CELL_CONTENT, GridVariant.MATERIAL_COLUMN_DIVIDERS,
                GridVariant.LUMO_COLUMN_BORDERS, GridVariant.LUMO_ROW_STRIPES);
        if (showNcokCommandStatusGrid) {
            NppaView.setupNppaCommandsGrid(statusTab, workflowCommandsGrid, workflowCommands,
                    userService, nppaService, "ncok-command-status-grid");
        }
        return statusTab;
    }

    private static class NcokStatus extends HorizontalLayout {

        public NcokStatus() {

            add();
        }

    }

    private class NcokStatusTabFirstColumn extends VerticalLayout {

        public NcokStatusTabFirstColumn() {

            TextField tvNcok = new TextField("ТВ НЦОК");
            tvNcok.setReadOnly(true);
            tvNcok.addClassName("tv-ncok");
            tvNcok.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            ncokBinder.forField(tvNcok)
                    .bindReadOnly(ncok -> {
                        try {
                            if (ncok != null && ncok.getTvNcok() != null) {
                                return ncok.getTvNcok().getValueUa();
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "Невідомо";
                        }
                        return AdjacentSystemStatus.UNDEFINED.getValueUa();
                    });
            tvNcok.addValueChangeListener(valueChangeEvent -> ConfigurationUtils.setTextAnimation(valueChangeEvent, BaseProperty.class));
            tvNcok.setValue((nppaService.getNcok().isPresent() && nppaService.getNcok().get().tvNcok() != null) ?
                    nppaService.getNcok().get().tvNcok().getValueUa() : BaseProperty.UNDEFINED.getValueUa()
            );

            TextField connectionNcok = new TextField("Зв'язок з НЦОК");
            connectionNcok.setReadOnly(true);
            connectionNcok.addClassName("connection-ncok");
            connectionNcok.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            ncokBinder.forField(connectionNcok)
                    .bindReadOnly(ncok -> {
                        try {
                            if (ncok != null) {
                                return ncok.isNcokConnected() ? "Норма" : "Немає зв'язку";
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "Невідомо";
                        }
                        return "Невідомо";
                    });
            connectionNcok.addValueChangeListener(valueChangeEvent ->
                    ConfigurationUtils.setBooleanToTextAnimation(
                            valueChangeEvent,
                            "Норма",
                            "Немае зв'зяку",
                            AnimationColor.GREEN,
                            AnimationColor.YELLOW)
            );
            connectionNcok.setValue((nppaService.getNcok().isPresent()) ?
                    (nppaService.getNcok().get().isNcokConnected() ? "Норма" : "Немає зв'язку") : unknownValue);

            TextField connectionSuto = new TextField("Зв'язок з СУТО");
            connectionSuto.setReadOnly(true);
            connectionSuto.addClassName("connection-suto");
            connectionSuto.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            ncokBinder.forField(connectionSuto)
                    .bindReadOnly(ncok -> {
                        try {
                            if (ncok != null) {
                                return ncok.isSutoConnected() ? "Норма" : "Немає зв'язку";
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "Невідомо";
                        }
                        return "Невідомо";
                    });

            connectionSuto.addValueChangeListener(valueChangeEvent ->
                    ConfigurationUtils.setBooleanToTextAnimation(
                            valueChangeEvent,
                            "Норма",
                            "Немає зв'язку",
                            AnimationColor.GREEN,
                            AnimationColor.YELLOW)
            );
            connectionSuto.setValue((nppaService.getNcok().isPresent()) ?
                    (nppaService.getNcok().get().isSutoConnected() ? "Норма" : "Немає зв'язку") : unknownValue);

            add(tvNcok, connectionNcok, connectionSuto);
        }
    }

    private class NcokStatusTabSecondColumn extends VerticalLayout {

        public NcokStatusTabSecondColumn() {

            TextField checkNppa = new TextField("Перевірка НППА");
            checkNppa.setReadOnly(true);
            checkNppa.addClassName("check-nppa");
            checkNppa.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            ncokBinder.forField(checkNppa)
                    .bindReadOnly(ncok -> {
                        try {
                            if (ncok != null) {
                                return ncok.getNppaTestResult().getValueUa();
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "Невідомо";
                        }
                        return AdjacentSystemStatus.UNDEFINED.getValueUa();
                    });
            checkNppa.addValueChangeListener(valueChangeEvent -> ConfigurationUtils.setTextAnimation(valueChangeEvent, BaseProperty.class));
            checkNppa.setValue((nppaService.getNcok().isPresent() && nppaService.getNcok().get().nppaTestResult() != null) ?
                    nppaService.getNcok().get().nppaTestResult().getValueUa() : BaseProperty.UNDEFINED.getValueUa());

            TextField appNppa = new TextField("АПП НППА");
            appNppa.setReadOnly(true);
            appNppa.addClassName("app-nppa");
            appNppa.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            ncokBinder.forField(appNppa)
                    .bindReadOnly(ncok -> {
                        try {
                            if (ncok != null) {
                                return ncok.isAppPresence() ? "Не норма" : "Норма";
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "Невідомо";
                        }
                        return "Невідомо";
                    });

            appNppa.addValueChangeListener(valueChangeEvent ->
                    ConfigurationUtils.setBooleanToTextAnimation(
                            valueChangeEvent,
                            "Не норма",
                            "Норма",
                            AnimationColor.RED,
                            AnimationColor.GREEN)
            );
            appNppa.setValue((nppaService.getNcok().isPresent()) ?
                    (nppaService.getNcok().get().appPresence() ? "Не норма" : "Норма") : unknownValue);

            add(checkNppa, appNppa);
        }
    }

    private class NcokStatusTabThirdColumn extends VerticalLayout {

        public NcokStatusTabThirdColumn() {

            TextField checkOtrLeft = new TextField("Перевірка ОТР1");
            checkOtrLeft.setReadOnly(true);
            checkOtrLeft.addClassName("check-otr-left");
            checkOtrLeft.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            ncokBinder.forField(checkOtrLeft)
                    .bindReadOnly(ncok -> {
                        try {
                            if (ncok != null) {
                                return ncok.getOtr1TestResult().getValueUa();
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                        }
                        return AdjacentSystemStatus.UNDEFINED.getValueUa();
                    });
            checkOtrLeft.addValueChangeListener(valueChangeEvent -> ConfigurationUtils.setTextAnimation(valueChangeEvent, BaseProperty.class));
            checkOtrLeft.setValue((nppaService.getNcok().isPresent() && nppaService.getNcok().get().otr1TestResult() != null) ?
                    nppaService.getNcok().get().otr1TestResult().getValueUa() : BaseProperty.UNDEFINED.getValueUa());

            TextField launchOtrLeft = new TextField("Схід з СПУ ОТР1");
            launchOtrLeft.setReadOnly(true);
            launchOtrLeft.addClassName("launch-otr-left");
            launchOtrLeft.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            ncokBinder.forField(launchOtrLeft)
                    .bindReadOnly(ncok -> {
                        try {
                            if (ncok != null) {
                                return ncok.getIsOtr1Lunched().getValueUa();
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return AdjacentSystemStatus.UNDEFINED.getValueUa();
                        }
                        return AdjacentSystemStatus.UNDEFINED.getValueUa();
                    });
            launchOtrLeft.addValueChangeListener(valueChangeEvent ->
                    ConfigurationUtils.setTextAnimation(valueChangeEvent, BaseProperty.class));
            launchOtrLeft.setValue((nppaService.getNcok().isPresent() && nppaService.getNcok().get().isOtr1Lunched() != null) ?
                    nppaService.getNcok().get().isOtr1Lunched().getValueUa() : BaseProperty.UNDEFINED.getValueUa());

            TextField appOtrLeft = new TextField("АПП ОТР1");
            appOtrLeft.setReadOnly(true);
            appOtrLeft.addClassName("app-otr-left");
            appOtrLeft.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            ncokBinder.forField(appOtrLeft)
                    .bindReadOnly(ncok -> {
                        try {
                            if (ncok != null) {
                                return ncok.isOtr1AppPresence() ? "Не норма" : "Норма";
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "Невідомо";
                        }
                        return "Невідомо";
                    });

            appOtrLeft.addValueChangeListener(valueChangeEvent ->
                    ConfigurationUtils.setBooleanToTextAnimation(
                            valueChangeEvent,
                            "Не норма",
                            "Норма",
                            AnimationColor.RED,
                            AnimationColor.GREEN)
            );
            appOtrLeft.setValue((nppaService.getNcok().isPresent()) ?
                    (nppaService.getNcok().get().otr1AppPresence() ? "Не норма" : "Норма") : unknownValue);

            add(checkOtrLeft, launchOtrLeft, appOtrLeft);
        }
    }

    private class NcokStatusTabFourthColumn extends VerticalLayout {

        public NcokStatusTabFourthColumn() {
            TextField checkOtrRight = new TextField("Перевірка ОТР2");
            checkOtrRight.setReadOnly(true);
            checkOtrRight.addClassName("check-otr-right");
            checkOtrRight.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            ncokBinder.forField(checkOtrRight)
                    .bindReadOnly(ncok -> {
                        try {
                            if (ncok != null) {
                                return ncok.getOtr2TestResult().getValueUa();
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                        }
                        return AdjacentSystemStatus.UNDEFINED.getValueUa();
                    });
            checkOtrRight.addValueChangeListener(valueChangeEvent -> ConfigurationUtils.setTextAnimation(valueChangeEvent, BaseProperty.class));
            checkOtrRight.setValue((nppaService.getNcok().isPresent() && nppaService.getNcok().get().otr2TestResult() != null) ?
                    nppaService.getNcok().get().otr2TestResult().getValueUa() : BaseProperty.UNDEFINED.getValueUa());

            TextField launchOtrRight = new TextField("Схід з СПУ ОТР2");
            launchOtrRight.setReadOnly(true);
            launchOtrRight.addClassName("launch-otr-right");
            launchOtrRight.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            ncokBinder.forField(launchOtrRight)
                    .bindReadOnly(ncok -> {
                        try {
                            if (ncok != null) {
                                return ncok.getIsOtr2Lunched().getValueUa();
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return AdjacentSystemStatus.UNDEFINED.getValueUa();
                        }
                        return AdjacentSystemStatus.UNDEFINED.getValueUa();
                    });
            launchOtrRight.addValueChangeListener(valueChangeEvent ->
                    ConfigurationUtils.setTextAnimation(valueChangeEvent, BaseProperty.class));
            launchOtrRight.setValue((nppaService.getNcok().isPresent() && nppaService.getNcok().get().isOtr2Lunched() != null) ?
                    nppaService.getNcok().get().isOtr2Lunched().getValueUa() : AdjacentSystemStatus.UNDEFINED.getValueUa());

            TextField appOtrRight = new TextField("АПП ОТР2");
            appOtrRight.setReadOnly(true);
            appOtrRight.addClassName("app-otr-right");
            appOtrRight.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
            ncokBinder.forField(appOtrRight)
                    .bindReadOnly(ncok -> {
                        try {
                            if (ncok != null) {
                                return ncok.isOtr2AppPresence() ? "Не норма" : "Норма";
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                            return "Невідомо";
                        }
                        return "Невідомо";
                    });

            appOtrRight.addValueChangeListener(valueChangeEvent ->
                    ConfigurationUtils.setBooleanToTextAnimation(
                            valueChangeEvent,
                            "Не норма",
                            "Норма",
                            AnimationColor.RED,
                            AnimationColor.GREEN)
            );
            appOtrRight.setValue((nppaService.getNcok().isPresent()) ?
                    (nppaService.getNcok().get().otr2AppPresence() ? "Не норма" : "Норма") : unknownValue);

            add(checkOtrRight, launchOtrRight, appOtrRight);
        }
    }


    private VerticalLayout initNcokCommandsTab() {
        VerticalLayout commandsTab = new VerticalLayout();

        ncokModeCommands = nppaService.getAvailableNcokTestModeCommands();
        try {
            if (nppaService.getNcok().isPresent() && nppaService.getNcok().get().operatingMode() == NppaOperatingMode.COMBAT) {
                ncokModeCommands = nppaService.getAvailableNcokCombatModeCommands();
            }
        } catch (NullPointerException e) {
            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
        }

        ncokModeCommandsGrid = new Grid<>(NppaCommand.class, false);
        ncokModeCommandsGrid.addThemeVariants(GridVariant.LUMO_WRAP_CELL_CONTENT);

        NppaView.setupNppaCommandsGrid(commandsTab, ncokModeCommandsGrid, ncokModeCommands,
                userService, nppaService, "ncok-command-commands-grid");

        ncokModeCommandsGrid.setAllRowsVisible(true);
        return commandsTab;
    }

    private VerticalLayout initNcokLogTab() {
        VerticalLayout logTab = new LogTab(ncokHistoryRepository, "ncok-log-tab");

        return logTab;
    }

    private NppaEvent previousNppaEvent;

    @Override
    public void receiveBroadcast(Object incomingEvent) {
        if (!(incomingEvent instanceof NppaEvent eventUnderProcessing)) {
            log.debug("expected " + NppaEvent.class.getSimpleName() + "but received unexpected event at " + this.getClassName());
            return;
        }
        if (previousNppaEvent != null && previousNppaEvent.equals(incomingEvent)) {
            return;
        }
        if (eventUnderProcessing.getNppa() == null) {
            log.error("received event without Nppa entity " + eventUnderProcessing + " " + this);
            return;
        }
        if (eventUnderProcessing.getNppa().getNcok() == null) {
            log.error("received NPPA event without Ncok entity " + eventUnderProcessing + " " + this);
            return;
        }

        getUI().get().access(() -> ncokBinder.readBean(eventUnderProcessing.getNppa().getNcok()));

        previousNppaEvent = eventUnderProcessing;

        ncokModeCommands = nppaService.getAvailableNcokTestModeCommands();
        try {
            if (nppaService.getNcok().isPresent() && nppaService.getNcok().get().operatingMode() == NppaOperatingMode.COMBAT) {
                ncokModeCommands = nppaService.getAvailableNcokCombatModeCommands();
            }
        } catch (NullPointerException e) {
            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
        }

        getUI().get().access(() -> {
            ncokModeCommandsGrid.setItems(ncokModeCommands);
            ncokModeCommandsGrid.getDataProvider().refreshAll();
        });
    }
}
