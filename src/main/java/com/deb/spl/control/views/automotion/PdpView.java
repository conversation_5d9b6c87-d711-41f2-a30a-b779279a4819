package com.deb.spl.control.views.automotion;

import com.deb.spl.control.controller.NotFoundException;
import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.Bins;
import com.deb.spl.control.data.asku.AskuInfo;
import com.deb.spl.control.data.asku.LaunchType;
import com.deb.spl.control.data.asku.Readiness;
import com.deb.spl.control.data.asku.RocketMapper;
import com.deb.spl.control.data.nppa.*;
import com.deb.spl.control.data.sae.CommandState;
import com.deb.spl.control.service.*;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.service.asku.PlcService;
import com.deb.spl.control.views.MainLayout;
import com.deb.spl.control.views.adjacentSystems.nppa.NppaView;
import com.deb.spl.control.views.adjacentSystems.utils.AnimationColor;
import com.deb.spl.control.views.adjacentSystems.utils.ConfigurationUtils;
import com.deb.spl.control.views.adjacentSystems.utils.CountdownOTR;
import com.deb.spl.control.views.adjacentSystems.utils.StatusToColorMapper;
import com.deb.spl.control.views.events.*;
import com.deb.spl.control.views.rocket.RocketFormDataView;
import com.deb.spl.control.views.rocket.RocketInitialDataLayout;
import com.vaadin.flow.component.*;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.dialog.Dialog;
import com.vaadin.flow.component.html.Image;
import com.vaadin.flow.component.html.Span;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.tabs.Tab;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.component.textfield.TextFieldVariant;
import com.vaadin.flow.data.binder.Binder;
import com.vaadin.flow.data.binder.Result;
import com.vaadin.flow.data.binder.ValueContext;
import com.vaadin.flow.data.converter.Converter;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import lombok.extern.slf4j.Slf4j;
import net.sf.marineapi.nmea.util.BinsCalibrationStatus;
import net.sf.marineapi.nmea.util.BinsGpsStatus;
import org.vaadin.tabs.PagedTabs;

import javax.validation.constraints.NotNull;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@PageTitle("Підготовка до пуску")
@Route(value = "automation/pdp", layout = MainLayout.class)
public class PdpView extends VerticalLayout implements Broadcaster.BroadcastListener {

    public static final String OTR_1_AUTOMATIC_LAUNCH_CANCEL_ACKNOWLEDGMENT = "Otr1AutomaticLaunchCancelAcknowledgment";
    public static final String OTR_2_AUTOMATIC_LAUNCH_CANCEL_ACKNOWLEDGMENT = "Otr2AutomaticLaunchCancelAcknowledgment";
    private final CountdownOTR countdownOTR;
    boolean messageOTR1LaunchIsDisplayed = false;
    boolean messageOTR2LaunchIsDisplayed = false;
    boolean messagePPPiPIsDisplayed = false;
    boolean errorAPPOTR1MessageIsDisplayed = false;
    boolean errorAPPOTR2MessageIsDisplayed = false;
    boolean errorAPPNPPAMessageIsDisplayed = false;

    boolean otr1LaunchCancell = false;
    boolean otr2LaunchCancell = false;

    private static final String BINS_IS_CONNECTED = "bins-is-connected"; // об'явити всі зміні для вектора
    private static final String IC_FOR_OTR1_LOADED_TO_PLC = "ic-for-OTR1-loaded_to_plc";
    private static final String IC_FOR_OTR2_LOADED_TO_PLC = "ic-for-OTR2-loaded_to_plc";
    private static final String CHECK_OTR1 = "check-OTR1";
    private static final String CHECK_OTR2 = "check-OTR2";
    private static final String TV_BYN_PRE = "tv-byn-pre";

    private static final String TV_NCOK_LAUNCH = "tv-ncok-launch";
    private static final String ARROW_UP_LAUNCH = "arrow-up-launch";
    private static final String LAUNCH_COMMAND_OTR1 = "launch-command-otr1";
    private static final String LAUNCH_COMMAND_OTR2 = "launch-command-otr2";
    private static final String LAUNCH_COMMAND_2HOTR = "launch-command-2hotr";
    private static final String LAUNCH_COMMAND_CANCEL_OTR1 = "launch-command-cancel-otr1";
    private static final String LAUNCH_COMMAND_CANCEL_OTR2 = "launch-command-cancel-otr2";
    private static final String PPIP_WITH_2B = "ppip-with-2b";
    boolean ppipWith2bCommandPassed = false;
    boolean ppipWith2bCommandPassedOTR1 = false;
    boolean ppipWith2bCommandPassedOTR2 = false;
    boolean ppipWith2bCommandPassed2hOTR = false;

    private static final String LAUNCH_OTR_LEFT_PDP = "launch-otr-left-pdp-badge";

    private static final String LAUNCH_OTR_RIGHT_PDP = "launch-otr-right-pdp-badge";

    private static final String OUTRIGGERS_IN_MOBILE_STATE_OTR1 = "outriggers-in-mobile-state-otr1";
    private static final String OUTRIGGERS_IN_MOBILE_STATE_OTR2 = "outriggers-in-mobile-state-otr2";
    private static final String OUTRIGGERS_IN_MOBILE_STATE_2HOTR = "outriggers-in-mobile-state-2hotr";
    boolean outriggersInMobileStatePassedOTR1 = false;
    boolean outriggersInMobileStatePassedOTR2 = false;
    boolean outriggersInMobileStatePassed2hOTR = false;

    private static final String STARTED_INITIAL_SET_SK_BINS_OTR1 = "started-initial-set-sk-bins-otr1";

    private static final String STARTED_INITIAL_SET_SK_BINS_OTR2 = "started-initial-set-sk-bins-otr2";

    private static final String PRECISE_DRIVE = "precise-drive";

    private static final String CHASSIS_HORIZONTAL_POSITION_STATE_OTR1 = "chassis-horizontal-position-state-otr1";

    private static final String CHASSIS_HORIZONTAL_POSITION_STATE_OTR2 = "chassis-horizontal-position-state-otr2";

    private static final String CHASSIS_HORIZONTAL_POSITION_STATE_2HOTR = "chassis-horizontal-position-state-2hotr";
    boolean chassisHorizontalPositionStatePassedOTR1 = false;
    boolean chassisHorizontalPositionStatePassedOTR2 = false;
    boolean chassisHorizontalPositionStatePassed2hOTR = false;
    private final BinsService binsService;
    private final UserService userService;
    private final SutoService sutoService;
    private final AskuService askuService;
    final NppaService nppaService;
    private final RocketMapper rocketMapper;
    private final Binder<RocketEvent> rocketBinder = new Binder<>(RocketEvent.class);
    private final Binder<AdjacentSystem> msuBinder = new Binder<>(AdjacentSystem.class);
    private final Binder<Bins> binsBinder = new Binder<>(Bins.class);
    private final Binder<AdjacentSystem> ppoBinder = new Binder<>(AdjacentSystem.class);
    private final Binder<AdjacentSystem> sutoBinder = new Binder<>(AdjacentSystem.class);
    private final Binder<AdjacentSystem> saeBinder = new Binder<>(AdjacentSystem.class);
    private final Binder<AdjacentSystem> plcBinder = new Binder<>(AdjacentSystem.class);
    private final Binder<AskuInfo> askuInfoBinder = new Binder<>(AskuInfo.class);
    private final Binder<Byn> bynBinder = new Binder<>(Byn.class);
    private final Binder<Ncok> ncokBinder = new Binder<>(Ncok.class);
    private final Hashtable<String, AnimationColor> pdpStages = new Hashtable<>();
    private final Binder<Hashtable<String, AnimationColor>> badgeBinder = new Binder<>();

    private final Map<String, HasValue> readModeChangerList = new HashMap<>();

    private final String unknownValue = "Невідомо";

    private static final Map<Class, AdjacentSystemEvent> events = new HashMap<>();

    @Override
    protected void onAttach(AttachEvent attachEvent) {
        super.onAttach(attachEvent);

        events.put(PpoEvent.class, null);
        events.put(SutoEvent.class, null);
        events.put(BinsEvent.class, null);
        events.put(MsuEvent.class, null);
        events.put(SaeEvent.class, null);
        events.put(PlcEvent.class, null);
        events.put(AskuInfoEvent.class, null);
        events.put(NppaEvent.class, null);
        events.put(MsgEvent.class, null);
        events.put(RocketEvent.class, null);
        events.put(RocketLaunchEvent.class, null);

        Broadcaster.register(this, events.keySet().toArray(new Class[0]));
    }

    @Override
    protected void onDetach(DetachEvent detachEvent) {
        super.onDetach(detachEvent);

        Broadcaster.unregister(this);
    }

    public PdpView(BinsService binsService,
                   SutoService sutoService,
                   AskuService askuService,
                   NppaService nppaService,
                   UserService userService,
                   RocketMapper rocketMapper,
                   CountdownOTR countdownOTR) {
        this.binsService = binsService;
        this.sutoService = sutoService;
        this.askuService = askuService;
        this.nppaService = nppaService;
        this.rocketMapper = rocketMapper;
        this.userService = userService;
        this.countdownOTR = countdownOTR;

        initVIew();
    }

    private void initVIew() {
        VerticalLayout pdpLayout = new VerticalLayout();
        VerticalLayout tabContainer = new VerticalLayout();
        PagedTabs tabs = new PagedTabs(tabContainer);
        tabs.getContent();
        Tab launchOTR1Tab = new Tab();
        Tab launchOTR2Tab = new Tab();
        Tab launch2hOTRTab = new Tab();

        VerticalLayout launchOTR1 = new VerticalLayout();
        launchOTR1.addClassName("launch-OTR1-tab");
        launchOTR1.setWidth("100%");
        launchOTR1.setMaxWidth("100%");

        VerticalLayout launchOTR2 = new VerticalLayout();
        launchOTR2.addClassName("launch-OTR2-tab");
        launchOTR2.setWidth("100%");
        launchOTR2.setMaxWidth("100%");
        VerticalLayout launch2hOTR = new VerticalLayout();
        launch2hOTR.addClassName("launch-2hOTR-tab");
        launch2hOTR.setWidth("100%");
        launch2hOTR.setMaxWidth("100%");

        VerticalLayout preparationForLaunchOTR1 = new VerticalLayout();
        if (askuService.getRocketCopy(true).isPresent() &&
            askuService.getRocketCopy(true).get().getInitialData() != null) {
            Readiness readienessLeft = askuService.getRocketCopy(true).get().getInitialData().getReadiness();
            preparationForLaunchOTR1 = createTabContent(LaunchType.FIRST, readienessLeft);
            preparationForLaunchOTR1.addClassName("preparation-for-launch");
        } else {
            preparationForLaunchOTR1 = createTabContent(LaunchType.FIRST, Readiness.BG_3);
            preparationForLaunchOTR1.addClassName("preparation-for-launch");
        }
        VerticalLayout preparationForLaunchOTR2 = new VerticalLayout();
        if (askuService.getRocketCopy(false).isPresent() &&
            askuService.getRocketCopy(false).get().getInitialData() != null) {
            Readiness readienessRight = askuService.getRocketCopy(false).get().getInitialData().getReadiness();
            preparationForLaunchOTR2 = createTabContent(LaunchType.SECOND, readienessRight);
            preparationForLaunchOTR2.addClassName("preparation-for-launch");
        } else {
            preparationForLaunchOTR2 = createTabContent(LaunchType.SECOND, Readiness.BG_3);
            preparationForLaunchOTR2.addClassName("preparation-for-launch");
        }
        VerticalLayout preparationForLaunch2hOTR = new VerticalLayout();
        if (askuService.getRocketCopy(true).isPresent() &&
            askuService.getRocketCopy(true).get().getInitialData() != null &&
            askuService.getRocketCopy(false).isPresent() &&
            askuService.getRocketCopy(false).get().getInitialData() != null) {
            if (askuService.getRocketCopy(true).get().getInitialData().getReadiness() ==
                askuService.getRocketCopy(false).get().getInitialData().getReadiness()) {
                Readiness readienessTwo = askuService.getRocketCopy(true).get().getInitialData().getReadiness();
                preparationForLaunch2hOTR = createTabContent(LaunchType.FIRST_AND_SECOND, readienessTwo);
                preparationForLaunch2hOTR.addClassName("preparation-for-launch");
            } else {
                preparationForLaunch2hOTR = createTabContent(LaunchType.FIRST_AND_SECOND, Readiness.BG_3);
                preparationForLaunch2hOTR.addClassName("preparation-for-launch");
            }
        } else {
            preparationForLaunch2hOTR = createTabContent(LaunchType.FIRST_AND_SECOND, Readiness.BG_3);
            preparationForLaunch2hOTR.addClassName("preparation-for-launch");
        }

        launchOTR1Tab.add(launchOTR1);
        launchOTR1Tab.addClassName("launch-otr1-tab");

        launchOTR2Tab.add(launchOTR2);
        launchOTR2Tab.addClassName("launch-otr2-tab");
        launch2hOTRTab.add(launch2hOTR);
        launch2hOTRTab.addClassName("launch-2hotr-tab");

        tabs.add("Пуск ОТР1", launchOTR1Tab, false);
        tabs.add("Пуск ОТР2", launchOTR2Tab, false);
        tabs.add("Пуск 2-х ОТР", launch2hOTRTab, false);

        pdpLayout.add(tabs, tabContainer);
        pdpLayout.addClassName("pdp-layout");
        pdpLayout.setWidth("100%");
        pdpLayout.setMaxWidth("100%");

        VerticalLayout launchOTR1Layout = new VerticalLayout();

        if (askuService.getRocketCopy(true).isPresent() &&
            askuService.getRocketCopy(true).get().getInitialDataTS() != null &&
            askuService.getRocketCopy(true).get().getInitialData() != null) {

            Readiness readieness = askuService.getRocketCopy(true).get().getInitialData().getReadiness();
            launchOTR1Layout = createTabLaunchContent(LaunchType.FIRST, readieness);

            launchOTR1Layout.addClassName("launch-OTR1-layout");

        } else {
            String errorMsg = "";
            log.error(errorMsg);
            ConfigurationUtils.getErrorNotification(
                            "Помилка даних по цілі",
                            "Виникла помилка даних по цілі для ОТР1, дані по цілі відсутні або не завантаженні! " +
                            "Завантажте дані по цілі в ПЛК або змініть готовність на готовність з даних по цілі." +
                            "Перезавантажте сторінку та повторіть спробу",
                            75000, true)
                    .open();
        }

        VerticalLayout launchOTR2Layout = new VerticalLayout();

        if (askuService.getRocketCopy(false).isPresent() &&
            askuService.getRocketCopy(false).get().getInitialDataTS() != null &&
            askuService.getRocketCopy(false).get().getInitialData() != null) {
            Readiness readieness = askuService.getRocketCopy(false).get().getInitialData().getReadiness();

            launchOTR2Layout = createTabLaunchContent(LaunchType.SECOND, readieness);

            launchOTR2Layout.addClassName("launch-OTR2-layout");

        }

        VerticalLayout launch2hOTRLayout = new VerticalLayout();

        if (askuService.getRocketCopy(true).isPresent() && askuService.getRocketCopy(false).isPresent() &&
            askuService.getRocketCopy(true).get().getInitialDataTS() != null &&
            askuService.getRocketCopy(false).get().getInitialDataTS() != null &&
            askuService.getRocketCopy(true).get().getInitialData() != null &&
            askuService.getRocketCopy(false).get().getInitialData() != null &&
            askuService.getRocketCopy(true).get().getInitialData().getReadiness() ==
            askuService.getRocketCopy(false).get().getInitialData().getReadiness()) {
            Readiness readiness = askuService.getRocketCopy(false).get().getInitialData().getReadiness();

            launch2hOTRLayout = createTabLaunchContent(LaunchType.FIRST_AND_SECOND, readiness);

            launch2hOTRLayout.addClassName("launch-2hOTR-layout");

        }

        launchOTR1.add(preparationForLaunchOTR1, launchOTR1Layout);
        launchOTR2.add(preparationForLaunchOTR2, launchOTR2Layout);
        launch2hOTR.add(preparationForLaunch2hOTR, launch2hOTRLayout);

        tabs.addSelectedChangeListener(event -> {
            Tab selectedTab = tabs.getSelectedTab();
            if (selectedTab.getLabel().equals("Пуск ОТР1")) {
                if (askuService.getRocketCopy(true).isEmpty() ||
                    askuService.getRocketCopy(true).get().getInitialDataTS() == null ||
                    askuService.getRocketCopy(true).get().getInitialData() == null) {
                    String errorMsg = "";
                    log.error(errorMsg);
                    ConfigurationUtils.getErrorNotification(
                                    "Помилка даних по цілі",
                                    "Виникла помилка даних по цілі для ОТР1, дані по цілі відсутні або не завантаженні! " +
                                    "Завантажте дані по цілі в ПЛК або змініть готовність на готовність з даних по цілі." +
                                    "Перезавантажте сторінку та повторіть спробу",
                                    75000, true)
                            .open();
                }
            } else if (selectedTab.getLabel().equals("Пуск ОТР2")) {
                if (askuService.getRocketCopy(false).isEmpty() ||
                    askuService.getRocketCopy(false).get().getInitialDataTS() == null ||
                    askuService.getRocketCopy(false).get().getInitialData() == null) {
                    String errorMsg = "";
                    log.error(errorMsg);
                    ConfigurationUtils.getErrorNotification(
                                    "Помилка даних по цілі",
                                    "Виникла помилка даних по цілі для ОТР2, дані по цілі відсутні або не завантаженні! " +
                                    "Завантажте дані по цілі в ПЛК або змініть готовність на готовність з даних по цілі." +
                                    "Перезавантажте сторінку та повторіть спробу",
                                    75000, true)
                            .open();
                }
            } else if (selectedTab.getLabel().equals("Пуск 2-х ОТР")) {
                if (askuService.getRocketCopy(true).isEmpty() || askuService.getRocketCopy(false).isEmpty() ||
                    askuService.getRocketCopy(true).get().getInitialDataTS() == null ||
                    askuService.getRocketCopy(false).get().getInitialDataTS() == null ||
                    askuService.getRocketCopy(true).get().getInitialData() == null ||
                    askuService.getRocketCopy(false).get().getInitialData() == null ||
                    askuService.getRocketCopy(true).get().getInitialData().getReadiness() !=
                    askuService.getRocketCopy(false).get().getInitialData().getReadiness()) {
                    String errorMsg = "";
                    log.error(errorMsg);
                    ConfigurationUtils.getErrorNotification(
                                    "Помилка даних по цілі",
                                    "Виникла помилка даних по цілі для 2-х ОТР, дані по цілі відсутні або не завантаженні! " +
                                    "Завантажте дані по цілі в ПЛК або змініть готовність на готовність з даних по цілі." +
                                    "Перевірте значення \"Ознаку типу готовності\" для обох ОТР. Перезавантажте сторінку та повторіть спробу",
                                    75000, true)
                            .open();
                }
            }

        });

        add(pdpLayout);

        initializeBadgeStates();
    }

    private void initializeBadgeStates() {
        updateWorkflowIndicatorsBadgeLaunch();

        if (nppaService.checkCommandCommittedAndInTheTop("PPiPWithBg2bContinuation")) {
            messagePPPiPIsDisplayed = true;
            outriggersInMobileStatePassedOTR1 = true;
            outriggersInMobileStatePassedOTR2 = true;
            outriggersInMobileStatePassed2hOTR = true;
            pdpStages.put(OUTRIGGERS_IN_MOBILE_STATE_OTR1,
                    StatusToColorMapper.fromElementsStatuses(
                            AdjacentSystemStatus.OK.getValueUa(),
                            (Class) AdjacentSystemStatus.class));
            ppipWith2bCommandPassedOTR1 = true;
            ppipWith2bCommandPassedOTR2 = true;
            ppipWith2bCommandPassed2hOTR = true;
        }
        if (getUI().isPresent()) {
            getUI().get().access(() -> badgeBinder.readBean(pdpStages));
        }
    }

    /**
     * @param baseClassName class name for Check
     * @param value         color of Check initializations
     * @param binder        huony v 69 string
     * @param stages        68 str lists of value
     * @return configurable badge returned
     */
    private Badge configureStageBadge(String baseClassName,
                                      AnimationColor value,
                                      Binder<Hashtable<String, AnimationColor>> binder,
                                      Hashtable<String, AnimationColor> stages) {
        Badge badge = ConfigurationUtils.setUpBadge(baseClassName, value);
        stages.put(baseClassName, value);
        binder.forField(badge)
                .bindReadOnly(v -> v.getOrDefault(badge.getBaseClassName(), AnimationColor.UNDEFINED)
                        .getText());

        return badge;
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    private VerticalLayout createTabContent(LaunchType launchType, Readiness readiness) {
        if (readiness == null) {
            readiness = Readiness.UNDEFINED;
        }

        VerticalLayout layout = new VerticalLayout();
        layout.addClassName("launch-OTR1-tab-content");
        layout.setWidth("100%");
        layout.setMaxWidth("100%");

        TextField latitudeTf;
        TextField longitudeTf;

        TextField altitudeTf;

        Span preparationForLaunchCaptionOTR1 = new Span("Підготовка до пуску ОТР1");
        preparationForLaunchCaptionOTR1.addClassName("preparation-for-launch-caption");

        Span preparationForLaunchCaptionOTR2 = new Span("Підготовка до пуску ОТР2");
        preparationForLaunchCaptionOTR2.addClassName("preparation-for-launch-caption");

        Span preparationForLaunchCaption2hOTR = new Span("Підготовка до пуску двох ОТР");
        preparationForLaunchCaption2hOTR.addClassName("preparation-for-launch-caption");

        HorizontalLayout preparationForLaunchFirstRow = new HorizontalLayout();
        preparationForLaunchFirstRow.addClassName("preparation-for-launch-first-row-OTR1");
        Image checkStep1Image = new Image("images/Vector1.svg", "check");
        checkStep1Image.addClassNames("preparation-check-step-1");

        Badge checkStep1 = configureStageBadge(BINS_IS_CONNECTED,
                StatusToColorMapper.fromElementsStatuses(getBinsStageState().getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);

        Span coordinateSPL = new Span("Координати СПУ");
        coordinateSPL.addClassName("coordinate-SPL");

        latitudeTf = new TextField("Широта", "llll.ll");
        latitudeTf.setWidth("12em");
        binsBinder.forField(latitudeTf)
                .bindReadOnly(bins -> new DecimalFormat("00.00000000").format(bins.getPosition().getLatitude()));
        latitudeTf.addClassName("latitude");

        longitudeTf = new TextField("Довгота", "yyyyy.yy");
        binsBinder.forField(longitudeTf)
                .bindReadOnly(bins -> new DecimalFormat("00.00000000").format(bins.getPosition().getLongitude()));
        longitudeTf.addClassName("longitude");

        altitudeTf = new TextField("Висота", "x.x");
        binsBinder.forField(altitudeTf)
                .bindReadOnly(bins -> new DecimalFormat("###,###,##0.0").format(bins.getPosition().getAltitude()));
        altitudeTf.addClassName("altitude");

        TextField binsGpsStatus = new TextField("Статус GPS");
        binsGpsStatus.addClassName("bins-gps-status");
        Map<BinsGpsStatus, String> binsGpsStatusComparisonMap = new WeakHashMap<>();
        binsGpsStatusComparisonMap.put(BinsGpsStatus.NON_RELIABLE, "Дані не достовірні");
        binsGpsStatusComparisonMap.put(BinsGpsStatus.RELIABLE, "Дані достовірні");
        binsGpsStatusComparisonMap.put(BinsGpsStatus.PROVIDED_MANUALLY, "Отримані без GPS");

        binsBinder.forField(binsGpsStatus)
                .withConverter(new Converter<String, BinsGpsStatus>() {
                    @Override
                    public Result<BinsGpsStatus> convertToModel(String s, ValueContext valueContext) {
                        return null;
                    }

                    @Override
                    public String convertToPresentation(BinsGpsStatus binsGpsStatus, ValueContext valueContext) {
                        if (binsGpsStatus == null) {
                            return "";
                        }
                        return Objects.requireNonNullElse(binsGpsStatusComparisonMap.get(binsGpsStatus), "");
                    }
                })
                .bindReadOnly(Bins::getBinsGpsStatus);

        TextField binsCalibrationStatus = new TextField("Статус УКННС");
        binsCalibrationStatus.addClassName("bins-calibration-status");
        Map<BinsCalibrationStatus, String> comparisonMapBinsCalibrationStatus = new WeakHashMap<>();
        comparisonMapBinsCalibrationStatus.put(BinsCalibrationStatus.RELIABLE,
                "Дані достовірні");
        comparisonMapBinsCalibrationStatus.put(BinsCalibrationStatus.RESOLVE_IS_CALCULATED_WITHOUT_PROOF_OF_RELIABLE,
                "Дані не достовірні");
        comparisonMapBinsCalibrationStatus.put(BinsCalibrationStatus.AUTOMATIC_CALIBRATION_IN_PROGRESS,
                "Автоматичене калібрування");
        comparisonMapBinsCalibrationStatus.put(BinsCalibrationStatus.RESOLVE_NOT_CALCULATED,
                "Немає рішення");
        binsBinder.forField(binsCalibrationStatus)
                .withConverter(new Converter<String, BinsCalibrationStatus>() {
                    @Override
                    public Result<BinsCalibrationStatus> convertToModel(String s, ValueContext valueContext) {
                        return null;
                    }

                    @Override
                    public String convertToPresentation(BinsCalibrationStatus binsCalibrationStatus, ValueContext valueContext) {
                        if (binsCalibrationStatus == null) {
                            return "";
                        }
                        return Objects.requireNonNullElse(comparisonMapBinsCalibrationStatus.get(binsCalibrationStatus), "");
                    }
                })
                .bindReadOnly(Bins::getBinsCalibrationStatus);


        preparationForLaunchFirstRow.add(checkStep1, coordinateSPL, latitudeTf, longitudeTf,
                altitudeTf, binsGpsStatus, binsCalibrationStatus);


        HorizontalLayout preparationForLaunchSecondRowOTR1 = new HorizontalLayout();
        preparationForLaunchSecondRowOTR1.addClassName("preparation-for-launch-second-row-OTR1");


        Badge checkStep2 = configureStageBadge(IC_FOR_OTR1_LOADED_TO_PLC,
                StatusToColorMapper.fromElementsStatuses(getInitialConditionsState(LaunchType.FIRST).getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);

        RocketFormDataView rocketFormDataPdpView = new RocketFormDataView(this.askuService, true, "rocket-form-data-pdp-view");
        VerticalLayout formDataOTR1 = new VerticalLayout();
        formDataOTR1.add(new Span("Формулярні дані ОТР1"), rocketFormDataPdpView.buildView());
        formDataOTR1.addClassName("form-data-otr");
        RocketInitialDataLayout rocketInitialDataLayoutOTR1 = new RocketInitialDataLayout(
                true,
                true,
                this.askuService,
                this.rocketMapper,
                true,
                "initial-data-latitude-pdp",
                "initial-data-altitude-pdp",
                "initial-data-longitude-pdp",
                "initial-data-inclination-angle-pdp",
                "initial-data-readiness-pdp",
                "initial-data-time-stamp-pdp",
                "otr-vd-layout-pdp",
                "change-initial-data-pdp",
                "launch-planned-time-pdp",
                "order-valid-until-pdp");

        VerticalLayout initialDataOTR1 = new VerticalLayout();

        Span titleInitialDataOTR1 = new Span("Дані по цілі для ОТР1");
        titleInitialDataOTR1.addClassName("title-initial-data");

        initialDataOTR1.add(titleInitialDataOTR1, rocketInitialDataLayoutOTR1.buildView());
        initialDataOTR1.addClassName("initial-data-dialog");

        preparationForLaunchSecondRowOTR1.add(checkStep2, formDataOTR1, initialDataOTR1);

        HorizontalLayout preparationForLaunchSecondRowOTR2 = new HorizontalLayout();
        preparationForLaunchSecondRowOTR2.addClassName("preparation-for-launch-second-row-OTR2");

        Badge checkStep3 = configureStageBadge(IC_FOR_OTR2_LOADED_TO_PLC,
                StatusToColorMapper.fromElementsStatuses(getInitialConditionsState(LaunchType.SECOND).getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);

        VerticalLayout initialDataOTR2 = new VerticalLayout();

        Span titleInitialDataOTR2 = new Span("Дані по цілі для ОТР2");
        titleInitialDataOTR2.addClassName("title-initial-data");

        rocketFormDataPdpView = new RocketFormDataView(this.askuService, false, "rocket-form-data-pdp-view");
        VerticalLayout formDataOTR2 = new VerticalLayout();
        formDataOTR2.add(new Span("Формулярні дані ОТР2"), rocketFormDataPdpView.buildView());
        formDataOTR2.addClassName("form-data-otr");
        RocketInitialDataLayout rocketInitialDataLayoutOTR2 = new RocketInitialDataLayout(
                false,
                true,
                this.askuService,
                this.rocketMapper,
                true,
                "initial-data-latitude-pdp",
                "initial-data-altitude-pdp",
                "initial-data-longitude-pdp",
                "initial-data-inclination-angle-pdp",
                "initial-data-readiness-pdp",
                "initial-data-time-stamp-pdp",
                "otr-vd-layout-pdp",
                "change-initial-data-pdp",
                "launch-planned-time-pdp",
                "order-valid-until-pdp");


        initialDataOTR2.add(titleInitialDataOTR2, rocketInitialDataLayoutOTR2.buildView());
        initialDataOTR2.addClassName("initial-data-dialog");

        preparationForLaunchSecondRowOTR2.add(checkStep3, formDataOTR2, initialDataOTR2);

        HorizontalLayout preparationForLaunchThirdRow = new HorizontalLayout();
        preparationForLaunchThirdRow.addClassName("preparation-for-launch-third-row");

        HorizontalLayout preparationThreeInRowForLaunch = new HorizontalLayout();
        preparationThreeInRowForLaunch.addClassName("preparation-three-in-row-for-launch");

        Badge checkStep4 = configureStageBadge(CHECK_OTR1,
                StatusToColorMapper.fromElementsStatuses(getCheckedOTR(LaunchType.FIRST).getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);

        TextField checkOtrLeft = new TextField("Перевірка ОТР1");
        checkOtrLeft.setReadOnly(true);
        checkOtrLeft.addClassName("check-otr-left-pdp");
        checkOtrLeft.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        ncokBinder.forField(checkOtrLeft)
                .bindReadOnly(ncok -> {
                    try {
                        if (ncok != null) {
                            return ncok.getOtr1TestResult().getValueUa();
                        }
                    } catch (Exception e) {
                        log.error("{} {}", e.getMessage(), Arrays.toString(e.getStackTrace()));
                    }
                    return AdjacentSystemStatus.UNDEFINED.getValueUa();
                });

        checkOtrLeft.addValueChangeListener(valueChangeEvent ->
                ConfigurationUtils.setTextAnimation(valueChangeEvent, BaseProperty.class));
        checkOtrLeft.setValue((nppaService.getNcok().isPresent() && nppaService.getNcok().get().otr1TestResult() != null) ?
                nppaService.getNcok().get().otr1TestResult().getValueUa() : BaseProperty.UNDEFINED.getValueUa());

        preparationForLaunchThirdRow.add(checkStep4, checkOtrLeft);

        HorizontalLayout preparationForLaunchFourthRow = new HorizontalLayout();
        preparationForLaunchFourthRow.addClassName("preparation-for-launch-fourth-row");

        Badge checkStep5 = configureStageBadge(CHECK_OTR2,
                StatusToColorMapper.fromElementsStatuses(getCheckedOTR(LaunchType.SECOND).getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);


        TextField checkOtrRight = new TextField("Перевірка ОТР2");
        checkOtrRight.setReadOnly(true);
        checkOtrRight.addClassName("check-otr-right-pdp");
        checkOtrRight.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        ncokBinder.forField(checkOtrRight)
                .bindReadOnly(ncok -> {
                    try {
                        if (ncok != null) {
                            return ncok.getOtr2TestResult().getValueUa();
                        }
                    } catch (Exception e) {
                        log.error("{} {}", e.getMessage(), Arrays.toString(e.getStackTrace()));
                    }
                    return AdjacentSystemStatus.UNDEFINED.getValueUa();
                });
        checkOtrRight.addValueChangeListener(valueChangeEvent -> ConfigurationUtils.setTextAnimation(valueChangeEvent, BaseProperty.class));
        checkOtrRight.setValue((nppaService.getNcok().isPresent() && nppaService.getNcok().get().otr2TestResult() != null) ?
                nppaService.getNcok().get().otr2TestResult().getValueUa() : BaseProperty.UNDEFINED.getValueUa());

        preparationForLaunchFourthRow.add(checkStep5, checkOtrRight);

        HorizontalLayout preparationForLaunchFiveRow = new HorizontalLayout();
        preparationForLaunchFiveRow.addClassName("preparation-for-launch-five-row");

        Badge checkStep6 = configureStageBadge(TV_BYN_PRE,
                StatusToColorMapper.fromElementsStatuses(getTVBynStageState().getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);

        TextField tvByn = new TextField("ТВ БВН");
        tvByn.setReadOnly(true);
        tvByn.addClassName("tv-byn-pdp");
        tvByn.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        bynBinder.forField(tvByn)
                .bindReadOnly(byn -> {
                    try {
                        if (byn != null && byn.getTvByn() != null) {
                            return byn.getTvByn().getValueUa();
                        }
                    } catch (Exception e) {
                        log.error("{} {}", e.getMessage(), Arrays.toString(e.getStackTrace()));
                        return "Невідомо";
                    }
                    return AdjacentSystemStatus.UNDEFINED.getValueUa();
                });
        tvByn.addValueChangeListener(valueChangeEvent -> ConfigurationUtils.setTextAnimation(valueChangeEvent, BaseProperty.class));
        tvByn.setValue(nppaService.getByn().isPresent() ?
                nppaService.getByn().get().tvByn().getValueUa() : AdjacentSystemStatus.UNDEFINED.getValueUa());

        preparationForLaunchFiveRow.add(checkStep6, tvByn);

        HorizontalLayout preparationForLaunchSixthRow = new HorizontalLayout();
        preparationForLaunchSixthRow.addClassName("preparation-for-launch-sixth-row");

        HorizontalLayout preparationForLaunchSevenRow = new HorizontalLayout();
        preparationForLaunchSevenRow.addClassName("preparation-for-launch-seven-row");

        Badge checkStep8 = configureStageBadge(TV_NCOK_LAUNCH,
                StatusToColorMapper.fromElementsStatuses(getTVNcokStageState().getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);

        TextField tvNcok = new TextField("ТВ НЦОК");
        tvNcok.setReadOnly(true);
        tvNcok.addClassName("tv-ncok-pdp");
        tvNcok.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        ncokBinder.forField(tvNcok)
                .bindReadOnly(ncok -> {
                    try {
                        if (ncok != null && ncok.getTvNcok() != null) {
                            return ncok.getTvNcok().getValueUa();
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                        return "Невідомо";
                    }
                    return AdjacentSystemStatus.UNDEFINED.getValueUa();
                });
        tvNcok.addValueChangeListener(valueChangeEvent ->
                ConfigurationUtils.setTextAnimation(valueChangeEvent, BaseProperty.class));
        tvNcok.setValue((nppaService.getNcok().isPresent() && nppaService.getNcok().get().tvNcok() != null) ?
                nppaService.getNcok().get().tvNcok().getValueUa() : BaseProperty.UNDEFINED.getValueUa()
        );

        preparationForLaunchSevenRow.add(checkStep8, tvNcok);

        if (readiness == Readiness.BG_1) {
            switch (launchType) {
                case FIRST -> {
                    preparationThreeInRowForLaunch.add(preparationForLaunchThirdRow,
                            preparationForLaunchFiveRow, preparationForLaunchSevenRow);
                    layout.add(preparationForLaunchCaptionOTR1, preparationForLaunchFirstRow,
                            preparationForLaunchSecondRowOTR1, preparationThreeInRowForLaunch);
                }
                case SECOND -> {
                    preparationThreeInRowForLaunch.add(preparationForLaunchFourthRow,
                            preparationForLaunchFiveRow, preparationForLaunchSevenRow);
                    layout.add(preparationForLaunchCaptionOTR2, preparationForLaunchFirstRow,
                            preparationForLaunchSecondRowOTR2, preparationThreeInRowForLaunch);
                }
                case FIRST_AND_SECOND -> {
                    preparationThreeInRowForLaunch.add(preparationForLaunchThirdRow, preparationForLaunchFourthRow,
                            preparationForLaunchFiveRow, preparationForLaunchSevenRow);
                    layout.add(preparationForLaunchCaption2hOTR, preparationForLaunchFirstRow,
                            preparationForLaunchSecondRowOTR1, preparationForLaunchSecondRowOTR2,
                            preparationThreeInRowForLaunch);
                }
            }
        } else {
            switch (launchType) {
                case FIRST -> {
                    preparationThreeInRowForLaunch.add(preparationForLaunchThirdRow,
                            preparationForLaunchFiveRow);
                    layout.add(preparationForLaunchCaptionOTR1, preparationForLaunchFirstRow,
                            preparationForLaunchSecondRowOTR1, preparationThreeInRowForLaunch);
                }
                case SECOND -> {
                    preparationThreeInRowForLaunch.add(preparationForLaunchFourthRow,
                            preparationForLaunchFiveRow);
                    layout.add(preparationForLaunchCaptionOTR2, preparationForLaunchFirstRow,
                            preparationForLaunchSecondRowOTR2, preparationThreeInRowForLaunch);
                }
                case FIRST_AND_SECOND -> {
                    preparationThreeInRowForLaunch.add(preparationForLaunchThirdRow, preparationForLaunchFourthRow,
                            preparationForLaunchFiveRow);
                    layout.add(preparationForLaunchCaption2hOTR, preparationForLaunchFirstRow,
                            preparationForLaunchSecondRowOTR1, preparationForLaunchSecondRowOTR2,
                            preparationThreeInRowForLaunch);
                }
            }
        }

        return layout;
    }


    /**
     * @param nppaCommand command to send
     * @param key         used as key in pdpStages
     * @return new button
     * @apiNote sends specified command then changes value of the pdpStage associated with a key if command was committed
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    private Button setupGenerateSendCommandButton(NppaCommand nppaCommand, String key, boolean startTimerUncommit, boolean isLaunch) {
        Button sendBt;
        if (isLaunch) {
            sendBt = new Button("Пуск");
        } else {
            sendBt = new Button("Відміна Пуску");
        }

        sendBt.addClassName("send-sentence-button-command");
        sendBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY, ButtonVariant.LUMO_SMALL);
        sendBt.getStyle().set("background-color", "#4F4F4F");

        sendBt.addClickListener(event -> {
            NppaCommand generatedCommand = NppaCommand.builder()
                    .command(nppaCommand.getCommand())
                    .caption(nppaCommand.getCaption())
                    .commandState(nppaCommand.getCommandState())
                    .adjacentSystem(nppaCommand.getAdjacentSystem())
                    .generationTime(LocalDateTime.now())
                    .originator(userService.getUserName())
                    .build();

            Dialog dialog = new Dialog();

            CommandValidationResult commandValidationResult = nppaService.validateCommand(generatedCommand);

            if (commandValidationResult.isValid()) {
                dialog.addClassName("dialog-nppa");
//dialog setup
                dialog.setHeaderTitle("Видача команди");
                if (generatedCommand.getCommand().equals("Otr1FromBg3Launch") ||
                    generatedCommand.getCommand().equals("Otr1FromBg2bLaunch") ||
                    generatedCommand.getCommand().equals("Otr1FromBg1Launch")) {
                    if (askuService.getRocketCopy(true).isPresent() &&
                        askuService.getRocketCopy(true).get().getInitialData() != null &&
                        askuService.getRocketCopy(true).get().getInitialData().isScheduled()) {
                        dialog.add("Ви впевнені, що бажаєте відправити команду " + nppaCommand.getCaption() + " з плановим часом пуску - " +
                                   askuService.getRocketCopy(true).get().getInitialData().getStartTime().format(DateTimeFormatter.ofPattern("dd/MM/yyyy\tHH:mm:ss")) + " ?");
                    } else dialog.add("Ви впевнені, що бажаєте відправити команду " + nppaCommand.getCaption() + "?");
                } else if (generatedCommand.getCommand().equals("Otr2FromBg3Launch") ||
                           generatedCommand.getCommand().equals("Otr2FromBg2bLaunch") ||
                           generatedCommand.getCommand().equals("Otr2FromBg1Launch")) {
                    if (askuService.getRocketCopy(false).isPresent() &&
                        askuService.getRocketCopy(false).get().getInitialData() != null &&
                        askuService.getRocketCopy(false).get().getInitialData().isScheduled()) {
                        dialog.add("Ви впевнені, що бажаєте відправити команду " + nppaCommand.getCaption() + " з плановим часом пуску - " +
                                   askuService.getRocketCopy(false).get().getInitialData().getStartTime().format(DateTimeFormatter.ofPattern("dd/MM/yyyy\tHH:mm:ss")) + " ?");
                    } else dialog.add("Ви впевнені, що бажаєте відправити команду " + nppaCommand.getCaption() + "?");
                } else if (generatedCommand.getCommand().equals("Otr1Otr2FromBg3Launch") ||
                           generatedCommand.getCommand().equals("Otr1Otr2FromBg2bLaunch") ||
                           generatedCommand.getCommand().equals("Otr1Otr2FromBg1Launch")) {
                    if (askuService.getRocketCopy(true).isPresent() &&
                        askuService.getRocketCopy(true).get().getInitialData() != null &&
                        askuService.getRocketCopy(true).get().getInitialData().isScheduled()) {
                        dialog.add("Ви впевнені, що бажаєте відправити команду " + nppaCommand.getCaption() + " з плановим часом пуску - " +
                                   askuService.getRocketCopy(true).get().getInitialData().getStartTime().format(DateTimeFormatter.ofPattern("dd/MM/yyyy\tHH:mm:ss")) + " ?");
                    }
                } else dialog.add("Ви впевнені, що бажаєте відправити команду " + nppaCommand.getCaption() + "?");

                Button confirmBt = new Button("Відправити", (e) -> {
                    UI ui = getUI().get();
                    nppaService.setNppaCommand(generatedCommand);

                    if (!key.isBlank()) {
                        nppaService.checkForCommandCommitDelayed(generatedCommand).
                                addCallback(successResult -> {

                                    AnimationColor color = null;
                                    if (successResult == null) {
                                        color = AnimationColor.GREY;
                                    } else {
                                        color = successResult ? AnimationColor.GREEN : AnimationColor.GREY;

                                        if (startTimerUncommit && (nppaService.checkCommandCommittedAndInTheTop("Otr1FromBg3Launch") ||
                                                                   nppaService.checkCommandCommittedAndInTheTop("Otr1FromBg2bLaunch") ||
                                                                   nppaService.checkCommandCommittedAndInTheTop("Otr1FromBg1Launch"))) {
                                            color = StatusToColorMapper.fromElementsStatuses(getLaunchCommandState(LaunchType.FIRST).getValueUa(),
                                                    (Class) AdjacentSystemStatus.class);
                                            if (pdpStages.containsKey(LAUNCH_COMMAND_OTR1) && !pdpStages.get(LAUNCH_COMMAND_OTR1).equals(color)) {
                                                pdpStages.put(LAUNCH_COMMAND_OTR1, color);
                                                if (getUI().isPresent()) {
                                                    getUI().get().access(() -> badgeBinder.readBean(pdpStages));
                                                }
                                            }
                                            otr1LaunchCancell = false;
                                        }

                                        if (startTimerUncommit && (nppaService.checkCommandCommittedAndInTheTop("Otr2FromBg3Launch") ||
                                                                   nppaService.checkCommandCommittedAndInTheTop("Otr2FromBg2bLaunch") ||
                                                                   nppaService.checkCommandCommittedAndInTheTop("Otr2FromBg1Launch"))) {
                                            color = StatusToColorMapper.fromElementsStatuses(getLaunchCommandState(LaunchType.SECOND).getValueUa(),
                                                    (Class) AdjacentSystemStatus.class);
                                            if (pdpStages.containsKey(LAUNCH_COMMAND_OTR2) && !pdpStages.get(LAUNCH_COMMAND_OTR2).equals(color)) {
                                                pdpStages.put(LAUNCH_COMMAND_OTR2, color);
                                                if (getUI().isPresent()) {
                                                    getUI().get().access(() -> badgeBinder.readBean(pdpStages));
                                                }
                                            }
                                            otr2LaunchCancell = false;

                                        }

                                        if (startTimerUncommit && (nppaService.checkCommandCommittedAndInTheTop("Otr1Otr2FromBg3Launch") ||
                                                                   nppaService.checkCommandCommittedAndInTheTop("Otr1Otr2FromBg2bLaunch") ||
                                                                   nppaService.checkCommandCommittedAndInTheTop("Otr1Otr2FromBg1Launch"))) {
                                            color = StatusToColorMapper.fromElementsStatuses(getLaunchCommandState(LaunchType.FIRST_AND_SECOND).getValueUa(),
                                                    (Class) AdjacentSystemStatus.class);
                                            if (pdpStages.containsKey(LAUNCH_COMMAND_2HOTR) && !pdpStages.get(LAUNCH_COMMAND_2HOTR).equals(color)) {
                                                pdpStages.put(LAUNCH_COMMAND_2HOTR, color);
                                                if (getUI().isPresent()) {
                                                    getUI().get().access(() -> badgeBinder.readBean(pdpStages));
                                                }
                                            }
                                            otr1LaunchCancell = false;
                                            otr2LaunchCancell = false;

                                        }

                                        if (!startTimerUncommit && nppaService.checkCommandCommittedAndInTheTop("Otr1LaunchCancell")) {
                                            color = StatusToColorMapper.fromElementsStatuses(getLaunchCommandState(LaunchType.FIRST).getValueUa(),
                                                    (Class) AdjacentSystemStatus.class);
                                            if (pdpStages.containsKey(LAUNCH_COMMAND_OTR1) && !pdpStages.get(LAUNCH_COMMAND_OTR1).equals(color)) {
                                                pdpStages.put(LAUNCH_COMMAND_OTR1, color);
                                                if (getUI().isPresent()) {
                                                    getUI().get().access(() -> badgeBinder.readBean(pdpStages));
                                                }
                                            }
                                            color = StatusToColorMapper.fromElementsStatuses(getPpipWith2bCommandState().getValueUa(),
                                                    (Class) AdjacentSystemStatus.class);
                                            if (pdpStages.containsKey(PPIP_WITH_2B) && !pdpStages.get(PPIP_WITH_2B).equals(color)) {
                                                pdpStages.put(PPIP_WITH_2B, color);
                                                if (getUI().isPresent()) {
                                                    getUI().get().access(() -> badgeBinder.readBean(pdpStages));
                                                }
                                            }
                                            otr1LaunchCancell = true;
                                            outriggersInMobileStatePassedOTR1 = false;
                                            ppipWith2bCommandPassedOTR1 = false;
                                            chassisHorizontalPositionStatePassedOTR1 = false;
                                            if (otr1LaunchCancell && otr2LaunchCancell) {
                                                color = StatusToColorMapper.fromElementsStatuses(getLaunchCommandState(LaunchType.FIRST_AND_SECOND).getValueUa(),
                                                        (Class) AdjacentSystemStatus.class);
                                                if (pdpStages.containsKey(LAUNCH_COMMAND_2HOTR) && !pdpStages.get(LAUNCH_COMMAND_2HOTR).equals(color)) {
                                                    pdpStages.put(LAUNCH_COMMAND_2HOTR, color);
                                                    if (getUI().isPresent()) {
                                                        getUI().get().access(() -> badgeBinder.readBean(pdpStages));
                                                    }
                                                    color = StatusToColorMapper.fromElementsStatuses(getPpipWith2bCommandState().getValueUa(),
                                                            (Class) AdjacentSystemStatus.class);
                                                    if (pdpStages.containsKey(PPIP_WITH_2B) && !pdpStages.get(PPIP_WITH_2B).equals(color)) {
                                                        pdpStages.put(PPIP_WITH_2B, color);
                                                        if (getUI().isPresent()) {
                                                            getUI().get().access(() -> badgeBinder.readBean(pdpStages));
                                                        }
                                                    }
                                                    ppipWith2bCommandPassed2hOTR = false;
                                                    outriggersInMobileStatePassed2hOTR = false;
                                                    chassisHorizontalPositionStatePassed2hOTR = false;
                                                }
                                            }

                                            countdownOTR.stopCountdownOtr1();
                                        }

                                        if (!startTimerUncommit && nppaService.checkCommandCommittedAndInTheTop("Otr2LaunchCancell")) {
                                            otr2LaunchCancell = true;
                                            outriggersInMobileStatePassedOTR2 = false;
                                            ppipWith2bCommandPassedOTR2 = false;
                                            chassisHorizontalPositionStatePassedOTR2 = false;
                                            color = StatusToColorMapper.fromElementsStatuses(getLaunchCommandState(LaunchType.SECOND).getValueUa(),
                                                    (Class) AdjacentSystemStatus.class);
                                            if (pdpStages.containsKey(LAUNCH_COMMAND_OTR2) && !pdpStages.get(LAUNCH_COMMAND_OTR2).equals(color)) {
                                                pdpStages.put(LAUNCH_COMMAND_OTR2, color);
                                                if (getUI().isPresent()) {
                                                    getUI().get().access(() -> badgeBinder.readBean(pdpStages));
                                                }
                                            }
                                            color = StatusToColorMapper.fromElementsStatuses(getPpipWith2bCommandState().getValueUa(),
                                                    (Class) AdjacentSystemStatus.class);
                                            if (pdpStages.containsKey(PPIP_WITH_2B) && !pdpStages.get(PPIP_WITH_2B).equals(color)) {
                                                pdpStages.put(PPIP_WITH_2B, color);
                                                if (getUI().isPresent()) {
                                                    getUI().get().access(() -> badgeBinder.readBean(pdpStages));
                                                }
                                            }
                                            if (otr1LaunchCancell && otr2LaunchCancell) {
                                                color = StatusToColorMapper.fromElementsStatuses(getLaunchCommandState(LaunchType.FIRST_AND_SECOND).getValueUa(),
                                                        (Class) AdjacentSystemStatus.class);
                                                if (pdpStages.containsKey(LAUNCH_COMMAND_2HOTR) && !pdpStages.get(LAUNCH_COMMAND_2HOTR).equals(color)) {
                                                    pdpStages.put(LAUNCH_COMMAND_2HOTR, color);
                                                    if (getUI().isPresent()) {
                                                        getUI().get().access(() -> badgeBinder.readBean(pdpStages));
                                                    }
                                                }
                                                color = StatusToColorMapper.fromElementsStatuses(getPpipWith2bCommandState().getValueUa(),
                                                        (Class) AdjacentSystemStatus.class);
                                                if (pdpStages.containsKey(PPIP_WITH_2B) && !pdpStages.get(PPIP_WITH_2B).equals(color)) {
                                                    pdpStages.put(PPIP_WITH_2B, color);
                                                    if (getUI().isPresent()) {
                                                        getUI().get().access(() -> badgeBinder.readBean(pdpStages));
                                                    }
                                                }
                                                ppipWith2bCommandPassed2hOTR = false;
                                                outriggersInMobileStatePassed2hOTR = false;
                                                chassisHorizontalPositionStatePassed2hOTR = false;
                                            }
                                            countdownOTR.stopCountdownOtr2();
                                        }
                                        if (!startTimerUncommit && nppaService.checkCommandCommittedAndInTheTop("PPiPWithBg2bContinuation")) {
                                            if (countdownOTR.countdownTaskOtr1DoesNotExists() && countdownOTR.getCountdownOTR1Seconds() > 0
                                                && countdownOTR.countdownTaskOtr2DoesNotExists() && countdownOTR.getCountdownOTR2Seconds() > 0) {
                                                countdownOTR.continueCountDownOtr1();
                                                countdownOTR.continueCountDownOtr2();
                                            } else if (countdownOTR.countdownTaskOtr1DoesNotExists() && countdownOTR.getCountdownOTR1Seconds() > 0) {
                                                countdownOTR.continueCountDownOtr1();
                                            } else if (countdownOTR.countdownTaskOtr2DoesNotExists() && countdownOTR.getCountdownOTR2Seconds() > 0) {
                                                countdownOTR.continueCountDownOtr2();
                                            }
                                        }
                                    }
                                    AnimationColor finalColor = color;
                                    ui.access(() -> {
                                        pdpStages.put(key, finalColor);
                                        badgeBinder.readBean(pdpStages);
                                    });
                                }, failureResult -> {
                                    ui.access(() -> Notification.show("Стан команди " + nppaCommand.toStringUa() + " не було визначено"));
                                });
                    }
                    dialog.close();
                });
                confirmBt.addClassName("confirm-button-nppa-dialog");

                confirmBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY,
                        ButtonVariant.LUMO_ERROR);
                confirmBt.getStyle().set("margin-right", "auto");
                dialog.getFooter().add(confirmBt);
            } else {
                String errorMsg = commandValidationResult.errorMessageUa();
                log.error(errorMsg + " " + NppaView.class);

                ConfigurationUtils.getErrorNotification(errorMsg, "",
                        0, true).open();

                dialog.addClassName("dialog-nppa");

                dialog.setHeaderTitle("Помилка");
                dialog.add(commandValidationResult.errorMessageUa());
                return;
            }
            Button cancelButton = new Button("Відміна", (e) -> dialog.close());
            cancelButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
            cancelButton.addClassName("cancel-button-nppa-dialog");

            dialog.getFooter().add(cancelButton);


            dialog.open();

        });
        return sendBt;
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    private VerticalLayout createTabLaunchContent(LaunchType launchType, Readiness readieness) {

        VerticalLayout layout = new VerticalLayout();


        Span launchOTR1Caption = new Span("Пуск ОТР1");
        launchOTR1Caption.addClassName("launch-otr-caption");

        Span launchOTR2Caption = new Span("Пуск ОТР2");
        launchOTR2Caption.addClassName("launch-otr-caption");
        Span launch2hOTRCaption = new Span("Пуск ОТР1 та ОТР2");
        launch2hOTRCaption.addClassName("launch-otr-caption");

        HorizontalLayout launchOTRFirstRow = new HorizontalLayout();
        launchOTRFirstRow.addClassName("launch-OTR1-first-row");

        Span noteLaunchOTR1fromBG3Caption = new Span();

        NppaCommand launchOTR = null;


        switch (launchType) {
            case FIRST -> {
                switch (readieness) {
                    case BG_3 -> {
                        launchOTR = nppaService.getAvailableNcokCombatModeCommands().stream().
                                filter(e -> e.getCommand().equals("Otr1FromBg3Launch")).findFirst().orElseThrow(NotFoundException::new);
                        if (askuService.getRocketCopy(true).isPresent() &&
                            askuService.getRocketCopy(true).get().getInitialData() != null &&
                            askuService.getRocketCopy(true).get().getInitialData().isScheduled()) {
                            noteLaunchOTR1fromBG3Caption = new Span("*Необхідно видати команду до  " +
                                                                    askuService.getRocketCopy(true).get().getInitialData().getStartTime().format(DateTimeFormatter.ofPattern("dd/MM/yyyy\tHH:mm:ss")));
                            noteLaunchOTR1fromBG3Caption.addClassName("note-launch-OTR1-from-BG3-caption");
                        }

                    }
                    case BG_2B -> {
                        launchOTR = nppaService.getAvailableNcokCombatModeCommands().stream().
                                filter(e -> e.getCommand().equals("Otr1FromBg2bLaunch")).findFirst().orElseThrow(NotFoundException::new);
                        if (askuService.getRocketCopy(true).isPresent() &&
                            askuService.getRocketCopy(true).get().getInitialData() != null &&
                            askuService.getRocketCopy(true).get().getInitialData().isScheduled()) {
                            noteLaunchOTR1fromBG3Caption = new Span("*Необхідно видати команду до  " +
                                                                    askuService.getRocketCopy(true).get().getInitialData().getStartTime().format(DateTimeFormatter.ofPattern("dd/MM/yyyy\tHH:mm:ss")));
                        }
                        noteLaunchOTR1fromBG3Caption.addClassName("note-launch-OTR1-from-BG3-caption");
                    }
                    case BG_1 -> {
                        launchOTR = nppaService.getAvailableNcokCombatModeCommands().stream().
                                filter(e -> e.getCommand().equals("Otr1FromBg1Launch")).findFirst().orElseThrow(NotFoundException::new);
                        if (askuService.getRocketCopy(true).isPresent() &&
                            askuService.getRocketCopy(true).get().getInitialData() != null &&
                            askuService.getRocketCopy(true).get().getInitialData().isScheduled()) {
                            noteLaunchOTR1fromBG3Caption = new Span("*Необхідно видати команду до  " +
                                                                    askuService.getRocketCopy(true).get().getInitialData().getStartTime().format(DateTimeFormatter.ofPattern("dd/MM/yyyy\tHH:mm:ss")));
                        }
                        noteLaunchOTR1fromBG3Caption.addClassName("note-launch-OTR1-from-BG3-caption");
                    }
                }
            }
            case SECOND -> {
                switch (readieness) {
                    case BG_3 -> {
                        launchOTR = nppaService.getAvailableNcokCombatModeCommands().stream().
                                filter(e -> e.getCommand().equals("Otr2FromBg3Launch")).findFirst().orElseThrow(NotFoundException::new);
                        if (askuService.getRocketCopy(false).isPresent() &&
                            askuService.getRocketCopy(false).get().getInitialData() != null &&
                            askuService.getRocketCopy(false).get().getInitialData().isScheduled()) {
                            noteLaunchOTR1fromBG3Caption = new Span("*Необхідно видати команду до  " +
                                                                    askuService.getRocketCopy(false).get().getInitialData().getStartTime().format(DateTimeFormatter.ofPattern("dd/MM/yyyy\tHH:mm:ss")));
                        }
                        noteLaunchOTR1fromBG3Caption.addClassName("note-launch-OTR1-from-BG3-caption");
                    }
                    case BG_2B -> {
                        launchOTR = nppaService.getAvailableNcokCombatModeCommands().stream().
                                filter(e -> e.getCommand().equals("Otr2FromBg2bLaunch")).findFirst().orElseThrow(NotFoundException::new);
                        if (askuService.getRocketCopy(false).isPresent() &&
                            askuService.getRocketCopy(false).get().getInitialData() != null &&
                            askuService.getRocketCopy(false).get().getInitialData().isScheduled()) {
                            noteLaunchOTR1fromBG3Caption = new Span("*Необхідно видати команду до  " +
                                                                    askuService.getRocketCopy(false).get().getInitialData().getStartTime().format(DateTimeFormatter.ofPattern("dd/MM/yyyy\tHH:mm:ss")));
                        }
                        noteLaunchOTR1fromBG3Caption.addClassName("note-launch-OTR1-from-BG3-caption");
                    }
                    case BG_1 -> {
                        launchOTR = nppaService.getAvailableNcokCombatModeCommands().stream().
                                filter(e -> e.getCommand().equals("Otr2FromBg1Launch")).findFirst().orElseThrow(NotFoundException::new);
                        if (askuService.getRocketCopy(false).isPresent() &&
                            askuService.getRocketCopy(false).get().getInitialData() != null &&
                            askuService.getRocketCopy(false).get().getInitialData().isScheduled()) {
                            noteLaunchOTR1fromBG3Caption = new Span("*Необхідно видати команду до  " +
                                                                    askuService.getRocketCopy(false).get().getInitialData().getStartTime().format(DateTimeFormatter.ofPattern("dd/MM/yyyy\tHH:mm:ss")));
                        }
                        noteLaunchOTR1fromBG3Caption.addClassName("note-launch-OTR1-from-BG3-caption");
                    }
                }
            }
            case FIRST_AND_SECOND -> {
                switch (readieness) {
                    case BG_3 -> {
                        launchOTR = nppaService.getAvailableNcokCombatModeCommands().stream().
                                filter(e -> e.getCommand().equals("Otr1Otr2FromBg3Launch")).findFirst().orElseThrow(NotFoundException::new);
                        if (askuService.getRocketCopy(true).isPresent() &&
                            askuService.getRocketCopy(true).get().getInitialData() != null &&
                            askuService.getRocketCopy(true).get().getInitialData().isScheduled()) {
                            noteLaunchOTR1fromBG3Caption = new Span("*Необхідно видати команду до  " +
                                                                    askuService.getRocketCopy(true).get().getInitialData().getStartTime().format(DateTimeFormatter.ofPattern("dd/MM/yyyy\tHH:mm:ss")));
                        }
                        noteLaunchOTR1fromBG3Caption.addClassName("note-launch-OTR1-from-BG3-caption");
                    }
                    case BG_2B -> {
                        launchOTR = nppaService.getAvailableNcokCombatModeCommands().stream().
                                filter(e -> e.getCommand().equals("Otr1Otr2FromBg2bLaunch")).findFirst().orElseThrow(NotFoundException::new);
                        if (askuService.getRocketCopy(true).isPresent() &&
                            askuService.getRocketCopy(true).get().getInitialData() != null &&
                            askuService.getRocketCopy(true).get().getInitialData().isScheduled()) {
                            noteLaunchOTR1fromBG3Caption = new Span("*Необхідно видати команду до  " +
                                                                    askuService.getRocketCopy(true).get().getInitialData().getStartTime().format(DateTimeFormatter.ofPattern("dd/MM/yyyy\tHH:mm:ss")));
                        }
                        noteLaunchOTR1fromBG3Caption.addClassName("note-launch-OTR1-from-BG3-caption");
                    }
                    case BG_1 -> {
                        launchOTR = nppaService.getAvailableNcokCombatModeCommands().stream().
                                filter(e -> e.getCommand().equals("Otr1Otr2FromBg1Launch")).findFirst().orElseThrow(NotFoundException::new);
                        if (askuService.getRocketCopy(true).isPresent() &&
                            askuService.getRocketCopy(true).get().getInitialData() != null &&
                            askuService.getRocketCopy(true).get().getInitialData().isScheduled()) {
                            noteLaunchOTR1fromBG3Caption = new Span("*Необхідно видати команду до  " +
                                                                    askuService.getRocketCopy(true).get().getInitialData().getStartTime().format(DateTimeFormatter.ofPattern("dd/MM/yyyy\tHH:mm:ss")));
                        }
                        noteLaunchOTR1fromBG3Caption.addClassName("note-launch-OTR1-from-BG3-caption");
                    }
                }
            }
        }
        Badge checkStep1OTR1 = configureStageBadge(LAUNCH_COMMAND_OTR1,
                StatusToColorMapper.fromElementsStatuses(getLaunchCommandState(LaunchType.FIRST).getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);


        Span launchCaptionOTR1 = new Span(launchOTR.getCaption());

        Badge checkStep1OTR2 = configureStageBadge(LAUNCH_COMMAND_OTR2,
                StatusToColorMapper.fromElementsStatuses(getLaunchCommandState(LaunchType.SECOND).getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);


        Span launchCaptionOTR2 = new Span(launchOTR.getCaption());

        Badge checkStep12hOTR = configureStageBadge(LAUNCH_COMMAND_2HOTR,
                StatusToColorMapper.fromElementsStatuses(getLaunchCommandState(LaunchType.FIRST_AND_SECOND).getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);


        Span launchCaption2hOTR = new Span(launchOTR.getCaption());


        Button sendBtLaunchOtr1 = setupGenerateSendCommandButton(launchOTR, LAUNCH_COMMAND_OTR1, true, true);
        sendBtLaunchOtr1.addClassName("send-launch-button");
        sendBtLaunchOtr1.addThemeVariants(ButtonVariant.LUMO_PRIMARY, ButtonVariant.LUMO_SMALL);
        sendBtLaunchOtr1.getStyle().set("background-color", "#EB5757");

        Button sendBtLaunchOtr2 = setupGenerateSendCommandButton(launchOTR, LAUNCH_COMMAND_OTR2, true, true);
        sendBtLaunchOtr2.addClassName("send-launch-button");
        sendBtLaunchOtr2.addThemeVariants(ButtonVariant.LUMO_PRIMARY, ButtonVariant.LUMO_SMALL);
        sendBtLaunchOtr2.getStyle().set("background-color", "#EB5757");

        Button sendBtLaunch2hOtr = setupGenerateSendCommandButton(launchOTR, LAUNCH_COMMAND_2HOTR, true, true);
        sendBtLaunch2hOtr.addClassName("send-launch-button");
        sendBtLaunch2hOtr.addThemeVariants(ButtonVariant.LUMO_PRIMARY, ButtonVariant.LUMO_SMALL);
        sendBtLaunch2hOtr.getStyle().set("background-color", "#EB5757");


        NppaCommand cancellationOtr1 = nppaService.getAvailableNcokCombatModeCommands().stream().
                filter(e -> e.getCommand().equals("Otr1LaunchCancell")).findFirst().orElseThrow(NotFoundException::new);

        Span launchCancellationOTR1 = new Span(cancellationOtr1.getCaption());
        launchCancellationOTR1.addClassName("launch-cancellation-OTR1");

        Button sendBtLaunchCancellationOTR1 = setupGenerateSendCommandButton(cancellationOtr1, LAUNCH_COMMAND_CANCEL_OTR1, false, false);
        sendBtLaunchCancellationOTR1.addClassName("send-launch-button");
        sendBtLaunchCancellationOTR1.addThemeVariants(ButtonVariant.LUMO_PRIMARY, ButtonVariant.LUMO_SMALL);
        sendBtLaunchCancellationOTR1.getStyle().set("background-color", "#EB5757");

        NppaCommand cancellationOtr2 = nppaService.getAvailableNcokCombatModeCommands().stream().
                filter(e -> e.getCommand().equals("Otr2LaunchCancell")).findFirst().orElseThrow(NotFoundException::new);

        Span launchCancellationOTR2 = new Span(cancellationOtr2.getCaption());
        launchCancellationOTR2.addClassName("launch-cancellation-OTR2");

        Button sendBtLaunchCancellationOTR2 = setupGenerateSendCommandButton(cancellationOtr2, LAUNCH_COMMAND_CANCEL_OTR2, false, false);
        sendBtLaunchCancellationOTR2.addClassName("send-launch-button");
        sendBtLaunchCancellationOTR2.addThemeVariants(ButtonVariant.LUMO_PRIMARY, ButtonVariant.LUMO_SMALL);
        sendBtLaunchCancellationOTR2.getStyle().set("background-color", "#EB5757");

        TextField appNppa = new TextField("АПП НППА");
        appNppa.setReadOnly(true);
        appNppa.addClassName("app-nppa-pdp");
        appNppa.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        ncokBinder.forField(appNppa)
                .bindReadOnly(ncok -> {
                    try {
                        if (ncok != null) {
                            return ncok.isAppPresence() ? "Не норма" : "Норма";
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                        return "Невідомо";
                    }
                    return "Невідомо";
                });
        appNppa.addValueChangeListener(valueChangeEvent ->
                ConfigurationUtils.setBooleanToTextAnimation(
                        valueChangeEvent,
                        "Не норма",
                        "Норма",
                        AnimationColor.RED,
                        AnimationColor.GREEN)
        );
        appNppa.setValue((nppaService.getNcok().isPresent()) ?
                (nppaService.getNcok().get().appPresence() ? "Не норма" : "Норма") : unknownValue);

        TextField appOtrLeft = new TextField("АПП ОТР1");
        appOtrLeft.setReadOnly(true);
        appOtrLeft.addClassName("app-otr-left-pdp");
        appOtrLeft.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        ncokBinder.forField(appOtrLeft)
                .bindReadOnly(ncok -> {
                    try {
                        if (ncok != null) {
                            return ncok.isOtr1AppPresence() ? "Не норма" : "Норма";
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                        return "Невідомо";
                    }
                    return "Невідомо";
                });

        appOtrLeft.addValueChangeListener(valueChangeEvent ->
                ConfigurationUtils.setBooleanToTextAnimation(
                        valueChangeEvent,
                        "Не норма",
                        "Норма",
                        AnimationColor.RED,
                        AnimationColor.GREEN)
        );
        appOtrLeft.setValue((nppaService.getNcok().isPresent()) ?
                (nppaService.getNcok().get().otr1AppPresence() ? "Не норма" : "Норма") : unknownValue);

        TextField appOtrRight = new TextField("АПП ОТР2");
        appOtrRight.setReadOnly(true);
        appOtrRight.addClassName("app-otr-right-pdp");
        appOtrRight.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        ncokBinder.forField(appOtrRight)
                .bindReadOnly(ncok -> {
                    try {
                        if (ncok != null) {
                            return ncok.isOtr2AppPresence() ? "Не норма" : "Норма";
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                        return "Невідомо";
                    }
                    return "Невідомо";
                });

        appOtrRight.addValueChangeListener(valueChangeEvent ->
                ConfigurationUtils.setBooleanToTextAnimation(
                        valueChangeEvent,
                        "Не норма",
                        "Норма",
                        AnimationColor.RED,
                        AnimationColor.GREEN)
        );
        appOtrRight.setValue((nppaService.getNcok().isPresent()) ?
                (nppaService.getNcok().get().otr2AppPresence() ? "Не норма" : "Норма") : unknownValue);

        double basuOtr1Temperature = askuService.getRocketCopy(true).isPresent() ?
                askuService.getRocketCopy(true).get().getSensorTemperature()
                : -999.9;
        Component sensorLayoutOTR1 = ConfigurationUtils.buildRocketSensorsLayout(
                askuService,
                "basu-temperature-otr1-pdp",
                rocketBinder,
                true,
                "Температура БАСУ 1", String.valueOf(basuOtr1Temperature));

        double basuOtr2Temperature = askuService.getRocketCopy(false).isPresent() ?
                askuService.getRocketCopy(false).get().getSensorTemperature()
                : -999.9;
        Component sensorLayoutOTR2 = ConfigurationUtils.buildRocketSensorsLayout(
                askuService,
                "basu-temperature-otr2-pdp",
                rocketBinder,
                false,
                "Температура БАСУ 2",
                String.valueOf(basuOtr2Temperature));

        HorizontalLayout preparationForLaunchSevenRow = new HorizontalLayout();
        preparationForLaunchSevenRow.addClassName("preparation-for-launch-seven-row");

        Badge checkStepNcok = configureStageBadge(TV_NCOK_LAUNCH,
                StatusToColorMapper.fromElementsStatuses(getTVNcokStageState().getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);

        TextField tvNcok = new TextField("ТВ НЦОК");
        tvNcok.setReadOnly(true);
        tvNcok.addClassName("tv-ncok-pdp");
        tvNcok.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        ncokBinder.forField(tvNcok)
                .bindReadOnly(ncok -> {
                    try {
                        if (ncok != null && ncok.getTvNcok() != null) {
                            return ncok.getTvNcok().getValueUa();
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                        return "Невідомо";
                    }
                    return AdjacentSystemStatus.UNDEFINED.getValueUa();
                });
        tvNcok.addValueChangeListener(valueChangeEvent -> ConfigurationUtils.setTextAnimation(valueChangeEvent, BaseProperty.class));
        tvNcok.setValue((nppaService.getNcok().isPresent() && nppaService.getNcok().get().tvNcok() != null) ?
                nppaService.getNcok().get().tvNcok().getValueUa() : BaseProperty.UNDEFINED.getValueUa()
        );

        HorizontalLayout launchOTRFourthRow = new HorizontalLayout();
        launchOTRFourthRow.addClassName("launch-OTR1-fourth-row");


        Badge checkStep4OTR1 = configureStageBadge(OUTRIGGERS_IN_MOBILE_STATE_OTR1,
                StatusToColorMapper.fromElementsStatuses(getOutrrigersInMobileStageStateOTR1().getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);

        Badge checkStep4OTR2 = configureStageBadge(OUTRIGGERS_IN_MOBILE_STATE_OTR2,
                StatusToColorMapper.fromElementsStatuses(getOutrrigersInMobileStageStateOTR2().getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);

        Badge checkStep42hOTR = configureStageBadge(OUTRIGGERS_IN_MOBILE_STATE_2HOTR,
                StatusToColorMapper.fromElementsStatuses(getOutrrigersInMobileStageState2hOTR().getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);

        Span outriggersDerivedPosition = new Span("Аутригери в похідному положені (ПП)");
        outriggersDerivedPosition.addClassName("outriggers-derived-position-pdp");

        Span noteOutriggersDerivedPosition = new Span("*Після підняття аутригерів переміщення СПУ на стартову позицію.");
        noteOutriggersDerivedPosition.addClassName("note-outriggers-derived-position");
        HorizontalLayout launchOTRFiveRow = new HorizontalLayout();
        launchOTRFiveRow.addClassName("launch-OTR1-five-row");


        NppaCommand ppipWithBG2b = nppaService.getAvailableNcokCombatModeCommands().stream().
                filter(e -> e.getCommand().equals("PPiPWithBg2bContinuation")).findFirst().orElseThrow(NotFoundException::new);

        Badge checkStep5 = configureStageBadge(PPIP_WITH_2B,
                StatusToColorMapper.fromElementsStatuses(getPpipWith2bCommandState().getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);

        Span continuePPiPfromBG2b = new Span(ppipWithBG2b.getCaption());
        Button sendBtcontinuePPiPfromBG2b = setupGenerateSendCommandButton(ppipWithBG2b, PPIP_WITH_2B, false, true);
        sendBtcontinuePPiPfromBG2b.addClassName("send-sentence-button-ppip");
        sendBtcontinuePPiPfromBG2b.addThemeVariants(ButtonVariant.LUMO_PRIMARY, ButtonVariant.LUMO_SMALL);
        sendBtcontinuePPiPfromBG2b.getStyle().set("background-color", "#4F4F4F");

        Span noteOutriggersDerivedPositionAfter = new Span("*Після зайняття стартової позиції видати команду.");
        noteOutriggersDerivedPositionAfter.addClassName("note-outriggers-derived-position-after");

        HorizontalLayout launchOTR1SixthRow = new HorizontalLayout();
        launchOTR1SixthRow.addClassName("launch-OTR1-sixth-row");


        Badge checkStep6OTR1 = configureStageBadge(CHASSIS_HORIZONTAL_POSITION_STATE_OTR1,
                StatusToColorMapper.fromElementsStatuses(getChassisHorizontalPositionStateOTR1().getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);

        Span outriggersHorizontalPosition = new Span("Шасі в горизонтальному положенні");
        outriggersHorizontalPosition.addClassName("outriggers-horizontal-position");

        Badge checkStep6OTR2 = configureStageBadge(CHASSIS_HORIZONTAL_POSITION_STATE_OTR2,
                StatusToColorMapper.fromElementsStatuses(getChassisHorizontalPositionStateOTR2().getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);

        Badge checkStep62hOTR = configureStageBadge(CHASSIS_HORIZONTAL_POSITION_STATE_2HOTR,
                StatusToColorMapper.fromElementsStatuses(getChassisHorizontalPositionState2hOTR().getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);


        HorizontalLayout launchOTRSevenRow = new HorizontalLayout();
        launchOTRSevenRow.addClassName("launch-OTR1-seven-row");

        Button sendBt = new Button("Відправити");
        sendBt.addClassName("send-launch-button");
        sendBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY, ButtonVariant.LUMO_SMALL);
        sendBt.getStyle().set("background-color", "#EB5757");

        Badge checkStep61 = configureStageBadge(STARTED_INITIAL_SET_SK_BINS_OTR1,
                StatusToColorMapper.fromElementsStatuses(getStartedInitialSetSkBinsOtr1().getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);

        Badge checkStep612 = configureStageBadge(STARTED_INITIAL_SET_SK_BINS_OTR2,
                StatusToColorMapper.fromElementsStatuses(getStartedInitialSetSkBinsOtr2().getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);

        Span noteSkBins = new Span("Розпочато початкове виставлення СК БІНС");
        noteSkBins.addClassName("note-sk-bins");

        HorizontalLayout launchStartedInitialSkBins = new HorizontalLayout();
        launchStartedInitialSkBins.addClassName("launch-started-initial-sk-bins");

        Badge checkStep62 = configureStageBadge(PRECISE_DRIVE,
                StatusToColorMapper.fromElementsStatuses(getPreciseDrive().getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);

        Span notePreciseDrive = new Span("Закінчення точного приведення");
        notePreciseDrive.addClassName("note-precise-drive");

        TextField launchTvNoInsOtr1 = new TextField("ТВ НО ІНС ОТР1");
        launchTvNoInsOtr1.setReadOnly(true);
        launchTvNoInsOtr1.addClassName("launch-tv-no-ins-otr1");
        launchTvNoInsOtr1.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        ncokBinder.forField(launchTvNoInsOtr1)
                .bindReadOnly(ncok -> {
                    try {
                        if (ncok != null) {
                            return ncok.getOtr1tvNoIns().getValueUa();
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                        return AdjacentSystemStatus.UNDEFINED.getValueUa();
                    }
                    return AdjacentSystemStatus.UNDEFINED.getValueUa();
                });
        launchTvNoInsOtr1.addValueChangeListener(valueChangeEvent ->
                ConfigurationUtils.setTextAnimation(valueChangeEvent, BaseProperty.class));
        launchTvNoInsOtr1.setValue((nppaService.getNcok().isPresent() && nppaService.getNcok().get().otr1tvNoIns() != null) ?
                nppaService.getNcok().get().otr1tvNoIns().getValueUa() : AdjacentSystemStatus.UNDEFINED.getValueUa());

        TextField launchTvNoInsOtr2 = new TextField("ТВ НО ІНС ОТР2");
        launchTvNoInsOtr2.setReadOnly(true);
        launchTvNoInsOtr2.addClassName("launch-tv-no-ins-otr2");
        launchTvNoInsOtr2.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        ncokBinder.forField(launchTvNoInsOtr2)
                .bindReadOnly(ncok -> {
                    try {
                        if (ncok != null) {
                            return ncok.getOtr2tvNoIns().getValueUa();
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                        return AdjacentSystemStatus.UNDEFINED.getValueUa();
                    }
                    return AdjacentSystemStatus.UNDEFINED.getValueUa();
                });
        launchTvNoInsOtr2.addValueChangeListener(valueChangeEvent ->
                ConfigurationUtils.setTextAnimation(valueChangeEvent, BaseProperty.class));
        launchTvNoInsOtr2.setValue((nppaService.getNcok().isPresent() && nppaService.getNcok().get().otr2tvNoIns() != null) ?
                nppaService.getNcok().get().otr2tvNoIns().getValueUa() : AdjacentSystemStatus.UNDEFINED.getValueUa());

        TextField launchBasSnsPzOtr1 = new TextField("БАС СНС ПЗ ОТР1");
        launchBasSnsPzOtr1.setReadOnly(true);
        launchBasSnsPzOtr1.addClassName("launch-bas-sns-pz-otr1");
        launchBasSnsPzOtr1.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        ncokBinder.forField(launchBasSnsPzOtr1)
                .bindReadOnly(ncok -> {
                    try {
                        if (ncok != null) {
                            return ncok.getOtr1BasSnsPz().getValueUa();
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                        return AdjacentSystemStatus.UNDEFINED.getValueUa();
                    }
                    return AdjacentSystemStatus.UNDEFINED.getValueUa();
                });
        launchBasSnsPzOtr1.addValueChangeListener(valueChangeEvent ->
                ConfigurationUtils.setTextAnimation(valueChangeEvent, BasSnsStatus.class));
        launchBasSnsPzOtr1.setValue((nppaService.getNcok().isPresent() && nppaService.getNcok().get().otr1BasSnsPz() != null) ?
                nppaService.getNcok().get().otr1BasSnsPz().getValueUa() : BasSnsStatus.UNDEFINED.getValueUa());

        TextField launchBasSnsPzOtr2 = new TextField("БАС СНС ПЗ ОТР2");
        launchBasSnsPzOtr2.setReadOnly(true);
        launchBasSnsPzOtr2.addClassName("launch-bas-sns-pz-otr2");
        launchBasSnsPzOtr2.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        ncokBinder.forField(launchBasSnsPzOtr2)
                .bindReadOnly(ncok -> {
                    try {
                        if (ncok != null) {
                            return ncok.getOtr2BasSnsPz().getValueUa();
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                        return AdjacentSystemStatus.UNDEFINED.getValueUa();
                    }
                    return AdjacentSystemStatus.UNDEFINED.getValueUa();
                });
        launchBasSnsPzOtr2.addValueChangeListener(valueChangeEvent ->
                ConfigurationUtils.setTextAnimation(valueChangeEvent, BasSnsStatus.class));
        launchBasSnsPzOtr2.setValue((nppaService.getNcok().isPresent() && nppaService.getNcok().get().otr2BasSnsPz() != null) ?
                nppaService.getNcok().get().otr2BasSnsPz().getValueUa() : BasSnsStatus.UNDEFINED.getValueUa());

        HorizontalLayout launchPreciseDrive = new HorizontalLayout();
        launchPreciseDrive.addClassName("launch-precise-drive");

        HorizontalLayout launchTvNoBasSns = new HorizontalLayout();
        launchTvNoBasSns.addClassName("launch-tv-no-bas-sns");

        Badge checkStep7 = configureStageBadge(ARROW_UP_LAUNCH,
                StatusToColorMapper.fromElementsStatuses(getArrowUpLaunchStageState().getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);

        Span arrowUp = new Span("Cтріла піднята");

        Span noteArrowUp = new Span("*Початок не зворотніх процесів ~ 3 с до кінця таймеру.");
        noteArrowUp.addClassName("note-arrow-up");

        HorizontalLayout launchOTREightRow = new HorizontalLayout();
        launchOTREightRow.addClassName("launch-OTR1-eight-row");


        Badge checkStep8 = configureStageBadge(LAUNCH_OTR_LEFT_PDP,
                StatusToColorMapper.fromElementsStatuses(getLaunchOtrLeftStageState().getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);


        TextField launchOtr1Left = new TextField("Схід з СПУ ОТР1");
        launchOtr1Left.setReadOnly(true);
        launchOtr1Left.addClassName("launch-otr-left-pdp");
        launchOtr1Left.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        ncokBinder.forField(launchOtr1Left)
                .bindReadOnly(ncok -> {
                    try {
                        if (ncok != null) {
                            return ncok.getIsOtr1Lunched().getValueUa();
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                        return AdjacentSystemStatus.UNDEFINED.getValueUa();
                    }
                    return AdjacentSystemStatus.UNDEFINED.getValueUa();
                });
        launchOtr1Left.addValueChangeListener(valueChangeEvent ->
                ConfigurationUtils.setTextAnimation(valueChangeEvent, BaseProperty.class));
        launchOtr1Left.setValue((nppaService.getNcok().isPresent() && nppaService.getNcok().get().isOtr1Lunched() != null) ?
                nppaService.getNcok().get().isOtr1Lunched().getValueUa() : BaseProperty.UNDEFINED.getValueUa());


        HorizontalLayout launchOTRNineRow = new HorizontalLayout();
        launchOTRNineRow.addClassName("launch-OTR1-nine-row");

        HorizontalLayout launchOTRReadiness2 = new HorizontalLayout();
        launchOTRReadiness2.addClassName("launch-OTR-readiness-2");


        Badge checkStep9 = configureStageBadge(LAUNCH_OTR_RIGHT_PDP,
                StatusToColorMapper.fromElementsStatuses(getLaunchOtrRightStageState().getValueUa(), (Class) AdjacentSystemStatus.class),
                badgeBinder, pdpStages);


        TextField launchOtrRight = new TextField("Схід з СПУ ОТР2");
        launchOtrRight.setReadOnly(true);
        launchOtrRight.addClassName("launch-otr-right-pdp");
        launchOtrRight.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        ncokBinder.forField(launchOtrRight)
                .bindReadOnly(ncok -> {
                    try {
                        if (ncok != null) {
                            return ncok.getIsOtr2Lunched().getValueUa();
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                        return AdjacentSystemStatus.UNDEFINED.getValueUa();
                    }
                    return AdjacentSystemStatus.UNDEFINED.getValueUa();
                });
        launchOtrRight.addValueChangeListener(valueChangeEvent ->
                ConfigurationUtils.setTextAnimation(valueChangeEvent, BaseProperty.class));
        launchOtrRight.setValue((nppaService.getNcok().isPresent() && nppaService.getNcok().get().isOtr1Lunched() != null) ?
                nppaService.getNcok().get().isOtr1Lunched().getValueUa() : BaseProperty.UNDEFINED.getValueUa());


        HorizontalLayout launchOTRTenRow = new HorizontalLayout();
        launchOTRTenRow.addClassName("launch-OTR1-ten-row");

        HorizontalLayout launchOTRSensor = new HorizontalLayout();
        launchOTRSensor.addClassName("launch-OTR-sensor");


        HorizontalLayout launchOTRElevenRow = new HorizontalLayout();
        launchOTRElevenRow.addClassName("launch-OTR1-eleven-row");


        switch (readieness) {
            case BG_3 -> {
                if (launchType == LaunchType.FIRST) {

                    launchOTRFirstRow.add(checkStep1OTR1, launchCaptionOTR1, sendBtLaunchOtr1, noteLaunchOTR1fromBG3Caption);
                    preparationForLaunchSevenRow.add(checkStepNcok, tvNcok, sensorLayoutOTR1);
                    launchOTRFourthRow.add(checkStep6OTR1, outriggersHorizontalPosition, launchBasSnsPzOtr1, launchTvNoInsOtr1);
                    launchStartedInitialSkBins.add(checkStep61, noteSkBins, appNppa, appOtrLeft);
                    launchPreciseDrive.add(checkStep62, notePreciseDrive);
                    launchOTRElevenRow.add(launchCancellationOTR1, sendBtLaunchCancellationOTR1);
                    launchOTRElevenRow.addClassName("launch-fourth-row-bg3");
                } else if (launchType == LaunchType.SECOND) {
                    launchOTRFirstRow.add(checkStep1OTR2, launchCaptionOTR2, sendBtLaunchOtr2, noteLaunchOTR1fromBG3Caption);
                    preparationForLaunchSevenRow.add(checkStepNcok, tvNcok, sensorLayoutOTR2);
                    launchOTRFourthRow.add(checkStep6OTR2, outriggersHorizontalPosition, launchBasSnsPzOtr2, launchTvNoInsOtr2);
                    launchStartedInitialSkBins.add(checkStep612, noteSkBins, appNppa, appOtrRight);
                    launchPreciseDrive.add(checkStep62, notePreciseDrive);
                    launchOTRElevenRow.add(launchCancellationOTR2, sendBtLaunchCancellationOTR2);
                    launchOTRElevenRow.addClassName("launch-fourth-row-bg3");
                } else {
                    launchOTRFirstRow.add(checkStep12hOTR, launchCaption2hOTR, sendBtLaunch2hOtr, noteLaunchOTR1fromBG3Caption);
                    preparationForLaunchSevenRow.add(checkStepNcok, tvNcok, sensorLayoutOTR1, sensorLayoutOTR2);
                    launchOTRFourthRow.add(checkStep62hOTR, outriggersHorizontalPosition, launchBasSnsPzOtr1, launchTvNoInsOtr1,
                            launchBasSnsPzOtr2, launchTvNoInsOtr2);
                    launchStartedInitialSkBins.add(checkStep61, noteSkBins, appNppa, appOtrLeft, appOtrRight);
                    launchPreciseDrive.add(checkStep62, notePreciseDrive);
                    launchOTRElevenRow.add(launchCancellationOTR1, sendBtLaunchCancellationOTR1,
                            launchCancellationOTR2, sendBtLaunchCancellationOTR2);
                    launchOTRElevenRow.addClassName("launch-fourth-row-bg3");
                }
                launchOTRSevenRow.add(checkStep7, arrowUp, noteArrowUp);
                launchOTREightRow.add(checkStep8, launchOtr1Left);
                launchOTRNineRow.add(checkStep9, launchOtrRight);

            }
            case BG_2B -> {

                if (launchType == LaunchType.FIRST) {
                    launchOTRFirstRow.add(checkStep1OTR1, launchCaptionOTR1, sendBtLaunchOtr1, noteLaunchOTR1fromBG3Caption);
                    preparationForLaunchSevenRow.add(checkStepNcok, tvNcok, sensorLayoutOTR1);
                    launchOTRFiveRow.add(checkStep61, noteSkBins);
                    launchOTR1SixthRow.add(checkStep62, notePreciseDrive, launchBasSnsPzOtr1, launchTvNoInsOtr1);
                    launchOTRFourthRow.add(checkStep4OTR1, outriggersDerivedPosition, noteOutriggersDerivedPosition);
                    launchStartedInitialSkBins.add(checkStep5, continuePPiPfromBG2b, sendBtcontinuePPiPfromBG2b, noteOutriggersDerivedPositionAfter);

                    launchPreciseDrive.add(checkStep6OTR1, outriggersHorizontalPosition, appNppa, appOtrLeft);
                    launchOTRElevenRow.add(launchCancellationOTR1, sendBtLaunchCancellationOTR1);
                    launchOTRElevenRow.addClassName("launch-fourth-row-bg3");
                    launchOTRSevenRow.add(checkStep7, arrowUp, noteArrowUp);
                    launchOTREightRow.add(checkStep8, launchOtr1Left);
                } else if (launchType == LaunchType.SECOND) {
                    launchOTRFirstRow.add(checkStep1OTR2, launchCaptionOTR2, sendBtLaunchOtr2, noteLaunchOTR1fromBG3Caption);
                    preparationForLaunchSevenRow.add(checkStepNcok, tvNcok, sensorLayoutOTR2);
                    launchOTRFiveRow.add(checkStep612, noteSkBins);
                    launchOTR1SixthRow.add(checkStep62, notePreciseDrive, launchBasSnsPzOtr2, launchTvNoInsOtr2);
                    launchOTRFourthRow.add(checkStep4OTR2, outriggersDerivedPosition, noteOutriggersDerivedPosition);
                    launchStartedInitialSkBins.add(checkStep5, continuePPiPfromBG2b, sendBtcontinuePPiPfromBG2b, noteOutriggersDerivedPositionAfter);
                    launchPreciseDrive.add(checkStep6OTR2, outriggersHorizontalPosition, appNppa, appOtrRight);
                    launchOTRElevenRow.add(launchCancellationOTR2, sendBtLaunchCancellationOTR2);
                    launchOTRElevenRow.addClassName("launch-fourth-row-bg3");
                    launchOTRSevenRow.add(checkStep7, arrowUp, noteArrowUp);
                    launchOTRNineRow.add(checkStep9, launchOtrRight);
                } else {
                    launchOTRFirstRow.add(checkStep12hOTR, launchCaption2hOTR, sendBtLaunch2hOtr, noteLaunchOTR1fromBG3Caption);
                    preparationForLaunchSevenRow.add(checkStepNcok, tvNcok, sensorLayoutOTR1, sensorLayoutOTR2);

                    launchOTRFiveRow.add(checkStep61, noteSkBins);
                    launchOTR1SixthRow.add(checkStep62, notePreciseDrive, launchBasSnsPzOtr1, launchTvNoInsOtr1, launchBasSnsPzOtr2,
                            launchTvNoInsOtr2);
                    launchOTRFourthRow.add(checkStep42hOTR, outriggersDerivedPosition, noteOutriggersDerivedPosition);
                    launchStartedInitialSkBins.add(checkStep5, continuePPiPfromBG2b, sendBtcontinuePPiPfromBG2b, noteOutriggersDerivedPositionAfter);
                    launchPreciseDrive.add(checkStep62hOTR, outriggersHorizontalPosition, appNppa, appOtrLeft, appOtrRight);
                    launchOTRElevenRow.add(launchCancellationOTR1, sendBtLaunchCancellationOTR1,
                            launchCancellationOTR2, sendBtLaunchCancellationOTR2);
                    launchOTRElevenRow.addClassName("launch-fourth-row-bg3");
                    launchOTRSevenRow.add(checkStep7, arrowUp, noteArrowUp);
                    launchOTRReadiness2.add(checkStep8, launchOtr1Left, checkStep9, launchOtrRight);
                }
            }

            case BG_1 -> {
                if (launchType == LaunchType.FIRST) {
                    launchOTRFirstRow.add(checkStep1OTR1, launchCaptionOTR1, sendBtLaunchOtr1, noteLaunchOTR1fromBG3Caption);
                    launchPreciseDrive.add(checkStep62, notePreciseDrive, appNppa, appOtrLeft);
                    launchOTRSensor.add(sensorLayoutOTR1);
                    launchOTRTenRow.add(launchBasSnsPzOtr1, launchTvNoInsOtr1);
                    launchOTRElevenRow.add(launchCancellationOTR1, sendBtLaunchCancellationOTR1);
                    launchOTRElevenRow.addClassName("launch-fourth-row-bg3");
                } else if (launchType == LaunchType.SECOND) {
                    launchOTRFirstRow.add(checkStep1OTR2, launchCaptionOTR2, sendBtLaunchOtr2, noteLaunchOTR1fromBG3Caption);
                    launchPreciseDrive.add(checkStep62, notePreciseDrive, appNppa, appOtrRight);
                    launchOTRSensor.add(sensorLayoutOTR2);
                    launchOTRTenRow.add(launchBasSnsPzOtr2, launchTvNoInsOtr2);
                    launchOTRElevenRow.add(launchCancellationOTR2, sendBtLaunchCancellationOTR2);
                    launchOTRElevenRow.addClassName("launch-fourth-row-bg3");
                } else {
                    launchOTRFirstRow.add(checkStep12hOTR, launchCaption2hOTR, sendBtLaunch2hOtr, noteLaunchOTR1fromBG3Caption);
                    launchPreciseDrive.add(checkStep62, notePreciseDrive, appNppa, appOtrLeft, appOtrRight
                    );
                    launchOTRSensor.add(sensorLayoutOTR1, sensorLayoutOTR2);
                    launchOTRTenRow.add(launchBasSnsPzOtr1, launchTvNoInsOtr1, launchBasSnsPzOtr2, launchTvNoInsOtr2);
                    launchOTRElevenRow.add(launchCancellationOTR1, sendBtLaunchCancellationOTR1,
                            launchCancellationOTR2, sendBtLaunchCancellationOTR2);
                    launchOTRElevenRow.addClassName("launch-fourth-row-bg3");
                }
                launchOTRSevenRow.add(checkStep7, arrowUp, noteArrowUp);
                launchOTREightRow.add(checkStep8, launchOtr1Left);
                launchOTRNineRow.add(checkStep9, launchOtrRight);
            }
        }

        switch (launchType) {
            case FIRST -> {
                layout.add(launchOTR1Caption, launchOTRFirstRow, preparationForLaunchSevenRow, launchOTRElevenRow, launchOTRFiveRow, launchOTR1SixthRow
                        , launchOTRFourthRow, launchOTRSensor, launchOTRTenRow, launchStartedInitialSkBins, launchPreciseDrive, launchTvNoBasSns, launchOTRElevenRow, launchOTRSevenRow, launchOTREightRow);

            }
            case SECOND -> {
                layout.add(launchOTR2Caption, launchOTRFirstRow, preparationForLaunchSevenRow, launchOTRElevenRow, launchOTRFiveRow, launchOTR1SixthRow
                        , launchOTRFourthRow, launchOTRSensor, launchOTRTenRow, launchStartedInitialSkBins, launchPreciseDrive, launchTvNoBasSns, launchOTRElevenRow, launchOTRSevenRow, launchOTRNineRow);
            }
            case FIRST_AND_SECOND -> {
                layout.add(launch2hOTRCaption, launchOTRFirstRow, preparationForLaunchSevenRow, launchOTRFiveRow, launchOTR1SixthRow
                        , launchOTRFourthRow, launchOTRSensor, launchOTRTenRow, launchStartedInitialSkBins, launchPreciseDrive, launchTvNoBasSns,
                        launchOTRElevenRow, launchOTRSevenRow, launchOTREightRow, launchOTRNineRow, launchOTRReadiness2);
            }
        }
        return layout;

    }

    private AdjacentSystemStatus getBinsStageState() {

        updateWorkflowIndicatorsBadgeLaunch();
        return (binsService.isConnected() && binsService.getBinsPosition(false).isPresent()
                && binsService.getBinsPosition(false).get().getLongitude() != 0.0) ? AdjacentSystemStatus.OK : AdjacentSystemStatus.UNDEFINED;
    }

    private AdjacentSystemStatus getInitialConditionsState(LaunchType launchType) {
        updateWorkflowIndicatorsBadgeLaunch();
        switch (launchType) {
            case FIRST -> {
                if (askuService.getRocketCopy(true).isPresent() &&
                    askuService.getRocketCopy(true).get().getInitialDataTS() != null) {
                    return AdjacentSystemStatus.OK;
                } else {
                    return AdjacentSystemStatus.UNDEFINED;
                }
            }
            case SECOND -> {
                if (askuService.getRocketCopy(false).isPresent() &&
                    askuService.getRocketCopy(false).get().getInitialDataTS() != null) {

                    return AdjacentSystemStatus.OK;
                } else {
                    return AdjacentSystemStatus.UNDEFINED;
                }
            }
        }
        return AdjacentSystemStatus.UNDEFINED;
    }

    private AdjacentSystemStatus getCheckedOTR(LaunchType launchType) {
        switch (launchType) {
            case FIRST -> {
                if (nppaService.getNcok().get().otr1TestResult().equals(BaseProperty.OK)) {
                    return AdjacentSystemStatus.OK;
                } else {
                    return AdjacentSystemStatus.UNDEFINED;
                }

            }
            case SECOND -> {
                if (nppaService.getNcok().get().otr2TestResult().equals(BaseProperty.OK)) {
                    return AdjacentSystemStatus.OK;
                } else {
                    return AdjacentSystemStatus.UNDEFINED;
                }
            }
        }
        return AdjacentSystemStatus.UNDEFINED;
    }

    private AdjacentSystemStatus getTVBynStageState() {
        updateWorkflowIndicatorsBadgeLaunch();
        return (nppaService.getByn().get().tvByn().equals(BaseProperty.OK) && nppaService.getByn().get().isConnected()) ? AdjacentSystemStatus.OK : AdjacentSystemStatus.UNDEFINED;
    }


    private AdjacentSystemStatus getTVNcokStageState() {
        updateWorkflowIndicatorsBadgeLaunch();
        return (nppaService.getNcok().get().tvNcok().equals(BaseProperty.OK) && nppaService.getNcok().get().isNcokConnected()) ? AdjacentSystemStatus.OK : AdjacentSystemStatus.UNDEFINED;
    }


    private AdjacentSystemStatus getArrowUpLaunchStageState() {
        updateWorkflowIndicatorsBadgeLaunch();
        return (sutoService.getSutoPropertyByName("armRaised").get().getState().equals(CommandState.ON)
                && sutoService.getStatus().equals(AdjacentSystemStatus.OK)) ? AdjacentSystemStatus.OK : AdjacentSystemStatus.UNDEFINED;
    }


    private AdjacentSystemStatus getOutrrigersInMobileStageStateOTR1() {
        updateWorkflowIndicatorsBadgeLaunch();
        return outriggersInMobileStatePassedOTR1 ? AdjacentSystemStatus.OK : AdjacentSystemStatus.UNDEFINED;
    }

    private AdjacentSystemStatus getOutrrigersInMobileStageStateOTR2() {
        updateWorkflowIndicatorsBadgeLaunch();
        return outriggersInMobileStatePassedOTR2 ? AdjacentSystemStatus.OK : AdjacentSystemStatus.UNDEFINED;
    }

    private AdjacentSystemStatus getOutrrigersInMobileStageState2hOTR() {
        updateWorkflowIndicatorsBadgeLaunch();
        return outriggersInMobileStatePassed2hOTR ? AdjacentSystemStatus.OK : AdjacentSystemStatus.UNDEFINED;
    }


    private AdjacentSystemStatus getLaunchCommandState(LaunchType launchType) {
        updateWorkflowIndicatorsBadgeLaunch();
        switch (launchType) {
            case FIRST -> {
                if ((nppaService.checkCommandCommittedAndInTheTop("Otr1FromBg3Launch") ||
                     nppaService.checkCommandCommittedAndInTheTop("Otr1FromBg2bLaunch") ||
                     nppaService.checkCommandCommittedAndInTheTop("Otr1FromBg1Launch")) ||
                    nppaService.checkCommandCommittedAndInTheTop("PPiPWithBg2bContinuation")) {
                    return AdjacentSystemStatus.OK;
                } else return AdjacentSystemStatus.UNDEFINED;

            }
            case SECOND -> {
                if ((nppaService.checkCommandCommittedAndInTheTop("Otr2FromBg3Launch") ||
                     nppaService.checkCommandCommittedAndInTheTop("Otr2FromBg2bLaunch") ||
                     nppaService.checkCommandCommittedAndInTheTop("Otr2FromBg1Launch")) ||
                    nppaService.checkCommandCommittedAndInTheTop("PPiPWithBg2bContinuation")) {
                    return AdjacentSystemStatus.OK;
                } else return AdjacentSystemStatus.UNDEFINED;
            }
            case FIRST_AND_SECOND -> {
                if ((nppaService.checkCommandCommittedAndInTheTop("Otr1Otr2FromBg3Launch") ||
                     nppaService.checkCommandCommittedAndInTheTop("Otr1Otr2FromBg2bLaunch") ||
                     nppaService.checkCommandCommittedAndInTheTop("Otr1Otr2FromBg1Launch")) ||
                    nppaService.checkCommandCommittedAndInTheTop("PPiPWithBg2bContinuation")) {
                    return AdjacentSystemStatus.OK;
                } else return AdjacentSystemStatus.UNDEFINED;
            }
        }
        return AdjacentSystemStatus.UNDEFINED;
    }

    private AdjacentSystemStatus getPpipWith2bCommandState() {
        updateWorkflowIndicatorsBadgeLaunch();
        return (nppaService.checkCommandCommittedAndInTheTop("PPiPWithBg2bContinuation")) ? AdjacentSystemStatus.OK : AdjacentSystemStatus.UNDEFINED;
    }

    private AdjacentSystemStatus getChassisHorizontalPositionStateOTR1() {
        updateWorkflowIndicatorsBadgeLaunch();
        return chassisHorizontalPositionStatePassedOTR1 ? AdjacentSystemStatus.OK : AdjacentSystemStatus.UNDEFINED;
    }


    private AdjacentSystemStatus getStartedInitialSetSkBinsOtr1() {
        updateWorkflowIndicatorsBadgeLaunch();
        return (nppaService.getNcok().get().otr1BinsInitialSetup().equals(BaseProperty.OK)) ? AdjacentSystemStatus.OK : AdjacentSystemStatus.UNDEFINED;
    }

    private AdjacentSystemStatus getStartedInitialSetSkBinsOtr2() {
        updateWorkflowIndicatorsBadgeLaunch();
        return (nppaService.getNcok().get().otr2BinsInitialSetup().equals(BaseProperty.OK)) ? AdjacentSystemStatus.OK : AdjacentSystemStatus.UNDEFINED;
    }

    private AdjacentSystemStatus getPreciseDrive() {
        updateWorkflowIndicatorsBadgeLaunch();
        return (nppaService.getNcok().get().otrBinsPreciseSetup().equals(BaseProperty.OK)) ? AdjacentSystemStatus.OK : AdjacentSystemStatus.UNDEFINED;
    }

    private AdjacentSystemStatus getChassisHorizontalPositionStateOTR2() {
        updateWorkflowIndicatorsBadgeLaunch();
        return chassisHorizontalPositionStatePassedOTR2 ? AdjacentSystemStatus.OK : AdjacentSystemStatus.UNDEFINED;
    }

    private AdjacentSystemStatus getChassisHorizontalPositionState2hOTR() {
        updateWorkflowIndicatorsBadgeLaunch();
        return chassisHorizontalPositionStatePassed2hOTR ? AdjacentSystemStatus.OK : AdjacentSystemStatus.UNDEFINED;
    }

    private AdjacentSystemStatus getLaunchOtrLeftStageState() {
        updateWorkflowIndicatorsBadgeLaunch();
        return (nppaService.getNcok().get().isOtr1Lunched().equals(BaseProperty.OK)) ? AdjacentSystemStatus.OK : AdjacentSystemStatus.UNDEFINED;
    }

    private AdjacentSystemStatus getLaunchOtrRightStageState() {
        updateWorkflowIndicatorsBadgeLaunch();
        return (nppaService.getNcok().get().isOtr2Lunched().equals(BaseProperty.OK)) ? AdjacentSystemStatus.OK : AdjacentSystemStatus.UNDEFINED;
    }

    private void updateWorkflowIndicatorsBadgeLaunch() {
        if (!outriggersInMobileStatePassedOTR1 && nppaService.checkCommandCommittedAndInTheTop("Otr1FromBg2bLaunch")) {
            if (sutoService.getSutoPropertyByName("isOutriggersInMobileState").get().getState().equals(CommandState.ON)
                && sutoService.getStatus().equals(AdjacentSystemStatus.OK)) {
                outriggersInMobileStatePassedOTR1 = true;
                ppipWith2bCommandPassedOTR1 = true;
            } else {
                ppipWith2bCommandPassedOTR1 = false;
            }

        }

        if (!outriggersInMobileStatePassedOTR2 &&
            nppaService.checkCommandCommittedAndInTheTop("Otr2FromBg2bLaunch")) {
            if (sutoService.getSutoPropertyByName("isOutriggersInMobileState").get().getState().equals(CommandState.ON)
                && sutoService.getStatus().equals(AdjacentSystemStatus.OK)) {
                outriggersInMobileStatePassedOTR2 = true;
                ppipWith2bCommandPassedOTR2 = true;
            } else {
                ppipWith2bCommandPassedOTR2 = false;
            }
        }

        if (!outriggersInMobileStatePassed2hOTR &&
            nppaService.checkCommandCommittedAndInTheTop("Otr1Otr2FromBg2bLaunch")) {
            if (sutoService.getSutoPropertyByName("isOutriggersInMobileState").get().getState().equals(CommandState.ON)
                && sutoService.getStatus().equals(AdjacentSystemStatus.OK)) {
                outriggersInMobileStatePassed2hOTR = true;
                ppipWith2bCommandPassed2hOTR = true;
            } else {
                ppipWith2bCommandPassed2hOTR = false;
            }
        }

        if (!ppipWith2bCommandPassed) {
            if (nppaService.checkCommandCommittedAndInTheTop("PPiPWithBg2bContinuation")) {
                ppipWith2bCommandPassed = true;
            }
        }

        if (!chassisHorizontalPositionStatePassedOTR1 && ppipWith2bCommandPassedOTR1) {
            chassisHorizontalPositionStatePassedOTR1 = sutoService.getSutoPropertyByName("isChassisHorizontal").get().getState().equals(CommandState.ON)
                                                       && sutoService.getStatus().equals(AdjacentSystemStatus.OK);
        }

        if (!chassisHorizontalPositionStatePassedOTR2 && ppipWith2bCommandPassedOTR2) {
            chassisHorizontalPositionStatePassedOTR2 = sutoService.getSutoPropertyByName("isChassisHorizontal").get().getState().equals(CommandState.ON)
                                                       && sutoService.getStatus().equals(AdjacentSystemStatus.OK);
        }
        if (!chassisHorizontalPositionStatePassed2hOTR && ppipWith2bCommandPassed2hOTR) {
            chassisHorizontalPositionStatePassed2hOTR = sutoService.getSutoPropertyByName("isChassisHorizontal").get().getState().equals(CommandState.ON)
                                                        && sutoService.getStatus().equals(AdjacentSystemStatus.OK);
        }

        if (!ppipWith2bCommandPassedOTR1) {
            if (nppaService.checkCommandCommittedAndInTheTop("Otr1FromBg3Launch")) {
                chassisHorizontalPositionStatePassedOTR1 = (sutoService.getSutoPropertyByName("isChassisHorizontal").get().getState().equals(CommandState.ON));
            }
        }

        if (!ppipWith2bCommandPassedOTR2) {
            if (nppaService.checkCommandCommittedAndInTheTop("Otr2FromBg3Launch")) {
                chassisHorizontalPositionStatePassedOTR2 = (sutoService.getSutoPropertyByName("isChassisHorizontal").get().getState().equals(CommandState.ON));
            }
        }

        if (!ppipWith2bCommandPassed2hOTR) {
            if (nppaService.checkCommandCommittedAndInTheTop("Otr1Otr2FromBg3Launch")) {
                chassisHorizontalPositionStatePassed2hOTR = (sutoService.getSutoPropertyByName("isChassisHorizontal").get().getState().equals(CommandState.ON));
            }
        }
    }


    @Override
    public void receiveBroadcast(Object incomingEvent) {

        if (!(incomingEvent instanceof AdjacentSystemEvent event)) {
            return;
        }

        if (incomingEvent instanceof RocketLaunchEvent rocketLaunchEvent) {
            handleRocketLaunchEvent(RocketLaunchEvent.class, rocketLaunchEvent);
            return;
        }

        if (incomingEvent instanceof RocketEvent rocketEvent) {
            handleRocketEvent(RocketEvent.class, rocketEvent, rocketBinder);
            return;
        }

        if (event.getEntity().isEmpty()) {
            log.error("received event without entity " + event + " " + this);
            return;
        }

        switch (event.getSystemType()) {
            case PPO -> updateEvent(PpoEvent.class, event, ppoBinder);
            case SUTO -> handleSutoEvent(SutoEvent.class, event, sutoBinder);
            case SAE -> updateEvent(SaeEvent.class, event, saeBinder);
            case BINS -> handleBinsEvent(BinsEvent.class, event, binsBinder);
            case MSU -> updateEvent(MsuEvent.class, event, msuBinder);
            case PLC -> updateEvent(PlcEvent.class, event, plcBinder);
            case ASKU -> handleAskuEvent(event);
            case NCOK, BYN, NPPA -> handleNppaEvent(event);
        }
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    private void handleRocketLaunchEvent(Class<RocketLaunchEvent> eventClass, RocketLaunchEvent event) {
        if (event == null ||
            (events.get(eventClass) != null && !events.get(eventClass).equals(eventClass))) {
            return;
        }
        events.replace(eventClass, event);
//      response to PLC on event was already made in AskuService to guarantee response when page isn't active
        nppaService.getAvailableNcokCombatModeCommands().stream()
                .filter(command -> command.getCommand().equals(event.isLeft() ? OTR_1_AUTOMATIC_LAUNCH_CANCEL_ACKNOWLEDGMENT : OTR_2_AUTOMATIC_LAUNCH_CANCEL_ACKNOWLEDGMENT))
                .findFirst().ifPresentOrElse((acknowledgeCommand) ->
                        {
                            final String launchCommandToCancel = event.isLeft() ? LAUNCH_COMMAND_OTR1 :
                                    LAUNCH_COMMAND_OTR2;
                            AnimationColor color = StatusToColorMapper.fromElementsStatuses(AdjacentSystemStatus.UNDEFINED.getValueUa(),
                                    (Class) AdjacentSystemStatus.class);
                            if (pdpStages.containsKey(launchCommandToCancel) && !pdpStages.get(launchCommandToCancel).equals(color)) {
                                pdpStages.put(launchCommandToCancel, color);
                                if (getUI().isPresent()) {
                                    getUI().get().access(() -> badgeBinder.readBean(pdpStages));
                                }
                            }
                        },
                        () -> log.error("can't find command to handle" + (event.isLeft() ?
                                OTR_1_AUTOMATIC_LAUNCH_CANCEL_ACKNOWLEDGMENT :
                                OTR_2_AUTOMATIC_LAUNCH_CANCEL_ACKNOWLEDGMENT)));
        // TODO: 1/8/2025 send command and reload lunch badge
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    private void handleBinsEvent(Class eventClass, AdjacentSystemEvent event, Binder binder) {
        AnimationColor color = StatusToColorMapper.fromElementsStatuses(getBinsStageState().getValueUa(),
                (Class) AdjacentSystemStatus.class);
        if (pdpStages.containsKey(BINS_IS_CONNECTED) && !pdpStages.get(BINS_IS_CONNECTED).equals(color)) {
            pdpStages.put(BINS_IS_CONNECTED, color);
            if (getUI().isPresent()) {
                getUI().get().access(() -> badgeBinder.readBean(pdpStages));
            }
        }

        updateEvent(eventClass, event, binder);
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    private void handleSutoEvent(Class eventClass, AdjacentSystemEvent event, Binder binder) {

        AnimationColor color = StatusToColorMapper.fromElementsStatuses(getArrowUpLaunchStageState().getValueUa(),
                (Class) AdjacentSystemStatus.class);
        if (pdpStages.containsKey(ARROW_UP_LAUNCH) && !pdpStages.get(ARROW_UP_LAUNCH).equals(color)) {
            pdpStages.put(ARROW_UP_LAUNCH, color);
            if (getUI().isPresent()) {
                getUI().get().access(() -> badgeBinder.readBean(pdpStages));
            }
        }

        color = StatusToColorMapper.fromElementsStatuses(getOutrrigersInMobileStageStateOTR1().getValueUa(),
                (Class) AdjacentSystemStatus.class);
        if (pdpStages.containsKey(OUTRIGGERS_IN_MOBILE_STATE_OTR1) && !pdpStages.get(OUTRIGGERS_IN_MOBILE_STATE_OTR1).equals(color)) {
            pdpStages.put(OUTRIGGERS_IN_MOBILE_STATE_OTR1, color);
            if (getUI().isPresent()) {
                getUI().get().access(() -> badgeBinder.readBean(pdpStages));
            }
        }
        color = StatusToColorMapper.fromElementsStatuses(getOutrrigersInMobileStageStateOTR2().getValueUa(),
                (Class) AdjacentSystemStatus.class);
        if (pdpStages.containsKey(OUTRIGGERS_IN_MOBILE_STATE_OTR2) && !pdpStages.get(OUTRIGGERS_IN_MOBILE_STATE_OTR2).equals(color)) {
            pdpStages.put(OUTRIGGERS_IN_MOBILE_STATE_OTR2, color);
            if (getUI().isPresent()) {
                getUI().get().access(() -> badgeBinder.readBean(pdpStages));
            }
        }
        color = StatusToColorMapper.fromElementsStatuses(getOutrrigersInMobileStageState2hOTR().getValueUa(),
                (Class) AdjacentSystemStatus.class);
        if (pdpStages.containsKey(OUTRIGGERS_IN_MOBILE_STATE_2HOTR) && !pdpStages.get(OUTRIGGERS_IN_MOBILE_STATE_2HOTR).equals(color)) {
            pdpStages.put(OUTRIGGERS_IN_MOBILE_STATE_2HOTR, color);
            if (getUI().isPresent()) {
                getUI().get().access(() -> badgeBinder.readBean(pdpStages));
            }
        }
        color = StatusToColorMapper.fromElementsStatuses(getChassisHorizontalPositionStateOTR1().getValueUa(),
                (Class) AdjacentSystemStatus.class);
        if (pdpStages.containsKey(CHASSIS_HORIZONTAL_POSITION_STATE_OTR1) && !pdpStages.get(CHASSIS_HORIZONTAL_POSITION_STATE_OTR1).equals(color)) {
            pdpStages.put(CHASSIS_HORIZONTAL_POSITION_STATE_OTR1, color);
            if (getUI().isPresent()) {
                getUI().get().access(() -> badgeBinder.readBean(pdpStages));
            }
        }
        color = StatusToColorMapper.fromElementsStatuses(getChassisHorizontalPositionStateOTR2().getValueUa(),
                (Class) AdjacentSystemStatus.class);
        if (pdpStages.containsKey(CHASSIS_HORIZONTAL_POSITION_STATE_OTR2) && !pdpStages.get(CHASSIS_HORIZONTAL_POSITION_STATE_OTR2).equals(color)) {
            pdpStages.put(CHASSIS_HORIZONTAL_POSITION_STATE_OTR2, color);
            if (getUI().isPresent()) {
                getUI().get().access(() -> badgeBinder.readBean(pdpStages));
            }
        }
        color = StatusToColorMapper.fromElementsStatuses(getChassisHorizontalPositionState2hOTR().getValueUa(),
                (Class) AdjacentSystemStatus.class);
        if (pdpStages.containsKey(CHASSIS_HORIZONTAL_POSITION_STATE_2HOTR) && !pdpStages.get(CHASSIS_HORIZONTAL_POSITION_STATE_2HOTR).equals(color)) {
            pdpStages.put(CHASSIS_HORIZONTAL_POSITION_STATE_2HOTR, color);
            if (getUI().isPresent()) {
                getUI().get().access(() -> badgeBinder.readBean(pdpStages));
            }
        }

        updateEvent(eventClass, event, binder);
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    private void handleNppaEvent(AdjacentSystemEvent event) {

        events.replace(NppaEvent.class, event);

        if (event.getEntity().isEmpty()) {
            return;
        }
        if (event.getEntity().get() instanceof Nppa updatedNppa) {

            if (getUI().isPresent()) {
                getUI().get().access(() -> {
                    bynBinder.readBean(updatedNppa.getByn());
                    ncokBinder.readBean(updatedNppa.getNcok());
                });
            }
        }
        if (nppaService.getNcok().get().otr1AppPresence() && !errorAPPOTR1MessageIsDisplayed) {
            errorAPPOTR1MessageIsDisplayed = true;
            ppipWith2bCommandPassedOTR1 = false;
            outriggersInMobileStatePassedOTR1 = false;
            chassisHorizontalPositionStatePassedOTR1 = false;

        }
        if (!nppaService.getNcok().get().otr1AppPresence() && errorAPPOTR1MessageIsDisplayed) {
            errorAPPOTR1MessageIsDisplayed = false;
        }
        if (nppaService.getNcok().get().otr2AppPresence() && !errorAPPOTR2MessageIsDisplayed) {
            errorAPPOTR2MessageIsDisplayed = true;
            ppipWith2bCommandPassedOTR2 = false;
            outriggersInMobileStatePassedOTR2 = false;
            chassisHorizontalPositionStatePassedOTR2 = false;
        }
        if (!nppaService.getNcok().get().otr2AppPresence() && errorAPPOTR2MessageIsDisplayed) {
            errorAPPOTR2MessageIsDisplayed = false;
        }
        if (nppaService.getNcok().get().appPresence() && !errorAPPNPPAMessageIsDisplayed) {
            errorAPPNPPAMessageIsDisplayed = true;
            ppipWith2bCommandPassedOTR1 = false;
            outriggersInMobileStatePassedOTR1 = false;
            chassisHorizontalPositionStatePassedOTR1 = false;
            ppipWith2bCommandPassedOTR2 = false;
            outriggersInMobileStatePassedOTR2 = false;
            chassisHorizontalPositionStatePassedOTR2 = false;
            ppipWith2bCommandPassed2hOTR = false;
            outriggersInMobileStatePassed2hOTR = false;
            chassisHorizontalPositionStatePassed2hOTR = false;
        }
        if (!nppaService.getNcok().get().appPresence() && errorAPPNPPAMessageIsDisplayed) {
            errorAPPNPPAMessageIsDisplayed = false;
        }
        AnimationColor color = StatusToColorMapper.fromElementsStatuses(getCheckedOTR(LaunchType.FIRST).getValueUa(),
                (Class) AdjacentSystemStatus.class);
        if (pdpStages.containsKey(CHECK_OTR1) && !pdpStages.get(CHECK_OTR1).equals(color)) {
            pdpStages.put(CHECK_OTR1, color);
            if (getUI().isPresent()) {
                getUI().get().access(() -> badgeBinder.readBean(pdpStages));
            }
        }
        color = StatusToColorMapper.fromElementsStatuses(getCheckedOTR(LaunchType.SECOND).getValueUa(),
                (Class) AdjacentSystemStatus.class);
        if (pdpStages.containsKey(CHECK_OTR2) && !pdpStages.get(CHECK_OTR2).equals(color)) {
            pdpStages.put(CHECK_OTR2, color);
            if (getUI().isPresent()) {
                getUI().get().access(() -> badgeBinder.readBean(pdpStages));
            }
        }

        color = StatusToColorMapper.fromElementsStatuses(getTVBynStageState().getValueUa(),
                (Class) AdjacentSystemStatus.class);
        if (pdpStages.containsKey(TV_BYN_PRE) && !pdpStages.get(TV_BYN_PRE).equals(color)) {
            pdpStages.put(TV_BYN_PRE, color);
            if (getUI().isPresent()) {
                getUI().get().access(() -> badgeBinder.readBean(pdpStages));
            }
        }


        color = StatusToColorMapper.fromElementsStatuses(getTVNcokStageState().getValueUa(),
                (Class) AdjacentSystemStatus.class);
        if (pdpStages.containsKey(TV_NCOK_LAUNCH) && !pdpStages.get(TV_NCOK_LAUNCH).equals(color)) {
            pdpStages.put(TV_NCOK_LAUNCH, color);
            if (getUI().isPresent()) {
                getUI().get().access(() -> badgeBinder.readBean(pdpStages));
            }
        }


        color = StatusToColorMapper.fromElementsStatuses(getLaunchCommandState(LaunchType.FIRST).getValueUa(),
                (Class) AdjacentSystemStatus.class);
        if (pdpStages.containsKey(LAUNCH_COMMAND_OTR1) && !pdpStages.get(LAUNCH_COMMAND_OTR1).equals(color)) {
            pdpStages.put(LAUNCH_COMMAND_OTR1, color);
            if (getUI().isPresent()) {
                getUI().get().access(() -> badgeBinder.readBean(pdpStages));
            }
        }
        color = StatusToColorMapper.fromElementsStatuses(getLaunchCommandState(LaunchType.SECOND).getValueUa(),
                (Class) AdjacentSystemStatus.class);
        if (pdpStages.containsKey(LAUNCH_COMMAND_OTR2) && !pdpStages.get(LAUNCH_COMMAND_OTR2).equals(color)) {
            pdpStages.put(LAUNCH_COMMAND_OTR2, color);
            if (getUI().isPresent()) {
                getUI().get().access(() -> badgeBinder.readBean(pdpStages));
            }
        }
        color = StatusToColorMapper.fromElementsStatuses(getLaunchCommandState(LaunchType.FIRST_AND_SECOND).getValueUa(),
                (Class) AdjacentSystemStatus.class);
        if (pdpStages.containsKey(LAUNCH_COMMAND_2HOTR) && !pdpStages.get(LAUNCH_COMMAND_2HOTR).equals(color)) {
            pdpStages.put(LAUNCH_COMMAND_2HOTR, color);
            if (getUI().isPresent()) {
                getUI().get().access(() -> badgeBinder.readBean(pdpStages));
            }
        }

        color = StatusToColorMapper.fromElementsStatuses(getStartedInitialSetSkBinsOtr1().getValueUa(),
                (Class) AdjacentSystemStatus.class);
        if (pdpStages.containsKey(STARTED_INITIAL_SET_SK_BINS_OTR1) && !pdpStages.get(STARTED_INITIAL_SET_SK_BINS_OTR1).equals(color)) {
            pdpStages.put(STARTED_INITIAL_SET_SK_BINS_OTR1, color);
            if (getUI().isPresent()) {
                getUI().get().access(() -> badgeBinder.readBean(pdpStages));
            }
        }
        color = StatusToColorMapper.fromElementsStatuses(getStartedInitialSetSkBinsOtr2().getValueUa(),
                (Class) AdjacentSystemStatus.class);
        if (pdpStages.containsKey(STARTED_INITIAL_SET_SK_BINS_OTR2) && !pdpStages.get(STARTED_INITIAL_SET_SK_BINS_OTR2).equals(color)) {
            pdpStages.put(STARTED_INITIAL_SET_SK_BINS_OTR2, color);
            if (getUI().isPresent()) {
                getUI().get().access(() -> badgeBinder.readBean(pdpStages));
            }
        }
        color = StatusToColorMapper.fromElementsStatuses(getPreciseDrive().getValueUa(),
                (Class) AdjacentSystemStatus.class);
        if (pdpStages.containsKey(PRECISE_DRIVE) && !pdpStages.get(PRECISE_DRIVE).equals(color)) {
            pdpStages.put(PRECISE_DRIVE, color);
            if (getUI().isPresent()) {
                getUI().get().access(() -> badgeBinder.readBean(pdpStages));
            }
        }
        color = StatusToColorMapper.fromElementsStatuses(getLaunchOtrLeftStageState().getValueUa(),
                (Class) AdjacentSystemStatus.class);
        if (pdpStages.containsKey(LAUNCH_OTR_LEFT_PDP) && !pdpStages.get(LAUNCH_OTR_LEFT_PDP).equals(color)) {
            pdpStages.put(LAUNCH_OTR_LEFT_PDP, color);
            if (getUI().isPresent()) {
                getUI().get().access(() -> badgeBinder.readBean(pdpStages));
            }
        }

        if (nppaService.getNcok().get().isOtr1Lunched().equals(BaseProperty.OK) && (CountdownOTR.getCountdownOTR2Seconds() == 0) && !messageOTR1LaunchIsDisplayed) {
            messageOTR1LaunchIsDisplayed = true;
        }
        if (nppaService.getNcok().get().isOtr2Lunched().equals(BaseProperty.OK) && (CountdownOTR.getCountdownOTR1Seconds() == 0) && !messageOTR2LaunchIsDisplayed) {
            messageOTR2LaunchIsDisplayed = true;
        }

        color = StatusToColorMapper.fromElementsStatuses(getLaunchOtrRightStageState().getValueUa(),
                (Class) AdjacentSystemStatus.class);
        if (pdpStages.containsKey(LAUNCH_OTR_RIGHT_PDP) && !pdpStages.get(LAUNCH_OTR_RIGHT_PDP).equals(color)) {
            pdpStages.put(LAUNCH_OTR_RIGHT_PDP, color);
            if (getUI().isPresent()) {
                getUI().get().access(() -> badgeBinder.readBean(pdpStages));
            }
        }

    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    private void handleAskuEvent(@NotNull AdjacentSystemEvent event) {
        if (event.getEntity().isEmpty()) {
            return;
        }
        if (!(event.getEntity().get() instanceof AskuInfo)) {
            return;
        }
        AnimationColor color = StatusToColorMapper.fromElementsStatuses(getInitialConditionsState(LaunchType.FIRST).getValueUa(),
                (Class) AdjacentSystemStatus.class);
        if (pdpStages.containsKey(IC_FOR_OTR1_LOADED_TO_PLC) && !pdpStages.get(IC_FOR_OTR1_LOADED_TO_PLC).equals(color)) {
            pdpStages.put(IC_FOR_OTR1_LOADED_TO_PLC, color);
            if (getUI().isPresent()) {
                getUI().get().access(() -> {
                    badgeBinder.readBean(pdpStages);
                    removeAll();
                    initVIew();
                });
            }
        }
        color = StatusToColorMapper.fromElementsStatuses(getInitialConditionsState(LaunchType.SECOND).getValueUa(),
                (Class) AdjacentSystemStatus.class);
        if (pdpStages.containsKey(IC_FOR_OTR2_LOADED_TO_PLC) && !pdpStages.get(IC_FOR_OTR2_LOADED_TO_PLC).equals(color)) {
            pdpStages.put(IC_FOR_OTR2_LOADED_TO_PLC, color);
            if (getUI().isPresent()) {
                getUI().get().access(() -> {
                    badgeBinder.readBean(pdpStages);
                    removeAll();
                    initVIew();
                });
            }
        }

        updateEvent(AskuInfoEvent.class, event, askuInfoBinder);
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    private void updateEvent(Class eventClass, AdjacentSystemEvent event, Binder binder) {
        events.replace(eventClass, event);
        if (getUI().isPresent()) {
            getUI().get().access(() -> binder.readBean(event.getEntity().get()));
        }
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    private void handleRocketEvent(Class eventClass, RocketEvent event, Binder binder) {
        if (event.getRocket() != null && getUI().isPresent()) {
            getUI().get().access(() -> binder.readBean(event));
        }
    }
}


