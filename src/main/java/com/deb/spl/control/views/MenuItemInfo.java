package com.deb.spl.control.views;

import com.vaadin.flow.component.Component;
import com.vaadin.flow.component.dependency.NpmPackage;
import com.vaadin.flow.component.html.ListItem;
import com.vaadin.flow.component.html.Span;
import com.vaadin.flow.component.icon.Icon;
import com.vaadin.flow.router.RouterLink;
import com.vaadin.flow.theme.lumo.LumoUtility;

/**
 * A simple navigation item component, based on ListItem element.
 */
public class MenuItemInfo extends ListItem {

    private final Class<? extends Component> view;

    public MenuItemInfo(String menuTitle, String iconClass, Class<? extends Component> view) {
        this.view = view;
        RouterLink link = new RouterLink();
        // Use Lumo classnames for various styling
        link.addClassNames(LumoUtility.Display.FLEX, LumoUtility.Gap.XSMALL, LumoUtility.Height.MEDIUM, LumoUtility.AlignItems.CENTER, LumoUtility.Padding.Horizontal.SMALL, LumoUtility.TextColor.BODY);
        link.setRoute(view);

        Span text = new Span(menuTitle);
        text.addClassNames(LumoUtility.FontWeight.MEDIUM, LumoUtility.FontSize.MEDIUM, LumoUtility.Whitespace.NOWRAP);

        link.add(new LineAwesomeIcon(iconClass), text);
        add(link);
    }

    public MenuItemInfo(String menuTitle, Icon vaadinIcon, Class<? extends Component> view, String cssClassName) {
        this.view = view;
        RouterLink link = new RouterLink();
        link.addClassName(cssClassName);

        link.addClassNames(LumoUtility.Display.FLEX, LumoUtility.Gap.XSMALL, LumoUtility.Height.MEDIUM, LumoUtility.AlignItems.CENTER, LumoUtility.Padding.Horizontal.SMALL, LumoUtility.TextColor.BODY);
        link.setRoute(view);

        Span text = new Span(menuTitle);
        text.addClassNames(LumoUtility.FontWeight.MEDIUM, LumoUtility.FontSize.MEDIUM, LumoUtility.Whitespace.NOWRAP);

        link.add(vaadinIcon, text);
        add(link);
    }

    public MenuItemInfo(String menuTitle, Icon vaadinIcon, Class<? extends Component> view) {
        this(menuTitle, vaadinIcon, view, view.getSimpleName());
    }

    public Class<?> getView() {
        return view;
    }

    /**
     * Simple wrapper to create icons using LineAwesome iconset. See
     * https://icons8.com/line-awesome
     */
    @NpmPackage(value = "line-awesome", version = "1.3.0")
    public static class LineAwesomeIcon extends Span {
        public LineAwesomeIcon(String lineawesomeClassnames) {
            // Use Lumo classnames for suitable font styling
            addClassNames(LumoUtility.FontSize.LARGE, LumoUtility.TextColor.SECONDARY);
            if (!lineawesomeClassnames.isEmpty()) {
                addClassNames(lineawesomeClassnames);
            }
        }
    }

}
