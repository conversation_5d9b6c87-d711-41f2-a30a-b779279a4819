package com.deb.spl.control.views.adjacentSystems.utils;

import com.deb.spl.control.controller.BadRequestException;
import com.deb.spl.control.controller.NotFoundException;
import com.deb.spl.control.data.asku.CountdownEventType;
import com.deb.spl.control.data.asku.LaunchType;
import com.deb.spl.control.data.asku.Readiness;
import com.deb.spl.control.service.Broadcaster;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.views.events.CountdownEvent;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;


@Component
@Slf4j
public class CountdownOTR implements Broadcaster.BroadcastListener {

    @Getter
    private static int countBg3;
    @Getter
    private static int countBg2a;
    @Getter
    private static int countBg2b;
    @Getter
    private static int countBg1;
    @Getter
    private static int startShift;
    private static final ScheduledExecutorService executorService = Executors.newScheduledThreadPool(2);
    private static ScheduledFuture<?> countdownTaskOtr1;
    private static ScheduledFuture<?> countdownTaskOtr2;
    @Getter
    private static int countdownOTR1Seconds;
    @Getter
    private static int countdownOTR2Seconds;
    private final AskuService askuService;

    public CountdownOTR(@Value("${asku.pdp.countbg1_time_sec}") int countbg1,
                        @Value("${asku.pdp.countbg2a_time_sec}") int countbg2A,
                        @Value("${asku.pdp.countbg2b_time_sec}") int countbg2B,
                        @Value("${asku.pdp.countbg3_time_sec}") int countbg3,
                        @Value("${asku.pdp.start_shift_sec}") int startShift,
                        AskuService askuService
    ) {
        CountdownOTR.countBg3 = countbg3;
        CountdownOTR.countBg2a = countbg2A;
        CountdownOTR.countBg2b = countbg2B;
        CountdownOTR.countBg1 = countbg1;
        CountdownOTR.startShift = startShift;
        this.askuService = askuService;

        Broadcaster.register(this, CountdownEvent.class);
    }

    public boolean countdownTaskOtr1DoesNotExists() {
        return CountdownOTR.countdownTaskOtr1 == null || CountdownOTR.countdownTaskOtr1.isDone();
    }

    public boolean countdownTaskOtr2DoesNotExists() {
        return CountdownOTR.countdownTaskOtr2 == null || CountdownOTR.countdownTaskOtr2.isDone();
    }

    /**
     * @param readiness
     * @param startShift shift time in sec. Positive
     * @return
     */
    public int calculateLaunchTime(Readiness readiness, @Positive int startShift) {
        if (startShift < 0) {
            throw new IllegalArgumentException("startShift must be positive");
        }

        if (readiness == Readiness.BG_3) {
            return countBg3 + startShift;
        } else if (readiness == Readiness.BG_2A) {
            return countBg2a + startShift;
        } else if (readiness == Readiness.BG_2B) {
            return countBg2b + startShift;
        } else if (readiness == Readiness.BG_1) {
            return countBg1 + startShift;
        } else {
            return 0;
        }
    }

    public int calculateLaunchTime(Readiness readiness) {
        return calculateLaunchTime(readiness, 0);
    }

    public void startCountdownOtr2(int countdownOTRSeconds) {
        if (countdownTaskOtr2 == null || countdownTaskOtr2.isDone()) {
            countdownOTR2Seconds = countdownOTRSeconds;
            countdownTaskOtr2 = executorService.scheduleAtFixedRate(() -> {
                if (countdownOTR2Seconds > 0) {
                    countdownOTR2Seconds--;
                    // Update UI on the UI thread
                    CountdownEvent event = CountdownEvent.builder().eventType(CountdownEventType.UPDATE)
                            .launchType(LaunchType.SECOND)
                            .timerValue(countdownOTR2Seconds)
                            .build();
                    Broadcaster.broadcast(event);

                } else {
                    // Update UI on the UI thread
                    stopCountdown(LaunchType.SECOND, CountdownOTR.countdownTaskOtr2);
                }
            }, 0, 1, TimeUnit.SECONDS);
        }
    }

    public void startCountdownOtr1(int countdownOTRSeconds) {
        if (countdownTaskOtr1 == null || countdownTaskOtr1.isDone()) {
            countdownOTR1Seconds = countdownOTRSeconds;
            countdownTaskOtr1 = executorService.scheduleAtFixedRate(() -> {
                if (countdownOTR1Seconds > 0) {
                    countdownOTR1Seconds--;
                    // Update UI on the UI thread
                    CountdownEvent event = CountdownEvent.builder().eventType(CountdownEventType.UPDATE)
                            .launchType(LaunchType.FIRST)
                            .timerValue(countdownOTR1Seconds)
                            .build();
                    Broadcaster.broadcast(event);

                } else {
                    // Update UI on the UI thread
                    stopCountdown(LaunchType.FIRST, CountdownOTR.countdownTaskOtr1);
                }
            }, 0, 1, TimeUnit.SECONDS);
        }
    }

    public void pauseCountDownOtr1() {
        countdownOTR1Seconds = 183;
        pauseCountDown(LaunchType.FIRST, countdownTaskOtr1);

    }

    public void pauseCountDownOtr2() {
        countdownOTR2Seconds = 183;
        pauseCountDown(LaunchType.SECOND, countdownTaskOtr2);

    }


    private void pauseCountDown(@NotNull LaunchType launchType,
                                ScheduledFuture<?> countdownTask) {
        if (launchType == null) {
            String msg = "cant pauseCountDown because launchType is null";
            log.error(msg);
            throw new IllegalArgumentException(msg);
        }

        if (countdownTask != null) {
            countdownTask.cancel(true);
        }

        CountdownEvent stopEvent;
        if (launchType.equals(LaunchType.FIRST)) {

            stopEvent = CountdownEvent.builder()
                    .eventType(CountdownEventType.UPDATE)
                    .launchType(LaunchType.FIRST)
                    .timerValue(countdownOTR1Seconds)
                    .build();
        } else {
            stopEvent = CountdownEvent.builder()
                    .eventType(CountdownEventType.UPDATE)
                    .launchType(LaunchType.SECOND)
                    .timerValue(countdownOTR2Seconds)
                    .build();
        }
        Broadcaster.broadcast(stopEvent);
    }

    public void continueCountDownOtr1() {
        if (countdownTaskOtr1 == null || countdownTaskOtr1.isDone()) {
            countdownTaskOtr1 = executorService.scheduleAtFixedRate(() -> {
                if (countdownOTR1Seconds > 0) {
                    countdownOTR1Seconds--;
                    // Update UI on the UI thread
                    CountdownEvent event = CountdownEvent.builder().eventType(CountdownEventType.UPDATE)
                            .launchType(LaunchType.FIRST)
                            .timerValue(countdownOTR1Seconds)
                            .build();
                    Broadcaster.broadcast(event);

                } else {
                    // Update UI on the UI thread
                    CountdownEvent stopEvent = CountdownEvent.builder().eventType(CountdownEventType.STOP)
                            .launchType(LaunchType.FIRST)
                            .timerValue(countdownOTR1Seconds)
                            .build();

                    Broadcaster.broadcast(stopEvent);
                }
            }, 0, 1, TimeUnit.SECONDS);
        }
    }

    public void continueCountDownOtr2() {
        if (countdownTaskOtr2 == null || countdownTaskOtr2.isDone()) {
            countdownTaskOtr2 = executorService.scheduleAtFixedRate(() -> {
                if (countdownOTR2Seconds > 0) {
                    countdownOTR2Seconds--;
                    // Update UI on the UI thread
                    CountdownEvent event = CountdownEvent.builder().eventType(CountdownEventType.UPDATE)
                            .launchType(LaunchType.SECOND)
                            .timerValue(countdownOTR2Seconds)
                            .build();
                    Broadcaster.broadcast(event);

                } else {
                    // Update UI on the UI thread
                    CountdownEvent stopEvent = CountdownEvent.builder().eventType(CountdownEventType.STOP)
                            .launchType(LaunchType.SECOND)
                            .timerValue(countdownOTR2Seconds)
                            .build();

                    Broadcaster.broadcast(stopEvent);
                }
            }, 0, 1, TimeUnit.SECONDS);
        }
    }

    public void stopCountdownOtr1() {
        stopCountdown(LaunchType.FIRST);
    }

    public void stopCountdownOtr2() {
        stopCountdown(LaunchType.SECOND);
    }

    public void stopCountdown(@NotNull LaunchType launchType) {
        if (launchType == null) {
            String msg = "cant pauseCountDown because launchType is null";
            log.error(msg);
            throw new IllegalArgumentException(msg);
        }

        if (launchType.equals(LaunchType.FIRST)) {
            stopCountdown(launchType, countdownTaskOtr1);
        } else {
            stopCountdown(launchType, countdownTaskOtr2);
        }
    }

    private void stopCountdown(LaunchType launchType, ScheduledFuture<?> countdownTask) {
        if (countdownTask != null) {
            countdownTask.cancel(true);
        }

        if (LaunchType.FIRST == launchType) {
            CountdownEvent stopEvent = CountdownEvent.builder().eventType(CountdownEventType.UPDATE)
                    .launchType(LaunchType.FIRST)
                    .timerValue(0)
                    .build();
            countdownOTR1Seconds = 0;

            Broadcaster.broadcast(stopEvent);
        } else if (LaunchType.SECOND == launchType) {
            CountdownEvent stopEvent = CountdownEvent.builder().eventType(CountdownEventType.UPDATE)
                    .launchType(LaunchType.SECOND)
                    .timerValue(0)
                    .build();
            countdownOTR2Seconds = 0;
            Broadcaster.broadcast(stopEvent);
        }
    }

    /**
     * @param timeInSec time to format in seconds
     * @return formatted string
     */
    public static String formatCountDownTime(int timeInSec) {
        int minutesOtr1 = timeInSec / 60;
        int secondsOtr1 = timeInSec % 60;

        return String.format("%02d:%02d", minutesOtr1, secondsOtr1);
    }

    @Override
    public void receiveBroadcast(Object incomingEvent) {
        if (incomingEvent == null) {
            log.error("unexpected null event at {}", this.getClass().getSimpleName());
            return;
        }
        if (incomingEvent instanceof CountdownEvent event) {
            processCountdownEvent(event);
        } else {
            log.error("unexpected event type{} at {}",
                    incomingEvent.toString(),
                    this.getClass().getSimpleName());
        }
    }

    private void processCountdownEvent(CountdownEvent incomingEvent) {
        if (incomingEvent.getEventType().equals(CountdownEventType.START)) {
            if (incomingEvent.getLaunchType().equals(LaunchType.FIRST)) {
                startCountdownOtr1(calculateLaunchTime(askuService.getRocketCopy(true).get().getInitialData().getReadiness()));
            } else if (incomingEvent.getLaunchType().equals(LaunchType.SECOND)) {
                startCountdownOtr2(calculateLaunchTime(askuService.getRocketCopy(false).get().getInitialData().getReadiness()));
            } else if (incomingEvent.getLaunchType().equals(LaunchType.FIRST_AND_SECOND)) {
                startCountdownOtr1(calculateLaunchTime(askuService.getRocketCopy(true).get().getInitialData().getReadiness()));
                startCountdownOtr2(calculateLaunchTime(askuService.getRocketCopy(false).get().getInitialData().getReadiness(), CountdownOTR.getStartShift()));
            }
        }
    }

    public boolean isCountDown1Active() {
        return isCountDownTaskActive(countdownTaskOtr1);
    }

    public boolean isCountDown2Active() {
        return isCountDownTaskActive(countdownTaskOtr2);
    }

    private boolean isCountDownTaskActive(ScheduledFuture<?> countdownTask) {
        return countdownTask != null && !countdownTask.isDone() && !countdownTask.isCancelled();
    }
}
