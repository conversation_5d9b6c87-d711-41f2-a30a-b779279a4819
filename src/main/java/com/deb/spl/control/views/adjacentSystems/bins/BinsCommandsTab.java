package com.deb.spl.control.views.adjacentSystems.bins;

import com.deb.spl.control.service.BinsService;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.combobox.ComboBox;
import com.vaadin.flow.component.dialog.Dialog;
import com.vaadin.flow.component.html.Div;
import com.vaadin.flow.component.html.H2;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.NumberField;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.router.Route;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.marineapi.nmea.sentence.Checksum;
import net.sf.marineapi.nmea.util.CompassPoint;
import net.sf.marineapi.nmea.util.Position;
import net.sf.marineapi.nmea.util.Units;

import java.util.List;
import java.util.Optional;



@Slf4j
class BinsCommandsTab extends VerticalLayout {
    private final BinsService binsService;
    BinsDataView binsDataView;
    private Button resetBt;
    private Button startButton;
    private Button calibrateButton;




    public BinsCommandsTab(BinsService binsService) {
        this.binsService = binsService;

        binsDataView = new BinsDataView(binsService);
        add(binsDataView);

        configureSentenceTime();
        configurePocrm();

        configureStartButton();
        configureRestartAndCalibrate();


        HorizontalLayout coordButtonStartLayout = new HorizontalLayout();
        coordButtonStartLayout.addClassName("coord-start-down");
        coordButtonStartLayout.add(resetBt,calibrateButton,startButton);
        coordButtonStartLayout.setWidth("800px");
        add(coordButtonStartLayout);
    }

    private static final List<String> OUTGOING_SENTENCES = List.of("GPGGA", "POHPR", "GPRMC", "POPMP", "START");
    private static final List<SentenceStatus> SENTENCE_STATUSES = List.of(SentenceStatus.ON, SentenceStatus.OFF);
    private static final String SET_COORDINATES_MANUALLY = "POCRM";


    private void configureStartButton() {
        startButton = new Button("Включити", (event -> {
            Dialog dialog = new Dialog();
            dialog.addClassName("dialog-bins");

            dialog.setHeaderTitle("Включити БІНС?");
            dialog.add("Ви впевнені, що бажаєте включити навігаційноу систему БІНС?");

            Button confirmBt = new Button("Включити", (e) -> {
                binsService.sendStart();
                dialog.close();
            });

            confirmBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY,
                    ButtonVariant.LUMO_ERROR);
            confirmBt.getStyle().set("margin-right", "auto");
            dialog.getFooter().add(confirmBt);

            Button cancelButton = new Button("Відміна", (e) -> dialog.close());
            cancelButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
            dialog.getFooter().add(cancelButton);

            dialog.open();

        }));
        startButton.addThemeVariants(ButtonVariant.LUMO_PRIMARY, ButtonVariant.LUMO_SUCCESS,ButtonVariant.LUMO_LARGE/*ButtonVariant.LUMO_SUCCESS, ButtonVariant.LUMO_LARGE*/);
    }

    private void configureSentenceTime() {
        addClassName("configure-sentence-time");
        H2 title = new H2("Налаштування речень, що видаються");

        setAlignSelf(Alignment.CENTER, title);
        add(title);

        ComboBox<String> sentenceNamesCb = new ComboBox<>("Речення");
        sentenceNamesCb.setItems(OUTGOING_SENTENCES);
        sentenceNamesCb.setValue(OUTGOING_SENTENCES.get(0));

        ComboBox<SentenceStatus> sentenceStatusCb = new ComboBox<>("Статус");

        sentenceStatusCb.setItems(SENTENCE_STATUSES);
        sentenceStatusCb.setItemLabelGenerator(SentenceStatus::name);
        sentenceStatusCb.setValue(SentenceStatus.ON);

        Button setSentenceBt = new Button("Задати");
        setSentenceBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY, ButtonVariant.LUMO_SUCCESS);
        setSentenceBt.setIconAfterText(true);

        setSentenceBt.addClickListener(event -> {
            binsService.sendSetupMessages(sentenceNamesCb.getValue(),
                    sentenceStatusCb.getValue() == SentenceStatus.ON);
        });

        Div splitter = new Div();
        splitter.setWidth("2em");
        Div buttonBox = new Div();
        buttonBox.addClassName("button-box-upper");
        buttonBox.add(setSentenceBt);

        HorizontalLayout coordSentenceLayout = new HorizontalLayout();
        coordSentenceLayout.addClassName("coord-sentence");
        coordSentenceLayout.add(sentenceNamesCb, sentenceStatusCb, splitter, buttonBox);
        add(coordSentenceLayout);
    }

    private void configurePocrm() {
        addClassName("configure-pocrm");
        H2 title = new H2("Налаштування координат у ручному режимі");
        setAlignSelf(Alignment.CENTER, title);
        add(title);

        TextField sentenceName = new TextField("Речення");
        sentenceName.setValue(SET_COORDINATES_MANUALLY);
        sentenceName.setEnabled(false);
        sentenceName.addClassName("sentence-name-bins");

        NumberField latitude = new NumberField("Широта", "llll.ll");
        latitude.setValue(46.322144);
        latitude.setWidth("7.75em");

        List<CompassPoint> SOUTH_NORT_HEMISPHERE = List.of(CompassPoint.SOUTH, CompassPoint.NORTH);
        ComboBox<CompassPoint> latitudeHemisphere = new ComboBox<>("Півкуля");
        latitudeHemisphere.setItems(SOUTH_NORT_HEMISPHERE);
        latitudeHemisphere.setItemLabelGenerator(item -> String.valueOf(item.toChar()));
        latitudeHemisphere.setValue(CompassPoint.NORTH);
        latitudeHemisphere.setWidth("5em");

        NumberField longitude = new NumberField("Довготота", "yyyyy.yy");
        longitude.setValue(30.603362);
        longitude.setWidth("7.75em");

        List<CompassPoint> EAST_WEST_HEMISPHERE = List.of(CompassPoint.EAST, CompassPoint.WEST);
        ComboBox<CompassPoint> longitudeHemisphere = new ComboBox<>("Півкуля");
        longitudeHemisphere.setItems(EAST_WEST_HEMISPHERE);
        longitudeHemisphere.setItemLabelGenerator(item -> String.valueOf(item.toChar()));
        longitudeHemisphere.setValue(CompassPoint.EAST);
        longitudeHemisphere.setWidth("5em");

        NumberField altitude = new NumberField("Висота", "x.x");

        ComboBox<Units> altitudeUnit = new ComboBox<>("Од.");
        altitudeUnit.setItems(Units.METER);
        altitudeUnit.setItemLabelGenerator(item -> String.valueOf(item.toChar()));
        altitudeUnit.setValue(Units.METER);
        altitudeUnit.setWidth("5em");

        altitude.setValue(101.0);

        Button setBt = new Button("Задати", event -> {
            if (latitude.getValue() == null || longitude.getValue() == null || altitude.getValue() == null) {
                String message = "cant set initial BINS Coordinates with emptydata ";
                Notification.show(message);
                log.warn(message);
            } else {
                binsService.sendCoordinatesManually(
                        new Position(latitude.getValue(), longitude.getValue(), altitude.getValue()));
            }

        });

        setBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY, ButtonVariant.LUMO_SUCCESS);

        HorizontalLayout coordManualLayout = new HorizontalLayout();
        coordManualLayout.addClassName("coord-manual");
        coordManualLayout.add(sentenceName, latitude, latitudeHemisphere, longitude, longitudeHemisphere, altitude, altitudeUnit);
        add(coordManualLayout);

        Div buttonBox = new Div();
        buttonBox.addClassName("button-box");
        buttonBox.add(setBt);
        buttonBox.getStyle().set("padding-top", "2em");
        HorizontalLayout coordButtonLayout = new HorizontalLayout();
        coordButtonLayout.addClassName("coord-button-down");
        coordButtonLayout.add(buttonBox);
        add(coordButtonLayout);
    }

    private void configureRestartAndCalibrate() {
        resetBt = new Button("Перезавантажити");
        resetBt.addThemeVariants(ButtonVariant.LUMO_ERROR, ButtonVariant.LUMO_LARGE, ButtonVariant.LUMO_PRIMARY);
        resetBt.addClickListener(event -> {
            ResetDialog dialog = new ResetDialog();
            dialog.open();
        });

         calibrateButton = new Button("Калібрувати", (event -> {

            Dialog dialog = new Dialog();
            dialog.addClassName("dialog-bins");
            dialog.setHeaderTitle("Розпочати калібрування БІНС?");
            dialog.add("Ви впевнені, що бажаєте розпочати калібрування навігаційної системи БІНС?");

            Button confirmBt = new Button("Калібрувати", (e) -> {
                binsService.sendCalibrate();
                dialog.close();
            });
            confirmBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY,
                    ButtonVariant.LUMO_ERROR);
            confirmBt.getStyle().set("margin-right", "auto");
            dialog.getFooter().add(confirmBt);

            Button cancelButton = new Button("Відміна", (e) -> dialog.close());
            cancelButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
            dialog.getFooter().add(cancelButton);

            dialog.open();

        }));
        calibrateButton.addThemeVariants(ButtonVariant.LUMO_ERROR, ButtonVariant.LUMO_LARGE);
        calibrateButton.addClassName("bins-calibrate-button");
   }


    @AllArgsConstructor
    private enum SentenceStatus {
        ON(1),
        OFF(0);

        private int value;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BinsSentenceSettings {
        final String sentenceName = "PORQS";
        String configuringSentenceName;
        SentenceStatus status;

        public boolean isValid() {
            return (validateName(configuringSentenceName));
        }

        public boolean validateName(String value) {
            return OUTGOING_SENTENCES.contains(value);
        }

        public String getCheckSum(String value) {
            return Checksum.xor(value);
        }

        public String toString() {
            StringBuilder builder = new StringBuilder();

            StringBuilder dataString = new StringBuilder();
            dataString.append(sentenceName).append(",")
                    .append(configuringSentenceName != null ? configuringSentenceName : "").append(",")
                    .append(status != null ? status.value : "");

            builder.append("$")
                    .append(dataString)
                    .append("*")
                    .append(getCheckSum(dataString.toString()));

            return builder.toString();
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PocrmSentence {
        @JsonIgnore
        private final String SENTENCE_NAME = SET_COORDINATES_MANUALLY;
        private Double latitude;
        private CompassPoint latitudeHemisphere;
        private Double longitude;
        private CompassPoint longitudeHemisphere;
        private Double altitude;
        private Units altitudeUnit = Units.METER;


        public String getCheckSum(String value) {
            return Checksum.xor(value);
        }

        private Optional<String> normalizeStringLength(String incomingString, int digitsBeforeDelimiter) {
            String normalizedString = null;
            int delimiter = incomingString.indexOf(".");

            if (delimiter == -1 || digitsBeforeDelimiter - delimiter < 0) {
                return Optional.empty();
            }

            if (delimiter == digitsBeforeDelimiter) {
                normalizedString = incomingString;
            } else {
                StringBuilder additionalZero = new StringBuilder();
                for (int i = 0; i <= digitsBeforeDelimiter - delimiter; i++) {
                    additionalZero.append("0");
                }
                normalizedString = additionalZero.toString().concat(incomingString);
            }

            return Optional.of(normalizedString);
        }

        public String toString() {
            StringBuilder builder = new StringBuilder();

            StringBuilder dataString = new StringBuilder();
            dataString.append(SENTENCE_NAME).append(",")
                    .append(latitude != null ? latitude : "").append(",")
                    .append(latitudeHemisphere != null ? latitudeHemisphere.toChar() : "").append(",")
                    .append(longitude != null ? longitude : "").append(",")
                    .append(longitudeHemisphere != null ? longitudeHemisphere.toChar() : "").append(",")
                    .append(altitude != null ? altitude : "").append(",")
                    .append(altitudeUnit != null ? altitudeUnit.toChar() : "");

            builder.append("$")
                    .append(dataString)
                    .append("*")
                    .append(getCheckSum(dataString.toString()));

            return builder.toString();
        }

        public boolean isValid() {
            if (latitude == null || latitudeHemisphere == null || longitude == null || longitudeHemisphere == null
                    || altitude == null && altitudeUnit == null) {
                return false;
            }

            return (normalizeStringLength(String.valueOf(latitude), 4).isPresent() &&
                    normalizeStringLength(String.valueOf(longitude), 6).isPresent());
        }
    }

    @Route("dialog-footer")
    private class ResetDialog extends Div {
        Dialog dialog;

        public void open() {
            dialog.open();
        }

        public ResetDialog() {

            dialog = new Dialog();

            dialog.setHeaderTitle("Перезавантажити БІНС?");
            dialog.add("Ви впевнені, що бажаєте перезавантахити навігаційну систему БІНС?");
            dialog.addClassName("dialog-bins");
            Button confirmBt = new Button("Перезавантажити", (e) -> {
                binsService.sendReset();
                dialog.close();
            });
            confirmBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY,
                    ButtonVariant.LUMO_ERROR);
            confirmBt.getStyle().set("margin-right", "auto");
            dialog.getFooter().add(confirmBt);

            Button cancelButton = new Button("Відміна", (e) -> dialog.close());
            cancelButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
            dialog.getFooter().add(cancelButton);
        }
    }
}


