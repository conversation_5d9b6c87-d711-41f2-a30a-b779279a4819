package com.deb.spl.control.views.log;

import com.deb.spl.control.repository.LogRepository;
import com.deb.spl.control.service.Broadcaster;
import com.deb.spl.control.service.DateTimeService;
import com.deb.spl.control.views.MainLayout;
import com.deb.spl.control.views.adjacentSystems.utils.AnimationColor;
import com.deb.spl.control.views.adjacentSystems.utils.ConfigurationUtils;
import com.deb.spl.control.views.adjacentSystems.utils.LogExtendedInfoDialogBuilder;
import com.deb.spl.control.views.adjacentSystems.utils.StatusToColorMapper;
import com.deb.spl.control.views.events.FileCreatedEvent;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;
import com.deb.spl.control.data.LogBean;
import com.deb.spl.control.utils.FutureConvertor;
import com.deb.spl.control.utils.LogBeanService;
import com.deb.spl.control.utils.Pdf.ExportDataType;
import com.deb.spl.control.utils.Pdf.TmpFileUtils;
import com.deb.spl.control.utils.ReportGenerator;
import com.deb.spl.control.utils.ReportSettings;
import com.deb.spl.control.utils.logs.EnumFilterUtils;
import com.vaadin.flow.component.Key;
import com.vaadin.flow.component.KeyModifier;
import com.vaadin.flow.component.UI;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.checkbox.Checkbox;
import com.vaadin.flow.component.combobox.MultiSelectComboBox;
import com.vaadin.flow.component.datepicker.DatePicker;
import com.vaadin.flow.component.datetimepicker.DateTimePicker;
import com.vaadin.flow.component.dependency.CssImport;
import com.vaadin.flow.component.dialog.Dialog;
import com.vaadin.flow.component.grid.Grid;
import com.vaadin.flow.component.icon.Icon;
import com.vaadin.flow.component.icon.VaadinIcon;
import com.vaadin.flow.component.notification.Notification;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.data.value.ValueChangeMode;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.util.concurrent.ListenableFuture;

import javax.validation.constraints.NotNull;
import java.io.File;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@PageTitle("Журнал")
@Route(value = "log_main_view_1", layout = MainLayout.class)
@CssImport(value = "./styles/dynamic-grid-row-background-color.css", themeFor = "vaadin-grid")
public class LogMainView_new extends VerticalLayout {

    private final LogRepository logRepository;
    private final LogBeanService logService;
    private final DateTimeService dateTimeService;
    private final ReportGenerator reportGenerator;
    private final List<String> tableHeaders;
    private final List<Float> rowWiths;
    private Button exportLogButton;
    private Button cancelExportButton;

    Grid<LogBean> grid = new Grid<>(LogBean.class, false);

    private DateTimeFormatter ukrainianFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy\tE  HH:mm:ss.SSS", new Locale("uk"));

    public LogMainView_new(@Qualifier("OpenPdfAsyncReportGenerator") ReportGenerator reportGenerator,
                           LogRepository logRepository,
                           LogBeanService logService,
                           DateTimeService dateTimeService,
                           @Value("${report.enabled}") boolean reportIsEnabled,
                           @Value("#{'${report.rows.header.captions}'.split(',')}") List<String> headerCaptions,
                           @Value("#{'${report.rows.withs}'.split(',')}") List<Float> rowWiths) {
        this.logRepository = logRepository;
        this.logService = logService;
        this.reportGenerator = reportGenerator;
        this.dateTimeService = dateTimeService;
        this.reportIsEnabled = reportIsEnabled;
        this.rowWiths = new ArrayList<>(rowWiths);
        for (int i = 0; i < headerCaptions.size(); i++) {
            try {
                headerCaptions.set(i, new String(headerCaptions.get(i).getBytes("ISO-8859-1"), StandardCharsets.UTF_8));
            } catch (UnsupportedEncodingException e) {
                log.error(String.valueOf(e));
            }
        }
        this.tableHeaders = new ArrayList<>(headerCaptions);

        initFilterField();
        grid.setPageSize(500);
        add(grid);
        addClassName("log-layout-all");
        grid.addClassName("grid-layout-log");
        grid.addColumn(LogBean::getId).setHeader("ID");
        grid.addColumn(e -> e.getSystem().getUa()).setHeader("Система");
        grid.addColumn(e -> (e.getUpdatedAt()).format(ukrainianFormatter)).setHeader("Оновлено").setSortable(true);
        grid.addColumn(LogBean::getStatus).setHeader("Ознака").setSortable(false);
        grid.addColumn(LogBean::getInfo).setHeader("Опис").setSortable(false);

        grid.addItemDoubleClickListener(event -> {
            Dialog itemInfoDialog = LogExtendedInfoDialogBuilder.getDialog(event.getItem());
            itemInfoDialog.open();
        });

        grid.setItems(query -> logService.selectFiltered(PageRequest.of(query.getPage(), query.getPageSize()),
                filterTextField.getValue(),
                (systemFilterCb.getSelectedItems().size() == 0) ? List.of(AdjacentSystemType.UNDEFINED) : (systemFilterCb.isSelected(AdjacentSystemType.UNCLASSIFIED) ? List.of(AdjacentSystemType.UNCLASSIFIED) : systemFilterCb.getSelectedItems().stream().toList()),
                (statusCb.getSelectedItems().size() == 0 ? List.of(AdjacentSystemStatus.UNDEFINED) : statusCb.isSelected(AdjacentSystemStatus.ANY) ? List.of(AdjacentSystemStatus.ANY) : statusCb.getSelectedItems().stream().toList()),
                startDatePicker.getValue(),
                endDatePicker.getValue()).stream());


        grid.setClassNameGenerator(item -> {
            if (item == null || item.getStatus() == null) {
                return AnimationColor.UNDEFINED.getText();
            }
            Class aClass = AdjacentSystemStatus.class;
            if (!AdjacentSystemWithDescriptionUa.class.isAssignableFrom(aClass)) {
                return AnimationColor.UNDEFINED.getText();
            }
            return StatusToColorMapper.fromElementsStatuses(item.getStatus().value, aClass).getText();
        });
    }


    private TextField filterTextField;
    private MultiSelectComboBox<AdjacentSystemType> systemFilterCb;
    private MultiSelectComboBox<AdjacentSystemStatus> statusCb;
    private DateTimePicker startDatePicker;
    private DateTimePicker endDatePicker;
    private final boolean reportIsEnabled;

    private void initFilterField() {
        HorizontalLayout filterHz = new HorizontalLayout();
        filterHz.setClassName("grid-filter-layout");

        HorizontalLayout filterDtWithButtonsHz = new HorizontalLayout();
        filterDtWithButtonsHz.setClassName("grid-filters-buttons-layout");

        filterTextField = new TextField();
        filterTextField.setPlaceholder("Пошук");
        filterTextField.addClassName("filter-text-field");
        filterTextField.setPrefixComponent(new Icon("lumo", "search"));
        filterTextField.setValueChangeMode(ValueChangeMode.LAZY);

        systemFilterCb = new MultiSelectComboBox<>();
        systemFilterCb.addClassName("system-filter-cb");
        systemFilterCb.setItemLabelGenerator(AdjacentSystemType::getUa);
        systemFilterCb.setItems(EnumFilterUtils.getConsiderableSystemsFilter());
        systemFilterCb.setValue(AdjacentSystemType.UNCLASSIFIED);
        systemFilterCb.addValueChangeListener(event -> {
            if (event == null || event.getValue() == null || event.getValue().size() < 1) {
                return;
            }
            if (!event.getOldValue().contains(AdjacentSystemType.UNDEFINED) && event.getValue().contains(AdjacentSystemType.UNDEFINED)) {
                systemFilterCb.setValue(AdjacentSystemType.UNDEFINED);
            } else if (!event.getOldValue().contains(AdjacentSystemType.UNCLASSIFIED) && event.getValue().contains(AdjacentSystemType.UNCLASSIFIED)) {
                systemFilterCb.setValue(AdjacentSystemType.UNCLASSIFIED);
            } else if (systemFilterCb.getValue().size() > 1 && (systemFilterCb.getValue().contains(AdjacentSystemType.UNCLASSIFIED) || systemFilterCb.getValue().contains(AdjacentSystemType.UNDEFINED))) {
                systemFilterCb.setValue(systemFilterCb.getValue().stream().filter(e -> !(e.equals(AdjacentSystemType.UNCLASSIFIED) || e.equals(AdjacentSystemType.UNDEFINED))).toList());
            }
        });

        statusCb = new MultiSelectComboBox<>();
        statusCb.addClassName("status-cb");
        statusCb.setItemLabelGenerator(AdjacentSystemStatus::getValueUa);
        statusCb.setItems(EnumFilterUtils.getConsiderableSystemStatusesFilter());
        statusCb.setValue(AdjacentSystemStatus.ANY);
        statusCb.addValueChangeListener(event -> {
            if (event == null || event.getValue() == null || event.getValue().size() < 1) {
                return;
            }

            if (!event.getOldValue().contains(AdjacentSystemStatus.ANY) && event.getValue().contains(AdjacentSystemStatus.ANY)) {
                statusCb.setValue(AdjacentSystemStatus.ANY);
            } else if (event.getValue().size() > 1 && event.getValue().contains(AdjacentSystemStatus.ANY)) {
                statusCb.setValue(statusCb.getValue().stream().filter(e -> !e.equals(AdjacentSystemStatus.ANY)).toList());
            }
        });

        startDatePicker = getDateI18nUa("Початок", true);

        endDatePicker = getDateI18nUa("Кінець", false);

        Button refresh = new Button(VaadinIcon.REFRESH.create(), ev -> {
            if (getUI().isPresent()) {
                getUI().get().access(() -> grid.getDataProvider().refreshAll());
            }
        });
        refresh.setText("Оновити");
        refresh.addClickShortcut(Key.ENTER, KeyModifier.CONTROL);
        refresh.addClassName("refresh-button");


        Button clearBt = new Button(VaadinIcon.FILTER.create(), event -> {
            if (getUI().isPresent()) {
                getUI().get().access(() -> {
                    filterTextField.clear();
                    systemFilterCb.setValue(AdjacentSystemType.UNCLASSIFIED);
                    statusCb.setValue(AdjacentSystemStatus.ANY);
                    startDatePicker.clear();
                    endDatePicker.clear();
                });
            }

        });
        clearBt.setText("Зкинути фільтри");
        clearBt.addClassName("clear-button");
        clearBt.setWidth("200px");
        filterHz.add(filterTextField, systemFilterCb, statusCb);
        exportLogButton = initExportToPdf();
        cancelExportButton = initCancelPdf();

        if (reportIsEnabled) {
            VerticalLayout exportActionsButtonsLayout = new VerticalLayout(refresh, exportLogButton, cancelExportButton);
            exportActionsButtonsLayout.addClassName("refresh-export-button");

            filterDtWithButtonsHz.add(startDatePicker, endDatePicker, clearBt, exportActionsButtonsLayout);
        } else {
            filterDtWithButtonsHz.add(startDatePicker, endDatePicker, clearBt, refresh);
        }
        add(filterHz, filterDtWithButtonsHz);
    }

    private Button initExportToPdf() {
        Button exportBt = new Button("Экспортувати", new Icon(VaadinIcon.FILE_TEXT));
        exportBt.addThemeVariants(ButtonVariant.LUMO_SUCCESS, ButtonVariant.LUMO_SMALL);
        exportBt.setClassName("export-pdf-button");
        exportBt.addClickListener((event) -> createReportOptionsDialog(exportBt));

        return exportBt;
    }

    private Button initCancelPdf() {
        Button button = new Button("Скасувати", new Icon(VaadinIcon.CLOSE_SMALL));
        button.setVisible(false);
        button.addThemeVariants(ButtonVariant.LUMO_SUCCESS, ButtonVariant.LUMO_SMALL);
        button.setClassName("cancel-export-pdf-button");

        return button;
    }

    private ListenableFuture<String> createReport(String fileName, @NotNull List<LogBean> data) {
        try {
            List<AdjacentSystemType> systemsFilter = (systemFilterCb.getSelectedItems().size() == 0) ? List.of(AdjacentSystemType.UNDEFINED) :
                    (systemFilterCb.isSelected(AdjacentSystemType.UNCLASSIFIED) ? List.of(AdjacentSystemType.UNCLASSIFIED) :
                            systemFilterCb.getSelectedItems().stream().toList());
            List<AdjacentSystemStatus> systemStatusesFilter = statusCb.getSelectedItems().size() == 0 ? List.of(AdjacentSystemStatus.UNDEFINED) :
                    statusCb.isSelected(AdjacentSystemStatus.ANY) ? List.of(AdjacentSystemStatus.ANY) : statusCb.getSelectedItems().stream().toList();
            ReportSettings reportSettings = new ReportSettings(filterTextField.getValue(),
                    systemsFilter,
                    systemStatusesFilter,
                    startDatePicker.getValue(),
                    endDatePicker.getValue());

            ListenableFuture<String> future = reportGenerator.exportToPdf(fileName,
                    data,
                    tableHeaders,
                    rowWiths);

            return future;
        } catch (Exception e) {
            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
            return AsyncResult.forValue("failed to create pdf");
        }

    }

    private DateTimePicker getDateI18nUa(String label, boolean initAsStartOfDay) {
        DateTimePicker dateTimePicker = new DateTimePicker(label);
        dateTimePicker.setLocale(new Locale("en", "GB"));
        DatePicker.DatePickerI18n uaI18n = new DatePicker.DatePickerI18n();
        uaI18n.setMonthNames(List.of("Січень", "Лютий", "Березень", "Квітень", "Травень", "Червень", "Липень", "Серпень", "Вересень", "Жовтень", "Листопад", "Грудень"));
        uaI18n.setWeekdays(List.of("Неділя", "Понеділок", "Вівторок", "Середа", "Четвер", "П'ятниця", "Субота"));
        uaI18n.setWeekdaysShort(List.of("Нд", "Пн", "Вт", "Ср", "Чт", "Пт", "Сб"));
        uaI18n.setToday("Сьгодні");
        uaI18n.setCancel("Відміна");
        uaI18n.setDateFormat("dd/MM/yyyy");

        dateTimePicker.setDatePickerI18n(uaI18n);

        dateTimePicker.setStep(Duration.ofMinutes(1));
        dateTimePicker.setHelperText("Формат: дд/ММ/рррр гг:хх");
        if (initAsStartOfDay) {
            dateTimePicker.getElement().executeJs("this.getElementsByTagName(\"vaadin-date-time-picker-date-picker\")[0]" +
                                                  ".addEventListener('change', function(){this.getElementsByTagName(\"vaadin-date-time-picker-time-picker\")[0].value='00:00';}" +
                                                  ".bind(this));");
        } else {
            dateTimePicker.getElement().executeJs("this.getElementsByTagName(\"vaadin-date-time-picker-date-picker\")[0]" +
                                                  ".addEventListener('change', function(){this.getElementsByTagName(\"vaadin-date-time-picker-time-picker\")[0].value='23:59';}" +
                                                  ".bind(this));");
        }
        return dateTimePicker;
    }

    private Dialog createReportOptionsDialog(Button exportBt) {
        Dialog dialog = new Dialog();
        dialog.addClassName("log-export-options-dialog");

        dialog.setHeaderTitle("Експорт даних");
        dialog.add("Оберіть параметри експорту");
        VerticalLayout exportOptionLayout = new VerticalLayout();
        Checkbox exportSystemStatesRequestsOption = new Checkbox("Єкспортувати історію параметрів систем?", true);
        Checkbox exportCommandsOption = new Checkbox("Єкспортувати історію команд?");
        Checkbox exportNavigationServiceNmea = new Checkbox("Єкспортувати історію роботи УКННС та Метеостанції?");
        Checkbox exportNavigationServiceRequestsOption = new Checkbox("Єкспортувати журнали HTTP запитів УКННС та Метеостанції?");
        exportOptionLayout.add(exportSystemStatesRequestsOption, exportCommandsOption, exportNavigationServiceNmea, exportNavigationServiceRequestsOption);

        dialog.add(exportOptionLayout);

        Button confirmBt = new Button("Так", (confirmEvent) -> {
            UI ui = getUI().get();

            ui.access(() -> {
                exportBt.setVisible(false);
                cancelExportButton.setVisible(true);
            });

            LocalDateTime startDate = startDatePicker.getValue() != null ? startDatePicker.getValue() :
                    LocalDateTime.of(1917, 1, 1, 0, 0);
            LocalDateTime endDate = endDatePicker.getValue() != null ? endDatePicker.getValue() : LocalDateTime.now();

            if (exportNavigationServiceNmea.getValue()) {
                List<AdjacentSystemType> selectedSystems = List.of(AdjacentSystemType.BINS, AdjacentSystemType.MSU);

                for (AdjacentSystemType selectedSystem : selectedSystems) {
                    List<LogBean> data = logService.selectCommandLogBetweenDates(selectedSystem, startDate, endDate);
                    log.debug("exportNavigationServiceNmea system:" + selectedSystem);

                    if (data.size() > 0) {
                        data = data.stream().limit(10).toList(); // TODO: 12/8/2023 remove
                        String fileName = reportGenerator.getReportFileName(reportGenerator.getFileNameTsFormatter(),
                                selectedSystem.getEn(), ExportDataType.COMMAND);
                        log.debug("creating a signUpExportTask system:" + selectedSystem + " fileName: " + fileName
                                  + "data size " + data.size());
                        signUpExportTask(ui, exportBt, fileName, data);
                    } else {
                        log.debug("no record to export found in " + selectedSystem);
                    }
                }
            }

            //update validate and update settings
            if (exportCommandsOption.getValue()) {
                // TODO: 11/30/2023 export commands logs
                List<AdjacentSystemType> selectedSystems = (systemFilterCb.getSelectedItems().size() == 0) ?
                        List.of(AdjacentSystemType.UNDEFINED) : (systemFilterCb.isSelected(AdjacentSystemType.UNCLASSIFIED) ?
                        List.of(AdjacentSystemType.UNCLASSIFIED) : systemFilterCb.getSelectedItems().stream().toList());
                selectedSystems = logService.getSystemFiltersAsSystemType(selectedSystems);
                for (AdjacentSystemType selectedSystem : selectedSystems) {
                    List<LogBean> data = logService.selectCommandLogBetweenDates(selectedSystem, startDate, endDate);

                    String fileName = reportGenerator.getReportFileName(reportGenerator.getFileNameTsFormatter(),
                            selectedSystem.getEn(), ExportDataType.COMMAND);
                    signUpExportTask(ui, exportBt, fileName, data);
                }
            }
            if (exportNavigationServiceRequestsOption.getValue()) {
                List<LogBean> data = logService.selectHttpLogs(startDate, endDate);

                String fileName = reportGenerator.getReportFileName(reportGenerator.getFileNameTsFormatter(),
                        "", ExportDataType.HTTP);
                signUpExportTask(ui, exportBt, fileName, data);
            }
            if (exportSystemStatesRequestsOption.getValue()) {
                List<LogBean> data = logService.selectFiltered(filterTextField.getValue(),
                        (systemFilterCb.getSelectedItems().size() == 0) ? List.of(AdjacentSystemType.UNDEFINED) : (systemFilterCb.isSelected(AdjacentSystemType.UNCLASSIFIED) ? List.of(AdjacentSystemType.UNCLASSIFIED) : systemFilterCb.getSelectedItems().stream().toList()),
                        (statusCb.getSelectedItems().size() == 0 ? List.of(AdjacentSystemStatus.UNDEFINED) : statusCb.isSelected(AdjacentSystemStatus.ANY) ? List.of(AdjacentSystemStatus.ANY) : statusCb.getSelectedItems().stream().toList()),
                        startDatePicker.getValue(),
                        endDatePicker.getValue());

                String fileName = reportGenerator.getReportFileName(reportGenerator.getFileNameTsFormatter(), ExportDataType.STATES);
                signUpExportTask(ui, exportBt, fileName, data);
            }
            dialog.close();
        });

        confirmBt.addClassName("confirm-button-export-options-dialog");

        confirmBt.addThemeVariants(ButtonVariant.LUMO_PRIMARY,
                ButtonVariant.LUMO_ERROR);
        confirmBt.getStyle().set("margin-right", "auto");
        dialog.getFooter().add(confirmBt);

        Button cancelButton = new Button("Відміна", (cancelEvent) -> dialog.close());
        cancelButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
        cancelButton.addClassName("cancel-button-export-options-dialog");

        dialog.getFooter().add(cancelButton);

        dialog.open();

        return dialog;
    }

    private static List<CompletableFuture<String>> assignedExports = new ArrayList<>();

    private void signUpExportTask(UI ui, Button exportBt, String fileName, List<LogBean> data) {

        ListenableFuture<String> future = createReport(fileName, data);
        assignedExports.add(FutureConvertor.buildCompletableFutureFromListenableFuture(future));
        CompletableFuture<String>[] futures = new CompletableFuture[assignedExports.size()];
        assignedExports.toArray(futures);

        CompletableFuture<Void> allOf = CompletableFuture.allOf(
                assignedExports.toArray(futures)
        );
        allOf.thenRunAsync(() -> {
            // This block is executed when all CompletableFuture instances are completed
            try {
                AtomicBoolean interrupted = new AtomicBoolean(false);
                // Extract results or handle completion as needed
                List<String> results = assignedExports.stream()
                        .map(f -> {
                            try {
                                return f.get();
                            } catch (InterruptedException | ExecutionException e) {
                                log.error("report export was interrupted" + Arrays.toString(e.getStackTrace()));
                                interrupted.set(true);

                                return "Error";
                            }
                        })
                        .toList();

                // Use results or perform other logic
                if (interrupted.get()) {
                    Broadcaster.broadcast(FileCreatedEvent.builder()
                            .adjacentSystemType(AdjacentSystemType.ASKU)
                            .source(this)
                            .payload("Eкспорт протоколу перевано!")
                            .build());

                    ui.access(() -> {
                        cancelExportButton.setVisible(false);
                        exportBt.setVisible(true);
                        exportBt.setEnabled(true);
                    });

                    log.debug("export interrupted ");
                    return;
                }

                if (results.size() > 0) {
                    ui.access(() -> Notification.show("Processing Completed! size " + results.size()));

                    List<FileCreatedEvent.FileInfo> exportResults = new ArrayList<>();
                    for (String singleResult : results) {
                        if (TmpFileUtils.getTopMostFileByName(singleResult).isEmpty()) {
                            return;
                        }
                        File file = TmpFileUtils.getTopMostFileByName(singleResult).get();
                        exportResults.add(FileCreatedEvent.FileInfo.builder()
                                .fileName(file.getName())
                                .pathToFile(file.getPath())
                                .size(file.length())
                                .createdAt(TmpFileUtils.getCreationTs(file.toPath(), dateTimeService.getTimeZoneId()))
                                .build());
                        assignedExports.removeIf(r -> {
                            try {
                                return r.get().equals(singleResult);
                            } catch (InterruptedException | ExecutionException e) {
                                log.error(Arrays.toString(e.getStackTrace()));
                                throw new RuntimeException(e);
                            }
                        });
                    }

                    Broadcaster.broadcast(FileCreatedEvent.builder()
                            .adjacentSystemType(AdjacentSystemType.ASKU)
                            .source(this)
                            .payload("Протокол готовий для завантаження")
                            .filesInfo(exportResults)
                            .build());
                } else {
                    ui.access(() -> Notification.show("No files were exported! size " + results.size()));
                }
                // Notify user using Vaadin Broadcaster

            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        cancelExportButton.addClickListener((cancelEvent) -> {
            future.cancel(true);
        });

        future.addCallback(successResult -> {
            ui.access(() -> {
                cancelExportButton.setVisible(false);
                exportBt.setVisible(true);
                exportBt.setEnabled(true);

            });
        }, failureException -> {
            ui.access(() -> {
                cancelExportButton.setVisible(false);
                exportBt.setVisible(true);
                exportBt.setEnabled(true);
            });
            ConfigurationUtils.getErrorNotification("Помилка єкспорту даних",
                            "Неможливо єкспортувати данні. Перезавантажте сторінку та повторіть спробу.",
                            -1,
                            true)
                    .open();
        });
    }

    private enum ExportType {
        COMMAND,
        SYSTEM_STATUS,
        NAVIGATION_METEO,
        HTTP
    }
}
