package com.deb.spl.control.views.adjacentSystems.bins;

import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.asku.RocketMapper;
import com.deb.spl.control.repository.bins.BinsHistoryRepository;
import com.deb.spl.control.repository.bins.NavigationRestRequestRepository;
import com.deb.spl.control.service.*;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.service.asku.PlcService;
import com.deb.spl.control.views.MainLayout;
import com.deb.spl.control.views.adjacentSystems.AdjacentSystemView;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.router.PageTitle;
import com.vaadin.flow.router.Route;
import org.springframework.beans.factory.annotation.Value;
import org.vaadin.tabs.PagedTabs;

@PageTitle("Сатус систем | УКННС")
@Route(value = "adjacent-systems/bins", layout = MainLayout.class)
public class BinsView extends AdjacentSystemView {
    private final BinsHistoryRepository binsHistoryRepository;

    public BinsView(MsuService msuService,
                    PpoService ppoService,
                    SutoService sutoService,
                    BinsService binsService,
                    SaeService saeService,
                    AskuService askuService,
                    PlcService plcService,
                    UserService userService,
                    BinsHistoryRepository binsHistoryRepository,
                    NavigationRestRequestRepository navigationRestRequestRepository,
                    RocketMapper rocketMapper,
                    NppaService nppaService,
                    CcvCommunicationService ccvCommunicationService,
                    @Value("${log.adjacent-system-view.enable}")
                    boolean showRestLog) {
        super(msuService,
                ppoService,
                sutoService,
                binsService,
                saeService,
                askuService,
                plcService,
                "Навігаційна уніфікована комплексована система",
                rocketMapper,
                nppaService,
                ccvCommunicationService);
        this.binsHistoryRepository = binsHistoryRepository;

        BinsCommandsTab binsCommandsTab = new BinsCommandsTab(binsService);
        VerticalLayout commandTab = new VerticalLayout();
        commandTab.addClassName("command-tab-bins");

        commandTab.setWidth("100%");
        commandTab.setMaxWidth("100%");
        commandTab.add(binsCommandsTab);//commands class

        SplNavigationLogTab binsLogTab = new SplNavigationLogTab(binsHistoryRepository, "bins-log-tab");
        VerticalLayout logTab = new VerticalLayout();
        logTab.addClassName("log-tab");
        logTab.setWidth("100%");
        logTab.setMaxWidth("100%");
        logTab.add(binsLogTab); //log class

        VerticalLayout tabContainer = new VerticalLayout();
        PagedTabs tabs = new PagedTabs(tabContainer);
        tabs.getContent().setWidthFull();

        String commandTabHeader = "Керування";
        String logTabHeader = "Журнал";

        tabs.add(commandTabHeader, commandTab, false);
        tabs.add(logTabHeader, logTab, false);

        if (showRestLog) {
            String restLogHeader = "Журнал запитів";

            NavigationRestLogTab restLogTab = new NavigationRestLogTab(navigationRestRequestRepository,
                    AdjacentSystemType.BINS,
                    "nav-rest-log-tab");

            VerticalLayout restLogHolder = new VerticalLayout();
            restLogHolder.addClassName("rest-log-tab");
            restLogHolder.setWidth("100%");
            restLogHolder.setMaxWidth("100%");
            restLogHolder.add(restLogTab);

            tabs.add(restLogHeader, restLogHolder, false);
            tabs.addSelectedChangeListener(e -> {
                if (e.getLabel().equals(restLogHeader)) {
                    restLogTab.setupDataBinding();
                }
            });
        }

        getSystemLayout().add(tabs, tabContainer);
        getSystemLayout().addClassName("system-layout-bins");
        getSystemLayout().setWidth("69.5%");
        getSystemLayout().setMaxWidth("69.5%");
    }

}
