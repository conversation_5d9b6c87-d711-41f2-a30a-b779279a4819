package com.deb.spl.control.views.events;

import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.asku.AskuInfo;
import lombok.*;

import java.util.Optional;

@Getter
@Setter
@EqualsAndHashCode
@Builder
public class AskuInfoEvent implements AdjacentSystemEvent{
    final AskuInfo askuInfo;
    Object source;
    AdjacentSystemUpdateEventType eventType;

    /**
     * @param askuInfo changed object
     * @param source event source
     * @param eventType @{@link AdjacentSystemUpdateEventType}
     */
    public AskuInfoEvent(AskuInfo askuInfo, Object source, AdjacentSystemUpdateEventType eventType) {
        this.askuInfo = askuInfo;
        this.source = source;
        this.eventType = eventType;
    }

    public AskuInfoEvent(AskuInfoEvent prototype) {
        this.askuInfo =prototype.getAskuInfo();
        this.source=prototype.getSource();
        this.eventType=prototype.getEventType();
    }

    @Override
    public AdjacentSystemType getSystemType() {
        return AdjacentSystemType.ASKU;
    }

    @Override
    public Optional<AdjacentSystem> getEntity() {
        return Optional.ofNullable(askuInfo);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof AskuInfoEvent that)) return false;

        if (getAskuInfo() != null ? !getAskuInfo().equals(that.getAskuInfo()) : that.getAskuInfo() != null)
            return false;
        if (getSource() != null ? !getSource().equals(that.getSource()) : that.getSource() != null) return false;
        return getEventType() == that.getEventType();
    }

    @Override
    public int hashCode() {
        int result = getAskuInfo() != null ? getAskuInfo().hashCode() : 0;
        result = 31 * result + (getSource() != null ? getSource().hashCode() : 0);
        result = 31 * result + (getEventType() != null ? getEventType().hashCode() : 0);
        return result;
    }
}
