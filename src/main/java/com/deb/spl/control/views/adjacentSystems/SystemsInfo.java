package com.deb.spl.control.views.adjacentSystems;

import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.asku.*;
import com.deb.spl.control.data.ccv.VehicleResource;
import com.deb.spl.control.data.nppa.Nppa;
import com.deb.spl.control.service.*;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.service.asku.PlcService;
import com.deb.spl.control.views.adjacentSystems.utils.AnimationColor;
import com.deb.spl.control.views.adjacentSystems.utils.ConfigurationUtils;
import com.deb.spl.control.views.adjacentSystems.utils.StatusToColorMapper;
import com.deb.spl.control.views.events.*;
import com.deb.spl.control.views.rocket.RocketFormDataView;
import com.deb.spl.control.views.rocket.RocketInitialDataLayout;
import com.vaadin.flow.component.AbstractField;
import com.vaadin.flow.component.AttachEvent;
import com.vaadin.flow.component.Component;
import com.vaadin.flow.component.DetachEvent;
import com.vaadin.flow.component.button.Button;
import com.vaadin.flow.component.button.ButtonVariant;
import com.vaadin.flow.component.dialog.Dialog;
import com.vaadin.flow.component.formlayout.FormLayout;
import com.vaadin.flow.component.html.H4;
import com.vaadin.flow.component.html.Image;
import com.vaadin.flow.component.html.Span;
import com.vaadin.flow.component.icon.Icon;
import com.vaadin.flow.component.orderedlayout.HorizontalLayout;
import com.vaadin.flow.component.orderedlayout.VerticalLayout;
import com.vaadin.flow.component.textfield.TextField;
import com.vaadin.flow.component.textfield.TextFieldVariant;
import com.vaadin.flow.data.binder.Binder;
import com.vaadin.flow.theme.lumo.LumoUtility;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotNull;
import java.time.format.DateTimeFormatter;
import java.util.*;


@Slf4j
public class SystemsInfo extends VerticalLayout implements Broadcaster.BroadcastListener {

    public static final String MASTER_CCV_UNKNOWN_MSG = "Невідомо";
    @Getter
    private final Binder<AdjacentSystem> msuBinder = new Binder<>(AdjacentSystem.class);
    private final Binder<AdjacentSystem> binsBinder = new Binder<>(AdjacentSystem.class);
    private final Binder<AdjacentSystem> ppoBinder = new Binder<>(AdjacentSystem.class);
    private final Binder<AdjacentSystem> sutoBinder = new Binder<>(AdjacentSystem.class);
    private final Binder<AdjacentSystem> saebBinder = new Binder<>(AdjacentSystem.class);
    private final Binder<AdjacentSystem> plcBinder = new Binder<>(AdjacentSystem.class);
    private final Binder<AdjacentSystem> askuInfoBinder = new Binder<>(AdjacentSystem.class);
    private final Binder<AdjacentSystem> bynBinder = new Binder<>(AdjacentSystem.class);
    private final Binder<AdjacentSystem> ncokBinder = new Binder<>(AdjacentSystem.class);
    private final Binder<VehicleResource> ccvBinder = new Binder<>(VehicleResource.class);
    private final Binder<RocketEvent> rocketBinder = new Binder<>(RocketEvent.class);
    private final MsuService msuService;
    private final BinsService binsService;
    private final PpoService ppoService;
    private final SutoService sutoService;
    private final SaeService saeService;
    private final PlcService plcService;
    private final AskuService askuService;
    private final NppaService nppaService;
    private final CcvCommunicationService ccvCommunicationService;

    private final RocketMapper rocketMapper;
    static final Map<Class, AdjacentSystemEvent> events = new HashMap<>();

    @Getter
    private TextField binsStatusTf;
    @Getter
    private TextField msuStatusTf;

    private TextField missilePresentLeft;
    private TextField missilePresentRight;

    @Override
    protected void onAttach(AttachEvent attachEvent) {
        super.onAttach(attachEvent);

        events.put(PpoEvent.class, null);
        events.put(SutoEvent.class, null);
        events.put(BinsEvent.class, null);
        events.put(MsuEvent.class, null);
        events.put(SaeEvent.class, null);
        events.put(PlcEvent.class, null);
        events.put(AskuInfoEvent.class, null);
        events.put(NppaEvent.class, null);
        events.put(CcvVehicleEvent.class, null);
        events.put(RocketEvent.class, null);

        Broadcaster.register(this, events.keySet().toArray(new Class[0]));
    }

    @Override
    protected void onDetach(DetachEvent detachEvent) {
        super.onDetach(detachEvent);
        Broadcaster.unregister(this);
    }

    public SystemsInfo(MsuService msuService,
                       BinsService binsService,
                       PpoService ppoService,
                       SutoService sutoService,
                       SaeService saeService,
                       AskuService askuService,
                       PlcService plcService, NppaService nppaService,
                       RocketMapper rocketMapper,
                       CcvCommunicationService ccvCommunicationService) {
        this.msuService = msuService;
        this.binsService = binsService;
        this.ppoService = ppoService;
        this.sutoService = sutoService;
        this.saeService = saeService;
        this.plcService = plcService;
        this.askuService = askuService;
        this.nppaService = nppaService;
        this.rocketMapper = rocketMapper;
        this.ccvCommunicationService = ccvCommunicationService;

        Image img = new Image("images/image_6.png", "placeholder plant");
        img.addClassName("spl_image");

        this.add(img, setUpSystemsInfo());
        this.setWidth("400px");
        this.setMaxHeight("860px");
        this.addClassName("systems-info");
    }

    public HorizontalLayout setUpSystemsInfo() {
        Asku asku = askuService.getAskuEntity();

        HorizontalLayout statusPanel = new HorizontalLayout();
        statusPanel.addClassName("adjacent-systems-status-panel");
        statusPanel.addClassName(LumoUtility.Display.FLEX);
        statusPanel.addClassName(LumoUtility.AlignItems.CENTER);

        TextField splPlateNumberTF = new TextField();
        splPlateNumberTF.addClassName("vehicle-id");
        splPlateNumberTF.setLabel("ID");
        splPlateNumberTF.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        splPlateNumberTF.setValue(asku != null ? asku.getPlateNumber() : "");
        askuInfoBinder.forField(splPlateNumberTF)
                .bindReadOnly(event -> {
                    String failedResult = "Невідомо";
                    if (event == null) {
                        return failedResult;
                    }
                    if (!(AskuInfo.class.isAssignableFrom(event.getClass()))) {
                        return failedResult;
                    }
                    AskuInfo info = (AskuInfo) event;

                    return info.getPlateNumber() != null ? info.getPlateNumber() : failedResult;
                });

        TextField ccvMasterName = new TextField();
        ccvMasterName.addClassName("ccv-master-id");
        ccvMasterName.setLabel("КМУ");
        ccvMasterName.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        ccvMasterName.setValue(ccvCommunicationService.getMasterCcvName().orElse(MASTER_CCV_UNKNOWN_MSG));
        ccvBinder.forField(ccvMasterName)
                .bindReadOnly(event -> {
                    if (event == null) {
                        return MASTER_CCV_UNKNOWN_MSG;
                    }

                    if (!(VehicleResource.class.isAssignableFrom(event.getClass()))) {
                        return MASTER_CCV_UNKNOWN_MSG;
                    }

                    VehicleResource vehicleResource = event;

                    return vehicleResource.getNumber() != null ? vehicleResource.getNumber() : "";
                });


        TextField ncokTF = configureAdjacentSystemTextField(ncokBinder,
                "ncok-tf",
                "НЦОК",
                nppaService.getNcok().isPresent() && nppaService.getNcok().get().systemStatus() != null ?
                        nppaService.getNcok().get().systemStatus() : AdjacentSystemStatus.UNDEFINED);

        TextField startDateTf = new TextField();
        startDateTf.addClassName("start-date");
        startDateTf.setLabel("Дата переходу до БГ");
        startDateTf.setReadOnly(true);
        startDateTf.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);

// Create a DateTimeFormatter with a Ukrainian Locale and pattern
        DateTimeFormatter ukrainianFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy\tE", new Locale("uk"));

        startDateTf.setValue(asku != null && asku.getStartedDate() != null ?
                asku.getStartedDate().format(ukrainianFormatter) : "");

        askuInfoBinder.forField(startDateTf)
                .bindReadOnly(event -> {
                    String failedResult = "";
                    if (event == null) {
                        return failedResult;
                    }

                    if (!(AskuInfo.class.isAssignableFrom(event.getClass()))) {
                        return failedResult;
                    }
                    AskuInfo info = (AskuInfo) event;

                    return info.getStartedDate() != null ? info.getStartedDate().format(ukrainianFormatter) :
                            failedResult;
                });

        TextField startTimeTf = new TextField();
        startTimeTf.addClassName("start-time");
        startTimeTf.setLabel("Час переходу до БГ");
        startTimeTf.setReadOnly(true);
        startTimeTf.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        startTimeTf.setValue(asku != null && asku.getStartedDate() != null ?
                asku.getStartedDate().format(DateTimeFormatter.ofPattern("HH:mm:ss")) : "");
        askuInfoBinder.forField(startTimeTf)
                .bindReadOnly(event -> {
                    String failedResult = "";
                    if (event == null) {
                        return failedResult;
                    }

                    if (!(AskuInfo.class.isAssignableFrom(event.getClass()))) {
                        return failedResult;
                    }
                    AskuInfo info = (AskuInfo) event;

                    return info.getStartedDate() != null ? info.getStartedDate().format(DateTimeFormatter.ofPattern("HH:mm:ss")) :
                            failedResult;
                });

        TextField plcStatusTf = configureAdjacentSystemTextField(plcBinder,
                "plc-state",
                "Контроллер",
                plcService.getStatus() != null ? plcService.getStatus() : AdjacentSystemStatus.UNDEFINED);
        plcStatusTf.setValue(plcService.getStatus().getValueUa());

        TextField ppoStatusTf = configureAdjacentSystemTextField(ppoBinder,
                "ppo-state",
                "ППО",
                ppoService.getCurrentStatus() != null ? ppoService.getCurrentStatus() : AdjacentSystemStatus.UNDEFINED);

        binsStatusTf = configureAdjacentSystemTextField(binsBinder,
                "bins-state",
                "УКННС",
                binsService.getStatus() != null ? binsService.getStatus() : AdjacentSystemStatus.UNDEFINED);

        TextField saeStatusTf = configureAdjacentSystemTextField(saebBinder,
                "sae-state",
                "САЕ",
                saeService.getStatus() != null ? saeService.getStatus() : AdjacentSystemStatus.UNDEFINED);

        TextField sutoStatusTf = configureAdjacentSystemTextField(sutoBinder,
                "suto-state",
                "СУТО",
                sutoService.getStatus() != null ? sutoService.getStatus() : AdjacentSystemStatus.UNDEFINED);

        msuStatusTf = configureAdjacentSystemTextField(msuBinder,
                "meteo-state",
                "Метеостанція",
                msuService.getStatus() != null ? msuService.getStatus() : AdjacentSystemStatus.UNDEFINED);

        TextField bynTf = configureAdjacentSystemTextField(bynBinder,
                "byn-tf",
                "БВН",
                nppaService.getByn().isPresent() && nppaService.getByn().get().systemStatus() != null ?
                        nppaService.getByn().get().systemStatus() : AdjacentSystemStatus.UNDEFINED);

        H4 missileLeft = new H4("ОТР №1");
        missileLeft.addClassName("missile-h4-left");

        TextField missileLeftIdTf = new TextField();
        missileLeftIdTf.addClassName("missile-state-left");
        missileLeftIdTf.setReadOnly(true);
        missileLeftIdTf.setValue((askuService.getAskuInfo().isPresent() &&
                                  !askuService.getAskuInfo().get().getMissileLeftFactoryNumber().isBlank()) ?
                askuService.getAskuInfo().get().getMissileLeftFactoryNumber() : "Невідомо");
        missileLeftIdTf.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        askuInfoBinder.forField(missileLeftIdTf)
                .bindReadOnly(event -> {
                    String failedResult = "Невідомо";
                    if (event == null) {
                        return failedResult;
                    }

                    if (!(AskuInfo.class.isAssignableFrom(event.getClass()))) {
                        return failedResult;
                    }
                    AskuInfo info = (AskuInfo) event;

                    return (info.getMissileLeftFactoryNumber() != null && !info.getMissileLeftFactoryNumber().isBlank()) ? info.getMissileLeftFactoryNumber() : failedResult;
                });


        FormLayout missileFormLeft = new FormLayout();
        missileFormLeft.addClassName("missile-left");
        missileFormLeft.addFormItem(missileLeftIdTf, "ID");

        H4 missileRight = new H4("ОТР №2");
        missileRight.addClassName("missile-h4-right");

        TextField missileRightIdTf = new TextField();
        missileRightIdTf.addClassName("missile-state-right");
        missileRightIdTf.setReadOnly(true);
        missileRightIdTf.setValue((askuService.getAskuInfo().isPresent() &&
                                   !askuService.getAskuInfo().get().getMissileRightFactoryNumber().isBlank()) ?
                askuService.getAskuInfo().get().getMissileRightFactoryNumber() : "Невідомо");   // TODO need to add ID from system for this field
        missileRightIdTf.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        askuInfoBinder.forField(missileRightIdTf)
                .bindReadOnly(event -> {
                    String failedResult = "Невідомо";
                    if (event == null) {
                        return failedResult;
                    }

                    if (!(AskuInfo.class.isAssignableFrom(event.getClass()))) {
                        return failedResult;
                    }
                    AskuInfo info = (AskuInfo) event;

                    return (info.getMissileRightFactoryNumber() != null && !info.getMissileRightFactoryNumber().isBlank())
                            ? info.getMissileRightFactoryNumber() : failedResult;
                });

        FormLayout missileFormRight = new FormLayout();
        missileFormRight.addClassName("missile-right");
        missileFormRight.addFormItem(missileRightIdTf, "ID");

        Button missileInfoLeft = new Button("Інформація");
        missileInfoLeft.addClassName("missile-info-left");
        missileInfoLeft.addClickListener(event -> {
            Dialog dialog = loadMissileInfo(true);
            dialog.open();
        });

//it is necessary to add information about the first missile when the button is clicked
        Button missileInfoRight = new Button("Інформація");
        missileInfoRight.addClassName("missile-info-right");
        missileInfoRight.addClickListener(event -> {
            Dialog dialog = loadMissileInfo(false);
            dialog.open();
        });
//it is necessary to add information about the second missile when the button is clicked

        Icon missileLeftIcon = new Icon("vaadin", "rocket");
        missileLeftIcon.addClassNames("missile-left-icon");

        Icon missileRightIcon = new Icon("vaadin", "rocket");
        missileRightIcon.addClassNames("missile-right-icon");

        missilePresentLeft = new TextField();
        missilePresentLeft.addClassName("missile-present-left");
        missilePresentLeft.setReadOnly(true);
        missilePresentLeft.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);

        missilePresentLeft.addValueChangeListener(valueChangeEvent ->
                configureRocketIconUpdate(valueChangeEvent, missileLeftIcon));

        missilePresentLeft.addValueChangeListener(valueChangeEvent ->
                ConfigurationUtils.setTextAnimation(valueChangeEvent, TpcState.class));

        askuInfoBinder.forField(missilePresentLeft)
                .bindReadOnly(event -> {
                    String failedResult = "Невідомо";
                    if (event == null) {
                        return failedResult;
                    }

                    if (!(AskuInfo.class.isAssignableFrom(event.getClass()))) {
                        return failedResult;
                    }
                    AskuInfo info = (AskuInfo) event;

                    if (info.getLeftTpcState() != null) {
                        return (info.getLeftTpcState() == TpcState.TPC_WITH_ROCKET &&
                                (info.getMissileLeftFactoryNumber().length() > 0
                                 && !info.isLeftRocketTechnicalCondition())) ?
                                TpcState.ERROR.getValueUa() :
                                info.getLeftTpcState().getValueUa();
                    }

                    return failedResult;
                });

        missilePresentRight = new TextField();
        missilePresentRight.addClassName("missile-present-right");
        missilePresentRight.setReadOnly(true);

        missilePresentRight.addValueChangeListener(valueChangeEvent ->
                ConfigurationUtils.setTextAnimation(valueChangeEvent, TpcState.class));

        missilePresentRight.addValueChangeListener(valueChangeEvent ->
                configureRocketIconUpdate(valueChangeEvent, missileRightIcon));

        missilePresentRight.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);
        askuInfoBinder.forField(missilePresentRight)
                .bindReadOnly(event -> {
                    String failedResult = "Невідомо";
                    if (event == null) {
                        return failedResult;
                    }

                    if (!(AskuInfo.class.isAssignableFrom(event.getClass()))) {
                        return failedResult;
                    }
                    AskuInfo info = (AskuInfo) event;

                    if (info.getRightTpcState() != null) {

                        return (info.getRightTpcState() == TpcState.TPC_WITH_ROCKET &&
                                (info.getMissileRightFactoryNumber().length() > 0 && !info.isRightRocketTechnicalCondition())) ?
                                TpcState.ERROR.getValueUa() :
                                info.getRightTpcState().getValueUa();
                    }

                    return failedResult;
                });

        statusPanel.add(splPlateNumberTF, ccvMasterName, startDateTf, startTimeTf, plcStatusTf, ppoStatusTf, saeStatusTf, binsStatusTf,
                sutoStatusTf, msuStatusTf, ncokTF, bynTf, missileLeft, missileLeft, missileRight, missileFormLeft, missileLeftIdTf,
                missileFormRight, missileRightIdTf, missileInfoLeft, missileInfoRight, missileLeftIcon, missileRightIcon, missilePresentLeft,
                missilePresentRight);

        statusPanel.setSizeFull();

        missilePresentLeft.setValue(askuService.getLeftTpcLoadState().isPresent() ?
                askuService.getLeftTpcLoadState().get().getValueUa() : TpcState.UNKNOWN.getValueUa());

        missilePresentRight.setValue(askuService.getRightTpcLoadState().isPresent() ?
                askuService.getRightTpcLoadState().get().getValueUa() : TpcState.UNKNOWN.getValueUa());

        return statusPanel;
    }

    private Dialog loadMissileInfo(@NotNull boolean isLeftRocket) {
        Dialog dialog = new Dialog();
        dialog.setHeaderTitle("Дані по " + " ОТР" + (isLeftRocket ? " №1" : " №2"));
        RocketFormDataView rocketFormDataView = new RocketFormDataView(askuService, isLeftRocket, "rocket-form-data-view");
        VerticalLayout formData = new VerticalLayout();
        formData.add(new Span("Формулярні дані"), rocketFormDataView.buildView());
        RocketInitialDataLayout rocketInitialDataLayout = new RocketInitialDataLayout(
                isLeftRocket,
                false,
                askuService,
                rocketMapper,
                false,
                "initial-data-latitude",
                "initial-data-altitude",
                "initial-data-longitude",
                "initial-data-inclination-angle",
                "initial-data-readiness",
                "initial-data-time-stamp",
                "otr-vd-layout",
                "change-initial-data",
                "launch-planned-time",
                "order-valid-until");
        HorizontalLayout formLayout = new HorizontalLayout();
        VerticalLayout initialData = new VerticalLayout();
        Span titleInitialData = new Span("Дані по цілі");
        titleInitialData.addClassName("title-initial-data");
        initialData.add(titleInitialData, rocketInitialDataLayout.buildView());
        initialData.addClassName("initial-data-dialog");


        double basuOtrTemperature = askuService.getRocketCopy(isLeftRocket).isPresent() ?
                askuService.getRocketCopy(isLeftRocket).get().getSensorTemperature()
                : -999.9;

        Component sensorLayout = ConfigurationUtils.buildRocketSensorsLayout(
                askuService,
                "basu-temperature",
                rocketBinder,
                isLeftRocket, "Температура БАСУ" ,String.valueOf(basuOtrTemperature));

        formLayout.add(formData, initialData, sensorLayout);

        dialog.add(formLayout);

        Button cancelButton = new Button("Закрити", (e) -> dialog.close());
        cancelButton.addThemeVariants(ButtonVariant.LUMO_TERTIARY);
        dialog.getFooter().add(cancelButton);

        return dialog;
    }

    private TextField configureAdjacentSystemTextField(@NotNull Binder<AdjacentSystem> binder,
                                                       @NotNull String cssClassName,
                                                       @NotNull String label,
                                                       @NotNull AdjacentSystemStatus systemStatus) {
        TextField textField = new TextField();
        textField.addClassName(cssClassName);
        textField.setLabel(label);
        textField.setReadOnly(true);
        textField.addThemeVariants(TextFieldVariant.LUMO_ALIGN_CENTER);

        textField.addValueChangeListener(valueChangeEvent -> {
            ConfigurationUtils.setTextAnimation(valueChangeEvent, AdjacentSystemStatus.class);
        });

        bindAdjacentSystemTextField(textField, binder);

        textField.setValue(systemStatus.getValueUa());

        return textField;
    }

    private void bindAdjacentSystemTextField(@NotNull TextField textField,
                                             @NotNull Binder<AdjacentSystem> binder) {
        binder.forField(textField)
                .bindReadOnly(event -> {
                    if (event == null) {
                        return AdjacentSystemStatus.UNDEFINED.getValueUa();
                    }
                    try {
                        return event.getStatus().getValueUa();
                    } catch (Exception e) {
                        log.error("cant bind Ppo value in " + this + " cause: " + Arrays.toString(e.getStackTrace()));
                        return AdjacentSystemStatus.UNDEFINED.getValueUa();
                    }
                });
    }

    private void configureRocketIconUpdate(@NotNull AbstractField.ComponentValueChangeEvent<TextField, String> valueChangeEvent,
                                           @NotNull Icon missileLeftIcon) {
        try {
            if (valueChangeEvent.getValue() != null && !valueChangeEvent.getValue().isBlank()) {
                TpcState state = TpcState.fromValueUa(valueChangeEvent.getValue());
                AnimationColor animationColor = StatusToColorMapper.fromTpcStatus(state);
                if (animationColor != AnimationColor.UNDEFINED) {
                    if (getUI().isPresent()) {
                        getUI().get().access(() ->
                                missileLeftIcon.setClassName(ConfigurationUtils
                                        .updateClassNameValue(
                                                missileLeftIcon.getClassName(),
                                                missileLeftIcon.getClass().getSimpleName(),
                                                animationColor.getText())));
                    } else {
                        missileLeftIcon.setClassName(ConfigurationUtils
                                .updateClassNameValue(
                                        missileLeftIcon.getClassName(),
                                        missileLeftIcon.getClass().getSimpleName(),
                                        animationColor.getText()));
                    }
                }

            }
        } catch (Exception e) {
            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
        }
    }

    public void update(List<AdjacentSystemEvent> events) {
        for (AdjacentSystemEvent event : events) {
            if (event.getSystemType() == AdjacentSystemType.MSU)
                try {
                    AdjacentSystemStatus status = msuService.getStatus();
                } catch (Exception e) {
                    log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                }
        }
    }


    @Override
    public void receiveBroadcast(Object incomingEvent) {
        if (incomingEvent instanceof CcvVehicleEvent ccvVehicleEvent) {
            handleCcvEvent(ccvVehicleEvent);
            return;
        }

        if (incomingEvent instanceof RocketEvent rocketEvent) {
            handleRocketEvent(RocketEvent.class, rocketEvent, rocketBinder);
            return;
        }

        if (!(incomingEvent instanceof AdjacentSystemEvent event)) {
            return;
        }

        if (event.getEntity().isEmpty()) {
            log.error("received event without entity " + event + " " + this);
            return;
        }

        switch (event.getSystemType()) {
            case PPO -> updateEvent(PpoEvent.class, event, ppoBinder);
            case SUTO -> updateEvent(SutoEvent.class, event, sutoBinder);
            case SAE -> updateEvent(SaeEvent.class, event, saebBinder);
            case BINS -> updateEvent(BinsEvent.class, event, binsBinder);
            case MSU -> updateEvent(MsuEvent.class, event, msuBinder);
            case PLC -> updateEvent(PlcEvent.class, event, plcBinder);
            case ASKU -> handleAskuEvent(event);
            case NCOK, BYN, NPPA -> handleNppaEvent(event);
        }
    }

    private void handleNppaEvent(@NotNull AdjacentSystemEvent event) {
        if (event.getEntity().isEmpty()) {
            return;
        }
        AdjacentSystemEvent previousEvent = null;

        if (events.get(NppaEvent.class) != null) {
            previousEvent = events.get(NppaEvent.class);
        }

        if (event.getEntity().get() instanceof Nppa updatedNppa) {
            if (getUI().isPresent()) {
                getUI().get().access(() -> {
                    bynBinder.readBean(updatedNppa.getByn());
                    ncokBinder.readBean(updatedNppa.getNcok());
                });
            }
        }
    }

    private void handleCcvEvent(@NotNull CcvVehicleEvent event) {
        if (event.getVehicleResource().isEmpty()) {
            return;
        }
        if (!(event.getVehicleResource().get() instanceof VehicleResource)) {
            return;
        }

        if (getUI().isPresent()) {
            getUI().get().access(() -> {
                ccvBinder.readBean(event.getVehicleResource().get());
            });
        }
    }

    private void handleAskuEvent(@NotNull AdjacentSystemEvent event) {
        if (event.getEntity().isEmpty()) {
            return;
        }

        if (event.getEntity().get() instanceof AskuInfo) {
            updateEvent(AskuInfoEvent.class, event, askuInfoBinder);
        }


    }

    private void handleRocketEvent(Class eventClass, RocketEvent event, Binder binder) {
        if (event.getRocket() != null && getUI().isPresent()) {
            getUI().get().access(() -> binder.readBean(event.getRocket()));
        }
    }

    private void updateEvent(Class eventClass, AdjacentSystemEvent event, Binder binder) {

        if (event.getEntity().isEmpty()) {
            return;
        }

        if (getUI().isPresent()) {
            getUI().get().access(() -> {
                binder.readBean(event.getEntity().get());
                events.replace(eventClass, event);
            });
        }
    }
}
