package com.deb.spl.control.views.events;

import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.asku.LaunchResult;
import lombok.*;

import java.util.Optional;

@AllArgsConstructor
@Getter
@Setter
@EqualsAndHashCode
@Builder
public class RocketLaunchEvent implements AdjacentSystemEvent {
    final boolean isLeft;
    final String plantMissile;
    @Builder.Default
    final LaunchResult launchResult = LaunchResult.UNKNOWN;

    @Override
    public AdjacentSystemType getSystemType() {
        return AdjacentSystemType.ASKU;
    }

    @Override
    public Optional<AdjacentSystem> getEntity() {
        return Optional.empty();
    }
}
