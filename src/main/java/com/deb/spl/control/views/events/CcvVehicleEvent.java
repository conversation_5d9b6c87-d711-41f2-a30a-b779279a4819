package com.deb.spl.control.views.events;

import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.ccv.VehicleResource;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

@NoArgsConstructor
@Getter
@Setter
@Builder
@Slf4j
public class CcvVehicleEvent implements AdjacentSystemEvent {
    VehicleResource vehicleResource;
    boolean hasConnection;
    Object source;
    AdjacentSystemUpdateEventType eventType;

    public Optional<VehicleResource> getVehicleResource() {
        return Optional.ofNullable(vehicleResource);
    }

    /**
     * @param hasConnection connect state
     * @param source        event source
     * @param eventType     @{@link AdjacentSystemUpdateEventType}
     */
    public CcvVehicleEvent(VehicleResource vehicleResource, boolean hasConnection, Object source, AdjacentSystemUpdateEventType eventType) {
        this.vehicleResource = vehicleResource;
        this.source = source;
        this.eventType = eventType;
    }

    public CcvVehicleEvent(VehicleResource vehicleResource, boolean hasConnection, Object source) {
        this.vehicleResource = vehicleResource;
        this.hasConnection = hasConnection;
        this.source = source;
        this.eventType = AdjacentSystemUpdateEventType.DATA_UPDATE;
    }

    public Optional<String> getUnitName() {
        if (vehicleResource == null) {
            return Optional.empty();
        }

        return Optional.ofNullable(vehicleResource.getUnit().getName());
    }

    @Override
    public AdjacentSystemType getSystemType() {
        return AdjacentSystemType.CCV;
    }

    @Override
    @Deprecated
    public Optional<AdjacentSystem> getEntity() {
        log.error(this + " " + " getEntity() " + "unimplemented method was used");
        return Optional.empty();
    }

}
