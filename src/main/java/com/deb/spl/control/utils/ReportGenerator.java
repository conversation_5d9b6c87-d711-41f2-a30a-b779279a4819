package com.deb.spl.control.utils;

import com.deb.spl.control.utils.Pdf.ExportDataType;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.LogBean;
import org.springframework.util.concurrent.ListenableFuture;

import javax.validation.constraints.PositiveOrZero;
import java.io.ByteArrayOutputStream;
import java.time.format.DateTimeFormatter;
import java.util.List;

public interface ReportGenerator {

    int countReportsToExport(boolean exportSystemStatesRequestsOption,
                             boolean exportCommandsOption,
                             boolean exportNavigationServiceNmea,
                             boolean exportNavigationServiceRequestsOption,
                             List<AdjacentSystemType> selectedSystems);

    String getReportFileName(DateTimeFormatter dateTimeFormatter, ExportDataType exportDataType);

    String getReportFileName(DateTimeFormatter dateTimeFormatter,
                             String adjacentSystemName,
                             ExportDataType exportDataType);


    int getNumberOfReportsToProcess();
    void setNumberOfReportsToProcess(@PositiveOrZero int numberOfReportsToProcess);

    DateTimeFormatter getFileNameTsFormatter();

    ByteArrayOutputStream createReportWithStringData(boolean showTitlePage,
                                                     boolean showSettings,
                                                     ReportSettings reportSettings,
                                                     String documentDescription,
                                                     List<List<String>> data,
                                                     List<String> tableHeaders,
                                                     List<Float> rowWiths);

    ListenableFuture<String> exportToPdf(String fileName,
                                         List<LogBean> data,
                                         List<String> tableHeaders,
                                         List<Float> rowWiths);
}
