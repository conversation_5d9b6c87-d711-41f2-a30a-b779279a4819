package com.deb.spl.control.utils;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;

import java.time.LocalDateTime;
import java.util.List;

public record ReportSettings(String filterTextField,
                             List<AdjacentSystemType> systemFilter,
                             List<AdjacentSystemStatus> systemStatus,
                             LocalDateTime startDate,
                             LocalDateTime endDate/*,
                             boolean httLogIsSelected,
                             boolean commandsLogsIsSelected*/) {
}
