package com.deb.spl.control.utils.Pdf;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Slf4j

public class TmpFileUtils {
    public static final String TMP_FILES_LOCATION = "tmp_files";
    //    private static File topMostFile;
    private static final int maxSize = 20;
    private static final List<File> fileList = new ArrayList<>();

    private static void addFile(File file) {
        fileList.add(file);

        // Check if the size exceeds the maximum
        if (fileList.size() > maxSize) {
            // Remove the first file to maintain the fixed size
            removeTmpFile(fileList.remove(0));
            File removedFile = fileList.remove(0);
            log.debug("Removed file: " + removedFile.getName());
        }
    }

    public List<File> getFiles() {
        return new ArrayList<>(fileList); // Return a copy to avoid external modification
    }

    public static Optional<File> getTopMostFileByName(String fileName) {
        return fileList.stream().filter(f -> f.getName().equals(fileName)).findFirst();
    }

    public static Optional<File> getTopMostFileByPath(String pathToFile) {
        return fileList.stream().filter(f -> f.getPath().equals(pathToFile)).findFirst();
    }

    public static String getProjectRoot() {
        return Paths.get(".").normalize().toAbsolutePath().toString();
    }

    public static String getMetaInfo() {
        return Paths.get(getProjectRoot(), "src/main/resources/META-INF").
                normalize().toAbsolutePath().toString();
    }

    public static LocalDateTime getCreationTs(Path filePath, ZoneId zoneId) {
        try {
            return LocalDateTime.ofInstant(Files.getLastModifiedTime(filePath).toInstant(),
                    zoneId);
        } catch (IOException e) {
            log.error(Arrays.toString(e.getStackTrace()));
            return null;
        }
    }

    public static File getTmpFilesFolder() {
        File folder = new File(TMP_FILES_LOCATION);
        if (!folder.exists()) {
            folder.mkdirs();
        }
        return folder;
    }

    public static File createTmpFile(String fileName) {
        File file = new File(getTmpFilesFolder(), fileName);
        file.deleteOnExit();

        TmpFileUtils.addFile(file);

        return file;
    }

    public static void removeTmpFilesRoot() {
        File file = new File(TMP_FILES_LOCATION);
        if (file.exists()) {
            try {
                FileUtils.deleteDirectory(file);
            } catch (IOException e) {
                log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
            }
        }
    }

    public static void removeTmpFile(String fileName) {
        File file = new File(getTmpFilesFolder(), fileName);
        if (file.exists()) {
            removeTmpFile(file);
        }
    }

    public static void removeTmpFile(File file) {
        if (file.exists()) {
            try {
                FileUtils.delete(file);
            } catch (IOException e) {
                log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
            }
        }
    }

}
