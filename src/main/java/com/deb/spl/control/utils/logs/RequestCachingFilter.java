package com.deb.spl.control.utils.logs;

import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.bins.LogRecordDirection;
import com.deb.spl.control.data.bins.RestRequestDAO;
import com.deb.spl.control.repository.bins.NavigationRestRequestRepository;
import com.deb.spl.control.service.NsdService;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Order(value = Ordered.HIGHEST_PRECEDENCE)
@Component
@WebFilter(filterName = "RequestCachingFilter",
        displayName = "rest_log_filter",
        urlPatterns = {"/bins*", "/msu*", "/api*"})
public class RequestCachingFilter extends OncePerRequestFilter {
    private final static Logger log = LoggerFactory.getLogger(RequestCachingFilter.class);
    public static final int PAYLOAD_SIZE_LIMIT = 8096;
    public static final List<AdjacentSystemType> ADJACENT_SYSTEMS_ENDPOINTS = List.of(
            AdjacentSystemType.BINS,
            AdjacentSystemType.MSU,
            AdjacentSystemType.NCOK,
            AdjacentSystemType.BYN,
            AdjacentSystemType.SAE,
            AdjacentSystemType.PPO,
            AdjacentSystemType.SUTO);

    private final NavigationRestRequestRepository restRepository;
    private final NsdService nsdService;
    private final List<String> NSD_URL;

    public RequestCachingFilter(NavigationRestRequestRepository restRepository,
                                NsdService nsdService,
                                @Value("#{'${nsd.url}'.split(',')}") List<String> nsdUrls) {
        this.restRepository = restRepository;
        this.nsdService = nsdService;
        NSD_URL = new ArrayList<>(nsdUrls);
    }

    @Value("#{'${allowed.urls.for.logging}'.split(',')}")
    private List<String> allowedUrls;

    Exception loggedException;

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    HttpServletResponse response,
                                    FilterChain filterChain) throws ServletException, IOException {
        CachedHttpServletRequest cachedHttpServletRequest = new CachedHttpServletRequest(request);
        try {
            if (cachedHttpServletRequest.isConsiderable() &&
                cachedHttpServletRequest.isAllowed(request.getRequestURL().toString(), allowedUrls)) {

                logRequestWithPayload(cachedHttpServletRequest);

                AdjacentSystemType adjacentSystem = AdjacentSystemType.UNDEFINED;
                String requestUrl = cachedHttpServletRequest.getRequestUrl().toLowerCase();

                if (NSD_URL.stream().anyMatch(e -> requestUrl.contains(e.toLowerCase()))) {
                    loadNsdData(cachedHttpServletRequest);
                    adjacentSystem = AdjacentSystemType.NSD;
                } else {
                    for (AdjacentSystemType systemType : ADJACENT_SYSTEMS_ENDPOINTS) {
                        if (requestUrl.contains(systemType.toString().toLowerCase())) {
                            adjacentSystem = systemType;
                            break;
                        }
                    }
                }

                String payload = IOUtils.toString(cachedHttpServletRequest.getInputStream(), StandardCharsets.UTF_8);
                if (payload != null && payload.length() > PAYLOAD_SIZE_LIMIT) {
                    payload = payload.substring(0, PAYLOAD_SIZE_LIMIT - 1);
                }
                RestRequestDAO dao = RestRequestDAO.builder()
                        .url(cachedHttpServletRequest.getRequestUrl())
                        .adjacentSystem(adjacentSystem)
                        .direction(LogRecordDirection.IN)
                        .cachedPayload(payload)
                        .requestQueryString(cachedHttpServletRequest.getRequestQueryString())
                        .build();

                dao = restRepository.save(dao);
                log.info(dao.toString());
            }
        } catch (Exception e) {
            if (loggedException == null) {
                loggedException = e;
                log.error(e.getMessage());
            }
            throw new ServletException(e);
        }

        loggedException = null;
        filterChain.doFilter(cachedHttpServletRequest, response);
    }

    private static void logRequestWithPayload(CachedHttpServletRequest cachedHttpServletRequest) {
        try {
            log.info("REQUEST DATA: request URL {}; \t requestQueryString {}; \t payload {}",
                    cachedHttpServletRequest.getRequestUrl(),
                    cachedHttpServletRequest.getQueryString(),
                    cachedHttpServletRequest.getCachedPayload() == null ? "" : new String(cachedHttpServletRequest.getCachedPayload(), StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    private void loadNsdData(CachedHttpServletRequest cachedHttpServletRequest) {
        try {
            String payload = IOUtils.toString(cachedHttpServletRequest.getInputStream(), StandardCharsets.UTF_8);
            List<String> variables = Arrays.stream(payload.split(" ")).toList();
            if (variables.size() < 3) {
                String errorMsg = "Wrong number of parameters";
                log.error(errorMsg);
                return;
            }
            if (!nsdService.validateToken(variables.get(0))) {
                return;
            }

            nsdService.setVariables(variables);

        } catch (Exception e) {
            log.error(e.getMessage());
        }

    }
}
