package com.deb.spl.control.utils;

import com.deb.spl.control.repository.HasCommandWithCaption;
import com.deb.spl.control.repository.asku.AskuCommandRepository;
import com.deb.spl.control.repository.nppa.NppaCommandRepository;
import com.deb.spl.control.repository.ppo.PpoCommandRepository;
import com.deb.spl.control.repository.sae.SaeCommandsRepository;
import com.deb.spl.control.repository.suto.SutoCommandRepository;
import com.deb.spl.control.data.AdjacentSystemCommandDAO;
import com.deb.spl.control.data.AdjacentSystemType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Component
public class CommandsUtils {

    @Autowired
    NppaCommandRepository nppaCommandRepository;
    @Autowired
    PpoCommandRepository ppoCommandRepository;
    @Autowired
    SutoCommandRepository sutoCommandRepository;
    @Autowired
    SaeCommandsRepository saeCommandsRepository;
    @Autowired
    AskuCommandRepository askuCommandRepository;

    private Map<AdjacentSystemType, HasCommandWithCaption> repositories;


    @PostConstruct
    public void intit() {
        repositories = Map.of(
                AdjacentSystemType.NCOK, nppaCommandRepository,
                AdjacentSystemType.BYN, nppaCommandRepository,
                AdjacentSystemType.PPO, ppoCommandRepository,
                AdjacentSystemType.SUTO, sutoCommandRepository,
                AdjacentSystemType.SAE, saeCommandsRepository,
                AdjacentSystemType.ASKU, askuCommandRepository);
    }

    public Optional<String> getCaptionByAlias(@NotBlank String alias) {
        for (HasCommandWithCaption commandRepo : repositories.values()) {
            AdjacentSystemCommandDAO dao = commandRepo.findByCommandName(alias);
            if (dao != null) {
                return Optional.ofNullable(dao.getCaption());
            }
        }
        return Optional.empty();
    }

    public Optional<String> getCaptionByAliasAndSystem(@NotNull AdjacentSystemType system, @NotBlank String alias) {
        try {
            if (repositories.containsKey(system)) {
                return Optional.ofNullable(repositories.get(system).findByCommandName(alias).getCaption());
            }
            return Optional.empty();
        } catch (Exception e) {
            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
            return Optional.empty();
        }
    }
}
