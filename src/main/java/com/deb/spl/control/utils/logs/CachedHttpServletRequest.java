package com.deb.spl.control.utils.logs;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StreamUtils;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.*;
import java.util.Arrays;
import java.util.List;


@Slf4j
@Getter
public class CachedHttpServletRequest extends HttpServletRequestWrapper {
    private byte[] cachedPayload;
    private String requestUrl;
    private String requestQueryString;

    /**
     * Constructs a request object wrapping the given request.
     *
     * @param request The request to wrap
     * @throws IllegalArgumentException if the request is null
     */
    public CachedHttpServletRequest(HttpServletRequest request) {
        super(request);
        try (InputStream requestInputStream = request.getInputStream()) {
            this.cachedPayload = StreamUtils.copyToByteArray(requestInputStream);
            this.requestUrl = request.getRequestURL().toString();
            this.requestQueryString = request.getQueryString();
        } catch (IOException e) {
            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
        }
    }

    public boolean isConsiderable() {
        return (cachedPayload.length > 0 || (requestQueryString != null && !requestQueryString.isEmpty())
                || requestUrl.matches(".*/api/v\\d+/.+"));
    }


    boolean isAllowed(String url, List<String> allowedUrls) {
        if (allowedUrls == null) {
            return false;
        }
        for (String allowedUrl : allowedUrls) {
            String pattern = ".*" + allowedUrl + ".*";
            if (url.matches(pattern)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        return new CachedServletInputStream(this.cachedPayload);
    }

    @Override
    public BufferedReader getReader() throws IOException {
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(this.cachedPayload);
        return new BufferedReader(new InputStreamReader(byteArrayInputStream));
    }

    @Override
    public String toString() {
        return "CachedHttpServletRequest{" +
                "cachedPayload=" + Arrays.toString(cachedPayload) +
                ", requestUrl='" + requestUrl + '\'' +
                ", requestQueryString='" + requestQueryString + '\'' +
                '}';
    }
}
