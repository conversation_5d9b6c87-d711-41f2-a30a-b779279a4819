package com.deb.spl.control.utils.Pdf;

import com.lowagie.text.DocumentException;
import com.lowagie.text.Font;
import com.lowagie.text.pdf.BaseFont;

import java.awt.*;
import java.io.IOException;

public class FontUtils {

    public static final String ARIAL_TTF = "arial.ttf";
    public static final BaseFont baseFont;
    public static final Font catFont;
    public static final Font redFont;
    public static final Font greenFont;
    public static final Font yellowFont;
    public static final Font subFont16;
    public static final Font subFont14;
    public static final Font regular12;
    public static final Font bold12;
    public static final Font italic12;

    static {

        try {
            baseFont = BaseFont.createFont(ARIAL_TTF, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);

        } catch (DocumentException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        catFont = new Font(baseFont, 18, Font.BOLD);
        redFont = new Font(baseFont, 12,
                Font.NORMAL, Color.RED);
        greenFont = new Font(baseFont, 12,
                Font.NORMAL, Color.GREEN);
        yellowFont = new Font(baseFont, 12,
                Font.NORMAL, Color.YELLOW);

        subFont16 = new Font(baseFont, 16,
                Font.BOLD);
        subFont14 = new Font(baseFont, 14,
                Font.BOLD);
        regular12 = new Font(baseFont, 12,
                Font.NORMAL);
        bold12 = new Font(baseFont, 12,
                Font.BOLD);

        italic12 = new Font(baseFont,12,Font.ITALIC);
    }

    public FontUtils() {

    }

}
