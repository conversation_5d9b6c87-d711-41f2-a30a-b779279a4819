package com.deb.spl.control.utils.logs;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

public class EnumFilterUtils {
    public static List<AdjacentSystemType> getConsiderableSystemsFilter() {
        List<AdjacentSystemType> types = new java.util.ArrayList<>(Arrays.stream(AdjacentSystemType.values())
                .filter(system -> !(system.equals(AdjacentSystemType.MSU) || system.equals(AdjacentSystemType.BINS)
                                    || system.equals(AdjacentSystemType.NPPA) || system.equals(AdjacentSystemType.TPC)
                                    || system.equals(AdjacentSystemType.PLC)
                                    || system.equals(AdjacentSystemType.CCV)
                                    || system.equals(AdjacentSystemType.UNCLASSIFIED)
                                    || system.equals(AdjacentSystemType.UNDEFINED)))
                .sorted((o1, o2) -> o1.getEn().compareTo(o2.getEn()))
                .toList());

        types.add(AdjacentSystemType.UNCLASSIFIED);
        types.add(AdjacentSystemType.UNDEFINED);
        return types;
    }

    public static List<AdjacentSystemStatus> getConsiderableSystemStatusesFilter() {
        List<AdjacentSystemStatus> statuses= new java.util.ArrayList<>(Arrays.stream(AdjacentSystemStatus.values())
                .filter(s -> !(s.equals(AdjacentSystemStatus.ANY)))
                .sorted(Comparator.comparing(AdjacentSystemStatus::getValueEn)).toList());
        statuses.add(AdjacentSystemStatus.ANY);

        return statuses;

    }
}
