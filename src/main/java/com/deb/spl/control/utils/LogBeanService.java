package com.deb.spl.control.utils;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.LogBean;
import com.deb.spl.control.repository.LogRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.deb.spl.control.data.AdjacentSystemType.*;

@Service
public class LogBeanService {
    private final LogRepository logRepository;

    List<LogBean> logBeans = Collections.emptyList();

    public LogBeanService(LogRepository logRepository) {
        this.logRepository = logRepository;
    }


    public ListenableFuture<String> chainedTask(String name) {
        return AsyncResult.forValue("chained" + name);
    }

    public Page<LogBean> selectFiltered(Pageable pageable,
                                        String filterText,
                                        AdjacentSystemType systemFilter,
                                        LocalDateTime startDate,
                                        LocalDateTime endDate) {

        return selectFiltered(pageable, filterText, List.of(systemFilter), Collections.emptyList(), startDate, endDate);
    }

    public Page<LogBean> selectFiltered(Pageable pageable,
                                        String filterText,
                                        List<AdjacentSystemType> systemFilters,
                                        List<AdjacentSystemStatus> systemStatuses,
                                        LocalDateTime startDate,
                                        LocalDateTime endDate) {

        List<LogBean> filtered = logRepository.selectPagedBetweenDatesWitAnyTextSystemStatus(pageable,
                        filterText,
                        getSystemFiltersAsAll(systemFilters),
                        getSystemStatusFilterAsString(systemStatuses),
                        (startDate != null ? startDate : LocalDateTime.of(1917, 1, 1, 0, 0)),
                        (endDate != null ? endDate : LocalDateTime.now()))
                .toList();
        return filtered != null ? new PageImpl<>(filtered) : new PageImpl<>(Collections.emptyList());
    }

    public List<LogBean> selectFiltered(String filterText,
                                        List<AdjacentSystemType> systemFilters,
                                        List<AdjacentSystemStatus> systemStatuses,
                                        LocalDateTime startDate,
                                        LocalDateTime endDate) {

        return logRepository.selectPagedBetweenDatesWitAnyTextSystemStatus(filterText,
                getSystemFiltersAsAll(systemFilters),
                getSystemStatusFilterAsString(systemStatuses),
                (startDate != null ? startDate : LocalDateTime.of(1917, 1, 1, 0, 0)),
                (endDate != null ? endDate : LocalDateTime.now()));
    }

    public List<LogBean> selectCommandLogBetweenDates(AdjacentSystemType systemFilter,
                                                      LocalDateTime startDate,
                                                      LocalDateTime endDate) {

        List<LogBean> logs = logRepository.selectCommandsLogBetweenDates(systemFilter,
                (startDate != null ? startDate : LocalDateTime.of(1917, 1, 1, 0, 0)),
                (endDate != null ? endDate : LocalDateTime.now()));
        return logs;
    }

    @Async
    public ListenableFuture<List<LogBean>> selectCommandLogBetweenDatesAsync(AdjacentSystemType systemFilter,
                                                                             LocalDateTime startDate,
                                                                             LocalDateTime endDate) {

        List<LogBean> logs = logRepository.selectCommandsLogBetweenDates(systemFilter,
                (startDate != null ? startDate : LocalDateTime.of(1917, 1, 1, 0, 0)),
                (endDate != null ? endDate : LocalDateTime.now()));
        return AsyncResult.forValue(logs);
    }

    public List<LogBean> selectHttpLogs(LocalDateTime startDate,
                                        LocalDateTime endDate) {

        return logRepository.selectHttpLogs((startDate != null ? startDate : LocalDateTime.of(1917, 1, 1, 0, 0)),
                (endDate != null ? endDate : LocalDateTime.now()));
    }

    private List<String> getSystemFiltersAsAll(List<AdjacentSystemType> systemFilters) {
        if (systemFilters.size() == 0) {
            return List.of("");
        }

        if (systemFilters.contains(UNCLASSIFIED)) {
            return (Arrays.stream(AdjacentSystemType.values())
                    .filter(system -> !(system.equals(MSU) || system.equals(BINS) || system.equals(UNCLASSIFIED)))
                    .map(item -> item.equals(UNDEFINED) ? "" : item.getEn())
                    .toList());
        }

        return systemFilters.stream()
                .map(item -> item.equals(UNDEFINED) ? "" : item.getEn()).toList();
    }

    public List<AdjacentSystemType> getSystemFiltersAsSystemType(List<AdjacentSystemType> systemFilters) {
        if (systemFilters.size() == 0) {
            return new ArrayList<>();
        }

        if (systemFilters.contains(UNCLASSIFIED)) {
            return (Arrays.stream(AdjacentSystemType.values())
                    .filter(system -> !(system.equals(MSU) || system.equals(BINS) || system.equals(NPPA)
                                        || system.equals(UNCLASSIFIED) || system.equals(UNCLASSIFIED)))
                    .toList());
        }

        return systemFilters.stream().filter(system -> !(system.equals(UNDEFINED))).toList();
    }

    private List<String> getSystemStatusFilterAsString(List<AdjacentSystemStatus> statusFilter) {
        if (statusFilter.size() == 0) {
            return List.of("");
        }
        if (statusFilter.contains(AdjacentSystemStatus.ANY)) {
            return Arrays.stream(AdjacentSystemStatus.values())
                    .map(AdjacentSystemStatus::getValueEn)
                    .toList();
        }
        return statusFilter.stream().map(AdjacentSystemStatus::getValueEn).toList();
    }

    boolean isOutdated(LocalDateTime newDate, LocalDateTime startDate) {

        if (newDate == null && startDate == null) {
            return false;
        }
        if (newDate == null && startDate != null) {
            return true;
        }

        if (newDate != null && startDate == null) {
            return false;
        }

        return newDate.isBefore(startDate);
    }

    private boolean filterSystem(AdjacentSystemType systemFilter, @NotNull LogBean logBean) {
        if (systemFilter == null ||
            systemFilter.equals(UNCLASSIFIED)) {
            return true;
        }
        if (systemFilter.equals(AdjacentSystemType.UNDEFINED)) {
            return (isSystemUndefined(logBean.getSystem()));
        }
        return systemFilter.equals(logBean.getSystem());
    }

    public boolean isSystemUndefined(AdjacentSystemType source) {
        if (source.equals(AdjacentSystemType.UNDEFINED)) {
            return true;
        }
        return Arrays.stream(AdjacentSystemType.values()).filter(type -> type.equals(source))
                .findFirst().isEmpty();
    }

    private boolean filterText(String filterText, @NotNull LogBean logBean) {
        if (filterText.isBlank()) {
            return true;
        }

        if (logBean.getInfo().isBlank() && logBean.getStatus() == null) {
            return false;
        }

        if (logBean.getStatus().getValueUa().contains(filterText)) {
            return true;
        }

        return StringUtils.containsIgnoreCase(logBean.getInfo(), filterText);
    }

    public int count() {
        return logBeans.size();
    }
}
