package com.deb.spl.control.utils;

import com.deb.spl.control.data.msu.MeteoStationUnit;
import net.sf.marineapi.nmea.util.MSU.*;

public class TestHelper {
    public static MeteoStationUnit getMSU(){
        MeteoStationUnit msu = new MeteoStationUnit();
        msu.setWindDirection(166);
        msu.setWindSpeed(16.2);
        msu.setAirTemperature(18.9);
        msu.setVoltage(24);
        msu.setMsuTemperature(24.2);
        msu.setPosture(MsuPosture.OPERATING);
        msu.setOperatingMode(MsuOperatingMode.READY_FOR_OPERATION);
        msu.setHeatingMode(MsuHeatingMode.OFF);
        msu.setHeatingModePermission(MsuHeatingModePermission.DISABLED);
        msu.setTemperatureSensorBlowing(MsuTemperatureSensorBlowing.OFF);

        return msu;
    }

    public static MeteoStationUnit getMSUPostureError(){
        MeteoStationUnit msu = new MeteoStationUnit();
        msu.setWindDirection(166);
        msu.setWindSpeed(16.2);
        msu.setAirTemperature(18.9);
        msu.setVoltage(24);
        msu.setMsuTemperature(24.2);
        msu.setPosture(MsuPosture.OPERATING);
        msu.setOperatingMode(MsuOperatingMode.READY_FOR_OPERATION);
        msu.setHeatingMode(MsuHeatingMode.OFF);
        msu.setHeatingModePermission(MsuHeatingModePermission.DISABLED);
        msu.setTemperatureSensorBlowing(MsuTemperatureSensorBlowing.OFF);
        msu.setPostureSwitchingMalfunction(MsuPostureSwitchMalfunction.OPERATING_MODE_SWITCH_MALFUNCTION);

        return msu;
    }
}
