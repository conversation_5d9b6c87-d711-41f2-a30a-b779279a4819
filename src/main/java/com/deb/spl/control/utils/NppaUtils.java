package com.deb.spl.control.utils;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.nppa.*;

public class NppaUtils {
    public static Nppa getDafultNppa() {
        return Nppa.builder()
                .ncok(getDefaultNcok())
                .byn(getDefaultByn())
                .build();
    }

    private static Ncok getDefaultNcok() {
        return Ncok.builder()
                .operatingMode(NppaOperatingMode.NOT_SELECTED)
                .systemStatus(AdjacentSystemStatus.UNDEFINED)
                .tvNcok(BaseProperty.UNDEFINED)
                .isNcokConnected(false)
                .isSutoConnected(false)
                .nppaTestResult(BaseProperty.UNDEFINED)
                .appPresence(false)
                .otr1AppPresence(false)
                .otr2AppPresence(false)
                .otr1TestResult(BaseProperty.UNDEFINED)
                .otr2TestResult(BaseProperty.UNDEFINED)
                .isOtr1Lunched(BaseProperty.UNDEFINED)
                .isOtr2Lunched(BaseProperty.UNDEFINED)
                .build();
    }

    private static Byn getDefaultByn() {
        return Byn.builder()
                .systemStatus(AdjacentSystemStatus.UNDEFINED)
                .operatingMode(NppaOperatingMode.NOT_SELECTED)
                .tvByn(BaseProperty.UNDEFINED)
                .isConnected(false)
                .isNcok(false)
                .isRgOutNcok(false)
                .isBuveF2(false)
                .isBuveF4(false)
                .isBasuOtr1F3(false)
                .isBasuOtr2F3(false)
                .isF1(false)
                .isF2(false)
                .isF3(false)
                .isF4(false)
                .isF5(false)
                .isNppaConnected(false)
                .isBasuOtr1Connected(false)
                .isBasuOtr2Connected(false)
                .build();
    }


}
