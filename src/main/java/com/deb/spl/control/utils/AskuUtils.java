package com.deb.spl.control.utils;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.asku.*;
import com.deb.spl.control.data.bins.Position;
import com.deb.spl.control.service.asku.AskuService;
import org.apache.commons.codec.binary.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.UUID;

import static org.apache.commons.codec.CharEncoding.UTF_16;
import static org.apache.commons.codec.CharEncoding.UTF_8;

@Component
public class AskuUtils {

    @Autowired
    @PersistenceContext
    EntityManager entityManager;

    public static Asku getDefaultAsku(UUID splId, String plateNumber, Position position) {
        return Asku.builder()
                .splId(splId)
                .plateNumber(plateNumber)
                .unitTitle("невідомий підрозділ невідома СПУ")
                .splReadiness(Readiness.UNDEFINED)
                .tpcLeft(getDefaultTpc())
                .tpcRight(getDefaultTpc())
                .leftRocket(getDefaultRocket())//todo replace with default
                .rightRocket(getDefaultRocket())
                .status(AdjacentSystemStatus.UNDEFINED)
                .position(position)
                .plc(getDefaultPlc())
                .build();
    }

    private static Plc getDefaultPlc() {
        Plc plc = new Plc();
        plc.setStatus(AdjacentSystemStatus.UNDEFINED);
        return plc;
    }

    public static Rocket getDefaultRocket() {
        return Rocket.builder()
                .technicalCondition(false)
                .build();
    }

    public static Rocket getEmptyRocket() {
        return Rocket.builder()
                .formData(getDefaultFormData())
                .initialData(getDefaultInitialData())
                .initialDataSource(LaunchInitialDataSource.UNKNOWN)
                .initialDataSourceDescription("")
                .sensorTemperature(-999.9)
                .build();
    }

    private static Rocket getLeftRocket1L() {
        return Rocket.builder()
                .id(1L)
                .technicalCondition(true)
                .dateUseM(null)
                .formData(getTestFormData1L())
                .build();
    }

    public static RocketFormData getDefaultFormData() {
        return RocketFormData.builder()
                .plantMissile("")
                .warhead(WarheadType.UNDEFINED)
                .gsnType(GsnType.UNDEFINED)
                .alpType(AlpType.UNDEFINED)
                .telemetryIntegrated(false)
                .purposeType(OtrPurposeType.UNDEFINED)
                .isArchived(false)
                .build();
    }


    private static RocketFormData getTestFormData1L() {
        return RocketFormData.builder()
                .id(146L)
                .createdAt(LocalDateTime.now())
                .plantMissile("1Л")
                .warhead(WarheadType.MFBCH)
                .gsnType(GsnType.OE_GSN)
                .alpType(AlpType.FOUR_ALP)
                .telemetryIntegrated(true)
                .purposeType(OtrPurposeType.COMBAT)
                .isArchived(false)
                .build();
    }

    public static Tpc getDefaultTpc() {
        return Tpc.builder()
                .tpcLoadState(TpcState.UNKNOWN)
                .build();
    }

    public static LaunchInitialData getDefaultInitialData() {
        return LaunchInitialData
                .builder()
                .createdAt(LocalDateTime.now())
                .build();
    }

    public static LaunchInitialData getEmptyInitialData() {
        return LaunchInitialData.builder()
                .trajectory(TrajectoryType.UNKNOWN)
                .readiness(Readiness.UNDEFINED)
                .missileOperatingMode(MissileOperatingMode.UNKNOWN)
                .build();
    }

    public static RocketDto convertPlantMissileEncodingFromUtf16ToUtf8(@NotNull RocketDto rocketDto,
                                                                       @NotNull AskuService askuService,
                                                                       @NotNull RocketMapper rocketMapper) {
        return convertPlantMissileEncodingFromEncoding(rocketDto, askuService, rocketMapper, UTF_16);
    }

    public static RocketDto convertPlantMissileEncodingFromUtf8ToUtf16(@NotNull RocketDto rocketDto,
                                                                       @NotNull AskuService askuService,
                                                                       @NotNull RocketMapper rocketMapper) {
        return convertPlantMissileEncodingFromEncoding(rocketDto, askuService, rocketMapper, UTF_8);
    }

    public static RocketDto convertPlantMissileEncodingFromEncoding(@NotNull RocketDto rocketDto,
                                                                    @NotNull AskuService askuService,
                                                                    @NotNull RocketMapper rocketMapper,
                                                                    @NotBlank String encoding) {

        Rocket rocket = rocketMapper.map(rocketDto, askuService);
        String plantMissile = "";

        if (rocket.getPlantMissile().isEmpty() || rocket.getPlantMissile().get().isBlank()) {
            return rocketDto;
        }

        switch (encoding) {
            case UTF_8 -> {
                byte[] bytes = StringUtils.getBytesUtf8(rocket.getPlantMissile().get());
                plantMissile = StringUtils.newStringUtf8(bytes);
            }
            case UTF_16 -> {
                byte[] bytes = StringUtils.getBytesUtf16(rocket.getPlantMissile().get());
                plantMissile = StringUtils.newStringUtf16(bytes);
            }
            default -> plantMissile = "";
        }

        rocket.getFormData().setPlantMissile(plantMissile);


        return plantMissile.isBlank() ? rocketDto :
                rocketMapper.toDto(rocket);
    }

    public static String getSplReadinessPayloadPartByReadiness(Readiness readiness) {
        switch (readiness) {
            case AUTO_TEST_IN_PROGRESS,
                    BG_4_TO_BG_3,
                    BG_3_TO_BG_4,
                    BG_3_TO_BG_2A,
                    BG_3_TO_BG_2B,
                    BG_3_TO_BG_1,
                    BG_2A_TO_BG_1,
                    BG_2A_TO_BG_3,
                    BG_2A_TO_BG_4,
                    BG_2B_TO_BG_4,
                    BG_2B_TO_BG_3,
                    BG_1_TO_BG_3,
                    BG_1_TO_BG_4,
                    BG_1_TO_BG_2A -> {
                return "Виконується ";
            }
            case BG_1,
                    BG_2A,
                    BG_3,
                    BG_4 -> {
                return "СПУ знаходиться в готовності ";
            }
            default -> {
                return "";
            }
        }
    }
}
