package com.deb.spl.control.utils;

import com.deb.spl.control.data.Bins;
import com.deb.spl.control.data.BinsDto;
import com.deb.spl.control.data.UtcDateTime;
import lombok.extern.slf4j.Slf4j;
import net.sf.marineapi.nmea.parser.DataNotAvailableException;
import net.sf.marineapi.nmea.parser.SentenceFactory;
import net.sf.marineapi.nmea.sentence.GGASentence;
import net.sf.marineapi.nmea.sentence.PMPSentence;
import net.sf.marineapi.nmea.sentence.POHPRSentence;
import net.sf.marineapi.nmea.sentence.RMCSentence;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Optional;

@Slf4j
public class BinsToDtoConverter {

    public static final int DATATE_FILED_NUMBER = 9;
    public static final int DATE_STRING_LEN = 6;

    public static Bins fromDto(@NotNull BinsDto dto) {
        SentenceFactory factory = SentenceFactory.getInstance();
        Bins bins = new Bins();

        if (dto.getGga() != null || dto.getRmc() != null) {
            try {
                Optional<UtcDateTime> utcDateTime = toUtcDateTime((GGASentence) factory.createParser(dto.getGga()),
                        (RMCSentence) factory.createParser(dto.getRmc()));

                if (utcDateTime.isPresent()) {
                    bins.setGpsDate(utcDateTime.get().getDate());
                    bins.setGpsTime(utcDateTime.get().getTime());
                }
            } catch (Exception ignoredException) {
                log.debug(ignoredException.getMessage());
            }
        }

        if (dto.getGga() != null) {
            try {
                GGASentence ggaSentence = (GGASentence) factory.createParser(dto.getGga());
                bins.setPosition(ggaSentence.getPosition());
            } catch (Exception ggaException) {
                log.debug(ggaException.getMessage());
            }
        }
        if (dto.getRmc() != null) {
            try {
                RMCSentence rmcSentence = (RMCSentence) factory.createParser(dto.getRmc());
                if (bins.getPosition() == null) {
                    bins.setPosition(rmcSentence.getPosition());
                }
                bins.setCourse(rmcSentence.getCourse());
                bins.setSpeed(rmcSentence.getSpeed());
                bins.setCorrectedCourse(rmcSentence.getCorrectedCourse());

            } catch (Exception e) {
                log.debug(e.getMessage());
            }
        }

        if (dto.getPmp() != null) {
            try {
                PMPSentence pmpSentence = (PMPSentence) factory.createParser(dto.getPmp());
                bins.setLatitudePrecision(pmpSentence.getLatitudePrecision());
                bins.setLongitudePrecision(pmpSentence.getLongitudePrecision());
                bins.setAltitudePrecision(pmpSentence.getAltitudePrecision());
                bins.setNavigationTaskStatus(pmpSentence.getNavigationTaskStatus());
                bins.setBinsGpsStatus(pmpSentence.getBinsGPSStatus());
            } catch (Exception e) {
                log.debug(e.getMessage());
            }
        }

        if (dto.getHpr() != null) {
            try {
                POHPRSentence hprSentence = (POHPRSentence) factory.createParser(dto.getHpr());
                bins.setHeadingAzimuth(hprSentence.getHeadingAzimuth());
                bins.setTiltToStarboard(hprSentence.getTiltToStarboard());
                bins.setTrimHeadIsBottom(hprSentence.getTrimHeadIsBottom());
                bins.setBinsCalibrationStatus(hprSentence.getResolveStatus());
            } catch (Exception e) {
                log.debug(e.getMessage());
            }
        }

        return bins;
    }


    private static Optional<UtcDateTime> toUtcDateTime(GGASentence ggaSentence, RMCSentence rmcSentence) {
        if (ggaSentence == null && rmcSentence == null) {
            return Optional.empty();
        }

        UtcDateTime utcDateTime = new UtcDateTime();
        try {
            if (rmcSentence != null) {
                utcDateTime.setTime(LocalTime.parse(rmcSentence.getTime().toString(),
                        DateTimeFormatter.ofPattern("HHmmss.SSS")));

                LocalDate date = LocalDate.MIN;
                if (rmcSentence.toSentence().split(",", 9).length >= DATATE_FILED_NUMBER &&
                    rmcSentence.toSentence().split(".", 9).length >= DATE_STRING_LEN
                ) {
                    date = LocalDate.parse(rmcSentence.getDate().toISO8601(),
                            DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                }
                utcDateTime.setDate(date);
            }
        } catch (DataNotAvailableException ignoredException) {
            log.warn(Arrays.toString(ignoredException.getStackTrace()));
        }

        if (utcDateTime.getTime() == null) {
            try {
                if (ggaSentence != null) {
                    utcDateTime.setTime(LocalTime.parse(ggaSentence.getTime().toString(),
                            DateTimeFormatter.ofPattern("HHmmss.SSS")));
                }
            } catch (DataNotAvailableException ignoredException) {
                log.warn(Arrays.toString(ignoredException.getStackTrace()));
            }
        }
        if (utcDateTime.getTime() == null) {
            return Optional.empty();
        }

        return Optional.of(utcDateTime);
    }
}
