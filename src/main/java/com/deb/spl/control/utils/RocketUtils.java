package com.deb.spl.control.utils;

import com.deb.spl.control.data.asku.*;
import com.deb.spl.control.service.asku.AskuService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import javax.validation.constraints.NotBlank;
import java.util.Arrays;

@Slf4j
public class RocketUtils {
    public static boolean compareToDto(Rocket rocket, RocketDto dto, RocketMapper rocketMapper, AskuService askuService) {
        Rocket fromDto = rocketMapper.map(dto, askuService);

        if (rocket.getSensorTemperature() != fromDto.getSensorTemperature()) return false;
        if (rocket.isTechnicalCondition() != fromDto.isTechnicalCondition()) return false;
        if (rocket.getDateUseM() != null ? !rocket.getDateUseM().equals(fromDto.getDateUseM()) : fromDto.getDateUseM() != null)
            return false;
        if (rocket.getFormData() != null ? !rocket.getFormData().equals(fromDto.getFormData()) : fromDto.getFormData() != null)
            return false;

        if (rocket.getInitialData() == null && fromDto.getInitialData() != null) {
            return false;
        }
        if (fromDto.getInitialData() == null && rocket.getInitialData() != null) {
            return false;
        }
        if (rocket.getInitialData() != null && !compareInitialDataToDto(rocket.getInitialData(), fromDto.getInitialData()))
            return false;

        if (rocket.getStoredTlKeys() != null && fromDto.getStoredTlKeys() != null) {
            for (String storedKay : rocket.getStoredTlKeys()) {
                if (!fromDto.getStoredTlKeys().contains(storedKay)) return false;
            }
        }
        return (rocket.getStoredTlKeys() != null || fromDto.getStoredTlKeys() == null) && (rocket.getStoredTlKeys() == null || fromDto.getStoredTlKeys() != null);
    }

    public static boolean compareInitialDataToDto(@NotNull LaunchInitialData fromRocket, @NotNull LaunchInitialData fromDto) {
        if (!fromDto.getLatitudeRad().equals(fromRocket.getLatitudeRad())) return false;
        if (!fromDto.getLongitudeRad().equals(fromRocket.getLongitudeRad())) return false;
        if (Double.compare(fromDto.getAltitude(), fromRocket.getAltitude()) != 0) return false;
        if (Double.compare(fromDto.getInclinationAngle(), fromRocket.getInclinationAngle()) != 0) return false;
        if (fromRocket.isProDetected() != fromDto.isProDetected()) return false;
        if (fromRocket.getTrajectory() != fromDto.getTrajectory()) return false;
        if (fromRocket.getReadiness() != fromDto.getReadiness()) return false;
        if (fromRocket.getMissileOperatingMode() != fromDto.getMissileOperatingMode()) return false;
        return fromRocket.getTlCode() != null ? fromRocket.getTlCode().equals(fromDto.getTlCode()) : fromDto.getTlCode() == null;
    }

    public static boolean isValidAngle(@NotNull WarheadType warhead, @NotBlank String angle) {
        try {
            double val = Double.parseDouble(angle);
            switch (warhead) {
                case CBCH -> {
                    return (val >= -92.0 && val <= -88.0);
                }
                case MFBCH -> {
                    return (val >= -90.0 && val <= -68.0);
                }
                case UNDEFINED -> {
                    return (val >= -92.0 && val <= -68.0);
                }
                default -> {
                    return false;
                }
            }
        } catch (NumberFormatException e) {
            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
            return false;
        }
    }
}
