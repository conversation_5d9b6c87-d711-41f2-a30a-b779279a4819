package com.deb.spl.control.utils.Pdf;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.LogBean;
import com.deb.spl.control.repository.LogRepository;
import com.deb.spl.control.utils.LogBeanService;
import com.deb.spl.control.utils.ReportGenerator;
import com.deb.spl.control.utils.ReportSettings;
import com.lowagie.text.*;
import com.lowagie.text.pdf.*;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.SettableListenableFuture;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.DateTimeException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.CompletableFuture;

import static com.deb.spl.control.utils.Pdf.FontUtils.*;

@Service("OpenPdfAsyncReportGenerator")
@Slf4j
public class OpenPdfAsyncReportGenerator extends PdfPageEventHelper implements ReportGenerator {
    //look for samples at https://www.vogella.com/tutorials/JavaPDF/article.html#createpdf
    //https://www.tutorialspoint.com/itext/itext_adding_table.htm
    //https://kb.itextpdf.com/home/<USER>/examples/itext-in-action-chapter-2-using-itext-s-basic-building-blocks
    //watermarks
    //https://github.com/LibrePDF/OpenPDF/blob/master/pdf-toolbox/src/test/java/com/lowagie/examples/directcontent/pageevents/PageNumbersWatermark.java#L56
    private final LogRepository logRepository;
    private final LogBeanService logService;
    @Getter
    private SettableListenableFuture<String> exportResultFuture;
    private CompletableFuture<String> completableFuture;
    private final DateTimeFormatter ukrainianFormatterWithMillis = DateTimeFormatter
            .ofPattern("dd/MM/yyyy\t E  HH:mm:ss.SSS", new Locale("uk"));
    private final DateTimeFormatter ukrainianFormatterWithSec = DateTimeFormatter
            .ofPattern("dd/MM/yyyy E \nHH:mm:ss", new Locale("uk"));
    private final DateTimeFormatter ukrainianFormatterWithMin = DateTimeFormatter
            .ofPattern("dd/MM/yyyy E \nHH:mm", new Locale("uk"));


    private final DateTimeFormatter fileNameTsFormatter = DateTimeFormatter.ofPattern("dd-MM-yy",
            new Locale("uk"));

    private String pageNumberPrefix;
    private float xMatrix;
    private float yMatrix;
    private boolean enablePageNumberPrinting;
    @Getter
    private int totalRowsProcessed;
    private final ZoneId zoneId;

    @Getter
    private int numberOfReportsToProcess;

    @Override
    public void setNumberOfReportsToProcess(@PositiveOrZero int numberOfReportsToProcess) {
        this.numberOfReportsToProcess = numberOfReportsToProcess;
    }

    @Override
    public DateTimeFormatter getFileNameTsFormatter() {
        return fileNameTsFormatter;
    }

    public OpenPdfAsyncReportGenerator(LogRepository logRepository,
                                       LogBeanService logService,
                                       @Value("${report.filename.prefix}") String reportFileNamePrefix,
                                       @Value("${report.filename.type.http}") String reportTypeHttp,
                                       @Value("${report.filename.type.commands}") String reportTypeCommands,
                                       @Value("${report.filename.type.system_states}") String reportTypeSystemsStates,
                                       @Value("${report.filename.timestamp}") boolean reportFileNameTSEnabled,
                                       @Value("${report.filename.delimeter}") String reportFileNameDelimiter,
                                       @Value("${report.header_footer.enabled}") boolean enablePageNumberPrinting,
                                       @Value("${report.pageNumber.prefix}") String pageNumberPrefix,
                                       @Value("${report.pageNumber.x}") float xMatrix,
                                       @Value("${report.pageNumber.y}") float yMatrix,
                                       @Value("${report.title-page.show}") boolean showTitlePage,
                                       @Value("${report.title-page.settings.show}") boolean showSettings,
                                       @Value("${report.title-page.documentDescription}") String documentDescription,
                                       @Value("${time.utc-zone-id}") String zoneIdSettings) {
        this.logRepository = logRepository;
        this.logService = logService;
        this.reportFileNamePrefix = reportFileNamePrefix;
        this.reportFileNameTSEnabled = reportFileNameTSEnabled;
        this.reportFileNameDelimiter = reportFileNameDelimiter;

        try {
            this.pageNumberPrefix = new String(pageNumberPrefix.getBytes("ISO-8859-1"), StandardCharsets.UTF_8);
        } catch (UnsupportedEncodingException e) {
            this.pageNumberPrefix = "Сторінка";
        }

        this.xMatrix = xMatrix;
        this.yMatrix = yMatrix;
        this.enablePageNumberPrinting = enablePageNumberPrinting;

        this.documentDescription = documentDescription;
        this.showTitlePage = showTitlePage;
        this.showSettings = showSettings;

        this.reportTypeHttp = reportTypeHttp;
        this.reportTypeCommands = reportTypeCommands;
        this.reportTypeSystemsStates = reportTypeSystemsStates;

        ZoneId tmpZoneId;
        try {
            tmpZoneId = ZoneId.of(zoneIdSettings);
        } catch (DateTimeException e) {
            tmpZoneId = ZoneId.systemDefault();
            log.error(Arrays.toString(e.getStackTrace()));
        }
        this.zoneId = tmpZoneId;
    }

    @Override
    public int countReportsToExport(boolean exportSystemStatesRequestsOption,
                                    boolean exportCommandsOption,
                                    boolean exportNavigationServiceNmea,
                                    boolean exportNavigationServiceRequestsOption,
                                    List<AdjacentSystemType> selectedSystems) {
        int count = 0;
        final int maxSystems = 7;
        int selectedSystemsCount = selectedSystems.size();

        if (exportCommandsOption) count += selectedSystemsCount;
        if (exportSystemStatesRequestsOption) count += selectedSystemsCount;
        if (exportNavigationServiceNmea) count += 2;
        if (exportNavigationServiceRequestsOption) count += 1;
        return count;
    }

    private final String reportFileNamePrefix;
    private final String reportTypeHttp;
    private final String reportTypeCommands;
    private final String reportTypeSystemsStates;
    private final boolean reportFileNameTSEnabled;
    private final String reportFileNameDelimiter;
    private final String documentDescription;
    private final boolean showTitlePage;
    private final boolean showSettings;

    @Override
    public String getReportFileName(DateTimeFormatter dateTimeFormatter, ExportDataType exportDataType) {
        String reportType = exportDataType.toString().toLowerCase();
        return getReportFileName(dateTimeFormatter, "", exportDataType);
    }

    @Override
    public String getReportFileName(DateTimeFormatter dateTimeFormatter,
                                    String adjacentSystemName,
                                    ExportDataType exportDataType) {
        String reportType = exportDataType.toString().toLowerCase();
        String delimiter = reportFileNameDelimiter != null ? reportFileNameDelimiter : "";
        return (reportFileNamePrefix != null ? reportFileNamePrefix : "") +
               delimiter + reportType + delimiter +
               adjacentSystemName.toLowerCase() + delimiter +
               (reportFileNameTSEnabled ? LocalDateTime.now().format(dateTimeFormatter) : "") +
               ".pdf";
    }


    private List<String> logBeanToList(LogBean bean) {
        if (bean == null) {
            return new ArrayList<>();
        }
        return List.of(
                bean.getId() != null ? bean.getId().toString() : " ",
                bean.getSystem() != null ? bean.getSystem().getUa() : AdjacentSystemType.UNDEFINED.getUa(),
                bean.getUpdatedAt() != null ? bean.getUpdatedAt().format(ukrainianFormatterWithSec) : "",
                bean.getStatus() != null ? bean.getStatus().getValueUa() : AdjacentSystemStatus.UNDEFINED.getValueUa(),
                bean.getInfo() != null ? bean.getInfo() : "");
    }

    @Override
    public ByteArrayOutputStream createReportWithStringData(boolean showTitlePage,
                                                            boolean showSettings,
                                                            ReportSettings reportSettings,
                                                            String documentDescription,
                                                            List<List<String>> data,
                                                            List<String> tableHeaders,
                                                            List<Float> rowWiths) {
        ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();

        Document document = new Document();

        try {
            PdfWriter pdfWriter = PdfWriter.getInstance(document, arrayOutputStream);
            if (enablePageNumberPrinting) {
                pdfWriter.setPageEvent(new PageEventNumberInsertEvent(pageNumberPrefix, xMatrix, yMatrix, reportFileNameTSEnabled));
            }

            document.open();
            addContent(document, data, tableHeaders, rowWiths);
            document.close();

            return arrayOutputStream;
        } catch (DocumentException e) {
            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
            throw e;
        } catch (InterruptedException e) {
            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
            return null;
        }
    }

    @Async
    @Override
    public ListenableFuture<String> exportToPdf(String fileName,
                                                List<LogBean> logBeansData,
                                                List<String> tableHeaders,
                                                List<Float> rowWiths) {
        Document document = new Document();
        File file = TmpFileUtils.createTmpFile(fileName);

        if (Thread.currentThread().isInterrupted()) {
            setNumberOfReportsToProcess(numberOfReportsToProcess--);
            return AsyncResult.forExecutionException(new InterruptedException());
        }

        List<List<String>> data = logBeansData.stream().map(this::logBeanToList).toList();


        try (FileOutputStream fileOutputStream = new FileOutputStream(TmpFileUtils.createTmpFile(fileName))) {
            PdfWriter pdfWriter = PdfWriter.getInstance(document, fileOutputStream);
            if (enablePageNumberPrinting) {
                pdfWriter.setPageEvent(new PageEventNumberInsertEvent(pageNumberPrefix, xMatrix, yMatrix, reportFileNameTSEnabled));
            }
            document.open();

            totalRowsProcessed = addContent(document, data, tableHeaders, rowWiths);
            boolean interrupted = totalRowsProcessed < data.size();


            document.close();
            fileOutputStream.flush();
            if (interrupted) {
                log.info("Eкспорт перервано користувачем");
                if (file.exists()) {
                    file.delete();
                }
                setNumberOfReportsToProcess(numberOfReportsToProcess--);
                return AsyncResult.forExecutionException(new InterruptedException("Перевано користувачем"));
            }

            float fileSize = -1.00F;
            try {
                if (file.exists() && file.isFile()) {
                    fileSize = (Files.size(file.toPath())) / 1024.00F / 1024.00F;
                }
            } catch (Exception e) {
                log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
            }

            log.info("pdf exported to file " + file +
                     " at " + TmpFileUtils.getCreationTs(file.toPath(), zoneId));
            numberOfReportsToProcess--;
            return AsyncResult.forValue(file.getName());
        } catch (DocumentException | IOException e) {
            setNumberOfReportsToProcess(numberOfReportsToProcess--);
            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
            return AsyncResult.forExecutionException(e);
        } catch (InterruptedException e) {
            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
            setNumberOfReportsToProcess(numberOfReportsToProcess--);
            return AsyncResult.forExecutionException(e);
        }

    }

    private int addContent(Document document,
                           List<List<String>> data,
                           List<String> tableHeaders,
                           List<Float> rowWiths) throws DocumentException, InterruptedException {
        final boolean interrupted = false;
        //add title, agenda etc...
        //split to parts to interrupt in case of too long processing
        int rowsPerIteration = 50;
        int numberOfIteration = data.size() / rowsPerIteration + (data.size() % rowsPerIteration > 0 ? 1 : 0);
        int totalRowsProcessed = 0;
        PdfPTable table;
        for (int i = 0; i < numberOfIteration; i++) {
            if (Thread.currentThread().isInterrupted()) {
                return totalRowsProcessed;
            }
            int rowsNumberToProcess = Math.min(rowsPerIteration, data.size() - totalRowsProcessed);
            table = createTable(data.subList(totalRowsProcessed, totalRowsProcessed + rowsNumberToProcess),
                    tableHeaders, rowWiths);

            document.add(table);
            totalRowsProcessed += rowsNumberToProcess;
            log.info("added new row" + totalRowsProcessed + "\t" + Arrays.toString(data.get(i).toArray()));
        }


        return totalRowsProcessed;
    }

    private void addEmptyLine(Paragraph paragraph, int number) {
        for (int i = 0; i < number; i++) {
            paragraph.add(new Paragraph(" "));
        }
    }

    private PdfPTable createTable(List<List<String>> data,
                                  @NotNull List<String> headers,
                                  @NotNull List<Float> rowWiths) throws BadElementException, InterruptedException {

        if (headers.size() != rowWiths.size()) {
            String errorMsg = String.format("headers size(%s) must to be equal to rowWith size (%s):",
                    headers.size(), rowWiths.size());
            log.error(errorMsg);
            throw new IllegalArgumentException(errorMsg);
        }

        Float[] columnWidthsTmp = rowWiths.toArray(new Float[0]);
        float[] columnWidths = new float[columnWidthsTmp.length];
        for (int i = 0; i < columnWidthsTmp.length; i++) {
            columnWidths[i] = columnWidthsTmp[i];
        }

        PdfPTable table = new PdfPTable(columnWidths);


        for (String headerCaption : headers) {
            PdfPCell cell = new PdfPCell(new Phrase(headerCaption, subFont14));
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table.addCell(cell);
        }
        table.setHeaderRows(1);

        table.setWidthPercentage(100);

        for (List<String> row : data) {
            if (Thread.currentThread().isInterrupted()) {
                throw new InterruptedException();
            }
            for (int i = 0; i < headers.size(); i++) {
                table.addCell(new Phrase(row.get(i), regular12));
            }
        }

        return table;
    }

    class PageEventNumberInsertEvent extends PdfPageEventHelper {
        private String pageNumberPrefix = "Сторінка";
        private float xMatrix = 500; //0-600
        private float yMatrix = 10; //0-830
        private boolean enabled = true;

        private PdfGState gstate;
        private Image sdoLogo;

        /**
         * @param pageNumberPrefix introductory text preceding the page number
         * @param xMatrix          x coordinate of text start in range from 0 (left side) to 600 (right side).
         * @param yMatrix          y coordinate of text start in range from 0 (bottom) to 600 (top).
         * @param enabled          true to enable printing of page number
         */
        public PageEventNumberInsertEvent(String pageNumberPrefix, float xMatrix, float yMatrix, boolean enabled) {
            this.pageNumberPrefix = pageNumberPrefix;
            if (xMatrix >= 0 && xMatrix <= 600) {
                if (xMatrix - pageNumberPrefix.length() > 0) {
                    xMatrix = xMatrix - pageNumberPrefix.length();
                }
                this.xMatrix = xMatrix;
            }
            if (yMatrix >= 0 && yMatrix <= 830) {
                this.yMatrix = yMatrix;
            }
            this.enabled = enabled;

            gstate = new PdfGState();
            gstate.setFillOpacity(0.6f);
            gstate.setStrokeOpacity(0.6f);
            try {
                Path path = Paths.get(TmpFileUtils.getMetaInfo(), "/resources/images/sdo-logo-black.png");
                sdoLogo = Image.getInstance(path.normalize().toAbsolutePath().toString());
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        @Override
        public void onEndPage(PdfWriter writer, Document document) {
            try {
                PdfContentByte cb = writer.getDirectContent();
                BaseFont bf = baseFont;
                cb.setGState(gstate);
                cb.beginText();
                cb.addImage(sdoLogo, 105, 0, 0, 35, 25, 808);
                cb.endText();

                cb.beginText();
                cb.setGState(gstate);
                cb.setFontAndSize(bf, 10);
                cb.setTextMatrix(180, 810);
                cb.showText(" Протокол роботи АСКУ СПУ за " + LocalDateTime.now().format(ukrainianFormatterWithMin));
                cb.endText();

                int pageN = writer.getPageNumber();
                String text = pageNumberPrefix + " " + pageN;

                cb.beginText();
                cb.setFontAndSize(bf, 10);
                cb.setTextMatrix(xMatrix, yMatrix);

                cb.showText(text);
                cb.endText();
            } catch (DocumentException e) {
                log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
                throw new RuntimeException(e);
            }
        }

    }
}
