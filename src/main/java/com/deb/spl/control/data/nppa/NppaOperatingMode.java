package com.deb.spl.control.data.nppa;

import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

public enum NppaOperatingMode implements AdjacentSystemWithDescriptionUa {
    COMBAT("Бойовий"),
    TESTING("Перевірочний"),
    NOT_SELECTED("Не обрано");

    @Getter
    private String description;

    NppaOperatingMode(String description) {
        this.description = description;
    }

    @Override
    public String getValueUa() {
        return description;
    }

    public static NppaOperatingMode fromValueUa(String uaVal) {
        Optional<NppaOperatingMode> status = Arrays.stream(NppaOperatingMode.values())
                .filter(e -> e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(NOT_SELECTED);
    }
}
