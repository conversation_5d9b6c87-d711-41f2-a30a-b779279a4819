package com.deb.spl.control.data;

import com.deb.spl.control.data.sae.CommandState;
import com.deb.spl.control.views.adjacentSystems.utils.HasVerticalInfoAsList;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@MappedSuperclass
public class AdjacentSystemCommand implements HasVerticalInfoAsList {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;
    @NotBlank
    private String command;
    @NotBlank
    private String caption;
    @JsonIgnore
    private CommandState commandState;
    @NotNull
    @Column(name = "adjacent_system", columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private AdjacentSystemType adjacentSystem;
    private LocalDateTime generationTime;
    private LocalDateTime executionTime;
    @JsonIgnore
    private String originator;

    @Override
    public String toString() {
        return "Command {" +
               "command id " + getId() +
               ", command=' " + command + '\'' +
               ", originator " + originator +
               ", system" + getAdjacentSystem() +
               ", generationTime= " + generationTime +
               ", executingTime= " + executionTime +
               '}';
    }

    public String toStringUa() {
        return "Команда {" +
               "id команди - " + getId() +
               ", команда - ' " + command + '\'' +
               ", ситема - " + getAdjacentSystem() +
               ", час створення - " + generationTime +
               ", час виконання - " + executionTime +
               '}';
    }

    public List<Pair> toVerticalInfoListUa() {
        String executionTimeStr = executionTime != null ? executionTime.format(DateTimeFormatter.ofPattern("dd/MM/yyyy\t HH:mm:ss"))
                : (commandState == null || !commandState.equals(CommandState.CANCELLED)) ? "Не виконано" :
                "Скасовано";
        return List.of(
                new Pair(0, "Команда {"),
                new Pair(1, "id команди " + getId()),
                new Pair(1, "команда -  " + command),
                new Pair(1, "опис - " + caption),
                new Pair(1, "створив - " + originator),
                new Pair(1, "ситема - " + getAdjacentSystem().getUa()),
                new Pair(1, "час створення - " + generationTime.format(DateTimeFormatter.ofPattern("dd/MM/yyyy\t HH:mm:ss"))),
                new Pair(1, "час виконання - " + executionTimeStr),
                new Pair(0, "}")
        );
    }
}
