package com.deb.spl.control.data.ppo;

import java.util.Arrays;
import java.util.Optional;

public enum PpoBayType {
    ASKU("АСКУ"),
    NPPA("НППА"),
    DEA("ДЕА"),
    TO("ТО"),
    UNDEFINED("невідомий");
    private String bayName;

    PpoBayType(String bayName) {
        this.bayName = bayName;
    }

    public String getValueUa() {
        return bayName;
    }

    public static PpoBayType fromValueUa(String uaVal){
        Optional<PpoBayType> status= Arrays.stream(PpoBayType.values())
                .filter(e->e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(UNDEFINED);
    }

}
