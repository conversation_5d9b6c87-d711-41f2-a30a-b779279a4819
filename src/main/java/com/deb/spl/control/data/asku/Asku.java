package com.deb.spl.control.data.asku;

import com.deb.spl.control.data.bins.Position;
import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.NotImplementedException;

import javax.validation.constraints.NotNull;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.UUID;

@Getter
@Setter
@Builder
@Slf4j
public class Asku implements AdjacentSystem {
    private static final long serialVersionUID = 6671004136397219164L;
    @JsonIgnore
    private final AdjacentSystemType adjacentSystemType = AdjacentSystemType.ASKU;
    private Long id;

    private UUID splId;
    private String plateNumber;
    private String unitTitle;
    private LocalDateTime startedDate;
    private Readiness splReadiness;
    private Tpc tpcLeft;
    private Tpc tpcRight;
    private Rocket leftRocket;
    private Rocket rightRocket;
    private AdjacentSystemStatus status;
    private Position position;
    private Plc plc;

    @JsonIgnore
    public void setRocket(boolean isLeft, @NotNull Rocket rocket) {
        if (isLeft) {
            leftRocket = rocket;
        } else {
            rightRocket = rocket;
        }
    }

    @Override
    @JsonIgnore
    public AdjacentSystemType getAdjacentSystemType() {
        return AdjacentSystemType.ASKU;
    }

    @Override
    @JsonIgnore
    public String getErrorMessage() {
        throw new NotImplementedException();
    }

    @Override
    @JsonIgnore
    public boolean isMalfunctionPresent() {
        return status == AdjacentSystemStatus.ERROR || (plc != null && plc.isMalfunctionPresent());
    }

    public void setStatus(@NotNull AdjacentSystemStatus systemStatus) {
        this.status = systemStatus;
    }

    @Override
    @JsonIgnore
    public AdjacentSystemStatus getStatus() {
        if (status == null) {
            status = AdjacentSystemStatus.UNDEFINED;
        }

        return status;
    }

    @JsonIgnore
    public Duration getElapsedTime() {
        return (startedDate == null) ? Duration.ofMillis(0) : Duration.between(startedDate, LocalDateTime.now());
    }

    @JsonIgnore
    public boolean isLeftRocketKnown() {
        return leftRocket != null && leftRocket.getPlantMissile().isPresent();
    }

    @JsonIgnore
    public boolean isRightRocketKnown() {
        return rightRocket != null && rightRocket.getPlantMissile().isPresent();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Asku asku)) return false;

        if (getAdjacentSystemType() != asku.getAdjacentSystemType()) return false;
        if (getSplId() != null ? !getSplId().equals(asku.getSplId()) : asku.getSplId() != null) return false;
        if (getPlateNumber() != null ? !getPlateNumber().equals(asku.getPlateNumber()) : asku.getPlateNumber() != null)
            return false;
        if (getUnitTitle() != null ? !getUnitTitle().equals(asku.getUnitTitle()) : asku.getUnitTitle() != null)
            return false;
        if (getSplReadiness() != asku.getSplReadiness()) return false;
        if (getTpcLeft() != null ? !getTpcLeft().equals(asku.getTpcLeft()) : asku.getTpcLeft() != null) return false;
        if (getTpcRight() != null ? !getTpcRight().equals(asku.getTpcRight()) : asku.getTpcRight() != null)
            return false;
        if (getLeftRocket() != null ? !getLeftRocket().equals(asku.getLeftRocket()) : asku.getLeftRocket() != null)
            return false;
        if (getRightRocket() != null ? !getRightRocket().equals(asku.getRightRocket()) : asku.getRightRocket() != null)
            return false;
        if (getStatus() != asku.getStatus()) return false;
        if (getPosition() != null ? !getPosition().equals(asku.getPosition()) : asku.getPosition() != null)
            return false;
        return getPlc() != null ? getPlc().equals(asku.getPlc()) : asku.getPlc() == null;
    }

    @Override
    public int hashCode() {
        int result = getAdjacentSystemType().hashCode();
        result = 31 * result + (getSplId() != null ? getSplId().hashCode() : 0);
        result = 31 * result + (getPlateNumber() != null ? getPlateNumber().hashCode() : 0);
        result = 31 * result + (getSplReadiness() != null ? getSplReadiness().hashCode() : 0);
        result = 31 * result + (getTpcLeft() != null ? getTpcLeft().hashCode() : 0);
        result = 31 * result + (getTpcRight() != null ? getTpcRight().hashCode() : 0);
        result = 31 * result + (getLeftRocket() != null ? getLeftRocket().hashCode() : 0);
        result = 31 * result + (getRightRocket() != null ? getRightRocket().hashCode() : 0);
        result = 31 * result + (getStatus() != null ? getStatus().hashCode() : 0);
        result = 31 * result + (getPosition() != null ? getPosition().hashCode() : 0);
        result = 31 * result + (getPlc() != null ? getPlc().hashCode() : 0);
        return result;
    }
}
