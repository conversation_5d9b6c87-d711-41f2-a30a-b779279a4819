package com.deb.spl.control.data.nppa;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.WARN)
public interface BynMapper {
    Byn map(BynDto value);

    @Mapping(target = "isRgOutNcok", source = "rgOutNcok")
    @Mapping(target = "isNppaConnected", source = "nppaConnected")
    @Mapping(target = "isNcok", source = "ncok")
    @Mapping(target = "isF5", source = "f5")
    @Mapping(target = "isF4", source = "f4")
    @Mapping(target = "isF3", source = "f3")
    @Mapping(target = "isF2", source = "f2")
    @Mapping(target = "isF1", source = "f1")
    @Mapping(target = "isConnected", source = "connected")
    @Mapping(target = "isBuveF4", source = "buveF4")
    @Mapping(target = "isBuveF2", source = "buveF2")
    @Mapping(target = "isBasuOtr2F3", source = "basuOtr2F3")
    @Mapping(target = "isBasuOtr2Connected", source = "basuOtr2Connected")
    @Mapping(target = "isBasuOtr1F3", source = "basuOtr1F3")
    @Mapping(target = "isBasuOtr1Connected", source = "basuOtr1Connected")
    Byn map(BynDao value);

    @Mapping(target = "isRgOutNcok", source = "rgOutNcok")
    @Mapping(target = "isNppaConnected", source = "nppaConnected")
    @Mapping(target = "isNcok", source = "ncok")
    @Mapping(target = "isConnected", source = "connected")
    @Mapping(target = "isBuveF4", source = "buveF4")
    @Mapping(target = "isBuveF2", source = "buveF2")
    @Mapping(target = "isF5", source = "f5")
    @Mapping(target = "isF4", source = "f4")
    @Mapping(target = "isF3", source = "f3")
    @Mapping(target = "isF2", source = "f2")
    @Mapping(target = "isF1", source = "f1")
    @Mapping(target = "isBasuOtr1F3", source = "basuOtr1F3")
    @Mapping(target = "isBasuOtr2F3", source = "basuOtr2F3")
    @Mapping(target = "isBasuOtr1Connected", source = "basuOtr1Connected")
    @Mapping(target = "isBasuOtr2Connected", source = "basuOtr2Connected")
    BynDto toDto(Byn value);

    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "isRgOutNcok", source = "rgOutNcok")
    @Mapping(target = "isNppaConnected", source = "nppaConnected")
    @Mapping(target = "isNcok", source = "ncok")
    @Mapping(target = "isF5", source = "f5")
    @Mapping(target = "isF4", source = "f4")
    @Mapping(target = "isF3", source = "f3")
    @Mapping(target = "isF2", source = "f2")
    @Mapping(target = "isF1", source = "f1")
    @Mapping(target = "isConnected", source = "connected")
    @Mapping(target = "isBuveF4", source = "buveF4")
    @Mapping(target = "isBuveF2", source = "buveF2")
    @Mapping(target = "isBasuOtr2F3", source = "basuOtr2F3")
    @Mapping(target = "isBasuOtr2Connected", source = "basuOtr2Connected")
    @Mapping(target = "isBasuOtr1F3", source = "basuOtr1F3")
    @Mapping(target = "isBasuOtr1Connected", source = "basuOtr1Connected")
    @Mapping(target = "id", ignore = true)
    BynDao toDao(Byn value);
}
