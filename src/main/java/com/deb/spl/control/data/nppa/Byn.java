package com.deb.spl.control.data.nppa;

import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

@Slf4j
@Getter
@Setter
@Builder
@AllArgsConstructor
public class Byn implements AdjacentSystem, Serializable {
    private static final long serialVersionUID = 2195262273985952159L;
    @JsonIgnore
    private final AdjacentSystemType adjacentSystemType = AdjacentSystemType.BYN;
    @Builder.Default
    private AdjacentSystemStatus systemStatus = AdjacentSystemStatus.UNDEFINED;
    private NppaOperatingMode operatingMode;
    private BaseProperty tvByn;
    private boolean isConnected;
    private boolean isNcok;
    private boolean isRgOutNcok;
    private boolean isBuveF2;
    private boolean isBuveF4;
    private boolean isBasuOtr1F3;
    private boolean isBasuOtr2F3;
    private boolean isF1;
    private boolean isF2;
    private boolean isF3;
    private boolean isF4;
    private boolean isF5;
    private boolean isNppaConnected;
    private boolean isBasuOtr1Connected;
    private boolean isBasuOtr2Connected;



    public Byn() {
        super();
    }

    @JsonIgnore
    @Override
    public AdjacentSystemType getAdjacentSystemType() {
        return AdjacentSystemType.BYN;
    }

    @JsonIgnore
    @Override
    public String getErrorMessage() {
        return null;
    }

    @JsonIgnore
    @Override
    public boolean isMalfunctionPresent() {
        return systemStatus!=AdjacentSystemStatus.ERROR && tvByn!=BaseProperty.ERROR;
    }

    @JsonIgnore
    @Override
    public AdjacentSystemStatus getStatus() {
        return systemStatus != null ? systemStatus : AdjacentSystemStatus.UNDEFINED;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Byn byn)) return false;

        if (isConnected() != byn.isConnected()) return false;
        if (isNcok() != byn.isNcok()) return false;
        if (isRgOutNcok() != byn.isRgOutNcok()) return false;
        if (isBuveF2() != byn.isBuveF2()) return false;
        if (isBuveF4() != byn.isBuveF4()) return false;
        if (isBasuOtr1F3() != byn.isBasuOtr1F3()) return false;
        if (isBasuOtr2F3() != byn.isBasuOtr2F3()) return false;
        if (isF1() != byn.isF1()) return false;
        if (isF2() != byn.isF2()) return false;
        if (isF3() != byn.isF3()) return false;
        if (isF4() != byn.isF4()) return false;
        if (isF5() != byn.isF5()) return false;
        if (isNppaConnected() != byn.isNppaConnected()) return false;
        if (isBasuOtr1Connected() != byn.isBasuOtr1Connected()) return false;
        if (isBasuOtr2Connected() != byn.isBasuOtr2Connected()) return false;
        if (getAdjacentSystemType() != byn.getAdjacentSystemType()) return false;
        if (getSystemStatus() != byn.getSystemStatus()) return false;
        if (getOperatingMode() != byn.getOperatingMode()) return false;
        return getTvByn() == byn.getTvByn();
    }

    @Override
    public int hashCode() {
        int result = getAdjacentSystemType().hashCode();
        result = 31 * result + (getSystemStatus() != null ? getSystemStatus().hashCode() : 0);
        result = 31 * result + (getOperatingMode() != null ? getOperatingMode().hashCode() : 0);
        result = 31 * result + (getTvByn() != null ? getTvByn().hashCode() : 0);
        result = 31 * result + (isConnected() ? 1 : 0);
        result = 31 * result + (isNcok() ? 1 : 0);
        result = 31 * result + (isRgOutNcok() ? 1 : 0);
        result = 31 * result + (isBuveF2() ? 1 : 0);
        result = 31 * result + (isBuveF4() ? 1 : 0);
        result = 31 * result + (isBasuOtr1F3() ? 1 : 0);
        result = 31 * result + (isBasuOtr2F3() ? 1 : 0);
        result = 31 * result + (isF1() ? 1 : 0);
        result = 31 * result + (isF2() ? 1 : 0);
        result = 31 * result + (isF3() ? 1 : 0);
        result = 31 * result + (isF4() ? 1 : 0);
        result = 31 * result + (isF5() ? 1 : 0);
        result = 31 * result + (isNppaConnected() ? 1 : 0);
        result = 31 * result + (isBasuOtr1Connected() ? 1 : 0);
        result = 31 * result + (isBasuOtr2Connected() ? 1 : 0);
        return result;
    }
}
