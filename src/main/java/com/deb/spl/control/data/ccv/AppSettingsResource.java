package com.deb.spl.control.data.ccv;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Optional;
import java.util.UUID;

@Getter
@Setter
@Slf4j
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AppSettingsResource {
    public static final String VEHICLE_NUMBER_KEY = "number";
    private UUID unitId;
    private UUID vehicleId;
    private Object vehicle;
    private UUID splUserId;

    public AppSettingsResource() {
    }

    @JsonIgnore
    public Optional<String> getVehicleNumber() {

        if (vehicle == null) {
            return Optional.empty();
        }

        try {
            if (!((LinkedHashMap) vehicle).containsKey(VEHICLE_NUMBER_KEY)) {
                return Optional.empty();
            }

            String number = (String) ((LinkedHashMap) vehicle).get(VEHICLE_NUMBER_KEY);

            return Optional.ofNullable(number);
        } catch (Exception e) {
            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
        }
        return Optional.empty();
    }
}
