package com.deb.spl.control.data.suto;

import com.deb.spl.control.controller.ServerErrorException;
import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.PropertyType;
import com.deb.spl.control.data.sae.CommandState;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.NotImplementedException;

import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;


@Getter
@Setter
@Builder
@AllArgsConstructor
@ToString
@Slf4j
public class Suto implements AdjacentSystem, Serializable {
    private static final long serialVersionUID = 3511502670152569069L;

    @Builder.Default
    private AdjacentSystemStatus status = AdjacentSystemStatus.UNDEFINED;
    @JsonIgnore
    private final AdjacentSystemType adjacentSystemType = AdjacentSystemType.SUTO;
    private Long id;
    private SutoStats stats;
    @NotNull
    private OutriggerEmergencyCode leftFrontOutriggerEmergencyCode;
    @NotNull
    private OutriggerEmergencyCode rightFrontOutriggerEmergencyCode;
    @NotNull
    private OutriggerEmergencyCode leftRearOutriggerEmergencyCode;
    @NotNull
    private OutriggerEmergencyCode rightRearOutriggerEmergencyCode;
    @NotNull
    Map<String, SutoProperty> properties;

    @Nullable
    SutoSettings sutoSettings;

    @Override
    public AdjacentSystemType getAdjacentSystemType() {
        return this.adjacentSystemType;
    }

    public Suto() {
        super();
    }

    public void updateSutoSettings(@NotNull SutoSettings settings) {
        if (sutoSettings != null) {
            sutoSettings = new SutoSettings(ChassisState.UNKNOWN);
        }

        if (settings.getChassisState() != null) {
            assert sutoSettings != null;
            if (!settings.getChassisState().equals(sutoSettings.getChassisState())) {
                sutoSettings.setChassisState(settings.getChassisState());
            }
        }
    }

    public static Suto copy(Suto original) {
        Suto copy;
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            copy = objectMapper.readValue(objectMapper.writeValueAsString(original), Suto.class);
        } catch (JsonProcessingException e) {
            log.error(e.getMessage() + " \n" + Arrays.toString(e.getStackTrace()));
            throw new ServerErrorException(e.getMessage());
        }
        return copy;
    }

    @JsonIgnore
    @Override
    public String getErrorMessage() {
        throw new NotImplementedException(this.toString());
    }

    @JsonIgnore
    @Override
    public boolean isMalfunctionPresent() {
        if (status.equals(AdjacentSystemStatus.ERROR) || status.equals(AdjacentSystemStatus.WARNING)) {
            return true;
        }

        if (!leftFrontOutriggerEmergencyCode.equals(OutriggerEmergencyCode.NONE)
                || !rightFrontOutriggerEmergencyCode.equals(OutriggerEmergencyCode.NONE)
                || !leftRearOutriggerEmergencyCode.equals(OutriggerEmergencyCode.NONE)
                || !rightRearOutriggerEmergencyCode.equals(OutriggerEmergencyCode.NONE)) {
            return true;
        }

        return properties.values().stream()
                .filter(p -> p.propertyType.equals(PropertyType.MALFUNCTION))
                .anyMatch(p -> p.getState().equals(CommandState.ON));
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Suto suto)) return false;

        if (getStatus() != suto.getStatus()) return false;
        if (getAdjacentSystemType() != suto.getAdjacentSystemType()) return false;
        if (getId() != null ? !getId().equals(suto.getId()) : suto.getId() != null) return false;
        if (getStats() != null ? !getStats().equals(suto.getStats()) : suto.getStats() != null) return false;
        if (getLeftFrontOutriggerEmergencyCode() != suto.getLeftFrontOutriggerEmergencyCode()) return false;
        if (getRightFrontOutriggerEmergencyCode() != suto.getRightFrontOutriggerEmergencyCode()) return false;
        if (getLeftRearOutriggerEmergencyCode() != suto.getLeftRearOutriggerEmergencyCode()) return false;
        if (getRightRearOutriggerEmergencyCode() != suto.getRightRearOutriggerEmergencyCode()) return false;
        if (getProperties() != null ? !getProperties().equals(suto.getProperties()) : suto.getProperties() != null)
            return false;
        return getSutoSettings() != null ? getSutoSettings().equals(suto.getSutoSettings()) : suto.getSutoSettings() == null;
    }

    @Override
    public int hashCode() {
        int result = getStatus() != null ? getStatus().hashCode() : 0;
        result = 31 * result + getAdjacentSystemType().hashCode();
        result = 31 * result + (getId() != null ? getId().hashCode() : 0);
        result = 31 * result + (getStats() != null ? getStats().hashCode() : 0);
        result = 31 * result + (getLeftFrontOutriggerEmergencyCode() != null ? getLeftFrontOutriggerEmergencyCode().hashCode() : 0);
        result = 31 * result + (getRightFrontOutriggerEmergencyCode() != null ? getRightFrontOutriggerEmergencyCode().hashCode() : 0);
        result = 31 * result + (getLeftRearOutriggerEmergencyCode() != null ? getLeftRearOutriggerEmergencyCode().hashCode() : 0);
        result = 31 * result + (getRightRearOutriggerEmergencyCode() != null ? getRightRearOutriggerEmergencyCode().hashCode() : 0);
        result = 31 * result + (getProperties() != null ? getProperties().hashCode() : 0);
        result = 31 * result + (getSutoSettings() != null ? getSutoSettings().hashCode() : 0);
        return result;
    }

    @JsonIgnore
    public Optional<ChassisState> getChassisState() {
        log.error("Using MOCK chassis state " + this);
        return Optional.empty();
    }

    public SutoSettings getSutoSettings() {
        return sutoSettings;
    }
}
