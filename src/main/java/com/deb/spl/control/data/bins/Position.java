package com.deb.spl.control.data.bins;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.sf.marineapi.nmea.util.Datum;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class Position {
    @Column(name = "latitude", columnDefinition = "double precision default 0.0")
    private double latitude;
    // longitude degrees
    @Column(name = "longitude", columnDefinition = "double precision default 0.0")
    private double longitude;
    // altitude
    @Column(name = "altitude", columnDefinition = "double precision default 0.0")
    private double altitude;
    // datum/coordinate system
    @Column(name = "datum", columnDefinition = "varchar(9)")
    @Enumerated(EnumType.STRING)
    private Datum datum;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Position position = (Position) o;

        if (Double.compare(position.getLatitude(), getLatitude()) != 0) return false;
        if (Double.compare(position.getLongitude(), getLongitude()) != 0) return false;
        if (Double.compare(position.getAltitude(), getAltitude()) != 0) return false;
        return getDatum() == position.getDatum();
    }

    @Override
    public int hashCode() {
        int result;
        long temp;
        temp = Double.doubleToLongBits(getLatitude());
        result = (int) (temp ^ (temp >>> 32));
        temp = Double.doubleToLongBits(getLongitude());
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        temp = Double.doubleToLongBits(getAltitude());
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        result = 31 * result + (getDatum() != null ? getDatum().hashCode() : 0);
        return result;
    }
}


