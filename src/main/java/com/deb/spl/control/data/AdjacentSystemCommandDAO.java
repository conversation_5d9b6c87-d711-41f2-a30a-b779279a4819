package com.deb.spl.control.data;

import com.deb.spl.control.data.sae.CommandState;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@MappedSuperclass
public class AdjacentSystemCommandDAO {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;
    @NotNull
    @Column(unique = true)
    private String command;
    @NotNull
    private String caption;
    @Transient
    private CommandState commandState;
    @Enumerated(EnumType.STRING)
    private AdjacentSystemType adjacentSystem;
}
