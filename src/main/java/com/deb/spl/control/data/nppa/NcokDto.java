package com.deb.spl.control.data.nppa;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonPropertyOrder({"systemStatus",
        "operatingMode",
        "tvNcok",
        "isNcokConnected",
        "isSutoConnected",
        "nppaTestResult",
        "appPresence",
        "otr1AppPresence",
        "otr2AppPresence",
        "otr1TestResult",
        "otr2TestResult",
        "isOtr1Lunched",
        "isOtr2Lunched",
        "otr1BinsInitialSetup",
        "otr2BinsInitialSetup",
        "otrBinsPreciseSetup",
        "otr1tvNoIns",
        "otr2tvNoIns",
        "otr1BasSnsPz",
        "otr2BasSnsPz"})
public record NcokDto(
        AdjacentSystemStatus systemStatus,
        NppaOperatingMode operatingMode,
        BaseProperty tvNcok,
        boolean isNcokConnected,
        boolean isSutoConnected,
        BaseProperty nppaTestResult,
        boolean appPresence,
        boolean otr1AppPresence,
        boolean otr2AppPresence,
        BaseProperty otr1TestResult,
        BaseProperty otr2TestResult,
        BaseProperty isOtr1Lunched,
        BaseProperty isOtr2Lunched,
        BaseProperty otr1BinsInitialSetup,
        BaseProperty otr2BinsInitialSetup,
        BaseProperty otrBinsPreciseSetup,
        BaseProperty otr1tvNoIns,
        BaseProperty otr2tvNoIns,
        BasSnsStatus otr1BasSnsPz,
        BasSnsStatus otr2BasSnsPz) {
}
