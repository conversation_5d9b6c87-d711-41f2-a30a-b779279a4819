package com.deb.spl.control.data.asku;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring",
        uses = {RocketMapper.class})
public interface AskuMapper {
    @Mapping(target = "id",ignore = true)
    @Mapping(target = "splId",ignore = true)
    @Mapping(target = "plc",ignore = true)
    @Mapping(target = "status",ignore = true)
    Asku map(AskuDto value);

    AskuDto toDto(Asku value);

    Asku map(AskuDao value);

    Asku clone(Asku value);

    AskuDao clone(AskuDao value);

    @Mapping(target = "systemStatus", source = "status")
    @Mapping(target = "updatedAt", ignore = true)
    AskuDao toDao(Asku value);
}
