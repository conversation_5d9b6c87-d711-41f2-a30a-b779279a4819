package com.deb.spl.control.data.asku;

import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

public enum SourceOfSPLCoordinate implements AdjacentSystemWithDescriptionUa {
        UKNSS("УКННС"),
        MANUAL("Ручне введення");

        @Getter
        private String description;

        SourceOfSPLCoordinate(String description) {
            this.description = description;
        }

        @Override
        public String getValueUa() {
            return description;
        }

    public static SourceOfSPLCoordinate fromValueUa(String uaVal) {
        Optional<SourceOfSPLCoordinate> status = Arrays.stream(SourceOfSPLCoordinate.values())
                .filter(e -> e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(MANUAL);
        }
    }