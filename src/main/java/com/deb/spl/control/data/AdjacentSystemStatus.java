package com.deb.spl.control.data;

import java.util.Arrays;
import java.util.Optional;

public enum AdjacentSystemStatus implements AdjacentSystemWithDescriptionUa {// TODO: 13.04.2023 rename to AdjacentSystemState
    OK("Норма"),
    WARNING("Увага"),
    ERROR("Помилка"),
    NOT_CONNECTED("Немає зв'язку"),
    UNDEFINED("Невідомо"),
    ANY("Будь-який"),
    RT_DATA("РЧ дані");
    public String value;

    AdjacentSystemStatus(String value) {
        this.value = value;
    }

    public String getValueUa() {
        return value;
    }

    public String getValueEn() {
        return name();
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return value;
    }

    public static AdjacentSystemStatus fromValueUa(String uaVal){
        Optional<AdjacentSystemStatus> status= Arrays.stream(AdjacentSystemStatus.values())
                .filter(e->e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(UNDEFINED);
    }

}
