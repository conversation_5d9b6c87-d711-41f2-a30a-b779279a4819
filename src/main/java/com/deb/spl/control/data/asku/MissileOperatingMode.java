package com.deb.spl.control.data.asku;

import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

public enum MissileOperatingMode implements AdjacentSystemWithDescriptionUa {
    BASE_SNS("Штатний політ з СНС"),
    UNKNOWN("Невідомо");

    @Getter
    private String description;

    MissileOperatingMode(String description) {
        this.description = description;
    }

    @Override
    public String getValueUa() {
        return description;
    }

    public static MissileOperatingMode fromValueUa(String uaVal) {
        Optional<MissileOperatingMode> status = Arrays.stream(MissileOperatingMode.values())
                .filter(e -> e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(UNKNOWN);
    }
}
