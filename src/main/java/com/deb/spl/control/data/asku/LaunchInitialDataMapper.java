package com.deb.spl.control.data.asku;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.WARN)
public interface LaunchInitialDataMapper {

    @Mapping(target = "loadTemperature", constant = "-999.9")
    LaunchInitialDataDao toDao(LaunchInitialData value);

    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "id", ignore = true)
//    @Mapping(target = "loadedToPlc", ignore = true)
    @Mapping(target = "proDetected", source = "isProDetected")
    @Mapping(target = "loadTemperature", constant = "-999.9")
    LaunchInitialDataDao toDao(LaunchInitialDataDto value);

    @Mapping(target = "loadTemperature", constant = "-999.9")
    LaunchInitialData map(LaunchInitialDataDao value);

//    @Mapping(target = "loadedToPlc", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "proDetected", source = "isProDetected")
    @Mapping(target = "loadTemperature", constant = "-999.9")
    LaunchInitialData map(LaunchInitialDataDto value);


    @Mapping(target = "isProDetected", source = "proDetected")
    @Mapping(target = "loadTemperature", constant = "-999.9")
    LaunchInitialDataDto toDto(LaunchInitialData value);

    @Mapping(target = "loadTemperature", constant = "-999.9")
    LaunchInitialData clone(LaunchInitialData value);
}
