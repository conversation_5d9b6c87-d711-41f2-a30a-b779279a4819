package com.deb.spl.control.data.ppo;

import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;

import java.util.Arrays;
import java.util.Optional;

public enum PpoUnitStatus implements AdjacentSystemWithDescriptionUa {
    ON("Включений"),
    OFF("Відключений"),
    ERROR("Не норма"),
    UNDEFINED("Невідомо");
    private String sourceName;

    PpoUnitStatus(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getValueUa() {
        return sourceName;
    }

    public static PpoUnitStatus fromValueUa(String uaVal){
        Optional<PpoUnitStatus> status= Arrays.stream(PpoUnitStatus.values())
                .filter(e->e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(ERROR);
    }
}
