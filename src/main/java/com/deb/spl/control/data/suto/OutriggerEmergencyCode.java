package com.deb.spl.control.data.suto;

import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;

import java.util.Arrays;
import java.util.Optional;


public enum OutriggerEmergencyCode implements AdjacentSystemWithDescriptionUa {
    OUTRIGGER_DRIVE_MALFUNCTION("Електропривід опори несправний"),
    OUTRIGGER_DIDNT_REACH_GROUND_OR_BROKEN("Опора не досягла ґрунту або несправна"),
    OUTRIGGER_STICKED_OUT_AND_REACH_GROUND("Опора висунута повністю і досягла ґрунту"),
    OUTRIGGER_CANT_REVERT_TO_INITIAL_STATE("Опора не повернена у вихідне положення"),
    OUTRIGGER_STICKED_OUT_AND_REACH_GROUND_2("Опора досягла ґрунту і повністю висунута_2"),
    MAXIMUM_PRESSURE_IN_HYDRO_CYLINDER("Тиск у гідроциліндрі максимальний"),
    OUTRIGGER_NOT_IN_INITIAL_STATE("Опора не у вихідному положенні"),
    SENSORS_MALFUNCTION("Некоректний стан датчиків"),
    NONE("Справна");


    private String sourceName;

    OutriggerEmergencyCode(String sourceName) {
        this.sourceName = sourceName;
    }

    @Override
    public String getValueUa() {
        return sourceName;
    }

    public static OutriggerEmergencyCode fromValueUa(String uaVal) {
        Optional<OutriggerEmergencyCode> status = Arrays.stream(OutriggerEmergencyCode.values())
                .filter(e -> e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(NONE);
    }

    public static OutriggerEmergencyCode decodeFromInt(int code) {
        OutriggerEmergencyCode decoded;
        switch (code) {
            case (0) -> decoded = NONE;
            case (1) -> decoded = OUTRIGGER_DRIVE_MALFUNCTION;
            case (3) -> decoded = OUTRIGGER_DIDNT_REACH_GROUND_OR_BROKEN;
            case (4) -> decoded = OUTRIGGER_STICKED_OUT_AND_REACH_GROUND;
            case (5) -> decoded = OUTRIGGER_CANT_REVERT_TO_INITIAL_STATE;
            case (7) -> decoded = OUTRIGGER_STICKED_OUT_AND_REACH_GROUND_2;
            case (8) -> decoded = MAXIMUM_PRESSURE_IN_HYDRO_CYLINDER;
            case (9) -> decoded = OUTRIGGER_NOT_IN_INITIAL_STATE;
            default -> decoded = SENSORS_MALFUNCTION;
        }

        return decoded;
    }

    public static int enodeToInt(OutriggerEmergencyCode code) {

        int encoded = switch (code) {
            case NONE -> 0;
            case OUTRIGGER_DRIVE_MALFUNCTION -> 1;
            case OUTRIGGER_DIDNT_REACH_GROUND_OR_BROKEN -> 3;
            case OUTRIGGER_STICKED_OUT_AND_REACH_GROUND -> 4;
            case OUTRIGGER_CANT_REVERT_TO_INITIAL_STATE -> 5;
            case OUTRIGGER_STICKED_OUT_AND_REACH_GROUND_2 -> 7;
            case MAXIMUM_PRESSURE_IN_HYDRO_CYLINDER -> 8;
            case OUTRIGGER_NOT_IN_INITIAL_STATE -> 9;
            default -> 10;
        };


        return encoded;
    }


}
