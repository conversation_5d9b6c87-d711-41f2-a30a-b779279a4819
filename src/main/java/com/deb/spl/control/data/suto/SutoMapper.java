package com.deb.spl.control.data.suto;


import com.deb.spl.control.data.AdjacentSystemProperty;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring",
        uses = {SutoStatsToDtoMapper.class, SutoPropertyMapper.class})

public interface SutoMapper {

    SutoPropertyMapper sutoPropertyMapper = Mappers.getMapper(SutoPropertyMapper.class);

    @Mapping(target = "properties",
            expression = "java(compileProperty(value.getProperties(),value.getLogPropertiesState()))")
    Suto map(SutoDao value);

    @Mapping(target = "logPropertiesState",
            expression = "java(convertToPropertiesStateLog(value.getProperties()))")
    SutoDao toDao(Suto value);


    @Mapping(target = "leftFrontOutriggerEmergencyCode",
            expression = "java(decodeFromInt(value.leftFrontOutriggerEmergencyCode()))")
    @Mapping(target = "rightFrontOutriggerEmergencyCode",
            expression = "java(decodeFromInt(value.rightFrontOutriggerEmergencyCode()))")
    @Mapping(target = "leftRearOutriggerEmergencyCode",
            expression = "java(decodeFromInt(value.leftRearOutriggerEmergencyCode()))")
    @Mapping(target = "rightRearOutriggerEmergencyCode",
            expression = "java(decodeFromInt(value.rightRearOutriggerEmergencyCode()))")
    Suto map(SutoDto value);

    @Mapping(target = "leftFrontOutriggerEmergencyCode",
            expression = "java(encodeToInt(value.getLeftFrontOutriggerEmergencyCode()))")
    @Mapping(target = "rightFrontOutriggerEmergencyCode",
            expression = "java(encodeToInt(value.getRightFrontOutriggerEmergencyCode()))")
    @Mapping(target = "leftRearOutriggerEmergencyCode",
            expression = "java(encodeToInt(value.getLeftRearOutriggerEmergencyCode()))")
    @Mapping(target = "rightRearOutriggerEmergencyCode",
            expression = "java(encodeToInt(value.getRightRearOutriggerEmergencyCode()))")
    SutoDto toDto(Suto value);

    default OutriggerEmergencyCode decodeFromInt(int code) {
        return OutriggerEmergencyCode.decodeFromInt(code);
    }

    default int encodeToInt(OutriggerEmergencyCode code) {
        return OutriggerEmergencyCode.enodeToInt(code);
    }


    default Map<String, SutoProperty> map(List<SutoPropertyDto> value) {
        if (value == null) {
            return null;
        }

        return value.stream().map(sutoPropertyMapper::map)
                .sorted(Comparator.comparing(AdjacentSystemProperty::getId))
                .toList().stream()
                .collect(Collectors.toMap(SutoProperty::getName, Function.identity()));
    }

    default List<SutoPropertyDto> map(Map<String, SutoProperty> value) {
        if (value == null) {
            return null;
        }
        return value.values().stream().map(sutoPropertyMapper::toDto)
                .sorted(Comparator.comparing(SutoPropertyDto::id))
                .collect(Collectors.toList());
    }

    default List<SutoProperty> convertToPropertiesStateLog(Map<String, SutoProperty> value) {
        if (value == null) {
            return Collections.emptyList();
        }

        return value.values().stream()
                .sorted(Comparator.comparing(AdjacentSystemProperty::getId))
                .toList();
    }

    default Map<String, SutoProperty> compileProperty(Map<String, SutoPropertyDao> value, List<SutoProperty> loggedProperties) {
        if (value == null) {
            return null;
        }

        return value.values().stream().map(s -> {
                    SutoProperty propertyWithVal = sutoPropertyMapper.map(s);

                    Optional<SutoProperty> loggedProperty = loggedProperties.stream()
                            .sorted(Comparator.comparing(AdjacentSystemProperty::getId))
                            .filter((p) -> p.getName().equals(s.getName())).findFirst();

                    if (loggedProperty.isPresent() && loggedProperty.get().getState() != null) {
                        propertyWithVal.setState(loggedProperty.get().getState());
                    }

                    return propertyWithVal;
                })
                .sorted(Comparator.comparing(AdjacentSystemProperty::getId))
                .toList()
                .stream().collect(Collectors.toMap(SutoProperty::getName, Function.identity()));
    }

}
