package com.deb.spl.control.data.nppa;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.*;
import java.time.LocalDateTime;

@Slf4j
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "byn")
public class BynDao {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @JsonIgnore
    @Transient
    private final AdjacentSystemType adjacentSystemType = AdjacentSystemType.BYN;


    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Builder.Default
    @Column(name = "system_status", columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private AdjacentSystemStatus systemStatus = AdjacentSystemStatus.UNDEFINED;

    @Column(name = "operating_mode",
            columnDefinition = "varchar(255) default 'NOT_SELECTED'")
    @Enumerated(EnumType.STRING)
    private NppaOperatingMode operatingMode;

    @Column(name = "tv_byn",
            columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private BaseProperty tvByn;

    @Column(name = "is_connected",
            columnDefinition = "boolean default 'false'",
            nullable = false)
    private boolean isConnected;

    @Column(name = "is_ncok",
            columnDefinition = "boolean default 'false'",
            nullable = false)
    private boolean isNcok;

    @Column(name = "is_rg_out_ncok",
            columnDefinition = "boolean default 'false'",
            nullable = false)
    private boolean isRgOutNcok;

    @Column(name = "is_buve_f2",
            columnDefinition = "boolean default 'false'",
            nullable = false)
    private boolean isBuveF2;

    @Column(name = "is_buve_f4",
            columnDefinition = "boolean default 'false'",
            nullable = false)
    private boolean isBuveF4;

    @Column(name = "is_basu_otr1_f3",
            columnDefinition = "boolean default 'false'",
            nullable = false)
    private boolean isBasuOtr1F3;

    @Column(name = "is_basu_otr2_f3",
            columnDefinition = "boolean default 'false'",
            nullable = false)
    private boolean isBasuOtr2F3;

    @Column(name = "is_f1",
            columnDefinition = "boolean default 'false'",
            nullable = false)
    private boolean isF1;

    @Column(name = "is_f2",
            columnDefinition = "boolean default 'false'",
            nullable = false)
    private boolean isF2;

    @Column(name = "is_f3",
            columnDefinition = "boolean default 'false'",
            nullable = false)
    private boolean isF3;

    @Column(name = "is_f4",
            columnDefinition = "boolean default 'false'",
            nullable = false)
    private boolean isF4;

    @Column(name = "is_f5",
            columnDefinition = "boolean default 'false'",
            nullable = false)
    private boolean isF5;

    @Column(name = "is_nppa_connected",
            columnDefinition = "boolean default 'false'",
            nullable = false)
    private boolean isNppaConnected;

    @Column(name = "is_basu_otr1_connected",
            columnDefinition = "boolean default 'false'",
            nullable = false)
    private boolean isBasuOtr1Connected;

    @Column(name = "is_basu_otr2_connected",
            columnDefinition = "boolean default 'false'",
            nullable = false)
    private boolean isBasuOtr2Connected;

    @PrePersist
    void prePersist() {
        updatedAt = LocalDateTime.now();
        systemStatus = systemStatus == null ? AdjacentSystemStatus.UNDEFINED : systemStatus;
        operatingMode = operatingMode == null ? NppaOperatingMode.NOT_SELECTED : operatingMode;
        tvByn = tvByn == null ? BaseProperty.UNDEFINED : tvByn;
    }

    @Override
    public String toString() {
        return "BynDao{" +
               "id=" + id +
               ", adjacentSystemType=" + adjacentSystemType +
               ", updatedAt=" + updatedAt +
               ", systemStatus=" + systemStatus +
               ", operatingMode=" + operatingMode +
               ", tvByn=" + tvByn +
               ", isConnected=" + isConnected +
               ", isNcok=" + isNcok +
               ", isRgOutNcok=" + isRgOutNcok +
               ", isBuveF2=" + isBuveF2 +
               ", isBuveF4=" + isBuveF4 +
               ", isBasuOtr1F3=" + isBasuOtr1F3 +
               ", isBasuOtr2F3=" + isBasuOtr2F3 +
               ", isF1=" + isF1 +
               ", isF2=" + isF2 +
               ", isF3=" + isF3 +
               ", isF4=" + isF4 +
               ", isF5=" + isF5 +
               ", isNppaConnected=" + isNppaConnected +
               ", isBasuOtr1Connected=" + isBasuOtr1Connected +
               ", isBasuOtr2Connected=" + isBasuOtr2Connected +
               '}';
    }
}
