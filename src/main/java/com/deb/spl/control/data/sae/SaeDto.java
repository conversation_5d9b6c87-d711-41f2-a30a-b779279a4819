package com.deb.spl.control.data.sae;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.VoltageStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.HashMap;

import static com.deb.spl.control.data.sae.Sae.getStringSaeFeederStatusHashMap;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@JsonPropertyOrder({"saeStatus",
        "readiness",
        "energizedByAdj",
        "energizedByHds",
        "energizedByExternalPowerSource",
        "adjStart",
        "adjStop",
        "adjLock",
        "adjUnlock",
        "hdsStatus",
        "voltageStatus",
        "externalPowerSourceVoltage",
        "feeder1Status",
        "feeder2Status",
        "feeder3Status",
        "feeder4Status",
        "feeder5Status",
        "feeder6Status",
        "feederNppa1Status",
        "bsvoltage"})
public class SaeDto {
    @NotNull
    private SaeStatus saeStatus;
    @NotNull
    private AdjacentSystemStatus readiness;
    @NotNull
    private SaePowerSourceStatus energizedByAdj;
    @NotNull
    private SaePowerSourceStatus energizedByHds;
    @NotNull
    private SaePowerSourceStatus energizedByExternalPowerSource;
    @NotNull
    private ADJstatus adjStart;
    @NotNull
    private ADJstatus adjStop;
    @NotNull
    private ADJstatus adjLock;
    @NotNull
    private ADJstatus adjUnlock;
    @NotNull
    private HDSstatus hdsStatus;
    @NotNull
    private VoltageStatus voltageStatus;
    @NotNull
    private VoltageStatus externalPowerSourceVoltage;
    @NotNull
    private SaeFeederStatus feeder1Status;
    @NotNull
    private SaeFeederStatus feeder2Status;
    @NotNull
    private SaeFeederStatus feeder3Status;
    @NotNull
    private SaeFeederStatus feeder4Status;
    @NotNull
    private SaeFeederStatus feeder5Status;
    @NotNull
    private SaeFeederStatus feeder6Status;
    @NotNull
    private SaeFeederStatus feederNppa1Status;
    @NotNull
    private VoltageStatus BSVoltage;

    public HashMap<String, SaeFeederStatus> listFeeders() {
        return getStringSaeFeederStatusHashMap(feeder1Status, feeder2Status, feeder3Status, feeder4Status,
                feeder5Status, feeder6Status, feederNppa1Status);
    }

    @JsonIgnore
    public boolean isConnected() {
        return this.saeStatus != SaeStatus.NOT_CONNECTED;
    }


}
