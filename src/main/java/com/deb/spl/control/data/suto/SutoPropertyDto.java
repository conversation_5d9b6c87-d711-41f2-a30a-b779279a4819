package com.deb.spl.control.data.suto;

import com.deb.spl.control.data.PropertyType;
import com.deb.spl.control.data.sae.CommandState;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
@JsonPropertyOrder({"id", "name", "state", "propertyType"})
public record SutoPropertyDto(
        @NotNull
        Long id,
        @NotBlank
        String name,
        @NotNull
        CommandState state,
        @NotNull
        PropertyType propertyType
) {
}
