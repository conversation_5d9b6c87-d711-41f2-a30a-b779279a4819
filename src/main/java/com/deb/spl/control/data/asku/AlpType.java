package com.deb.spl.control.data.asku;

import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;

import java.util.Arrays;
import java.util.Optional;

public enum AlpType implements AdjacentSystemWithDescriptionUa {
    NO_ALP("Без АХП"),
    FOUR_ALP("З чотирма АХП"),
    TWO_ALP("З двома АХП"),
    UNDEFINED("Невідомо");

    private final String value;

    AlpType(String value) {
        this.value = value;
    }

    @Override
    public String getValueUa() {
        return value;
    }

    public static AlpType fromValueUa(String uaVal) {
        Optional<AlpType> status = Arrays.stream(AlpType.values())
                .filter(e -> e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(UNDEFINED);
    }
}
