package com.deb.spl.control.data.nppa;

import com.deb.spl.control.data.asku.LaunchInitialDataMapper;
import com.deb.spl.control.data.asku.RocketFormDataMapper;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring",
        uses = {LaunchInitialDataMapper.class, RocketFormDataMapper.class, NcokMapper.class, BynMapper.class})
public interface NppaMapper {
    Nppa map(NppaDto value);

    NppaDto toDto(Nppa value);

}
