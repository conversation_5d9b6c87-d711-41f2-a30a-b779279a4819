package com.deb.spl.control.data.sae;

import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;

import java.util.Arrays;
import java.util.Optional;

public enum HDSstatus implements AdjacentSystemWithDescriptionUa {
    OK("Норма"),
    ERROR("Не норма"),
    NOT_ACTIVE("Відключено");

    private String sourceName;

    HDSstatus(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getValueUa() {
        return sourceName;
    }
    public static HDSstatus fromValueUa(String uaVal){
        Optional<HDSstatus> status= Arrays.stream(HDSstatus.values())
                .filter(e->e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(ERROR);
    }
}
