package com.deb.spl.control.data.nppa;

import com.deb.spl.control.data.AdjacentSystemStatus;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.*;
import java.time.LocalDateTime;

@Slf4j
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ncok")
public class NcokDao {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "readiness_started_at")
    private LocalDateTime startedDate;
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "system_status", columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private AdjacentSystemStatus systemStatus;

    @Column(name = "operating_mode",
            columnDefinition = "varchar(255) default 'NOT_SELECTED'")
    @Enumerated(EnumType.STRING)
    private NppaOperatingMode operatingMode;

    @Column(name = "tv_ncok",
            columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private BaseProperty tvNcok;

    @Column(name = "is_ncok_connected",
            columnDefinition = "boolean default 'false'",
            nullable = false)
    private boolean isNcokConnected = false;

    @Column(name = "is_suto_connected",
            columnDefinition = "boolean default 'false'",
            nullable = false)
    private boolean isSutoConnected = false;

    @Column(name = "nppa_test_result",
            columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private BaseProperty nppaTestResult;

    @Column(name = "app_presence",
            columnDefinition = "boolean default 'false'",
            nullable = false)
    private boolean appPresence = false;

    @Column(name = "otr1_app_presence",
            columnDefinition = "boolean default 'false'",
            nullable = false)
    private boolean otr1AppPresence = false;

    @Column(name = "otr2_app_presence",
            columnDefinition = "boolean default 'false'",
            nullable = false)
    private boolean otr2AppPresence = false;

    @Column(name = "otr1_test_result",
            columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private BaseProperty otr1TestResult;

    @Column(name = "otr2_test_result",
            columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private BaseProperty otr2TestResult;

    @Column(name = "is_otr1_lunched",
            columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private BaseProperty isOtr1Lunched;

    @Column(name = "is_otr2_lunched", columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private BaseProperty isOtr2Lunched;

    @Column(name = "otr1_bins_initial_setup",
            columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private BaseProperty otr1BinsInitialSetup;

    @Column(name = "otr2_bins_initial_setup", columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private BaseProperty otr2BinsInitialSetup;

    @Column(name = "otr_bins_precise_setup", columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private BaseProperty otrBinsPreciseSetup;

    @Column(name = "otr1_tv_no_ins", columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private BaseProperty otr1tvNoIns;

    @Column(name = "otr2_tv_no_ins", columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private BaseProperty otr2tvNoIns;

    @Column(name = "otr1_bas_sns_pz", columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private BasSnsStatus otr1BasSnsPz;

    @Column(name = "otr2_bas_sns_pz", columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private BasSnsStatus otr2BasSnsPz;

    @PrePersist
    void prePersist() {
        updatedAt = LocalDateTime.now();
        systemStatus = systemStatus == null ? AdjacentSystemStatus.UNDEFINED : systemStatus;
        operatingMode = operatingMode == null ? NppaOperatingMode.NOT_SELECTED : operatingMode;
        tvNcok = tvNcok == null ? BaseProperty.UNDEFINED : tvNcok;
        nppaTestResult = nppaTestResult == null ? BaseProperty.UNDEFINED : nppaTestResult;
        otr1TestResult = otr1TestResult == null ? BaseProperty.UNDEFINED : otr1TestResult;
        otr2TestResult = otr2TestResult == null ? BaseProperty.UNDEFINED : otr2TestResult;
        isOtr1Lunched = isOtr1Lunched == null ? BaseProperty.UNDEFINED : isOtr1Lunched;
        isOtr2Lunched = isOtr2Lunched == null ? BaseProperty.UNDEFINED : isOtr2Lunched;
        otr1BinsInitialSetup = otr1BinsInitialSetup == null ? BaseProperty.UNDEFINED : otr1BinsInitialSetup;
        otr2BinsInitialSetup = otr2BinsInitialSetup == null ? BaseProperty.UNDEFINED : otr2BinsInitialSetup;
        otrBinsPreciseSetup = otrBinsPreciseSetup == null ? BaseProperty.UNDEFINED : otrBinsPreciseSetup;
        otr1tvNoIns = otr1tvNoIns == null ? BaseProperty.UNDEFINED : otr1tvNoIns;
        otr2tvNoIns = otr2tvNoIns == null ? BaseProperty.UNDEFINED : otr2tvNoIns;
        otr1BasSnsPz = otr1BasSnsPz == null ? BasSnsStatus.UNDEFINED : otr1BasSnsPz;
        otr2BasSnsPz = otr2BasSnsPz == null ? BasSnsStatus.UNDEFINED : otr2BasSnsPz;
    }

    @Override
    public String toString() {
        return "NcokDao{" +
               "id=" + id +
               ", startedDate=" + startedDate +
               ", updatedAt=" + updatedAt +
               ", systemStatus=" + systemStatus +
               ", operatingMode=" + operatingMode +
               ", tvNcok=" + tvNcok +
               ", isNcokConnected=" + isNcokConnected +
               ", isSutoConnected=" + isSutoConnected +
               ", nppaTestResult=" + nppaTestResult +
               ", appPresence=" + appPresence +
               ", otr1AppPresence=" + otr1AppPresence +
               ", otr2AppPresence=" + otr2AppPresence +
               ", otr1TestResult=" + otr1TestResult +
               ", otr2TestResult=" + otr2TestResult +
               ", isOtr1Lunched=" + isOtr1Lunched +
               ", isOtr2Lunched=" + isOtr2Lunched +
               ", otr1BinsInitialSetup=" + otr1BinsInitialSetup +
               ", otr2BinsInitialSetup=" + otr2BinsInitialSetup +
               ", otrBinsPreciseSetup=" + otrBinsPreciseSetup +
               ", otr1tvNoIns=" + otr1tvNoIns +
               ", otr2tvNoIns=" + otr2tvNoIns +
               ", otr1BasSnsPz=" + otr1BasSnsPz +
               ", otr2BasSnsPz=" + otr2BasSnsPz +
               '}';
    }
}
