package com.deb.spl.control.data.ppo;

import com.deb.spl.control.controller.BadRequestException;
import com.deb.spl.control.controller.NotFoundException;
import com.deb.spl.control.controller.ServerErrorException;
import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
@Builder
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
public class Ppo implements AdjacentSystem, Serializable {
    private static final long serialVersionUID = -6906554509713911399L;

    @Builder.Default
    private AdjacentSystemStatus status = AdjacentSystemStatus.UNDEFINED;

    @JsonIgnore
    private final AdjacentSystemType adjacentSystemType = AdjacentSystemType.PPO;

    private Map<PpoBayType, PpoBay> bays;

    public static Ppo copy(@NotNull Ppo original){
        Ppo copyOfPpo;
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            copyOfPpo = objectMapper.readValue(objectMapper.writeValueAsString(original), Ppo.class);
        }catch (JsonProcessingException e){
            log.error(e.getMessage() +" \n"+ Arrays.toString(e.getStackTrace()));
            throw new ServerErrorException(e.getMessage());
        }
        return copyOfPpo;
    }

    public List<PpoBay> update(List<PpoBay> bayList) {
        return bayList.stream()
                .filter(Objects::nonNull)
                .map(this::updateWithBay)
                .collect(Collectors.toList());
    }

    public Ppo update(@NotNull Ppo newPpo) {
        for (Map.Entry<PpoBayType, PpoBay> bay : newPpo.getBays().entrySet()) {
            if (bay.getKey() != null && bay.getValue() != null) {
                updateWithBay(bay.getValue());
            }
        }
        return this;
    }

    public PpoBay updateWithBay(@NotNull PpoBay newBay) {
        PpoBay bayUnderUpdate = this.bays.get(newBay.getPpoBayType());
        if (bayUnderUpdate == null) {
            throw new NotFoundException("cant find bay of type " + newBay.getPpoBayType() + " for bay " + newBay);
        }

        if (newBay.getId() != null && !bayUnderUpdate.getId().equals(newBay.getId())) {
            throw new NotFoundException("cant find bay with id " + newBay.getId());
        }

        bayUnderUpdate.setFirePresent(newBay.isFirePresent());

        if (newBay.getUnitsInPPoBay() == null) {
            throw new BadRequestException("unsatisfied PpoUnits Set. PpoUnits set cant be null");
        }

        if (newBay.getUnitsInPPoBay().size() == 0) {
            throw new BadRequestException("unsatisfied PpoUnits Set. Provided PpoUnits set with size = 0" + newBay.toString());
        }

        for (PpoUnit newUnit : newBay.getUnitsInPPoBay().values()) {
            if (newUnit != null) {
                Optional<PpoUnit> foundUnit = bayUnderUpdate.getUnitsInPPoBay().values().stream()
                        .filter(oldUnit -> oldUnit.getUnitType() == newUnit.getUnitType() &&
                                oldUnit.getUnitName().equalsIgnoreCase(newUnit.getUnitName())).findFirst();

                if (foundUnit.isPresent()) {
                    updateUnitState(foundUnit.get(), newUnit.getUnitStatus());
                } else {
                    String errorMsg = "cant find PpoUnit  " + newUnit.toString() + " in bay " + bayUnderUpdate +
                            " requested with new unit " + newUnit;

                    log.error(errorMsg);
                    throw new NotFoundException(errorMsg);
                }
            }
        }

        return bayUnderUpdate;
    }

    public PpoUnit updateUnitState(@NotNull PpoUnit unit, @NotNull PpoUnitStatus ppoUnitStatus) {
        unit.setUnitStatus(ppoUnitStatus);

        return unit;
    }

    @JsonIgnore
    @Override
    public String getErrorMessage() {
        List<PpoUnit> unitsWithErrors = new ArrayList<>();
        for (PpoBay bay : bays.values()) {
            List<PpoUnit> unitInBay = bay.getUnitsWithState(PpoUnitStatus.ERROR);
            if (unitInBay != null) {
                unitsWithErrors.addAll(unitInBay);
            }
        }

        StringBuilder errorMassageBuilder;
        if (unitsWithErrors.size() > 0) {
            errorMassageBuilder = new StringBuilder("");
        } else {
            errorMassageBuilder = new StringBuilder("Помилок не знайдено");
        }
        for (PpoUnit unit : unitsWithErrors) {
            if (unit != null) {
                errorMassageBuilder
                        .append("Несправність у відсіку -").append(unit.getBayType())
                        .append("Пристрій -").append(unit.getUnitName()).append("\n");
            }
        }

        return errorMassageBuilder.toString();
    }

    @JsonIgnore
    @Override
    public boolean isMalfunctionPresent() {
        for (PpoBay bay : bays.values()) {
            if (bay.isMalfunctionPresent()) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Ppo ppo)) return false;

        if (getStatus() != ppo.getStatus()) return false;
        if (getAdjacentSystemType() != ppo.getAdjacentSystemType()) return false;
        if(getBays()!=null){
            if(ppo.getBays()==null){
                return false;
            }
            if (getBays().size()!=ppo.getBays().size()) return false;

            for(PpoBayType key:getBays().keySet()){
                if(ppo.getBays().get(key)==null) return false;

                if(!getBays().get(key).equals(ppo.getBays().get(key))) return false;
            }
            return true;
        }

        return ppo.getBays() == null;
    }

    @Override
    public int hashCode() {
        int result = getStatus() != null ? getStatus().hashCode() : 0;
        result = 31 * result + getAdjacentSystemType().hashCode();
        result = 31 * result + (getBays() != null ? getBays().hashCode() : 0);
        return result;
    }


}
