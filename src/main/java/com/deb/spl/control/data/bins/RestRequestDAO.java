package com.deb.spl.control.data.bins;

import com.deb.spl.control.views.adjacentSystems.utils.HasVerticalInfoAsList;
import com.deb.spl.control.data.AdjacentSystemType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Getter
@Setter
@Table(name = "spl_navigation_rest_log")
public class RestRequestDAO implements HasVerticalInfoAsList {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "spl_navigation_rest_log_id_seq")
    @SequenceGenerator(name = "spl_navigation_rest_log_id_seq",sequenceName = "spl_navigation_rest_log_id_seq",initialValue = 1000, allocationSize = 1)
    @Column(name = "id",nullable = false )
    private Long id;

    @Column(name = "generation_date")
    private LocalDateTime createdAt;

    @Column(name = "direction")
    @Enumerated(EnumType.ORDINAL)
    private LogRecordDirection direction;

    @Column(name = "adjacent_system", nullable = false)
    @Enumerated(EnumType.ORDINAL)
    private AdjacentSystemType adjacentSystem;

    @Column(name = "method")
    private String method;

    @Column(name = "url")
    private String url;

    @Column(name = "cached_payload", columnDefinition = "varchar(2048)")
    private String cachedPayload;

    @Column(name = "request_query_string")
    private String requestQueryString;

    @Column(name = "user_info")
    private String userInfo;

    @Transient
    @JsonIgnore
    private List<String> headers;

    @PrePersist
    void createdAt() {
        this.createdAt = LocalDateTime.now();
//        Postgres error on insert - ERROR: invalid byte sequence for encoding "UTF8": 0x00
//        https://stackoverflow.com/a/45810272
        if (cachedPayload!=null){
            cachedPayload= cachedPayload.replaceAll("\u0000", "");
        }
    }

    @Override
    public List<Pair> toVerticalInfoListUa() {
        List<Pair> infoList = new ArrayList<>();
        infoList.add(new Pair(0, "Речення {"));
        infoList.add(new Pair(1, "час створення - " + createdAt.format(DateTimeFormatter.ofPattern("dd/MM/yyyy\t HH:mm:ss"))));
        if (direction != null) {
            infoList.add(new Pair(1, "напрямок - " + direction.getCaptionUa()));
        }
        if (adjacentSystem != null) {
            infoList.add(new Pair(1, "система - " + adjacentSystem.getUa()));
        }
        infoList.add(new Pair(1, "URL - " + url));
        infoList.add(cachedPayload != null ? new Pair(1, "payload - " + cachedPayload) : new Pair(1, "payload - немає"));
        infoList.add(requestQueryString != null ? new Pair(1, "Request query string - " + requestQueryString) : new Pair(1, "Request query string - немає"));

        infoList.add(new Pair(0, "}"));
        return infoList;
    }

    @Override
    public String toString() {
        return "RestRequest{" +
                "id=" + id +
                ", createdAt=" + createdAt +
                ", direction=" + direction +
                ", adjacentSystem=" + adjacentSystem +
                ", method='" + method + '\'' +
                ", url='" + url + '\'' +
                ", cachedPayload='" + cachedPayload + '\'' +
                ", requestQueryString='" + requestQueryString + '\'' +
                ", headers=" + headers +
                '}';
    }

    public void addHeaders(String header) {
        if (headers == null) {
            headers = new ArrayList<>();
        }
        headers.add(header);
    }

    public boolean isConsiderable() {
        if(url == null || url.isBlank()) {
            return false;
        }
        return ((cachedPayload != null && cachedPayload.length() > 0) || (requestQueryString != null && !requestQueryString.isEmpty()));
    }
}
