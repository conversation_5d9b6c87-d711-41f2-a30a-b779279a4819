package com.deb.spl.control.data.asku;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nullable;
import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
@Slf4j
@NoArgsConstructor
@Entity(name = "rocket")
@Table(name = "rocket")
//@NamedEntityGraph(name = "RocketDao.details",
//        attributeNodes=@NamedAttributeNode("launchReferencePoints") )
public class RocketDao {
    private static final long serialVersionUID = -4792815919145364800L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "date_release_m")
    private LocalDateTime dateReleaseM;

    @Column
    private boolean technicalCondition;

    @Column(name = "date_use_m")
    private LocalDateTime dateUseM;

    @Nullable
    @OneToOne
    private RocketFormDataDao formData;

    @Nullable
    @OneToOne
    private LaunchInitialDataDao initialData;

    @Column(name = "loaded_to_plc", columnDefinition = "boolean default false")
    private boolean loadedToPlc;

    @Nullable
    @Column(name = "initial_data_time_stamp")
    private LocalDateTime initialDataTS;

    @Column(name = "initial_data_source", columnDefinition = "varchar(255) default 'UNKNOWN'")
    @Enumerated(EnumType.STRING)
    private LaunchInitialDataSource initialDataSource;

    @Column(name = "initial_datasource_description", columnDefinition = "varchar(255) default 'не вказан'")
    private String initialDataSourceDescription;

    @Column(name = "updated_at", nullable = false, columnDefinition = "TIMESTAMP DEFAULT now()")
    private LocalDateTime updatedAt;

    @Column(name = "launch_result", columnDefinition = "varchar(255) default 'UNKNOWN'")
    @Enumerated(EnumType.STRING)
    private LaunchResult launchResult;

    @ElementCollection(fetch = FetchType.EAGER)
    List<String> storedTlKeys = new ArrayList<>();

    @Column(name = "sensor_temperature", columnDefinition = "double precision default -999.9")
    private double sensorTemperature;

    @Embedded
    private LaunchResultDescription launchResultDescription;

    @OneToMany(mappedBy = "rocket", fetch = FetchType.LAZY)
    @Column(name = "launch_reference_points")
    private List<LaunchReferencePointRecord> launchReferencePoints;

    @PrePersist
    public void prepare() {
        updatedAt = LocalDateTime.now();
        initialDataSource = initialDataSource == null ? LaunchInitialDataSource.MSG : initialDataSource;
        initialDataSourceDescription = initialDataSourceDescription == null ? "не вказан" : initialDataSourceDescription;
        storedTlKeys = storedTlKeys == null ? new ArrayList<>() : storedTlKeys;
    }

    @Override
    public String toString() {
        return "RocketDao{" +
               "id=" + id +
               ", dateReleaseM=" + dateReleaseM +
               ", technicalCondition=" + technicalCondition +
               ", dateUseM=" + dateUseM +
               ", formData=" + formData +
               ", initialData=" + initialData +
               ", initialDataSource=" + initialDataSource +
               ", initialDataSourceDescription='" + initialDataSourceDescription +
               ", loadedToPlc='" + loadedToPlc + '\'' +
               ", storedTlKeys=" + storedTlKeys.stream().collect(Collectors.joining(";", "{", "}\n")) +
               ", sensorTemperature=" + sensorTemperature +
               ", launchResultCode " + (launchResultDescription != null ? launchResultDescription.getCode() : "") +
               ", launchResultDescription" + (launchResultDescription != null ? launchResultDescription.getDescription() : "") +
//               ", launchReferencePoints" + printLaunchReferencePoints() + // TODO: 12/26/2024  todo fix lazy load!!!!!!!!!!!!!!!!!!!!!!!

               '}';
    }

    private String printLaunchReferencePoints() {
        if (launchReferencePoints == null || launchReferencePoints.isEmpty()) {
            return "";
        }

        return launchReferencePoints.stream()
                .map(LaunchReferencePointRecord::toString)
                .collect(Collectors.joining(";", "[", "]\n"));
    }
}
