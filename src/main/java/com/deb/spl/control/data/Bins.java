package com.deb.spl.control.data;


import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.sf.marineapi.nmea.util.BinsCalibrationStatus;
import net.sf.marineapi.nmea.util.BinsGpsStatus;
import net.sf.marineapi.nmea.util.BinsNavigationTaskStatus;
import net.sf.marineapi.nmea.util.Position;
import org.apache.commons.lang3.NotImplementedException;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Map;
import java.util.Objects;
import java.util.WeakHashMap;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Bins implements AdjacentSystem {

    private static final long serialVersionUID = -9087251661915309371L;

    @JsonIgnore
    private final AdjacentSystemType adjacentSystemType = AdjacentSystemType.BINS;

    Position position;
    Double speed;
    Double course;
    /**
     * Get the corrected course over ground. Correction is done by subtracting
     * or adding the magnetic variation from true course (easterly variation
     * subtracted and westerly added).
     *
     * @return Corrected true course
     * @throws net.sf.marineapi.nmea.parser.DataNotAvailableException If course or
     * variation data is not available.
     * @throws net.sf.marineapi.nmea.parser.ParseException If course or variation
     * field contains unexpected or illegal value.
     * @see net.sf.marineapi.nmea.sentence.RMCSentence.getCorrectedCourse
     */
    Double correctedCourse;
    LocalDate gpsDate;
    LocalTime gpsTime;

    //PMP
    Double latitudePrecision;
    Double longitudePrecision;
    Double altitudePrecision;
    BinsNavigationTaskStatus navigationTaskStatus;
    BinsGpsStatus binsGpsStatus;

    //HPR
    Double headingAzimuth;
    Double tiltToStarboard;
    Double trimHeadIsBottom;
    BinsCalibrationStatus binsCalibrationStatus;

    public Position getPosition() {
        return Objects.requireNonNullElse(position, new Position(0.0, 0.0, 0.0));
    }

    public Double getSpeed() {
        return Objects.requireNonNullElse(speed, 0.0);
    }

    public Double getCourse() {
        return Objects.requireNonNullElse(course, 0.0);
    }

    public Double getCorrectedCourse() {
        return Objects.requireNonNullElse(correctedCourse, 0.0);
    }

    /**
     * @return gps date or LocalDate.MIN if the gps date os null
     */
    public LocalDate getGpsDate() {
        return Objects.requireNonNullElse(gpsDate, LocalDate.MIN);
    }

    /**
     * @return gps time or LocalTime.MIN if the gps time os null
     */
    public LocalTime getGpsTime() {
        return Objects.requireNonNullElse(gpsTime, LocalTime.MIN);
    }

    public Double getLatitudePrecision() {
        return Objects.requireNonNullElse(latitudePrecision, 0.0);
    }

    public Double getLongitudePrecision() {
        return Objects.requireNonNullElse(longitudePrecision, 0.0);
    }

    public Double getAltitudePrecision() {
        return Objects.requireNonNullElse(altitudePrecision, 0.0);
    }

    public BinsNavigationTaskStatus getNavigationTaskStatus() {
        return Objects.requireNonNullElse(navigationTaskStatus, BinsNavigationTaskStatus.NOT_RESOLVED);
    }

    public BinsGpsStatus getBinsGpsStatus() {
        return Objects.requireNonNullElse(binsGpsStatus, BinsGpsStatus.NON_RELIABLE);
    }

    public Double getHeadingAzimuth() {
        return Objects.requireNonNullElse(headingAzimuth, 0.0);
    }

    public Double getTiltToStarboard() {
        return Objects.requireNonNullElse(tiltToStarboard, 0.0);
    }

    public Double getTrimHeadIsBottom() {
        return Objects.requireNonNullElse(trimHeadIsBottom, 0.0);
    }

    public BinsCalibrationStatus getBinsCalibrationStatus() {
        return Objects.requireNonNullElse(binsCalibrationStatus, BinsCalibrationStatus.RESOLVE_NOT_CALCULATED);
    }

    public Double getSpeedInKmh() {
        return getSpeed() * 1.852;
    }

    public void setSpeedFromKmh(Double speedInKmh) {
        this.speed = speedInKmh * 0.539957;
    }

    @Override
    public String getErrorMessage() {
        if (binsCalibrationStatus == BinsCalibrationStatus.RESOLVE_IS_CALCULATED_WITHOUT_PROOF_OF_RELIABLE) {
            return ("рішення не розраховане");
        } else {
            return "";
        }
    }

    @Override
    public boolean isMalfunctionPresent() {
        throw new NotImplementedException(this.getClass().getName());
    }

    @Override
    public AdjacentSystemStatus getStatus() {
        if (binsCalibrationStatus == null) {
            return AdjacentSystemStatus.UNDEFINED;
        }

        switch (binsCalibrationStatus) {
            case RELIABLE -> {
                return AdjacentSystemStatus.OK;
            }
            case RESOLVE_IS_CALCULATED_WITHOUT_PROOF_OF_RELIABLE, AUTOMATIC_CALIBRATION_IN_PROGRESS -> {
                return AdjacentSystemStatus.WARNING;
            }
            case RESOLVE_NOT_CALCULATED -> {
                return AdjacentSystemStatus.ERROR;
            }
            default -> {
                return AdjacentSystemStatus.UNDEFINED;
            }
        }
    }

    public String getBinsStatusAsString() {
        Map<BinsCalibrationStatus, String> binsStatuses = new WeakHashMap<>();
        binsStatuses.put(BinsCalibrationStatus.RELIABLE, "Дані достовірні");
        binsStatuses.put(BinsCalibrationStatus.RESOLVE_IS_CALCULATED_WITHOUT_PROOF_OF_RELIABLE,
                "є рішення, достовірність не підтверджена");
        binsStatuses.put(BinsCalibrationStatus.AUTOMATIC_CALIBRATION_IN_PROGRESS, "виконується автоматичне калібрування");
        binsStatuses.put(BinsCalibrationStatus.RESOLVE_IS_CALCULATED_WITHOUT_PROOF_OF_RELIABLE, "рішення не розраховане");

        if (binsCalibrationStatus == null) {
            return "";
        }
        return binsStatuses.get(binsCalibrationStatus);
    }

}
