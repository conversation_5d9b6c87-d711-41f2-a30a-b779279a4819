package com.deb.spl.control.data.nppa;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonPropertyOrder({"systemStatus",
        "operatingMode",
        "tvByn",
        "isConnected",
        "isNcok",
        "isRgOutNcok",
        "isBuveF2",
        "isBuveF4",
        "isBasuOtr1F3",
        "isBasuOtr2F3",
        "isF1",
        "isF2",
        "isF3",
        "isF4",
        "isF5",
        "isNppaConnected",
        "isBasuOtr1Connected",
        "isBasuOtr2Connected"})
public record BynDto(
        AdjacentSystemStatus systemStatus,
        NppaOperatingMode operatingMode,
        BaseProperty tvByn,
        boolean isConnected,
        boolean isNcok,
        boolean isRgOutNcok,
        boolean isBuveF2,
        boolean isBuveF4,
        boolean isBasuOtr1F3,
        boolean isBasuOtr2F3,
        boolean isF1,
        boolean isF2,
        boolean isF3,
        boolean isF4,
        boolean isF5,
        boolean isNppaConnected,
        boolean isBasuOtr1Connected,
        boolean isBasuOtr2Connected) {
}
