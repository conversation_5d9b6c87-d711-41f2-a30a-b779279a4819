package com.deb.spl.control.data.ppo;

import com.deb.spl.control.data.sae.CommandState;
import com.deb.spl.control.data.AdjacentSystemCommand;
import com.deb.spl.control.data.AdjacentSystemType;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;


@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(name = "ppo_record")
public class PpoCommand extends AdjacentSystemCommand {
    @Builder
    public PpoCommand(Long id,
                      @NotNull String command,
                      @NotNull String caption,
                      CommandState commandState,
                      @NotNull AdjacentSystemType adjacentSystem,
                      LocalDateTime generationTime,
                      LocalDateTime executionTime,
                      String originator) {
        super(id, command, caption, commandState, adjacentSystem, generationTime, executionTime, originator);
    }
}
