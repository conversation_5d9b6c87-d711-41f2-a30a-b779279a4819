package com.deb.spl.control.data.asku;

import lombok.*;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.PrePersist;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;


@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class RocketFormData {
    private static final long serialVersionUID = 2928410439986221700L;

    //id
    private Long id;

    private LocalDateTime createdAt;

    //    Заводський номер ОТР
    @NotBlank
    @Size(min = 1, max = 8)
    private String plantMissile;

    //    Ознака БЧ
    @NotNull
    private WarheadType warhead;

    //    Ознака ГСН
    @NotNull
    private GsnType gsnType;

    //    Ознака АЛП
    @Enumerated(EnumType.STRING)
    @NotNull
    private AlpType alpType;

    //    Ознака СВ
    @NotNull
    private boolean telemetryIntegrated;

    //    Призначення ОТР
    @NotNull
    private OtrPurposeType purposeType;

    //    архивная(не активная) запись
    private boolean isArchived;

    //    Контрольная сумма
    private String crc;
    // TODO: 18.07.2023 add list of keys from tlc

    @PrePersist
    void createdAt() {
        this.createdAt = LocalDateTime.now();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof RocketFormData that)) return false;

        if (isTelemetryIntegrated() != that.isTelemetryIntegrated()) return false;
        if (isArchived() != that.isArchived()) return false;
        if (getPlantMissile() != null ? !getPlantMissile().equals(that.getPlantMissile()) : that.getPlantMissile() != null)
            return false;
        if (getWarhead() != that.getWarhead()) return false;
        if (getGsnType() != that.getGsnType()) return false;
        if (getAlpType() != that.getAlpType()) return false;
        if (getPurposeType() != that.getPurposeType()) return false;
        return getCrc() != null ? getCrc().equals(that.getCrc()) : that.getCrc() == null;
    }

    @Override
    public int hashCode() {

        int result = getPlantMissile() != null ? getPlantMissile().hashCode() : 0;
        result = 31 * result + (getWarhead() != null ? getWarhead().hashCode() : 0);
        result = 31 * result + (getGsnType() != null ? getGsnType().hashCode() : 0);
        result = 31 * result + (getAlpType() != null ? getAlpType().hashCode() : 0);
        result = 31 * result + (isTelemetryIntegrated() ? 1 : 0);
        result = 31 * result + (getPurposeType() != null ? getPurposeType().hashCode() : 0);
        result = 31 * result + (isArchived() ? 1 : 0);
        result = 31 * result + (getCrc() != null ? getCrc().hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "RocketFormData{" +
               "id=" + id +
               ", createdAt=" + createdAt +
               ", plantMissile='" + plantMissile + '\'' +
               ", warhead=" + warhead +
               ", gsnType=" + gsnType +
               ", alpType=" + alpType +
               ", isTelemetryIntegrated=" + telemetryIntegrated +
               ", purposeType=" + purposeType +
               ", iArchived=" + isArchived +
               '}';
    }
}
