package com.deb.spl.control.data.suto;

import com.deb.spl.control.data.suto.SutoStats;
import com.deb.spl.control.data.suto.SutoStatsDto;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public abstract class SutoStatsToDtoMapper {
    public SutoStatsDto toDto(SutoStats value) {
        if (value == null) {
            return null;
        }

        int armLiftStrokePosition = 0;
        int levelingCyclesCount = 0;
        double pressureInImpulseSection = 0.0d;
        int temperatureRR = 0;
        int workingFluidLevel = 0;
        int mainPumpRPM = 0;
        int overallOperatingTime = 0;
        int overallArmLiftingsCount = 0;

        armLiftStrokePosition = value.getArmLiftStrokePosition();
        levelingCyclesCount = value.getLevelingCyclesCount();
        pressureInImpulseSection = value.getPressureInImpulseSection();
        temperatureRR = value.getTemperatureRR();
        workingFluidLevel = value.getWorkingFluidLevel();
        mainPumpRPM = value.getMainPumpRPM();
        overallOperatingTime = value.getOverallOperatingTime();
        overallArmLiftingsCount = value.getOverallArmLiftingsCount();

        int pitch = toInt(value.getPitch());
        int roll = toInt(value.getRoll());

        SutoStatsDto sutoStatsDto = new SutoStatsDto(roll, pitch, armLiftStrokePosition, levelingCyclesCount, pressureInImpulseSection, temperatureRR, workingFluidLevel, mainPumpRPM, overallOperatingTime, overallArmLiftingsCount);

        return sutoStatsDto;
    }

    public SutoStats map(SutoStatsDto value) {
        if (value == null) {
            return null;
        }

        SutoStats.SutoStatsBuilder sutoStats = SutoStats.builder();

        sutoStats.armLiftStrokePosition(value.armLiftStrokePosition());
        sutoStats.levelingCyclesCount(value.levelingCyclesCount());
        sutoStats.pressureInImpulseSection(value.pressureInImpulseSection());
        sutoStats.temperatureRR(value.temperatureRR());
        sutoStats.workingFluidLevel(value.workingFluidLevel());
        sutoStats.mainPumpRPM(value.mainPumpRPM());
        sutoStats.overallOperatingTime(value.overallOperatingTime());
        sutoStats.overallArmLiftingsCount(value.overallArmLiftingsCount());

        sutoStats.pitch(toDouble(value.pitch()));
        sutoStats.roll(toDouble(value.roll()));

        return sutoStats.build();
    }

    private final int multiplier = 10;

    private double toDouble(int value) {
        return (double) value / (double) multiplier;
    }

    private int toInt(double value) {
        return (int) (value * 10);
    }
}
