package com.deb.spl.control.data.asku;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Getter
@Setter
@Builder
@Slf4j
@ToString
public class Rocket {
    private static final long serialVersionUID = -4792815919145364800L;

    private Long id;
    private boolean technicalCondition;
    private LocalDateTime dateUseM;
    private RocketFormData formData;
    private LaunchInitialData initialData;
    private boolean loadedToPlc;
    private LocalDateTime initialDataTS;
    private LaunchInitialDataSource initialDataSource;
    private String initialDataSourceDescription;
    private LaunchResult launchResult;
    private List<String> storedTlKeys;
    private double sensorTemperature;
    private LaunchResultDescription launchResultDescription;



    public Optional<String> getPlantMissile() {
        if (formData == null) {
            return Optional.empty();
        }

        return Optional.ofNullable(getFormData().getPlantMissile());
    }

    public void setInitialDataSource(LaunchInitialDataSource value) {
        try {
            this.initialDataSource = value;
        } catch (Exception e) {
            log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
        }
    }

    public void setStoredTlKeys(@NotNull List<String> keys) {
        storedTlKeys.removeIf(storedKey -> !keys.contains(storedKey));

        for (String key : keys) {
            if (!storedTlKeys.contains(key)) {
                storedTlKeys.add(key);
            }
        }
    }

    public Optional<RocketFormData> getRocketFormData() {
        return Optional.ofNullable(formData);
    }

    public Optional<WarheadType> getWarheadType() {
        return formData != null ? Optional.of(formData.getWarhead()) : Optional.empty();
    }

    public void setRocketFormData(@NotNull @Valid RocketFormData data) {
        if (formData == null) {
            formData = data;
        } else if (!formData.equals(data)) {
            formData = data;
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Rocket rocket)) return false;
        if (getSensorTemperature() != rocket.getSensorTemperature()) return false;
        if (isTechnicalCondition() != rocket.isTechnicalCondition()) return false;
        if (getDateUseM() != null ? !getDateUseM().equals(rocket.getDateUseM()) : rocket.getDateUseM() != null)
            return false;
        if (getFormData() != null ? !getFormData().equals(rocket.getFormData()) : rocket.getFormData() != null)
            return false;
        if (getInitialData() != null ? !getInitialData().equals(rocket.getInitialData()) : rocket.getInitialData() != null)
            return false;
        if (isLoadedToPlc() != rocket.isLoadedToPlc()) return false;
        if (getInitialDataSource() != rocket.getInitialDataSource()) return false;
        if (getInitialDataSourceDescription() != null ? !getInitialDataSourceDescription().equals(rocket.getInitialDataSourceDescription()) : rocket.getInitialDataSourceDescription() != null)
            return false;
        if (getInitialDataTS() == null && rocket.getInitialDataTS() != null)
            return false;
        if (getInitialDataTS() != null && rocket.getInitialDataTS() == null)
            return false;
        if (getInitialDataTS() != null && !getInitialDataTS().withNano(0).equals(rocket.getInitialDataTS().withNano(0)))
            return false;

        if (getLaunchResult() != null ? !getLaunchResult().equals(rocket.getLaunchResult()) : rocket.getLaunchResult() != null)
            return false;

        if ((getStoredTlKeys() == null && rocket.getStoredTlKeys() != null) ||
            (getStoredTlKeys() != null && rocket.getStoredTlKeys() == null)) {
            return false;
        }

        if (getLaunchResultDescription() != null ? !getLaunchResultDescription().equals(rocket.getLaunchResultDescription()) : rocket.getLaunchResultDescription() != null)
            return false;

        assert getStoredTlKeys() != null;
        return getStoredTlKeys().size() == rocket.getStoredTlKeys().size() &&
               getStoredTlKeys().stream().filter(e -> !rocket.getStoredTlKeys().contains(e)).count() == 0;
    }

    @Override
    public int hashCode() {
        long temp;
        int result = (isTechnicalCondition() ? 1 : 0);
        temp = Double.doubleToLongBits(getSensorTemperature());
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        result = 31 * result + (getDateUseM() != null ? getDateUseM().hashCode() : 0);
        result = 31 * result + (getFormData() != null ? getFormData().hashCode() : 0);
        result = 31 * result + (getInitialData() != null ? getInitialData().hashCode() : 0);
        result = 31 * result + (isLoadedToPlc() ? 1 : 0);
        result = 31 * result + (getInitialDataSource() != null ? getInitialDataSource().hashCode() : 0);
        result = 31 * result + (getInitialDataTS() != null ? getInitialDataTS().hashCode() : 0);
        result = 31 * result + (getInitialDataSourceDescription() != null ? getInitialDataSourceDescription().hashCode() : 0);
        result = 31 * result + (getLaunchResult() != null ? getLaunchResult().getDeclaringClass().hashCode() : 0);
        result = 31 * result + (getLaunchResultDescription() != null ? getLaunchResultDescription().hashCode() : 0);
        if (getStoredTlKeys() != null) {
            for (String key : getStoredTlKeys()) {
                result = 31 * result + key.hashCode();
            }
        } else {
            result = 31 * result;
        }
        return result;
    }
}
