package com.deb.spl.control.data.suto;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.StringListConverter;
import lombok.*;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "suto")
public class SutoDao {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "system_status",columnDefinition = "varchar(50) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private AdjacentSystemStatus status;

    @Embedded
    private SutoStats stats;

    @Enumerated(EnumType.STRING)
    @Column(name = "left_front_outrigger_emergency_code",columnDefinition = "varchar(50) default 'NONE'")
    private OutriggerEmergencyCode leftFrontOutriggerEmergencyCode;
    @Enumerated(EnumType.STRING)
    @Column(name = "right_front_outrigger_emergency_code",columnDefinition = "varchar(255) default 'NONE'")
    private OutriggerEmergencyCode rightFrontOutriggerEmergencyCode;
    @Enumerated(EnumType.STRING)
    @Column(name = "left_rear_outrigger_emergency_code",columnDefinition = "varchar(255) default 'NONE'")
    private OutriggerEmergencyCode leftRearOutriggerEmergencyCode;
    @Enumerated(EnumType.STRING)
    @Column(name = "right_rear_outrigger_emergency_code",columnDefinition = "varchar(255) default 'NONE'")
    private OutriggerEmergencyCode rightRearOutriggerEmergencyCode;

    @OneToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "suto_to_suto_properties",
            joinColumns = {@JoinColumn(name = "suto_id", referencedColumnName = "id")})
    @MapKey(name = "name")
    Map<String, SutoPropertyDao> properties;

    @Embedded
    private SutoSettings sutoSettings;

    @Column(name = "properties_state", columnDefinition = "varchar(4096)")
    @Convert(converter = StringListConverter.class)
    private List<SutoProperty> logPropertiesState;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @PrePersist
    void prePersist() {
        updatedAt = LocalDateTime.now();
    }

}
