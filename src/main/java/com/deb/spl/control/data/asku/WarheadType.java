package com.deb.spl.control.data.asku;


import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;

import java.util.Arrays;
import java.util.Optional;

public enum WarheadType implements AdjacentSystemWithDescriptionUa{
    <PERSON><PERSON>CH("М<PERSON><PERSON><PERSON>"),
    CBCH("КБЧ"),
    UNDEFINED("Невідомо");

    private final String value;

    WarheadType(String value) {
        this.value = value;
    }

    @Override
    public String getValueUa() {
        return value;
    }

    public static WarheadType fromValueUa(String uaVal) {
        Optional<WarheadType> status = Arrays.stream(WarheadType.values())
                .filter(e -> e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(UNDEFINED);
    }
}
