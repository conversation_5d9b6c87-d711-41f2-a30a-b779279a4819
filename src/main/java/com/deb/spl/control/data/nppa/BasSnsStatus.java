package com.deb.spl.control.data.nppa;

import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

public enum BasSnsStatus implements AdjacentSystemWithDescriptionUa {
    OK("Задачу вирішено"),
    WARNING("ТВ норма"),
    ERROR("Рішення відсутнє"),
    NOT_CONNECTED("Немає зв'язку"),
    UNDEFINED("Стан невідомий");

    @Getter
    private String description;

    BasSnsStatus(String description) {
        this.description = description;
    }

    @Override
    public String getValueUa() {
        return description;
    }

    public static BasSnsStatus fromValueUa(String uaVal) {
        Optional<BasSnsStatus> status = Arrays.stream(BasSnsStatus.values())
                .filter(e -> e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(UNDEFINED);
    }
}
