package com.deb.spl.control.data.asku;

import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

public enum TrajectoryType implements AdjacentSystemWithDescriptionUa {
    BALLISTIC("Балістична"),
    AERO_BALLISTIC("Аеробалістична"),
    UNKNOWN("Невідомо");

    @Getter
    private String description;

    TrajectoryType(String description) {
        this.description = description;
    }

    @Override
    public String getValueUa() {
        return description;
    }

    public static TrajectoryType fromValueUa(String uaVal) {
        Optional<TrajectoryType> status = Arrays.stream(TrajectoryType.values())
                .filter(e -> e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(UNKNOWN);
    }
}
