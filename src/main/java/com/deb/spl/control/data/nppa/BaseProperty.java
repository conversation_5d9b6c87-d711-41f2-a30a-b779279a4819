package com.deb.spl.control.data.nppa;

import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

public enum BaseProperty implements AdjacentSystemWithDescriptionUa {
    OK("Норма"),
    WARNING("Увага"),
    ERROR("Помилка"),
    NOT_CONNECTED("Немає зв'язку"),
    UNDEFINED("Невідомо");

    @Getter
    private String description;

    BaseProperty(String description) {
        this.description = description;
    }

    @Override
    public String getValueUa() {
        return description;
    }

    public static BaseProperty fromValueUa(String uaVal) {
        Optional<BaseProperty> status = Arrays.stream(BaseProperty.values())
                .filter(e -> e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(UNDEFINED);
    }
}
