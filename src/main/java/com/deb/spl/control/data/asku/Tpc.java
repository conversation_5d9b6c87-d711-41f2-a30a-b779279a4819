package com.deb.spl.control.data.asku;

import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.*;

import javax.persistence.*;
import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@Entity
@NoArgsConstructor
@AllArgsConstructor
@JsonPropertyOrder({"id","updatedAt","tpcLoadState","adjacentSystemType","errorMessage","malfunctionPresent","status"})
public class Tpc implements AdjacentSystem {

    private static final long serialVersionUID = 6671004136397219164L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    @Column(name = "tpc_load_state", columnDefinition = "varchar(255) default 'UNKNOWN'")
    @Enumerated(EnumType.STRING)
    private TpcState tpcLoadState;


    public AdjacentSystemType getAdjacentSystemType() {
        return AdjacentSystemType.TPC;
    }

    @Override
    public String getErrorMessage() {
        return null;
    }

    @Override
    public boolean isMalfunctionPresent() {
        return false;
    }

    @Override
    public AdjacentSystemStatus getStatus() {
        return AdjacentSystemStatus.UNDEFINED;
    }

    @PrePersist
    public void prePersist() {
        updatedAt = LocalDateTime.now();
        tpcLoadState = tpcLoadState == null ? TpcState.UNKNOWN : tpcLoadState;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Tpc tpc)) return false;

        return getTpcLoadState() == tpc.getTpcLoadState();
    }

    @Override
    public int hashCode() {
        return getTpcLoadState() != null ? getTpcLoadState().hashCode() : 0;
    }

    @Override
    public String toString() {
        return "Tpc{" +
               "id=" + id +
               ", dateReleaseM=" + updatedAt +
               ", tpcLoadState=" + tpcLoadState +
               '}';
    }
}
