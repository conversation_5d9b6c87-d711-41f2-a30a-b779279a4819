package com.deb.spl.control.data.ppo;

import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;

import java.util.Arrays;
import java.util.Optional;

public enum PpoBayStatus implements AdjacentSystemWithDescriptionUa {
    OK("Норма"),
    WARNING("Неспарвність"),
    ERROR("Пожежа"),
    UNDEFINED("Невідомо");
    private String sourceName;

    PpoBayStatus(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getValueUa() {
        return sourceName;
    }

    public static PpoBayStatus fromValueUa(String uaVal){
        Optional<PpoBayStatus> status= Arrays.stream(PpoBayStatus.values())
                .filter(e->e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(UNDEFINED);
    }
}
