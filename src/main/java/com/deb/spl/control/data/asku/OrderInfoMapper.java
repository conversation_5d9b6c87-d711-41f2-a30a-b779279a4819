package com.deb.spl.control.data.asku;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.WARN)
public interface OrderInfoMapper {
    OrderInfo map(OrderInfoDao value);

    @Mapping(target = "orderEntityId", source = "entityId")
    OrderInfo map(OrderInfoDto value);

    OrderInfoDao toDao(OrderInfo value);

    @Mapping(target = "orderEntityId", source = "entityId")
    OrderInfoDao toDao(OrderInfoDto value);

    @Mapping(target = "entityId", source = "orderEntityId")
    OrderInfoDto toDto(OrderInfo value);

    @Mapping(target = "entityId", source = "orderEntityId")
    OrderInfoDto toDto(OrderInfoDao value);
}
