package com.deb.spl.control.data.asku;


import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface RocketFormDataMapper {

    @Mapping(target = "isTelemetryIntegrated", source = "telemetryIntegrated")
    RocketFormDataDto toDto(RocketFormData value);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "crc", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "telemetryIntegrated", source = "isTelemetryIntegrated")
    @Mapping(target = "isArchived", ignore = true)
    RocketFormData map(RocketFormDataDto value);

    @Mapping(target = "telemetryIntegrated", source = "telemetryIntegrated")
    @Mapping(target = "isArchived", source = "archived")
    RocketFormData map(RocketFormDataDao value);

    @Mapping(target = "isTelemetryIntegrated", source = "telemetryIntegrated")
    @Mapping(target = "isArchived", source = "archived")
    RocketFormDataDao toDao(RocketFormData value);

    @Mapping(target = "isArchived", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "crc", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    RocketFormDataDao toDao(RocketFormDataDto value);

}
