package com.deb.spl.control.data.ppo;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Mapper(uses = {PpoUnitMapper.class}, componentModel = "spring")
public interface PpoBayMapper {

    PpoUnitMapper PPO_UNIT_MAPPER = Mappers.getMapper(PpoUnitMapper.class);

    PpoBayDTO toDto(PpoBay ppoBay);

    PpoBay toPpoBay(PpoBayDTO dto);

    default Map<String, PpoUnit> map(List<PpoUnitDTO> value) {

        Map<String, PpoUnit> map = value.stream()
                .sorted(Comparator.comparing(PpoUnitDTO::id))
                .map(PPO_UNIT_MAPPER::toPpoUnit).toList()
                .stream()
                .collect(Collectors.toMap(PpoUnit::getUnitAlias, Function.identity()));
        return map;
    }

    default List<PpoUnitDTO> map(Map<String, PpoUnit> value) {
        return value.values().stream()
                .map(PPO_UNIT_MAPPER::toDto)
                .sorted(Comparator.comparing(PpoUnitDTO::id))
                .collect(Collectors.toList());
    }

}
