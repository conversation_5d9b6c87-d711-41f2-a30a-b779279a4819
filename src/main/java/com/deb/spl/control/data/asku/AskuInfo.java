package com.deb.spl.control.data.asku;

import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.*;

@Data
@Builder
public class AskuInfo implements AdjacentSystem {
    private UUID splId;
    private String plateNumber;
    private String unitTitle;
    private LocalDateTime startedDate;
    private Readiness splReadiness;
    private String missileLeftFactoryNumber;
    private String missileRightFactoryNumber;
    private TpcState leftTpcState;
    private TpcState rightTpcState;
    private boolean leftRocketTechnicalCondition;
    private boolean rightRocketTechnicalCondition;
    private LocalDateTime dateUseLeftM;
    private LocalDateTime dateUseRightM;


    @Override
    public AdjacentSystemType getAdjacentSystemType() {
        return AdjacentSystemType.UNCLASSIFIED;
    }

    @Override
    public String getErrorMessage() {
        return null;
    }

    @Override
    public boolean isMalfunctionPresent() {
        return leftRocketTechnicalCondition && rightRocketTechnicalCondition;
    }

    @Override
    public AdjacentSystemStatus getStatus() {
        return AdjacentSystemStatus.OK;
    }

    public Optional<Readiness> getSplReadiness() {
        return Optional.ofNullable(splReadiness);
    }

    public List<String> getRocketsPlantMissiles() {
        List<String> plantMissiles = new ArrayList<>();
        if (missileLeftFactoryNumber != null && !missileLeftFactoryNumber.isBlank()) {
            plantMissiles.add(missileLeftFactoryNumber);
        }
        if (missileRightFactoryNumber != null && !missileRightFactoryNumber.isBlank()) {
            plantMissiles.add(missileRightFactoryNumber);
        }

        return plantMissiles;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        AskuInfo askuInfo = (AskuInfo) o;

        if (leftRocketTechnicalCondition != askuInfo.leftRocketTechnicalCondition) return false;
        if (rightRocketTechnicalCondition != askuInfo.rightRocketTechnicalCondition) return false;
        if (!splId.equals(askuInfo.splId)) return false;
        if (!Objects.equals(plateNumber, askuInfo.plateNumber))
            return false;
        if (!Objects.equals(unitTitle, askuInfo.unitTitle)) return false;
        if (!Objects.equals(startedDate, askuInfo.startedDate))
            return false;
        if (splReadiness != askuInfo.splReadiness) return false;
        if (!Objects.equals(missileLeftFactoryNumber, askuInfo.missileLeftFactoryNumber))
            return false;
        if (!Objects.equals(missileRightFactoryNumber, askuInfo.missileRightFactoryNumber))
            return false;
        if (leftTpcState != askuInfo.leftTpcState) return false;
        if (rightTpcState != askuInfo.rightTpcState) return false;
        if (!Objects.equals(dateUseLeftM, askuInfo.dateUseLeftM))
            return false;
        return Objects.equals(dateUseRightM, askuInfo.dateUseRightM);
    }

    @Override
    public int hashCode() {
        int result = splId.hashCode();
        result = 31 * result + (plateNumber != null ? plateNumber.hashCode() : 0);
        result = 31 * result + (unitTitle != null ? unitTitle.hashCode() : 0);
        result = 31 * result + (startedDate != null ? startedDate.hashCode() : 0);
        result = 31 * result + splReadiness.hashCode();
        result = 31 * result + (missileLeftFactoryNumber != null ? missileLeftFactoryNumber.hashCode() : 0);
        result = 31 * result + (missileRightFactoryNumber != null ? missileRightFactoryNumber.hashCode() : 0);
        result = 31 * result + leftTpcState.hashCode();
        result = 31 * result + rightTpcState.hashCode();
        result = 31 * result + (leftRocketTechnicalCondition ? 1 : 0);
        result = 31 * result + (rightRocketTechnicalCondition ? 1 : 0);
        result = 31 * result + (dateUseLeftM != null ? dateUseLeftM.hashCode() : 0);
        result = 31 * result + (dateUseRightM != null ? dateUseRightM.hashCode() : 0);
        return result;
    }
}