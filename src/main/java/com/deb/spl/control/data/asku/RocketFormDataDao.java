package com.deb.spl.control.data.asku;

import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;


@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "rocket_from_data")
public class RocketFormDataDao {
    private static final long serialVersionUID = 8896755460245764247L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "generation_date")
    private LocalDateTime createdAt;

    //    Заводской номер ОТР
    @NotBlank
    @Size(min = 1, max = 8)
    private String plantMissile;

    //    Ознака БЧ
    @Column(columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    @NotNull
    private WarheadType warhead;

    //    Ознака ГСН
    @Enumerated(EnumType.STRING)
    @Column(columnDefinition = "varchar(255) default 'UNDEFINED'")
    @NotNull
    private GsnType gsnType;

    //    Ознака АЛП
    @Column(columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    @NotNull
    private AlpType alpType;

    //    Признак СИ
    @NotNull
    @Column(columnDefinition = "boolean default false")
    private boolean isTelemetryIntegrated;

    //    Назначение ОТР
    @Column(columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    @NotNull
    private OtrPurposeType purposeType;

    //    архивный
    @Column(columnDefinition = "boolean default false")
    private boolean isArchived;

    //    Контрольная сумма
    @Column(columnDefinition = "varchar(255) default ''")
    private String crc;

    @PrePersist
    void prePersist() {
        this.id = null;
        this.createdAt = LocalDateTime.now();
        warhead = warhead == null ? WarheadType.UNDEFINED : warhead;
        gsnType = gsnType == null ? GsnType.UNDEFINED : gsnType;
        alpType = alpType == null ? AlpType.UNDEFINED : alpType;
        purposeType = purposeType == null ? OtrPurposeType.UNDEFINED : purposeType;
    }

    @Override
    public String toString() {
        return "RocketFormDataDao{" +
               "id=" + id +
               ", createdAt=" + createdAt +
               ", plantMissile='" + plantMissile + '\'' +
               ", warhead=" + warhead +
               ", gsnType=" + gsnType +
               ", alpType=" + alpType +
               ", isTelemetryIntegrated=" + isTelemetryIntegrated +
               ", purposeType=" + purposeType +
               ", iArchived=" + isArchived +
               ", crc='" + crc + '\'' +
               '}';
    }
}
