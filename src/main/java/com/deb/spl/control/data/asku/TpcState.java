package com.deb.spl.control.data.asku;

import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

public enum TpcState implements AdjacentSystemWithDescriptionUa {
    NONE("Немає"),
    TPC_ONLY("Тільки ТПК"),
    TPC_WITH_ROCKET("ТПК споряджений"),
    ERROR("Несправність"),
    UNKNOWN("Невідомо");

    @Getter
    private String description;

    TpcState(String description) {
        this.description = description;
    }

    @Override
    public String getValueUa() {
        return description;
    }

    public static TpcState fromValueUa(String uaVal) {
        Optional<TpcState> status = Arrays.stream(TpcState.values())
                .filter(e -> e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(UNKNOWN);
    }
}
