package com.deb.spl.control.data.asku;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.UUID;

@JsonPropertyOrder({"entityId",
        "validUntil"})
public record OrderInfoDto(@NotNull UUID entityId,
                           @NotNull LocalDateTime validUntil) {
}
