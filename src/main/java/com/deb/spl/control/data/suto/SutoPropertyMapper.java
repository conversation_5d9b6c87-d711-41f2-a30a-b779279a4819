package com.deb.spl.control.data.suto;

import com.deb.spl.control.data.suto.SutoProperty;
import com.deb.spl.control.data.suto.SutoPropertyDao;
import com.deb.spl.control.data.suto.SutoPropertyDto;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface SutoPropertyMapper {
    SutoProperty map(SutoPropertyDao value);

    SutoProperty map(SutoPropertyDto value);

    SutoPropertyDao toDao(SutoProperty value);

    SutoPropertyDto toDto(SutoProperty value);
}
