package com.deb.spl.control.data;

public enum AdjacentSystemType {
    BINS("УКННС"),
    MSU("Метео"),
    NPPA("НППА"),
    NCOK("НЦОК"),
    BYN("Б<PERSON><PERSON>"),
    SAE("СА<PERSON>"),
    SUTO("СУТО"),
    PPO("ППО"),
    PLC("ПЛК"),
    ASKU("АСКУ"),
    TPC("ТПК"),
    UNCLASSIFIED("Будь-яка"),
    UNDEFINED("Невідомо"),
    NSD("ПРД"),
    CCV("Модуль керування");

    private String nameUa;
    private String nameEn;

    AdjacentSystemType(String nameUa, String nameEn) {
        this.nameUa = nameUa;
        this.nameEn = nameEn;
    }

    AdjacentSystemType(String nameUa) {
        this.nameUa = nameUa;
        this.nameEn = this.name();
    }

    public String getUa() {
        return this.nameUa;
    }

    public String getEn() {
        return this.nameEn;
    }
}
