package com.deb.spl.control.data.asku;


import com.deb.spl.control.data.bins.Position;
import com.deb.spl.control.data.AdjacentSystemStatus;
import lombok.*;

import javax.annotation.Nullable;
import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.UUID;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "asku")
public class AskuDao {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "system_status", nullable = false, columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private AdjacentSystemStatus systemStatus;
    private UUID splId;
    @Column(name = "plate_number")
    private String plateNumber;
    private String unitTitle;

    @Column(name = "readiness_started_at")
    private LocalDateTime startedDate;
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "spl_readiness", columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private Readiness splReadiness;

    @Nullable
    @OneToOne(cascade = CascadeType.MERGE)
    private Tpc tpcLeft;

    @Nullable
    @OneToOne(cascade = CascadeType.MERGE)
    private Tpc tpcRight;

    @Nullable
    @OneToOne(cascade = CascadeType.MERGE)
    private RocketDao leftRocket;

    @Nullable
    @OneToOne(cascade = CascadeType.MERGE)
    private RocketDao rightRocket;

    @Transient
    @Column(name = "asku_status", columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private AdjacentSystemStatus status;

    @Embedded
    private Position position;

    @Embedded
    private Plc plc;


    @PrePersist
    void prePersist() {
        updatedAt = LocalDateTime.now();
        systemStatus = status == null ? AdjacentSystemStatus.UNDEFINED : status;
    }

    @Override
    public String toString() {
        return "AskuDao{" +
               "id=" + id +
               ", splId=" + splId +
               ", plateNumber='" + plateNumber + '\'' +
               ", unitTitle='" + unitTitle + '\'' +
               ", startedDate=" + startedDate +
               ", updatedAt=" + updatedAt +
               ", splReadiness=" + splReadiness +
               ", tpcLeft=" + tpcLeft +
               ", tpcRight=" + tpcRight +
               ", leftRocket=" + leftRocket +
               ", rightRocket=" + rightRocket +
               ", status=" + status +
               ", plc=" + plc +
               '}';
    }
}
