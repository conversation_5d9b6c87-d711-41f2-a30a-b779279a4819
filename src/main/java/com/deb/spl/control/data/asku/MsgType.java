package com.deb.spl.control.data.asku;

import java.util.Arrays;
import java.util.Optional;

public enum MsgType {
    COMMIT("Команду прийнято"),
    INFO("Інформація"),
    WARNING("Увага"),
    ERROR("Помилка");

    private final String caption;

    MsgType(String caption) {
        this.caption = caption;
    }

    public MsgType fromValueUa(String uaVal) {
        Optional<MsgType> status= Arrays.stream(MsgType.values())
                .filter(e->e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(ERROR);
    }

    public String getValueUa() {
        return caption;
    }
}
