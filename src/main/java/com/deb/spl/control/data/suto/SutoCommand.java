package com.deb.spl.control.data.suto;

import com.deb.spl.control.data.AdjacentSystemCommand;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.sae.CommandState;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;


@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(name = "suto_record")
public class SutoCommand extends AdjacentSystemCommand {
    @Builder
    public SutoCommand(Long id,
                       @NotNull String command,
                       @NotNull String caption,
                       CommandState commandState,
                       @NotNull AdjacentSystemType adjacentSystem,
                       LocalDateTime generationTime,
                       LocalDateTime executionTime,
                       String originator) {
        super(id, command, caption, commandState, adjacentSystem, generationTime, executionTime, originator);
    }
}
