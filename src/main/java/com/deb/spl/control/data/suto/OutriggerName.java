package com.deb.spl.control.data.suto;

import lombok.Getter;

public enum OutriggerName {
    leftFront("getLeftFrontOutriggerEmergencyCode"),
    rightFront("getRightFrontOutriggerEmergencyCode"),
    leftRear("getLeftRearOutriggerEmergencyCode"),
    rightRear("getRightRearOutriggerEmergencyCode");

    @Getter
    public final String methodName;

    OutriggerName(String methodName) {
        this.methodName = methodName;
    }
}
