package com.deb.spl.control.data.ppo;

import com.deb.spl.control.data.AdjacentSystemStatus;
import lombok.*;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ppo")
public class PpoDao {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, columnDefinition = "minvalue 1000")
    private Long id;

    @Column(name = "system_status", columnDefinition = "varchar(50) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private AdjacentSystemStatus status;

    @Column(name = "bays_state", columnDefinition = "varchar(8096)")
    @Convert(converter = PpoBayListConverter.class)
    private List<PpoBay> bays;

    @Column(name = "updated_at", nullable = false, columnDefinition = "timestamp default now()")
    private LocalDateTime updatedAt;

    @PrePersist
    void prePersist() {
        updatedAt = LocalDateTime.now();
    }


}
