package com.deb.spl.control.data.nppa;

import com.deb.spl.control.data.AdjacentSystemCommandDAO;
import com.deb.spl.control.data.asku.Readiness;
import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.List;

@Builder
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "nppa_commands")
public class NppaCommandDAO extends AdjacentSystemCommandDAO {
    @Column(name = "used_in_test_mode", columnDefinition = "boolean default false")
    @NotNull
    boolean usedInTestMode;

    @Column(name = "used_in_combat_mode", columnDefinition = "boolean default false")
    @NotNull
    boolean usedInCombatMode;

    @Column(name = "used_in_workflow", columnDefinition = "boolean default false")
    @NotNull
    boolean usedInWorkflow;


    @Column(name = "has_blocker", columnDefinition = "boolean default false")
    boolean hasBlocker;

    @Column(name = "available_at_readiness")
    @ElementCollection(fetch = FetchType.EAGER,targetClass = Readiness.class)
    @Enumerated(EnumType.STRING)
    List<Readiness> availableAtReadiness;
}
