package com.deb.spl.control.data.asku;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@JsonPropertyOrder({"technicalCondition",
        "dateUseM",
        "formData",
        "initialData",
        "initialDataTS",
        "initialDataSource",
        "initialDataSourceDescription",
        "storedTlKeys",
        "sensorTemperature"})
public record RocketDto(
        @NotNull
        boolean technicalCondition,
        LocalDateTime dateUseM,
        @NotNull
        RocketFormDataDto formData,
        LaunchInitialDataDto initialData,
        LocalDateTime initialDataTS,
        LaunchInitialDataSource initialDataSource,
        String initialDataSourceDescription,
        List<String> storedTlKeys,
        double sensorTemperature
) {
}
