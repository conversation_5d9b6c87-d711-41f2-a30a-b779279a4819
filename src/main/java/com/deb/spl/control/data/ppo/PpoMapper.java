package com.deb.spl.control.data.ppo;

import org.mapstruct.Mapper;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public abstract class PpoMapper {
    @Autowired
    PpoBayMapper bayMapper;

    public PpoDTO toDto(Ppo ppo) {
        if (ppo == null) {
            return null;
        }
        if (ppo.getBays() == null) {
            return new PpoDTO(ppo.getStatus(), null);
        }

        List<PpoBayDTO> bays = ppo.getBays().values().stream()
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(PpoBay::getId))
                .map(bayMapper::toDto).toList();
        return new PpoDTO(ppo.getStatus(), bays);
    }

    public Ppo map(PpoDTO dto) {
        if (dto == null) {
            return null;
        }
        if (dto.ppoBaysList() == null) {
            return Ppo.builder()
                    .status(dto.status())
                    .build();
        }

        Map<PpoBayType, PpoBay> bays = dto.ppoBaysList().stream()
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(PpoBayDTO::id))
                .map(bayMapper::toPpoBay)
                .collect(Collectors.toMap(PpoBay::getPpoBayType, Function.identity()));

        return Ppo.builder()
                .bays(bays)
                .status(dto.status())
                .build();
    }

    public PpoDao toDao(Ppo value) {
        if (value == null) {
            return null;
        }

        return PpoDao.builder()
                .status(value.getStatus())
                .bays(value.getBays() != null ? value.getBays().values().stream().toList() :
                        Collections.emptyList())
                .build();
    }

    public Ppo map(PpoDao value) {
        if (value == null) {
            return null;
        }
        Ppo ppo = Ppo.builder()
                .status(value.getStatus())
                .bays(value.getBays() != null ? value.getBays().stream()
                        .collect(Collectors.toMap(PpoBay::getPpoBayType, Function.identity())) :
                        Collections.emptyMap())
                .build();

        return ppo;
    }
}
