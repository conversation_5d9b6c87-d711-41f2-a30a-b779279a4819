package com.deb.spl.control.data.suto;

import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

public enum ChassisState implements AdjacentSystemWithDescriptionUa {
    MARCH("Похідний"),
    PARKING("Стоянка"),
    UNKNOWN("Невідомо");

    @Getter
    private final String description;

    ChassisState(String description) {
        this.description = description;
    }

    @Override
    public String getValueUa() {
        return description;
    }

    public static ChassisState fromValueUa(String uaVal) {
        Optional<ChassisState> status = Arrays.stream(ChassisState.values())
                .filter(e -> e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(UNKNOWN);
    }
}
