package com.deb.spl.control.data.suto;

import com.deb.spl.control.data.AdjacentSystemProperty;
import com.deb.spl.control.data.PropertyType;
import com.deb.spl.control.data.sae.CommandState;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;

@SuperBuilder
@NoArgsConstructor
@Getter
public class SutoProperty extends AdjacentSystemProperty {

    public PropertyType propertyType;

    public SutoProperty(Long id, @NotNull String name, @NotNull String caption, CommandState state, PropertyType propertyType) {
        super(id, name, caption, state);
        this.propertyType = propertyType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof SutoProperty that)) return false;
        if (!super.equals(o)) return false;

        return propertyType == that.propertyType;
    }

    @Override
    public int hashCode() {
        int result = super.hashCode();
        result = 31 * result + (propertyType != null ? propertyType.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "AdjacentSystemProperty{" +
                "id=" + getId() +
                ", name='" + getName() + '\'' +
                ", caption='" + getCaption() + '\'' +
                ", state=" + getState() +
                "propertyType=" + propertyType +
                '}';
    }
}
