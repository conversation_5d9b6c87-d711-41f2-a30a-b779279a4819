package com.deb.spl.control.data.asku;

import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;

import java.util.Arrays;
import java.util.Optional;

public enum OtrPurposeType implements AdjacentSystemWithDescriptionUa {
    COMBAT("Бойова"),
    TEM("ТЕМ"),
    UNDEFINED("Невідомо");

    private final String value;

    OtrPurposeType(String value) {
        this.value = value;
    }

    @Override
    public String getValueUa() {
        return value;
    }

    public static OtrPurposeType fromValueUa(String uaVal) {
        Optional<OtrPurposeType> status = Arrays.stream(OtrPurposeType.values())
                .filter(e -> e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(UNDEFINED);
    }
}
