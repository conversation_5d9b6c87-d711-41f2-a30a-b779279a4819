package com.deb.spl.control.data.asku;

import lombok.*;

import javax.persistence.*;
import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "launch_reference_points_record")
public class LaunchReferencePointRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private long id;

    @Column(name = "reference_point", columnDefinition = "varchar default ''")
    private String referencePoint;

    @Column(name = "time_stamp", nullable = false, columnDefinition = "timestamp default now()")
    private LocalDateTime timeStamp;

    @Column(name = "plant_missile", nullable = false)
    private String plantMissile;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "rocket_id")
    private RocketDao rocket;

    @Override
    public String toString() {
        return "LaunchReferencePointRecord{" +
               "id=" + id +
               ", referencePoint='" + referencePoint + '\'' +
               ", timeStamp=" + timeStamp +
               ", plantMissile='" + plantMissile + '\'' +
               '}';
    }

    public boolean equalsIgnoreId(LaunchReferencePoint launchReferencePoint) {
        if (getReferencePoint() != null ? !getReferencePoint().equals(launchReferencePoint.getReferencePoint())
                : launchReferencePoint.getReferencePoint() != null)
            return false;
        if (getTimeStamp() != null ? !getTimeStamp().equals(launchReferencePoint.getTimeStamp())
                : launchReferencePoint.getTimeStamp() != null)
            return false;
        return getPlantMissile() != null ? getPlantMissile().equals(launchReferencePoint.getPlantMissile())
                : launchReferencePoint.getPlantMissile() == null;
    }

    public boolean equalsIgnoreId(LaunchReferencePointRecord launchReferencePoint) {
        if (getReferencePoint() != null ? !getReferencePoint().equals(launchReferencePoint.getReferencePoint())
                : launchReferencePoint.getReferencePoint() != null)
            return false;
        if (getTimeStamp() != null ? !getTimeStamp().equals(launchReferencePoint.getTimeStamp())
                : launchReferencePoint.getTimeStamp() != null)
            return false;
        return getPlantMissile() != null ? getPlantMissile().equals(launchReferencePoint.getPlantMissile())
                : launchReferencePoint.getPlantMissile() == null;
    }
}
