package com.deb.spl.control.data.asku;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@Entity(name = "order_info")
public class OrderInfoDao {
    private static final long serialVersionUID = 9108625674183581471L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private long id;

    @Column(name = "order_entity_id", nullable = false)
    private UUID orderEntityId;

    @Column(name = "valid_until")
    private LocalDateTime validUntil;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        OrderInfoDao that = (OrderInfoDao) o;

        if (getId() != that.getId()) return false;
        if (getOrderEntityId() != null ? !getOrderEntityId().equals(that.getOrderEntityId()) : that.getOrderEntityId() != null)
            return false;
        return getValidUntil() != null ? getValidUntil().equals(that.getValidUntil()) : that.getValidUntil() == null;
    }

    @Override
    public int hashCode() {
        int result = (int) (getId() ^ (getId() >>> 32));
        result = 31 * result + (getOrderEntityId() != null ? getOrderEntityId().hashCode() : 0);
        result = 31 * result + (getValidUntil() != null ? getValidUntil().hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "OrderInfoDao{" +
               "id=" + id +
               ", orderEntityId=" + orderEntityId +
               ", validUntil=" + validUntil +
               '}';
    }
}
