package com.deb.spl.control.data.asku;

import com.deb.spl.control.service.asku.AskuService;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.Optional;

@Mapper(componentModel = "spring",
        uses = {RocketFormDataMapper.class, LaunchInitialDataMapper.class},
        unmappedTargetPolicy = ReportingPolicy.WARN)
public abstract class RocketMapper {
    public abstract RocketDto toDto(Rocket value);

    @Mapping(target = "dateReleaseM", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    public abstract RocketDao toDao(Rocket value);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "dateReleaseM", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
//    @Mapping(target = "sensorTemperature",
//            expression = "java(getSensorTemperature(value,service))")
    @Mapping(target = "sensorTemperature", defaultValue = "-999.9")
    @Mapping(target = "launchResult", ignore = true)
    @Mapping(target = "loadedToPlc", ignore = true)
    @Mapping(target = "launchResultDescription", ignore = true)
    public abstract RocketDao toDao(RocketDto value, AskuService service);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "initialDataSource", ignore = true)
    @Mapping(target = "initialDataSourceDescription", ignore = true)
    @Mapping(target = "sensorTemperature", defaultValue = "-999.9")
//    @Mapping(target = "sensorTemperature",
//            expression = "java(getSensorTemperature(value,service))")
    @Mapping(target = "launchResult", ignore = true)
    @Mapping(target = "loadedToPlc", ignore = true)
    @Mapping(target = "launchResultDescription", ignore = true)
    public abstract Rocket map(RocketDto value, AskuService service);

    //    @Mapping(target = "sensorTemperature",
//            expression = "java(getSensorTemperature(value,service))")
    @Mapping(target = "sensorTemperature", defaultValue = "-999.9")
    public abstract Rocket map(RocketDao value, AskuService service);

    public abstract Rocket clone(Rocket value);

    public abstract RocketDao clone(RocketDao value);

    double getSensorTemperature(RocketDto value, AskuService askuService) {
        if (value == null || value.formData() == null || value.formData().plantMissile() == null) {
            return -999.9;
        }
        Optional<Rocket> rocket = askuService.findRocketByPlantNumber(value.formData().plantMissile());

        return rocket.isEmpty() ? -999.9 : rocket.get().getSensorTemperature();
    }

    double getSensorTemperature(RocketDao value, AskuService askuService) {
        if (value == null || value.getFormData() == null || value.getFormData().getPlantMissile() == null) {
            return -999.9;
        }
        Optional<Rocket> rocket = askuService.findRocketByPlantNumber(value.getFormData().getPlantMissile());

        return rocket.isEmpty() ? value.getSensorTemperature() : rocket.get().getSensorTemperature();
    }

}
