package com.deb.spl.control.data.sae;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;


@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.WARN)
public interface SaeMapper {

    SaeDto toDto(Sae value);

    Sae map(SaeDto value);
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    SaeDao toDao(Sae value);

    Sae map(SaeDao value);

    Sae clone(Sae value);
}
