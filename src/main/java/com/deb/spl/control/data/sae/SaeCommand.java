package com.deb.spl.control.data.sae;

import com.deb.spl.control.data.AdjacentSystemCommand;
import com.deb.spl.control.data.AdjacentSystemType;
import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(name = "sae_record")
public class SaeCommand extends AdjacentSystemCommand {

    @Builder
    public SaeCommand(Long id,
                      @NotNull String command,
                      @NotNull String caption,
                      CommandState commandState,
                      @NotNull AdjacentSystemType adjacentSystem,
                      LocalDateTime generationTime,
                      LocalDateTime executionTime,
                      String originator) {
        super(id, command, caption, commandState, adjacentSystem, generationTime,
                executionTime, originator);
    }
}
