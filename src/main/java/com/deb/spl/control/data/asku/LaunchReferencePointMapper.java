package com.deb.spl.control.data.asku;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.WARN,
        componentModel = MappingConstants.ComponentModel.SPRING)
public interface LaunchReferencePointMapper {

    LaunchReferencePoint toPoint(LaunchReferencePointRecord launchReferencePointRecord);
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "rocket",ignore = true)
    LaunchReferencePointRecord toRecord(LaunchReferencePoint launchReferencePoint);
}