package com.deb.spl.control.data.asku;

import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;

public enum Readiness implements AdjacentSystemWithDescriptionUa {
    BG_1("БГ № 1"),
    @Deprecated
    BG_2A("БГ № 2а"),
    BG_2B("БГ № 2"),
    BG_3("БГ № 3"),
    BG_4("ТГ № 4"),
    BG_4_TO_BG_3("Перехід ТГ4 - БГ3"),
    BG_3_TO_BG_4("Перехід БГ3 - ТГ4"),
    BG_3_TO_BG_2A("Перехід БГ3 - БГ2а"),
    BG_3_TO_BG_2B("Перехід БГ3 - БГ2"),
    BG_3_TO_BG_1("Перехід БГ3 - БГ1"),
    BG_2A_TO_BG_1("Перехід БГ2а - БГ1"),
    BG_2A_TO_BG_3("Перехід БГ2а - БГ3"),
    BG_2A_TO_BG_4("Перехід БГ2а - ТГ4"),
    BG_2B_TO_BG_4("Перехід БГ2 - ТГ4"),
    BG_2B_TO_BG_3("Перехід БГ2 - БГ3"),
    BG_1_TO_BG_3("Перехід БГ1 - БГ3"),
    BG_1_TO_BG_4("Перехід БГ1 - ТГ4"),
    BG_1_TO_BG_2A("Перехід БГ1 - БГ2а"),
    TRANSITION("Відбувається перехід"),
    AUTO_TEST_IN_PROGRESS("Перевірка"),

    UNDEFINED("Невідомо");

    public String description;

    Readiness(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return description;
    }

    @Override
    public String getValueUa() {
        return description;
    }
}
