package com.deb.spl.control.data.suto;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import javax.validation.constraints.NotNull;

@JsonPropertyOrder({"roll",
        "pitch",
        "armLiftStrokePosition",
        "levelingCyclesCount",
        "pressureInImpulseSection",
        "temperatureRR",
        "workingFluidLevel",
        "mainPumpRPM",
        "overallOperatingTime",
        "overallArmLiftingsCount"})
public record SutoStatsDto(
        @NotNull
        int roll,
        @NotNull
        int pitch,
        @NotNull
        int armLiftStrokePosition,
        @NotNull
        int levelingCyclesCount,
        @NotNull
        double pressureInImpulseSection,
        @NotNull
        int temperatureRR,
        @NotNull
        int workingFluidLevel,
        @NotNull
        int mainPumpRPM,
        @NotNull
        int overallOperatingTime,
        @NotNull
        int overallArmLiftingsCount
) {
}
