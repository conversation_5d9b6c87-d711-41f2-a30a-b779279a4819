package com.deb.spl.control.data.sae;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.VoltageStatus;
import lombok.*;

import javax.persistence.*;
import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "sae")
public class SaeDao {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "sae_status", columnDefinition = "varchar(20) default 'NOT_CONNECTED'")
    @Enumerated(EnumType.STRING)
    private SaeStatus saeStatus;

    @Column(name = "readiness", columnDefinition = "varchar(20) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private AdjacentSystemStatus readiness;

    @Column(name = "energized_by_adj", columnDefinition = "varchar(20) default 'OFF'")
    @Enumerated(EnumType.STRING)
    private SaePowerSourceStatus energizedByAdj;

    @Column(name = "energized_by_hds", columnDefinition = "varchar(20) default 'OFF'")
    @Enumerated(EnumType.STRING)
    private SaePowerSourceStatus energizedByHds;

    @Column(name = "energized_by_external_power_source", columnDefinition = "varchar(20) default 'OFF'")
    @Enumerated(EnumType.STRING)
    private SaePowerSourceStatus energizedByExternalPowerSource;

    @Column(name = "adj_start", columnDefinition = "varchar(20) default 'NOT_ACTIVE'")
    @Enumerated(EnumType.STRING)
    private ADJstatus adjStart;

    @Column(name = "adj_stop", columnDefinition = "varchar(20) default 'NOT_ACTIVE'")
    @Enumerated(EnumType.STRING)
    private ADJstatus adjStop;

    @Column(name = "adj_lock", columnDefinition = "varchar(20) default 'NOT_ACTIVE'")
    @Enumerated(EnumType.STRING)
    private ADJstatus adjLock;

    @Column(name = "adj_unlock", columnDefinition = "varchar(20) default 'NOT_ACTIVE'")
    @Enumerated(EnumType.STRING)
    private ADJstatus adjUnlock;

    @Column(name = "hds_status", columnDefinition = "varchar(20) default 'NOT_ACTIVE'")
    @Enumerated(EnumType.STRING)
    private HDSstatus hdsStatus;

    @Column(name = "voltage_status", columnDefinition = "varchar(20) default 'NOT_ACTIVE'")
    @Enumerated(EnumType.STRING)
    private VoltageStatus voltageStatus;

    @Column(name = "external_power_source_voltage", columnDefinition = "varchar(20) default 'NOT_ACTIVE'")
    @Enumerated(EnumType.STRING)
    private VoltageStatus externalPowerSourceVoltage;

    @Column(name = "bs_voltage", columnDefinition = "varchar(20) default 'NOT_ACTIVE'")
    @Enumerated(EnumType.STRING)
    private VoltageStatus BSVoltage;

    @Column(name = "feeder_1_status", columnDefinition = "varchar(20) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private SaeFeederStatus feeder1Status;

    @Column(name = "feeder_2_status", columnDefinition = "varchar(20) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private SaeFeederStatus feeder2Status;

    @Column(name = "feeder_3_status", columnDefinition = "varchar(20) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private SaeFeederStatus feeder3Status;

    @Column(name = "feeder_4_status", columnDefinition = "varchar(20) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private SaeFeederStatus feeder4Status;

    @Column(name = "feeder_5_status", columnDefinition = "varchar(20) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private SaeFeederStatus feeder5Status;

    @Column(name = "feeder_6_status", columnDefinition = "varchar(20) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private SaeFeederStatus feeder6Status;

    @Column(name = "feeder_nppa_1_status", columnDefinition = "varchar(20) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private SaeFeederStatus feederNppa1Status;

    @Column(name = "updated_at", nullable = false, columnDefinition = "timestamp default now()")
    private LocalDateTime updatedAt;

    @PrePersist
    void prePersist() {
        updatedAt = LocalDateTime.now();
        saeStatus = saeStatus == null ? SaeStatus.UNDEFINED : saeStatus;
        readiness = readiness == null ? AdjacentSystemStatus.UNDEFINED : readiness;
        feeder1Status = feeder1Status == null ? SaeFeederStatus.UNDEFINED : feeder1Status;
        feeder2Status = feeder2Status == null ? SaeFeederStatus.UNDEFINED : feeder2Status;
        feeder3Status = feeder3Status == null ? SaeFeederStatus.UNDEFINED : feeder3Status;
        feeder4Status = feeder4Status == null ? SaeFeederStatus.UNDEFINED : feeder4Status;
        feeder5Status = feeder5Status == null ? SaeFeederStatus.UNDEFINED : feeder5Status;
        feeder6Status = feeder6Status == null ? SaeFeederStatus.UNDEFINED : feeder6Status;
        feederNppa1Status = feederNppa1Status == null ? SaeFeederStatus.UNDEFINED : feederNppa1Status;
    }
}

