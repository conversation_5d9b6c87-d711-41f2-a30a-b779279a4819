package com.deb.spl.control.data.asku;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

@Getter
@Setter
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonPropertyOrder({"referencePoint", "timeStamp", "plantMissile"})
public class LaunchReferencePoint {
    private static final long serialVersionUID = 69585626380835597L;

    private String referencePoint;
    private LocalDateTime timeStamp;
    private String plantMissile;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        LaunchReferencePoint that = (LaunchReferencePoint) o;

        if (getReferencePoint() != null ? !getReferencePoint().equals(that.getReferencePoint()) : that.getReferencePoint() != null)
            return false;
        if (getTimeStamp() != null ? !getTimeStamp().equals(that.getTimeStamp()) : that.getTimeStamp() != null)
            return false;
        return getPlantMissile() != null ? getPlantMissile().equals(that.getPlantMissile()) : that.getPlantMissile() == null;
    }

    @Override
    public int hashCode() {
        int result = getReferencePoint() != null ? getReferencePoint().hashCode() : 0;
        result = 31 * result + (getTimeStamp() != null ? getTimeStamp().hashCode() : 0);
        result = 31 * result + (getPlantMissile() != null ? getPlantMissile().hashCode() : 0);
        return result;
    }
}
