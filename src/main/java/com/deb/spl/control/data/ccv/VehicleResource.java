package com.deb.spl.control.data.ccv;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Optional;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VehicleResource {
    private UUID id;
    private UUID entityId;
    private String number;
    private UUID vehicleDictionaryId;
    private UUID unitId;
    private UUID staffId;
    private UUID appId;
    private boolean isAvailable;
    private UnitResource unit;
    private VehicleDictionary vehicleDictionary;
    private boolean hasConnection;
    private boolean isArchieved;

    @JsonIgnore
    public Optional<String> getUnitName() {
        if (unit == null) {
            return Optional.empty();
        }
        return Optional.ofNullable(unit.getName());
    }

    @JsonIgnore
    public boolean isCcv(String ccvTypeJsonPropertyValue) {
        if (vehicleDictionary == null || vehicleDictionary.getVehicleType() == null) {
            return false;
        }

        return ccvTypeJsonPropertyValue.equalsIgnoreCase(vehicleDictionary.getVehicleType().getType());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        VehicleResource that = (VehicleResource) o;

        if (isAvailable() != that.isAvailable()) return false;
        if (isHasConnection() != that.isHasConnection()) return false;
        if (isArchieved() != that.isArchieved()) return false;
        if (getId() != null ? !getId().equals(that.getId()) : that.getId() != null) return false;
        if (getEntityId() != null ? !getEntityId().equals(that.getEntityId()) : that.getEntityId() != null)
            return false;
        if (getNumber() != null ? !getNumber().equals(that.getNumber()) : that.getNumber() != null) return false;
        if (getVehicleDictionaryId() != null ? !getVehicleDictionaryId().equals(that.getVehicleDictionaryId()) : that.getVehicleDictionaryId() != null)
            return false;
        if (getUnitId() != null ? !getUnitId().equals(that.getUnitId()) : that.getUnitId() != null) return false;
        if (getStaffId() != null ? !getStaffId().equals(that.getStaffId()) : that.getStaffId() != null) return false;
        if (getAppId() != null ? !getAppId().equals(that.getAppId()) : that.getAppId() != null) return false;
        if (getUnit() != null ? !getUnit().equals(that.getUnit()) : that.getUnit() != null) return false;
        return getVehicleDictionary() != null ? getVehicleDictionary().equals(that.getVehicleDictionary()) : that.getVehicleDictionary() == null;
    }

    @Override
    public int hashCode() {
        int result = getId() != null ? getId().hashCode() : 0;
        result = 31 * result + (getEntityId() != null ? getEntityId().hashCode() : 0);
        result = 31 * result + (getNumber() != null ? getNumber().hashCode() : 0);
        result = 31 * result + (getVehicleDictionaryId() != null ? getVehicleDictionaryId().hashCode() : 0);
        result = 31 * result + (getUnitId() != null ? getUnitId().hashCode() : 0);
        result = 31 * result + (getStaffId() != null ? getStaffId().hashCode() : 0);
        result = 31 * result + (getAppId() != null ? getAppId().hashCode() : 0);
        result = 31 * result + (isAvailable() ? 1 : 0);
        result = 31 * result + (getUnit() != null ? getUnit().hashCode() : 0);
        result = 31 * result + (getVehicleDictionary() != null ? getVehicleDictionary().hashCode() : 0);
        result = 31 * result + (isHasConnection() ? 1 : 0);
        result = 31 * result + (isArchieved() ? 1 : 0);
        return result;
    }
}
