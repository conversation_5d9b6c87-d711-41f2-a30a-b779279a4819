package com.deb.spl.control.data.asku;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;

@JsonPropertyOrder("{code,description}")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LaunchResultDescription {
    @Column(name = "launch_code", columnDefinition = "varchar(6) default ''")
    private String code;
    @Column(name = "launch_description", columnDefinition = "varchar(140) default ''")
    private String description;
}



