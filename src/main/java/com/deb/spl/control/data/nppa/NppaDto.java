package com.deb.spl.control.data.nppa;

import com.deb.spl.control.data.asku.LaunchInitialDataDto;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.asku.RocketFormDataDto;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonPropertyOrder({"status",
        "ncok",
        "byn",
        "leftRocketFormData",
        "rightRocketFormData",
        "leftRocketInitialData",
        "rightRocketInitialData"})
public record NppaDto(AdjacentSystemStatus status,
                      NcokDto ncok,
                      BynDto byn,
                      RocketFormDataDto leftRocketFormData,
                      RocketFormDataDto rightRocketFormData,
                      LaunchInitialDataDto leftRocketInitialData,
                      LaunchInitialDataDto rightRocketInitialData) {
}
