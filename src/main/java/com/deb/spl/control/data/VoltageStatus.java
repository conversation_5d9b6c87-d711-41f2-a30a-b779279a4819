package com.deb.spl.control.data;

import java.util.Arrays;
import java.util.Optional;

public enum VoltageStatus implements AdjacentSystemWithDescriptionUa {

    OK("Норма"),
    ERROR("Не норма"),
    NOT_ACTIVE("Не використовується");


    private String sourceName;

    VoltageStatus(String sourceName) {
        this.sourceName = sourceName;
    }

    public static VoltageStatus fromValueUa(String uaVal) {
        Optional<VoltageStatus> status= Arrays.stream(VoltageStatus.values())
                .filter(e->e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(ERROR);
    }

    public String getValueUa() {
        return sourceName;
    }
}