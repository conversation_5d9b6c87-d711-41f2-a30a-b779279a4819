package com.deb.spl.control.data.ppo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Builder
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Embeddable
@Table(name = "ppo_unit",
        uniqueConstraints = @UniqueConstraint(columnNames = {"unit_name", "bay_type"}))
public class PpoUnit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;
    @Enumerated(EnumType.STRING)
    @Column(name = "unit_type")
    private PpoUnitType unitType;
    @NotBlank
    @Column(name = "unit_name")
    private String unitName;
    @NotBlank
    @Column(name = "unit_alias")
    private String unitAlias;
    @Enumerated(EnumType.STRING)
    @Column(name = "bay_type")
    private PpoBayType bayType;
    @Enumerated(EnumType.STRING)
    @Column(name = "unit_state", columnDefinition = "varchar(255) default 'UNDEFINED'")
    private PpoUnitStatus unitStatus;

    public void setUnitStatus(@NotNull PpoUnitStatus ppoUnitStatus){
        this.unitStatus = ppoUnitStatus;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PpoUnit unit)) return false;

        if (getId() != null ? !getId().equals(unit.getId()) : unit.getId() != null) return false;
        if (getUnitType() != unit.getUnitType()) return false;
        if (getUnitName() != null ? !getUnitName().equals(unit.getUnitName()) : unit.getUnitName() != null)
            return false;
        if (getUnitAlias() != null ? !getUnitAlias().equals(unit.getUnitAlias()) : unit.getUnitAlias() != null)
            return false;
        if (getBayType() != unit.getBayType()) return false;
        return getUnitStatus() == unit.getUnitStatus();
    }

    @Override
    public int hashCode() {
        int result = getId() != null ? getId().hashCode() : 0;
        result = 31 * result + (getUnitType() != null ? getUnitType().hashCode() : 0);
        result = 31 * result + (getUnitName() != null ? getUnitName().hashCode() : 0);
        result = 31 * result + (getUnitAlias() != null ? getUnitAlias().hashCode() : 0);
        result = 31 * result + (getBayType() != null ? getBayType().hashCode() : 0);
        result = 31 * result + (getUnitStatus() != null ? getUnitStatus().hashCode() : 0);
        return result;
    }

}
