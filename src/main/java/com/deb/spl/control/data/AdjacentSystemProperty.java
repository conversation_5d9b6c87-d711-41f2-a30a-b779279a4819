package com.deb.spl.control.data;

import com.deb.spl.control.data.sae.CommandState;
import lombok.*;
import lombok.experimental.SuperBuilder;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@MappedSuperclass
@SuperBuilder
public class AdjacentSystemProperty {
    private Long id;
    @NotNull
    @Column(unique = true)
    private String name;
    @NotNull
    private String caption;
    @Builder.Default
    private CommandState state=CommandState.OFF;

    @Override
    public String toString() {
        return "AdjacentSystemProperty{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", caption='" + caption + '\'' +
                ", state=" + state +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof AdjacentSystemProperty that)) return false;

        if (getId() != null ? !getId().equals(that.getId()) : that.getId() != null) return false;
        if (!getName().equals(that.getName())) return false;
        if (getCaption() != null ? !getCaption().equals(that.getCaption()) : that.getCaption() != null) return false;
        return getState() == that.getState();
    }

    @Override
    public int hashCode() {
        int result = getId() != null ? getId().hashCode() : 0;
        result = 31 * result + getName().hashCode();
        result = 31 * result + (getCaption() != null ? getCaption().hashCode() : 0);
        result = 31 * result + (getState() != null ? getState().hashCode() : 0);
        return result;
    }
}
