package com.deb.spl.control.data.asku;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@JsonPropertyOrder({"plantMissile",
        "warhead",
        "gsnType",
        "alpType",
        "isTelemetryIntegrated",
        "purposeType"})
public record RocketFormDataDto(
        @NotBlank
        String plantMissile,
        @NotNull
        WarheadType warhead,
        @NotNull
        GsnType gsnType,
        @NotNull
        AlpType alpType,
        @NotNull
        boolean isTelemetryIntegrated,
        @NotNull
        OtrPurposeType purposeType) {
}
