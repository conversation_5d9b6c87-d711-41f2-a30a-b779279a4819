package com.deb.spl.control.data.asku;

import com.deb.spl.control.data.bins.Position;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.time.LocalDateTime;

@JsonPropertyOrder({"plateNumber",
        "unitTitle",
        "startedDate",
        "splReadiness",
        "tpcLeft",
        "tpcRight",
        "leftRocket",
        "rightRocket",
        "position"})
public record AskuDto(String plateNumber,
                      String unitTitle,
                      LocalDateTime startedDate,
                      Readiness splReadiness,
                      Tpc tpcLeft,
                      Tpc tpcRight,
                      RocketDto leftRocket,
                      RocketDto rightRocket,
                      Position position) {
}
