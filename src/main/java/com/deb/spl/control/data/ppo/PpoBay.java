package com.deb.spl.control.data.ppo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import javax.persistence.*;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "ppo_bay")
public class PpoBay {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Enumerated(EnumType.STRING)
    private PpoBayType ppoBayType;
    @Column(nullable = false, columnDefinition = "boolean default false")
    private boolean firePresent;

    @OneToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "ppo_bay_units_inppo_bay",
            joinColumns = {@JoinColumn(name = "ppo_bay_id", referencedColumnName = "id")})
    @MapKey(name = "unitAlias")
    private Map<String, PpoUnit> unitsInPPoBay;


    @JsonIgnore
    public boolean isMalfunctionPresent() {

        for (PpoUnit unit : unitsInPPoBay.values()) {
            if (unit.getUnitStatus() == PpoUnitStatus.ERROR) {
                return true;
            }
        }
        return false;
    }

    public List<PpoUnit> getUnitsWithState(PpoUnitStatus state) {
        return unitsInPPoBay.values().stream().filter(ppoUnit -> ppoUnit.getUnitStatus() == state).toList();
    }

    public Optional<PpoUnit> getUnitByAlias(String alias) { //todo replace with search by key
        return Optional.ofNullable(unitsInPPoBay.get(alias.toUpperCase()));
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PpoBay bay)) return false;

        if (isFirePresent() != bay.isFirePresent()) return false;
        if (getPpoBayType() != bay.getPpoBayType()) return false;
        if (getUnitsInPPoBay() != null) {
            if (bay.getUnitsInPPoBay() == null) {
                return false;
            }
            if (getUnitsInPPoBay().size() != bay.getUnitsInPPoBay().size()) return false;

            for (PpoUnit unit : getUnitsInPPoBay().values()) {
                boolean contains = false;
                for (PpoUnit bayUnit : bay.getUnitsInPPoBay().values()) {
                    if (!contains) {
                        if (bayUnit.getUnitType().equals(unit.getUnitType())
                                && bayUnit.getBayType().equals(unit.getBayType())
                                && bayUnit.getUnitName().equalsIgnoreCase(unit.getUnitName())) {
                            contains = true;
                            if(bayUnit.getUnitStatus()==null){
                                return unit.getUnitStatus()==null;
                            }
                            if (!bayUnit.getUnitStatus().equals(unit.getUnitStatus())) {
                                return false;
                            }
                        }
                    }
                }
                if (!contains) {
                    return false;
                }
            }
            return true;
        }
        return bay.getUnitsInPPoBay() == null;
    }

    @Override
    public int hashCode() {
        int result = getPpoBayType().hashCode();
        result = 31 * result + (isFirePresent() ? 1 : 0);
        result = 31 * result + (getUnitsInPPoBay() != null ? getUnitsInPPoBay().hashCode() : 0);
        return result;
    }
}
