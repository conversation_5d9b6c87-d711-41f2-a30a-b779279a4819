package com.deb.spl.control.data.sae;

import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;

import java.util.Arrays;
import java.util.Optional;

public enum SaePowerSourceStatus implements AdjacentSystemWithDescriptionUa {
    OK("Норма"),
    ERROR("Не норма"),
    OFF("Відключено");

    private String sourceName;



    SaePowerSourceStatus(String sourceName) {
        this.sourceName = sourceName;
    }


    public String getValueUa() {
        return sourceName;
    }


    public static SaePowerSourceStatus fromValueUa(String uaVal) {
        Optional<SaePowerSourceStatus> status= Arrays.stream(SaePowerSourceStatus.values())
                .filter(e->e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(ERROR);
    }
}
