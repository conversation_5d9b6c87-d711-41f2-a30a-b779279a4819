package com.deb.spl.control.data.asku;

import com.deb.spl.control.data.asku.Asku;
import com.deb.spl.control.data.asku.AskuInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",
        unmappedSourcePolicy = ReportingPolicy.WARN,
        unmappedTargetPolicy = ReportingPolicy.WARN)
public interface AskuInfoMapper {
    @Mapping(target = "leftTpcState",source = "tpcLeft.tpcLoadState")
    @Mapping(target = "rightTpcState",source = "tpcRight.tpcLoadState")
    @Mapping(target = "leftRocketTechnicalCondition",source = "leftRocket.technicalCondition")
    @Mapping(target = "rightRocketTechnicalCondition",source = "rightRocket.technicalCondition")
    AskuInfo toAskuInfo(Asku value);

}
