package com.deb.spl.control.data.suto;

import com.deb.spl.control.utils.MathUtils;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Embeddable;

@Getter
@Builder
@NoArgsConstructor
@Embeddable
public class SutoStats {
    @Column(columnDefinition = "double precision default 0.0")
    private double roll;
    @Column(columnDefinition = "double precision default 0.0")
    private double pitch;
    @Setter
    private int armLiftStrokePosition;
    @Setter
    private int levelingCyclesCount;
    @Column(columnDefinition = "double precision default 0.0")
    private double pressureInImpulseSection;
    @Setter
    @Column(name = "temperature_rr")
    private int temperatureRR;
    @Setter
    private int workingFluidLevel;
    @Setter
    @Column(name = "main_pump_rmp")
    private int mainPumpRPM;
    @Setter
    private int overallOperatingTime;
    @Setter
    private int overallArmLiftingsCount;


    public void setRoll(double roll) {
        this.roll = MathUtils.round(roll, 1);
    }

    public void setPitch(double pitch) {
        this.pitch = MathUtils.round(pitch, 1);
    }

    public void setPressureInImpulseSection(double pressureInImpulseSection) {
        this.pressureInImpulseSection = MathUtils.round(pressureInImpulseSection, 1);
    }

    public SutoStats(double roll,
                     double pitch,
                     int armLiftStrokePosition,
                     int levelingCyclesCount,
                     double pressureInImpulseSection,
                     int temperatureRR,
                     int workingFluidLevel,
                     int mainPumpRPM,
                     int overallOperatingTime,
                     int overallArmLiftingsCount) {
        this.roll = MathUtils.round(roll,1);
        this.pitch = MathUtils.round(pitch,1);
        this.armLiftStrokePosition = armLiftStrokePosition;
        this.levelingCyclesCount = levelingCyclesCount;
        this.pressureInImpulseSection = MathUtils.round(pressureInImpulseSection,1);
        this.temperatureRR = temperatureRR;
        this.workingFluidLevel = workingFluidLevel;
        this.mainPumpRPM = mainPumpRPM;
        this.overallOperatingTime = overallOperatingTime;
        this.overallArmLiftingsCount = overallArmLiftingsCount;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof SutoStats sutoStats)) return false;

        if (getRoll() != sutoStats.getRoll()) return false;
        if (getPitch() != sutoStats.getPitch()) return false;
        if (getArmLiftStrokePosition() != sutoStats.getArmLiftStrokePosition()) return false;
        if (getLevelingCyclesCount() != sutoStats.getLevelingCyclesCount()) return false;
        if (getPressureInImpulseSection() != sutoStats.getPressureInImpulseSection()) return false;
        if (getTemperatureRR() != sutoStats.getTemperatureRR()) return false;
        if (getWorkingFluidLevel() != sutoStats.getWorkingFluidLevel()) return false;
        if (getMainPumpRPM() != sutoStats.getMainPumpRPM()) return false;
        if (getOverallOperatingTime() != sutoStats.getOverallOperatingTime()) return false;
        return getOverallArmLiftingsCount() == sutoStats.getOverallArmLiftingsCount();
    }

    @Override
    public int hashCode() {
        int result;
        long temp;
        temp = Double.doubleToLongBits(roll);
        result = (int) (temp ^ (temp >>> 32));
        temp = Double.doubleToLongBits(pitch);
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        result = 31 * result + getArmLiftStrokePosition();
        result = 31 * result + getLevelingCyclesCount();
        temp = Double.doubleToLongBits(pressureInImpulseSection);
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        result = 31 * result + getTemperatureRR();
        result = 31 * result + getWorkingFluidLevel();
        result = 31 * result + getMainPumpRPM();
        result = 31 * result + getOverallOperatingTime();
        result = 31 * result + getOverallArmLiftingsCount();
        return result;
    }
}
