package com.deb.spl.control.data;

import com.deb.spl.control.views.adjacentSystems.utils.HasVerticalInfoAsList;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Builder
@Slf4j
public class LogBean implements HasVerticalInfoAsList {

    AdjacentSystemType system;
    Long id;
    LocalDateTime updatedAt;
    AdjacentSystemStatus status;
    String info;


    public LogBean(AdjacentSystemType system, Long id, LocalDateTime updatedAt, AdjacentSystemStatus status, String payload) {
        this.system = system;
        this.id = id;
        this.updatedAt = updatedAt;
        this.status = status;
        this.info = payload;
    }


    @Override
    public List<Pair> toVerticalInfoListUa() {
        ObjectMapper mapper = new ObjectMapper().enable(SerializationFeature.INDENT_OUTPUT);
        Object jsonObject = null;
        String prettyJson = "";
        try {
            jsonObject = mapper.readValue(info, Object.class);
            prettyJson = mapper.writeValueAsString(jsonObject);
        } catch (JsonProcessingException e) {
            log.error(e.getMessage() + " " + info);
        }


        return List.of(
                new Pair(0, "Подія {"),
                new Pair(1, "id команди " + getId()),
                new Pair(1, "система -  " + getSystem()),
                new Pair(1, "стан - " + getStatus()),
                new Pair(1, "час створення - " +
                        (getUpdatedAt() != null ? getUpdatedAt().format(DateTimeFormatter
                                .ofPattern("dd/MM/yyyy\t HH:mm:ss.SSS")) : " ")),
                new Pair(1, "опис - " + (!prettyJson.isBlank() ? prettyJson
                        : getInfo())),

                new Pair(0, "}")
        );
    }
}
