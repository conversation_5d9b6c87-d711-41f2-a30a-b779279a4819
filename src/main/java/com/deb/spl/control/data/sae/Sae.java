package com.deb.spl.control.data.sae;

import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.VoltageStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.NotImplementedException;
import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.Map;

@NoArgsConstructor
@Getter
@Setter
@Builder
@AllArgsConstructor
@Slf4j
public class Sae implements AdjacentSystem {
    private static final long serialVersionUID = 3789607596181077227L;

    @JsonIgnore
    final AdjacentSystemType adjacentSystemType = AdjacentSystemType.SAE;
    private SaeStatus saeStatus;
    private AdjacentSystemStatus readiness;
    private SaePowerSourceStatus energizedByAdj;
    private SaePowerSourceStatus energizedByHds;
    private SaePowerSourceStatus energizedByExternalPowerSource;
    private ADJstatus adjStart;
    private ADJstatus adjStop;
    private ADJstatus adjLock;
    private ADJstatus adjUnlock;
    private HDSstatus hdsStatus;
    private VoltageStatus voltageStatus;
    private VoltageStatus externalPowerSourceVoltage;
    private VoltageStatus BSVoltage;
    private SaeFeederStatus feeder1Status;
    private SaeFeederStatus feeder2Status;
    private SaeFeederStatus feeder3Status;
    private SaeFeederStatus feeder4Status;
    private SaeFeederStatus feeder5Status;
    private SaeFeederStatus feeder6Status;
    private SaeFeederStatus feederNppa1Status;


    public Sae(@NotNull Sae prototype) {
        this.saeStatus = prototype.getSaeStatus();
        this.energizedByAdj = prototype.getEnergizedByAdj();
        this.energizedByHds = prototype.getEnergizedByHds();
        this.energizedByExternalPowerSource = prototype.getEnergizedByExternalPowerSource();
        this.readiness = prototype.getReadiness();
        this.adjStart = prototype.getAdjStart();
        this.adjStop = prototype.getAdjStop();
        this.adjLock = prototype.getAdjLock();
        this.adjUnlock = prototype.getAdjUnlock();
        this.hdsStatus = prototype.getHdsStatus();
        this.voltageStatus = prototype.getVoltageStatus();
        this.feeder1Status = prototype.getFeeder1Status();
        this.feeder2Status = prototype.getFeeder2Status();
        this.feeder3Status = prototype.getFeeder3Status();
        this.feeder4Status = prototype.getFeeder4Status();
        this.feeder5Status = prototype.getFeeder5Status();
        this.feeder6Status = prototype.getFeeder6Status();
        this.feederNppa1Status = prototype.getFeederNppa1Status();
        this.externalPowerSourceVoltage = prototype.getExternalPowerSourceVoltage();
        this.BSVoltage = prototype.getBSVoltage();
    }


    @Override
    public AdjacentSystemType getAdjacentSystemType() {
        return adjacentSystemType;
    }


    public HashMap<String, SaeFeederStatus> listFeeders() {
        return getStringSaeFeederStatusHashMap(feeder1Status, feeder2Status, feeder3Status,
                feeder4Status, feeder5Status, feeder6Status, feederNppa1Status);
    }


    @NotNull
    public static HashMap<String, SaeFeederStatus> getStringSaeFeederStatusHashMap(
            SaeFeederStatus feeder1Status, SaeFeederStatus feeder2Status,
            SaeFeederStatus feeder3Status, SaeFeederStatus feeder4Status,
            SaeFeederStatus feeder5Status, SaeFeederStatus feeder6Status,
            SaeFeederStatus feederNppa1Status) {
        HashMap<String, SaeFeederStatus> feeders = new HashMap<>();

        feeders.put("Feeder1", feeder1Status);
        feeders.put("Feeder2", feeder2Status);
        feeders.put("Feeder3", feeder3Status);
        feeders.put("Feeder4", feeder4Status);
        feeders.put("Feeder5", feeder5Status);
        feeders.put("Feeder6", feeder6Status);
        feeders.put("FeederNppa1", feederNppa1Status);

        return feeders;
    }

    public void updateFeeders(Map<String, SaeFeederStatus> feedersSates) {
        for (Map.Entry<String, SaeFeederStatus> feeder : feedersSates.entrySet()) {
            if (feeder.getValue() != null) {
                switch (feeder.getKey().toLowerCase()) {
                    case "feeder1" -> feeder1Status = feeder.getValue();
                    case "feeder2" -> feeder2Status = feeder.getValue();
                    case "feeder3" -> feeder3Status = feeder.getValue();
                    case "feeder4" -> feeder4Status = feeder.getValue();
                    case "feeder5" -> feeder5Status = feeder.getValue();
                    case "feeder6" -> feeder6Status = feeder.getValue();
                    case "feedernppa1" -> feederNppa1Status = feeder.getValue();
                }
            }
        }
    }

    @Override
    public String getErrorMessage() {
        throw new NotImplementedException("dupa");
    }

    @Override
    public boolean isMalfunctionPresent() {
        return SaeStatus.toAdjacentSystemStatus(saeStatus) == AdjacentSystemStatus.ERROR ||
                SaeStatus.toAdjacentSystemStatus(saeStatus) == AdjacentSystemStatus.WARNING;
    }

    @Override
    public AdjacentSystemStatus getStatus() {
        return SaeStatus.toAdjacentSystemStatus(saeStatus);
    }

    public boolean isConnected() {
        return SaeStatus.toAdjacentSystemStatus(saeStatus) != AdjacentSystemStatus.NOT_CONNECTED;
    }

    public boolean isReady() {
        return this.readiness.equals(AdjacentSystemStatus.OK);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Sae sae)) return false;

        if (getSaeStatus() != sae.getSaeStatus()) return false;
        if (getReadiness() != sae.getReadiness()) return false;
        if (getEnergizedByAdj() != sae.getEnergizedByAdj()) return false;
        if (getEnergizedByHds() != sae.getEnergizedByHds()) return false;
        if (getEnergizedByExternalPowerSource() != sae.getEnergizedByExternalPowerSource()) return false;
        if (getAdjStart() != sae.getAdjStart()) return false;
        if (getAdjStop() != sae.getAdjStop()) return false;
        if (getAdjLock() != sae.getAdjLock()) return false;
        if (getAdjUnlock() != sae.getAdjUnlock()) return false;
        if (getHdsStatus() != sae.getHdsStatus()) return false;
        if (getVoltageStatus() != sae.getVoltageStatus()) return false;
        if (getExternalPowerSourceVoltage() != sae.getExternalPowerSourceVoltage()) return false;
        if (getBSVoltage() != sae.getBSVoltage()) return false;
        if (getFeeder1Status() != sae.getFeeder1Status()) return false;
        if (getFeeder2Status() != sae.getFeeder2Status()) return false;
        if (getFeeder3Status() != sae.getFeeder3Status()) return false;
        if (getFeeder4Status() != sae.getFeeder4Status()) return false;
        if (getFeeder5Status() != sae.getFeeder5Status()) return false;
        if (getFeeder6Status() != sae.getFeeder6Status()) return false;
        return getFeederNppa1Status() == sae.getFeederNppa1Status();
    }

    @Override
    public int hashCode() {
        int result = getSaeStatus() != null ? getSaeStatus().hashCode() : 0;
        result = 31 * result + (getReadiness() != null ? getReadiness().hashCode() : 0);
        result = 31 * result + (getEnergizedByAdj() != null ? getEnergizedByAdj().hashCode() : 0);
        result = 31 * result + (getEnergizedByHds() != null ? getEnergizedByHds().hashCode() : 0);
        result = 31 * result + (getEnergizedByExternalPowerSource() != null ? getEnergizedByExternalPowerSource().hashCode() : 0);
        result = 31 * result + (getAdjStart() != null ? getAdjStart().hashCode() : 0);
        result = 31 * result + (getAdjStop() != null ? getAdjStop().hashCode() : 0);
        result = 31 * result + (getAdjLock() != null ? getAdjLock().hashCode() : 0);
        result = 31 * result + (getAdjUnlock() != null ? getAdjUnlock().hashCode() : 0);
        result = 31 * result + (getHdsStatus() != null ? getHdsStatus().hashCode() : 0);
        result = 31 * result + (getVoltageStatus() != null ? getVoltageStatus().hashCode() : 0);
        result = 31 * result + (getExternalPowerSourceVoltage() != null ? getExternalPowerSourceVoltage().hashCode() : 0);
        result = 31 * result + (getBSVoltage() != null ? getBSVoltage().hashCode() : 0);
        result = 31 * result + (getFeeder1Status() != null ? getFeeder1Status().hashCode() : 0);
        result = 31 * result + (getFeeder2Status() != null ? getFeeder2Status().hashCode() : 0);
        result = 31 * result + (getFeeder3Status() != null ? getFeeder3Status().hashCode() : 0);
        result = 31 * result + (getFeeder4Status() != null ? getFeeder4Status().hashCode() : 0);
        result = 31 * result + (getFeeder5Status() != null ? getFeeder5Status().hashCode() : 0);
        result = 31 * result + (getFeeder6Status() != null ? getFeeder6Status().hashCode() : 0);
        result = 31 * result + (getFeederNppa1Status() != null ? getFeederNppa1Status().hashCode() : 0);
        return result;
    }
}

