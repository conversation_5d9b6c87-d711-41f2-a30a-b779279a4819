package com.deb.spl.control.data.asku;

import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.*;


@NoArgsConstructor
@Getter
@Setter
@Slf4j
public class Plc implements AdjacentSystem {
    public static final long serialVersionUID = 521286412887409735L;


    @JsonIgnore
    @Transient
    private final AdjacentSystemType adjacentSystemType = AdjacentSystemType.PLC;
    @Column(name = "plc_status", columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private AdjacentSystemStatus status;

    @Override
    public AdjacentSystemType getAdjacentSystemType() {
        return adjacentSystemType;
    }

    @Override
    public String getErrorMessage() {
        return null;
    }

    @Override
    public boolean isMalfunctionPresent() {
        return status != AdjacentSystemStatus.ERROR;
    }

    @Override
    public AdjacentSystemStatus getStatus() {
        return status != null ? status : AdjacentSystemStatus.UNDEFINED;
    }

    @PrePersist
    public void setDefaultValues() {
        status = status == null ? AdjacentSystemStatus.UNDEFINED : status;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Plc plc)) return false;

        if (getAdjacentSystemType() != plc.getAdjacentSystemType()) return false;
        return getStatus() == plc.getStatus();
    }

    @Override
    public int hashCode() {
        int result = getAdjacentSystemType().hashCode();
        result = 31 * result + (getStatus() != null ? getStatus().hashCode() : 0);
        return result;
    }
}
