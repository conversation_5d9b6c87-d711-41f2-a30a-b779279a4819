package com.deb.spl.control.data.asku;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@JsonPropertyOrder({"loadTemperature",
        "latitudeRad",
        "longitudeRad",
        "altitude",
        "inclinationAngle",
        "trajectory",
        "readiness",
        "isProDetected",
        "missileOperatingMode",
        "validatedByTlc",
        "tlCode",
        "scheduled",
        "startTime"})
public record LaunchInitialDataDto(
        //null or -999.9
        double loadTemperature,
        @NotBlank
        String latitudeRad,
        @NotBlank
        String longitudeRad,
        @NotNull
        double altitude,
        @NotNull
        double inclinationAngle,
        @NotNull
        TrajectoryType trajectory,
        @NotNull
        Readiness readiness,
        @NotNull
        boolean isProDetected,
        @NotNull
        MissileOperatingMode missileOperatingMode,
        boolean validatedByTlc,
        String tlCode,
        boolean scheduled,
        LocalDateTime startTime) {
}
