package com.deb.spl.control.data;

import com.deb.spl.control.data.sae.CommandState;
import com.deb.spl.control.data.suto.SutoProperty;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Converter
@Slf4j
public class StringListConverter implements AttributeConverter<List<SutoProperty>, String> {

    public static final String LIST_ELEMENTS_SPLIT_CHAR = ";";
    public static final String STRING_SPLIT_CHAR = ",";

    @Override
    public String convertToDatabaseColumn(List<SutoProperty> stringList) {
        List<String> strings = stringList.stream().map(this::propertyToString).toList();
        return stringList != null ? String.join(LIST_ELEMENTS_SPLIT_CHAR, strings) : "";
    }

    public String propertyToString(SutoProperty property) {
        if (property == null) {
            return "";
        }

        return "{" +
                property.getId() +
                STRING_SPLIT_CHAR
                +
                property.getCaption() +
                STRING_SPLIT_CHAR +
                property.getName() +
                STRING_SPLIT_CHAR +
                property.getState() +
                "}";
    }

    public SutoProperty fromString(String string) {
        if (string.isBlank()) {
            return null;
        }
        string = string.trim();
        if (string.charAt(0) != '{' || string.charAt(string.length() - 1) != '}') {
            log.error("broken SutoProperty can't be processed" + string.toString());
            return null;
        }
        string = string.replace("{", "");
        string = string.replace("}", "");

        List<String> properties = Arrays.asList(string.split(STRING_SPLIT_CHAR));
        if (properties.size() < 4) {
            log.error("broken SutoProperty can't be processed. Expected number of element 3 but found "
                    + properties.size() + string.toString());
            return null;
        }

        return SutoProperty.builder()
                .id(Long.valueOf(properties.get(0).trim()))
                .caption(properties.get(1).trim())
                .name(properties.get(2).trim())
                .state(CommandState.valueOf(properties.get(3).trim()))
                .build();
    }


    @Override
    public List<SutoProperty> convertToEntityAttribute(String dbData) {
        List<String> strings = dbData != null ? Arrays.asList(dbData.split(LIST_ELEMENTS_SPLIT_CHAR)) : Collections.emptyList();
        return strings.stream().map(this::fromString).filter(Objects::nonNull).toList();
    }
}
