package com.deb.spl.control.data.suto;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;

@JsonPropertyOrder({"id",
        "status",
        "stats",
        "leftFrontOutriggerEmergencyCode",
        "rightFrontOutriggerEmergencyCode",
        "leftRearOutriggerEmergencyCode",
        "rightRearOutriggerEmergencyCode",
        "properties"})
public record SutoDto(
        @NotNull
        @Positive
        Long id,
        AdjacentSystemStatus status,
        @NotNull
        SutoStatsDto stats,
        @Range(min = 0, max = 15)
        int leftFrontOutriggerEmergencyCode,
        @Range(min = 0, max = 15)
        int rightFrontOutriggerEmergencyCode,
        @Range(min = 0, max = 15)
        int leftRearOutriggerEmergencyCode,
        @Range(min = 0, max = 15)
        int rightRearOutriggerEmergencyCode,
        @NotNull
        List<SutoPropertyDto> properties
) {

}
