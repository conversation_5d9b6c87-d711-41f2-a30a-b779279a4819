package com.deb.spl.control.data.bins;

import com.deb.spl.control.views.adjacentSystems.utils.HasVerticalInfoAsList;
import com.deb.spl.control.data.AdjacentSystemType;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Getter
@Setter
@MappedSuperclass
public class SplNavigationRecord implements HasVerticalInfoAsList {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Enumerated
    @Column(name = "adjacent_system", nullable = false)
    private AdjacentSystemType adjacentSystem;

    @Column(name = "generation_date")
    private LocalDateTime generationDate;

    @Enumerated
    @Column(name = "direction")
    private LogRecordDirection direction;

    @Size(max = 255)
    @Column(name = "original_sentence")
    private String originalSentence;

    @Size(max = 255)
    @Column(name = "originator")
    private String originator;

    @Override
    public List<Pair> toVerticalInfoListUa() {
        String loggingTimeStr = generationDate != null ?generationDate.format(DateTimeFormatter.ofPattern("dd/MM/yyyy\t HH:mm:ss"))
                : "Невідомо";
        return List.of(
                new Pair(0, "Речення {"),
                new Pair(1, "id запису " + getId()),
                new Pair(1, "речення - ' " + originalSentence),
//                new Pair(1, "створив - " + originator),
                new Pair(1, "ситема - " + getAdjacentSystem().getUa()),
                new Pair(1, "час тримання - " + loggingTimeStr),
                new Pair(0, "}")
        );
    }
}