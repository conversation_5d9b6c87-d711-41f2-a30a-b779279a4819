package com.deb.spl.control.data.suto;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Embeddable
public class SutoSettings {

    @Enumerated(EnumType.STRING)
    @Column(columnDefinition = "varchar(30) default 'UNKNOWN'")
    private ChassisState chassisState;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof SutoSettings settings)) return false;

        return getChassisState() == settings.getChassisState();
    }

    @Override
    public int hashCode() {
        return getChassisState() != null ? getChassisState().hashCode() : 0;
    }
}
