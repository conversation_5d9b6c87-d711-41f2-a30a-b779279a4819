package com.deb.spl.control.data.ppo;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.AttributeConverter;
import javax.persistence.Convert;
import java.util.*;

@Convert
@Slf4j
public class PpoBayListConverter implements AttributeConverter<List<PpoBay>, String> {
    public static final String LIST_ELEMENTS_SPLIT_CHAR = ";";
    public static final String STRING_SPLIT_CHAR = ",";

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String convertToDatabaseColumn(List<PpoBay> attributes) {
        List<String> strings = attributes.stream()
                .sorted(Comparator.comparing(PpoBay::getId))
                .map(this::setBayInitialValues)
                .map(this::bayToString).toList();

        return strings != null ? String.join(LIST_ELEMENTS_SPLIT_CHAR, strings) : "";
    }

    private PpoBay setBayInitialValues(PpoBay bay) {
        if (bay == null) {
            return null;
        }

        bay.getUnitsInPPoBay().replaceAll((k, v) -> {
            if (v == null) {
                return null;
            }
            v.setUnitStatus(v.getUnitStatus() == null ? PpoUnitStatus.UNDEFINED : v.getUnitStatus());
            return v;
        });

        return bay;
    }

    private String bayToString(PpoBay ppoBay) {
        if (ppoBay == null) {
            return "";
        }
        String converted = "";
        try {
            converted = objectMapper.writeValueAsString(ppoBay);
        } catch (JsonProcessingException e) {
            log.error(e.getMessage() + " \n" + Arrays.toString(e.getStackTrace()));
        }

        return converted;
    }

    private PpoBay fromString(String string) {
        if (string.isBlank()) {
            return null;
        }
        PpoBay ppoBay = null;
        try {
            ppoBay = objectMapper.readValue(string, PpoBay.class);
        } catch (JsonProcessingException e) {
            log.error(e.getMessage() + " \n" + Arrays.toString(e.getStackTrace()));
        }

        return ppoBay;
    }

    @Override
    public List<PpoBay> convertToEntityAttribute(String dbData) {
        List<String> strings = dbData != null ? Arrays.asList(dbData.split(LIST_ELEMENTS_SPLIT_CHAR)) :
                Collections.emptyList();
        return strings.stream().map(this::fromString).filter(Objects::nonNull)
                .sorted(Comparator.comparing(PpoBay::getId))
                .toList();
    }
}
