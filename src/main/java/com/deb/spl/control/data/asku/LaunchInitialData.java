package com.deb.spl.control.data.asku;


import lombok.*;

import java.time.LocalDateTime;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class LaunchInitialData {
    private static final long serialVersionUID = -2704405187981926130L;

    private Long id;

    private LocalDateTime createdAt;

    //    Температура заряда МД
    @Builder.Default
    private final double loadTemperature = -999.9;

    //    Геодезическая широта точки прицеливания
    private String latitudeRad;

    //    Геодезическая долгота точки прицеливания
    private String longitudeRad;

    //    Превышение точки прицеливания над ОЗЭ
    private double altitude;

    //    Требуемый угол наклона вектора относительной скорости к горизонту в точке прицеливания
    private double inclinationAngle;

    //    Признак типа траектории, Ptr
    private TrajectoryType trajectory;

    //    Признак типа готовности, Pg
    private Readiness readiness;

    //    Признак наличия противоракетной обороны, Ppro
    private boolean proDetected;

    //    Признак режима работы, Preg
    private MissileOperatingMode missileOperatingMode;

    private String tlCode;

    private boolean validatedByTlc;
    private boolean scheduled;
    private LocalDateTime startTime;
    private OrderInfo orderInfo;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        LaunchInitialData that = (LaunchInitialData) o;

        if (Double.compare(that.getLoadTemperature(), getLoadTemperature()) != 0) return false;
        if (Double.compare(that.getAltitude(), getAltitude()) != 0) return false;
        if (Double.compare(that.getInclinationAngle(), getInclinationAngle()) != 0) return false;
        if (isProDetected() != that.isProDetected()) return false;
        if (isValidatedByTlc() != that.isValidatedByTlc()) return false;
        if (isScheduled() != that.isScheduled()) return false;
        if (getId() != null ? !getId().equals(that.getId()) : that.getId() != null) return false;
        if (getCreatedAt() != null ? !getCreatedAt().equals(that.getCreatedAt()) : that.getCreatedAt() != null)
            return false;
        if (getLatitudeRad() != null ? !getLatitudeRad().equals(that.getLatitudeRad()) : that.getLatitudeRad() != null)
            return false;
        if (getLongitudeRad() != null ? !getLongitudeRad().equals(that.getLongitudeRad()) : that.getLongitudeRad() != null)
            return false;
        if (getTrajectory() != that.getTrajectory()) return false;
        if (getReadiness() != that.getReadiness()) return false;
        if (getMissileOperatingMode() != that.getMissileOperatingMode()) return false;
        if (getTlCode() != null ? !getTlCode().equals(that.getTlCode()) : that.getTlCode() != null) return false;
        return getStartTime() != null ? getStartTime().equals(that.getStartTime()) : that.getStartTime() == null;
    }

    @Override
    public int hashCode() {
        int result;
        long temp;
        result = getId() != null ? getId().hashCode() : 0;
        result = 31 * result + (getCreatedAt() != null ? getCreatedAt().hashCode() : 0);
        temp = Double.doubleToLongBits(getLoadTemperature());
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        result = 31 * result + (getLatitudeRad() != null ? getLatitudeRad().hashCode() : 0);
        result = 31 * result + (getLongitudeRad() != null ? getLongitudeRad().hashCode() : 0);
        temp = Double.doubleToLongBits(getAltitude());
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        temp = Double.doubleToLongBits(getInclinationAngle());
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        result = 31 * result + (getTrajectory() != null ? getTrajectory().hashCode() : 0);
        result = 31 * result + (getReadiness() != null ? getReadiness().hashCode() : 0);
        result = 31 * result + (isProDetected() ? 1 : 0);
        result = 31 * result + (getMissileOperatingMode() != null ? getMissileOperatingMode().hashCode() : 0);
        result = 31 * result + (getTlCode() != null ? getTlCode().hashCode() : 0);
        result = 31 * result + (isValidatedByTlc() ? 1 : 0);
        result = 31 * result + (isScheduled() ? 1 : 0);
        result = 31 * result + (getStartTime() != null ? getStartTime().hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "LaunchInitialData{" +
               "id=" + id +
               ", createdAt=" + createdAt +
               ", loadTemperature=" + loadTemperature +
               ", latitudeRad='" + latitudeRad + '\'' +
               ", longitudeRad='" + longitudeRad + '\'' +
               ", altitude=" + altitude +
               ", inclinationAngle=" + inclinationAngle +
               ", trajectory=" + trajectory +
               ", readiness=" + readiness +
               ", proDetected=" + proDetected +
               ", missileOperatingMode=" + missileOperatingMode +
               ", tlCode='" + tlCode + '\'' +
               ", validatedByTlc=" + validatedByTlc +
               ", scheduled=" + scheduled +
               ", startTime" + (startTime == null ? "null" : startTime.toString()) +
               '}';
    }
}
