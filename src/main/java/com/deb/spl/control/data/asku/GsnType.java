package com.deb.spl.control.data.asku;

import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;

import java.util.Arrays;
import java.util.Optional;

public enum GsnType implements AdjacentSystemWithDescriptionUa {
    NO_GSN("Без ГСН"),
    K_GSN("КГ<PERSON><PERSON>"),
    OE_GSN("ОЕГСН"),
    UNDEFINED("Невідомо");

    private final String value;

    GsnType(String value) {
        this.value = value;
    }

    @Override
    public String getValueUa() {
        return value;
    }

    public static GsnType fromValueUa(String uaVal) {
        Optional<GsnType> status = Arrays.stream(GsnType.values())
                .filter(e -> e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(UNDEFINED);
    }
}
