package com.deb.spl.control.data.msu;

import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import net.sf.marineapi.nmea.util.MSU.*;

import java.util.Objects;

import static com.google.common.primitives.Doubles.compare;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Slf4j
//@Builder
public class MeteoStationUnit implements AdjacentSystem {
    private static final long serialVersionUID = -4850606583017630293L;

    @JsonIgnore
    private final AdjacentSystemType adjacentSystemType = AdjacentSystemType.MSU;

    private double windDirection;
    private double windSpeed;
    private double airTemperature;

    private double voltage;
    private double msuTemperature;
    @Setter
    private MsuPosture posture;
    @Setter
    private MsuOperatingMode operatingMode;
    @Setter
    private MsuHeatingMode heatingMode;
    @Setter
    private MsuHeatingModePermission heatingModePermission;
    @Setter
    private MsuTemperatureSensorBlowing temperatureSensorBlowing;

    private MsuTemperatureSensorMalfunction temperatureSensorMalfunction;
    private MsuWindSpeedSensorMalfunction windSpeedSensorMalfunction;
    private MsuElectricalDriveMalfunction electricalDriveMalfunction;
    private MsuEndSwitchesMalfunction endSwitchesMalfunction;
    private MsuVoltageMalfunction voltageMalfunction;
    private MsuPostureSwitchMalfunction postureSwitchingMalfunction;
    private MsuDataExchangeMalfunction dataExchangeMalfunction;
    @JsonIgnore
    @Setter
    private boolean isConnected = true;

    /**
     * @param windDirection
     * @param windSpeed
     * @param airTemperature
     * @param voltage
     * @param msuTemperature
     * @param posture
     * @param operatingMode
     * @param heatingMode
     * @param heatingModePermission
     * @param temperatureSensorBlowing
     * @param malfunctionPresent
     * @param temperatureSensorMalfunction
     * @param windSpeedSensorMalfunction
     * @param electricalDriveMalfunction
     * @param endSwitchesMalfunction
     * @param voltageMalfunction
     * @param postureSwitchingMalfunction
     * @param dataExchangeMalfunction
     */
    public MeteoStationUnit(double windDirection, double windSpeed, double airTemperature, double voltage, double msuTemperature, MsuPosture posture, MsuOperatingMode operatingMode, MsuHeatingMode heatingMode, MsuHeatingModePermission heatingModePermission, MsuTemperatureSensorBlowing temperatureSensorBlowing, boolean malfunctionPresent, MsuTemperatureSensorMalfunction temperatureSensorMalfunction, MsuWindSpeedSensorMalfunction windSpeedSensorMalfunction, MsuElectricalDriveMalfunction electricalDriveMalfunction, MsuEndSwitchesMalfunction endSwitchesMalfunction, MsuVoltageMalfunction voltageMalfunction, MsuPostureSwitchMalfunction postureSwitchingMalfunction, MsuDataExchangeMalfunction dataExchangeMalfunction) {
        this.windDirection = windDirection;
        this.windSpeed = windSpeed;
        this.airTemperature = airTemperature;
        this.voltage = voltage;
        this.msuTemperature = msuTemperature;
        this.posture = posture;
        this.operatingMode = operatingMode;
        this.heatingMode = heatingMode;
        this.heatingModePermission = heatingModePermission;
        this.temperatureSensorBlowing = temperatureSensorBlowing;
        this.malfunctionPresent = malfunctionPresent;
        this.temperatureSensorMalfunction = temperatureSensorMalfunction;
        this.windSpeedSensorMalfunction = windSpeedSensorMalfunction;
        this.electricalDriveMalfunction = electricalDriveMalfunction;
        this.endSwitchesMalfunction = endSwitchesMalfunction;
        this.voltageMalfunction = voltageMalfunction;
        this.postureSwitchingMalfunction = postureSwitchingMalfunction;
        this.dataExchangeMalfunction = dataExchangeMalfunction;
        this.setConnected(true);
    }


    @Setter
    private boolean malfunctionPresent = false;

    public boolean isNormal() {
        return !malfunctionPresent;
    }

    public void setNormal(boolean state) {
        this.malfunctionPresent = !state;
    }


    private static final String OUT_OF_RANGE_STRING_TEMPLATE = "value of %s = %s is out of range %s .. %s ";

    public void setWindDirection(double windDirection) {
        if (compare(windDirection, 0.0) < 0.0 || compare(windDirection, 359.9) > 0.0) {
            log.warn(String.format(OUT_OF_RANGE_STRING_TEMPLATE, "windDirection", windDirection, 0.0, 359.0));
            return;
        }
        this.windDirection = windDirection;
    }

    public void setWindSpeed(double windSpeed) {
        if (compare(windSpeed, 0) < 0.0 || compare(windSpeed, 30.0) > 0) {
            log.warn(String.format(OUT_OF_RANGE_STRING_TEMPLATE, "windSpeed", windSpeed, 0.0, 30.0));
            return;
        }
        this.windSpeed = windSpeed;
    }

    public void setAirTemperature(double airTemperature) {
        if (compare(airTemperature, -40.0) < 0.0 || compare(airTemperature, 55.0) > 0) {
            log.warn(String.format(OUT_OF_RANGE_STRING_TEMPLATE, "airTemperature", airTemperature, -40.0, 55.0));
            return;
        }
        this.airTemperature = airTemperature;
    }

    public void setVoltage(double voltage) {
        if (compare(voltage, 0.0) < 0 || compare(voltage, 40.0) > 0) {
            log.warn(String.format(OUT_OF_RANGE_STRING_TEMPLATE, "voltage", voltage, 0.0, 40.0));
            return;
        }
        this.voltage = voltage;
    }

    public void setMsuTemperature(double msuTemperature) {
        if (compare(msuTemperature, -40.0) < 0.0 || compare(msuTemperature, 55.0) > 0) {
            log.warn(String.format(OUT_OF_RANGE_STRING_TEMPLATE, "msuTemperature", msuTemperature, -40.0, 55.0));
            return;
        }
        this.msuTemperature = msuTemperature;
    }

    public void setTemperatureSensorMalfunction(MsuTemperatureSensorMalfunction temperatureSensorMalfunction) {
        this.temperatureSensorMalfunction = temperatureSensorMalfunction;
        updateState(temperatureSensorMalfunction);
    }

    public void setWindSpeedSensorMalfunction(MsuWindSpeedSensorMalfunction windSpeedSensorMalfunction) {
        this.windSpeedSensorMalfunction = windSpeedSensorMalfunction;
        updateState(windSpeedSensorMalfunction);
    }

    public void setElectricalDriveMalfunction(MsuElectricalDriveMalfunction electricalDriveMalfunction) {
        this.electricalDriveMalfunction = electricalDriveMalfunction;
        updateState(electricalDriveMalfunction);
    }

    public void setEndSwitchesMalfunction(MsuEndSwitchesMalfunction endSwitchesMalfunction) {
        this.endSwitchesMalfunction = endSwitchesMalfunction;
        updateState(endSwitchesMalfunction);
    }

    public void setVoltageMalfunction(MsuVoltageMalfunction voltageMalfunction) {
        this.voltageMalfunction = voltageMalfunction;
        updateState(voltageMalfunction);
    }

    public void setPostureSwitchingMalfunction(MsuPostureSwitchMalfunction postureSwitchingMalfunction) {
        this.postureSwitchingMalfunction = postureSwitchingMalfunction;
        updateState(postureSwitchingMalfunction);
    }

    public void setDataExchangeMalfunction(MsuDataExchangeMalfunction dataExchangeMalfunction) {
        this.dataExchangeMalfunction = dataExchangeMalfunction;
        updateState(dataExchangeMalfunction);
    }

    public void updateState(MsuMalfunction malfunction) {
        if (malfunction == null) {
            return;
        }
        if (isNormal()) {
            this.malfunctionPresent = !malfunction.isNormal();
        }
    }

    public void updateState() {
        malfunctionPresent = !temperatureSensorMalfunction.equals(MsuTemperatureSensorMalfunction.NORMAL) ||
                             !windSpeedSensorMalfunction.equals(MsuWindSpeedSensorMalfunction.NORMAL) ||
                             !electricalDriveMalfunction.equals(MsuElectricalDriveMalfunction.NORMAL) ||
                             !endSwitchesMalfunction.equals(MsuEndSwitchesMalfunction.NORMAL) ||
                             !voltageMalfunction.equals(MsuVoltageMalfunction.NORMAL) ||
                             !postureSwitchingMalfunction.equals(MsuPostureSwitchMalfunction.NORMAL) ||
                             !dataExchangeMalfunction.equals(MsuDataExchangeMalfunction.NORMAL);
    }

    @Override
    public AdjacentSystemType getAdjacentSystemType() {
        return adjacentSystemType;
    }

    @Override
    public String getErrorMessage() {
        StringBuilder builder = new StringBuilder();
        updateState();
        if (!malfunctionPresent) {
            return "everything works fine!";
        }
        builder.append("Malfunctions has occurred: [").append("\n");
        if (temperatureSensorMalfunction != null && !temperatureSensorMalfunction.equals(MsuTemperatureSensorMalfunction.NORMAL)) {
            builder.append(temperatureSensorMalfunction.getStringValue());
        }
        if (windSpeedSensorMalfunction != null && !windSpeedSensorMalfunction.equals(MsuWindSpeedSensorMalfunction.NORMAL)) {
            builder.append(windSpeedSensorMalfunction.getStringValue()).append("\n");
        }
        if (electricalDriveMalfunction != null && !electricalDriveMalfunction.equals(MsuElectricalDriveMalfunction.NORMAL)) {
            builder.append(electricalDriveMalfunction.getStringValue()).append("\n");
        }

        if (endSwitchesMalfunction != null && !endSwitchesMalfunction.equals(MsuEndSwitchesMalfunction.NORMAL)) {
            builder.append(endSwitchesMalfunction.getStringValue()).append("\n");
        }

        if (!voltageMalfunction.equals(MsuVoltageMalfunction.NORMAL)) {
            builder.append(voltageMalfunction.getStringValue()).append("\n");
        }

        if (!postureSwitchingMalfunction.equals(MsuPostureSwitchMalfunction.NORMAL)) {
            builder.append(postureSwitchingMalfunction.getStringValue()).append("\n");
        }

        if (!dataExchangeMalfunction.equals(MsuDataExchangeMalfunction.NORMAL)) {
            builder.append(dataExchangeMalfunction.getStringValue()).append("\n");
        }

        builder.append("]");

        return builder.toString();
    }

    @Override
    public AdjacentSystemStatus getStatus() {

        return !isConnected ? AdjacentSystemStatus.NOT_CONNECTED
                : (isMalfunctionPresent() ? AdjacentSystemStatus.ERROR : AdjacentSystemStatus.OK);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MeteoStationUnit that = (MeteoStationUnit) o;
        return Double.compare(that.windDirection, windDirection) == 0 && Double.compare(that.windSpeed, windSpeed) == 0 && Double.compare(that.airTemperature, airTemperature) == 0 && Double.compare(that.voltage, voltage) == 0 && Double.compare(that.msuTemperature, msuTemperature) == 0 && malfunctionPresent == that.malfunctionPresent && adjacentSystemType == that.adjacentSystemType && posture == that.posture && operatingMode == that.operatingMode && heatingMode == that.heatingMode && heatingModePermission == that.heatingModePermission && temperatureSensorBlowing == that.temperatureSensorBlowing && temperatureSensorMalfunction == that.temperatureSensorMalfunction && windSpeedSensorMalfunction == that.windSpeedSensorMalfunction && electricalDriveMalfunction == that.electricalDriveMalfunction && endSwitchesMalfunction == that.endSwitchesMalfunction && voltageMalfunction == that.voltageMalfunction && postureSwitchingMalfunction == that.postureSwitchingMalfunction && dataExchangeMalfunction == that.dataExchangeMalfunction;
    }

    @Override
    public int hashCode() {
        return Objects.hash(adjacentSystemType, windDirection, windSpeed, airTemperature, voltage, msuTemperature, posture, operatingMode, heatingMode, heatingModePermission, temperatureSensorBlowing, temperatureSensorMalfunction, windSpeedSensorMalfunction, electricalDriveMalfunction, endSwitchesMalfunction, voltageMalfunction, postureSwitchingMalfunction, dataExchangeMalfunction, malfunctionPresent);
    }
}
