package com.deb.spl.control.data.asku;

import java.util.Arrays;
import java.util.Optional;

public enum LaunchResult {
    LAUNCH("Пуск виконано"),
    FAILURE("Пуск невиконано"),
    REMOVE("Видалено"),
    NONE("Не відбувався"),
    UNKNOWN("Невідомо");

    private final String value;

    LaunchResult(String value) {
        this.value = value;
    }

    public String getValueUa() {
        return value;
    }

    public static LaunchResult fromValueUa(String uaVal) {
        Optional<LaunchResult> status = Arrays.stream(LaunchResult.values())
                .filter(e -> e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(UNKNOWN);
    }
}
