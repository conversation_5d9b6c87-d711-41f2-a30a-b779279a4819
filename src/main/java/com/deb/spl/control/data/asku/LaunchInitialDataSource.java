package com.deb.spl.control.data.asku;

import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

public enum LaunchInitialDataSource implements AdjacentSystemWithDescriptionUa {
    MSG("Електронний наказ"),
    VOICE("Голосовий"),
    PAPER_WORK("Лист"),
    PLC("ПЛК"),
    OTHER("Інше"),

    UNKNOWN("Невідомо");

    @Getter
    private String description;

    LaunchInitialDataSource(String description) {
        this.description = description;
    }

    @Override
    public String getValueUa() {
        return description;
    }

    public static LaunchInitialDataSource fromValueUa(String uaVal) {
        Optional<LaunchInitialDataSource> status = Arrays.stream(LaunchInitialDataSource.values())
                .filter(e -> e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(UNKNOWN);
    }
}
