package com.deb.spl.control.data.nppa;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.WARN)
public interface NcokMapper {
    Ncok map(NcokDto value);

    @Mapping(target = "isSutoConnected", source = "sutoConnected")
    @Mapping(target = "isNcokConnected", source = "ncokConnected")
    Ncok map(NcokDao value);

    @Mapping(target = "isSutoConnected", source = "sutoConnected")
    @Mapping(target = "isNcokConnected", source = "ncokConnected")
    NcokDto toDto(Ncok value);

    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "startedDate", ignore = true)
    @Mapping(target = "isSutoConnected", source = "sutoConnected")
    @Mapping(target = "isNcokConnected", source = "ncokConnected")
    @Mapping(target = "id", ignore = true)
    NcokDao toDao(Ncok value);

}
