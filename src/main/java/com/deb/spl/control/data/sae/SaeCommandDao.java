package com.deb.spl.control.data.sae;

import com.deb.spl.control.data.AdjacentSystemCommandDAO;
import com.deb.spl.control.data.AdjacentSystemType;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@Slf4j
@EqualsAndHashCode
@Builder
@AllArgsConstructor
@Entity
@Table(name = "sae_commands")
public class SaeCommandDao extends AdjacentSystemCommandDAO {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;
    @NotNull
    @Column(unique = true)
    private String command;
    @NotNull
    private String caption;
    @Transient
    private CommandState commandState;
    @Enumerated(EnumType.STRING)
    private AdjacentSystemType adjacentSystem;

    public SaeCommandDao() {

    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

}

