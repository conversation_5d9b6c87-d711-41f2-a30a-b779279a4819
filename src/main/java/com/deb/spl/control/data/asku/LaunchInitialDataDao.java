package com.deb.spl.control.data.asku;


import lombok.*;

import javax.annotation.Nullable;
import javax.persistence.*;
import java.time.LocalDateTime;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "launch_initial_data")
public class LaunchInitialDataDao {
    private static final long serialVersionUID = 1355613873677923614L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "generation_date")
    private LocalDateTime createdAt;

    //    Температура заряда МД
    @Column(columnDefinition = "double precision default -999.9")
    private double loadTemperature;

    //    Геодезическая широта точки прицеливания
    @Column(name = "latitude")
    private String latitudeRad;

    //    Геодезическая долгота точки прицеливания
    @Column(name = "longitude")
    private String longitudeRad;

    //    Превышение точки прицеливания над ОЗЭ
    @Column(name = "altitude")
    private double altitude;

    //    Требуемый угол наклона вектора относительной скорости к горизонту в точке прицеливания
    private double inclinationAngle;

    //    Признак типа траектории, Ptr
    @Column(columnDefinition = "varchar(255) default 'UNKNOWN'")
    @Enumerated(EnumType.STRING)
    private TrajectoryType trajectory;

    //    Признак типа готовности, Pg
    @Column(columnDefinition = "varchar(255) default 'UNDEFINED'")
    @Enumerated(EnumType.STRING)
    private Readiness readiness;

    //    Признак наличия противоракетной обороны, Ppro
    @Column(name = "is_pro_detected")
    private boolean proDetected;

    //    Признак режима работы, Preg
    @Column(columnDefinition = "varchar(255) default 'UNKNOWN'")
    @Enumerated(EnumType.STRING)
    private MissileOperatingMode missileOperatingMode;

    @Column(name = "tl_signature")
    String tlCode;

    @Column(name = "validated_by_tlc", columnDefinition = "boolean DEFAULT false")
    private boolean validatedByTlc;

    @Column(name = "scheduled", columnDefinition = "boolean DEFAULT false")
    private boolean scheduled;

    @Column(name = "start_time")
    private LocalDateTime startTime;

    @Nullable
    @OneToOne
    private OrderInfoDao orderInfo;


    @PrePersist
    public void prePersist() {
        createdAt = LocalDateTime.now();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        LaunchInitialDataDao that = (LaunchInitialDataDao) o;

        if (Double.compare(that.getLoadTemperature(), getLoadTemperature()) != 0) return false;
        if (Double.compare(that.getAltitude(), getAltitude()) != 0) return false;
        if (Double.compare(that.getInclinationAngle(), getInclinationAngle()) != 0) return false;
        if (isProDetected() != that.isProDetected()) return false;
        if (isValidatedByTlc() != that.isValidatedByTlc()) return false;
        if (isScheduled() != that.isScheduled()) return false;
        if (getId() != null ? !getId().equals(that.getId()) : that.getId() != null) return false;
        if (getCreatedAt() != null ? !getCreatedAt().equals(that.getCreatedAt()) : that.getCreatedAt() != null)
            return false;
        if (getLatitudeRad() != null ? !getLatitudeRad().equals(that.getLatitudeRad()) : that.getLatitudeRad() != null)
            return false;
        if (getLongitudeRad() != null ? !getLongitudeRad().equals(that.getLongitudeRad()) : that.getLongitudeRad() != null)
            return false;
        if (getTrajectory() != that.getTrajectory()) return false;
        if (getReadiness() != that.getReadiness()) return false;
        if (getMissileOperatingMode() != that.getMissileOperatingMode()) return false;
        if (getTlCode() != null ? !getTlCode().equals(that.getTlCode()) : that.getTlCode() != null) return false;
        return getStartTime() != null ? getStartTime().equals(that.getStartTime()) : that.getStartTime() == null;
    }

    @Override
    public int hashCode() {
        int result;
        long temp;
        result = getId() != null ? getId().hashCode() : 0;
        result = 31 * result + (getCreatedAt() != null ? getCreatedAt().hashCode() : 0);
        temp = Double.doubleToLongBits(getLoadTemperature());
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        result = 31 * result + (getLatitudeRad() != null ? getLatitudeRad().hashCode() : 0);
        result = 31 * result + (getLongitudeRad() != null ? getLongitudeRad().hashCode() : 0);
        temp = Double.doubleToLongBits(getAltitude());
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        temp = Double.doubleToLongBits(getInclinationAngle());
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        result = 31 * result + (getTrajectory() != null ? getTrajectory().hashCode() : 0);
        result = 31 * result + (getReadiness() != null ? getReadiness().hashCode() : 0);
        result = 31 * result + (isProDetected() ? 1 : 0);
        result = 31 * result + (getMissileOperatingMode() != null ? getMissileOperatingMode().hashCode() : 0);
        result = 31 * result + (getTlCode() != null ? getTlCode().hashCode() : 0);
        result = 31 * result + (isValidatedByTlc() ? 1 : 0);
        result = 31 * result + (isScheduled() ? 1 : 0);
        result = 31 * result + (getStartTime() != null ? getStartTime().hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "LaunchInitialDataDao{" +
               "id=" + id +
               ", createdAt=" + createdAt +
               ", loadTemperature=" + loadTemperature +
               ", latitudeRad=" + latitudeRad +
               ", longitudeRad=" + longitudeRad +
               ", altitude=" + altitude +
               ", inclinationAngle=" + inclinationAngle +
               ", trajectory=" + trajectory +
               ", readiness=" + readiness +
               ", isProDetected=" + proDetected +
               ", missileOperatingMode=" + missileOperatingMode +
               ", tlCode='" + tlCode + /*'\'' +*/
               ", scheduled=" + scheduled +
               ", startTime" + (startTime == null ? "null" : startTime.toString()) + '\'' +
               '}' + orderInfo;
    }
}
