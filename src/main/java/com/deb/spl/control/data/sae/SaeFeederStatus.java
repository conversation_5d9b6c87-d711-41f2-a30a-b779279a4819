package com.deb.spl.control.data.sae;

import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;

import java.util.Arrays;
import java.util.Optional;

public enum SaeFeederStatus implements AdjacentSystemWithDescriptionUa {
    ON("Включений"),
    OFF("Відключений"),
    ERROR("Не норма"),
    UNDEFINED("Невідомо");
    private String sourceName;

    SaeFeederStatus(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getValueUa() {
        return sourceName;
    }

    public static SaeFeederStatus fromValueUa(String uaVal){
        Optional<SaeFeederStatus> status= Arrays.stream(SaeFeederStatus.values())
                .filter(e->e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(ERROR);
    }

}