package com.deb.spl.control.data.ppo;

import java.util.Arrays;
import java.util.Optional;

public enum PpoUnitType {
    BALLOON("Балон"),
    OPTICAL_SENSOR("Оптичний датчик"),
    THERMAL_SENSOR("Термодатчик"),
    UNDEFINED("Невідомо");
    private final String unitName;

    PpoUnitType(String sourceName) {
        this.unitName = sourceName;
    }

    public String getValueUa() {
        return unitName;
    }

    public static PpoUnitType fromValueUa(String uaVal){
        Optional<PpoUnitType> status= Arrays.stream(PpoUnitType.values())
                .filter(e->e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(UNDEFINED);
    }
}
