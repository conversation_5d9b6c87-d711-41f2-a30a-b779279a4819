package com.deb.spl.control.data.sae;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;

import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Optional;

public enum SaeStatus implements AdjacentSystemWithDescriptionUa {

    OK("Норма"),
    WARNING("Не критична помилка"),
    ERROR("Критична помилка"),
    NOT_CONNECTED("Немає зв'язку"),
    UNDEFINED("Невідомо");


    private String sourceName;

    SaeStatus(String sourceName) {
        this.sourceName = sourceName;
    }


    @Override
    public String getValueUa() {
        return sourceName;
    }

    public static SaeStatus fromValueUa(String uaVal) {
        Optional<SaeStatus> status = Arrays.stream(SaeStatus.values())
                .filter(e -> e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(ERROR);
    }

    public static AdjacentSystemStatus toAdjacentSystemStatus(@NotNull SaeStatus saeStatus) {
        switch (saeStatus) {
            case OK -> {
                return AdjacentSystemStatus.OK;
            }
            case WARNING -> {
                return AdjacentSystemStatus.WARNING;
            }
            case ERROR -> {
                return AdjacentSystemStatus.ERROR;
            }
            case NOT_CONNECTED -> {
                return AdjacentSystemStatus.NOT_CONNECTED;
            }
            default -> {
                return AdjacentSystemStatus.UNDEFINED;
            }
        }
    }

    public static SaeStatus fromAdjacentSystemStatus(@NotNull AdjacentSystemStatus status) {
        switch (status) {
            case OK -> {
                return SaeStatus.OK;
            }
            case WARNING -> {
                return SaeStatus.WARNING;
            }
            case ERROR -> {
                return SaeStatus.ERROR;
            }
            case NOT_CONNECTED -> {
                return SaeStatus.NOT_CONNECTED;
            }
            default -> {
                return SaeStatus.UNDEFINED;
            }
        }
    }


}