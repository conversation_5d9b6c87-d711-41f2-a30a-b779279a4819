package com.deb.spl.control.data.nppa;

import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.NotImplementedException;

import java.io.Serializable;

@Slf4j
@Getter
@Setter
@Builder
@AllArgsConstructor
public class Ncok implements AdjacentSystem, Serializable {
    private static final long serialVersionUID = 3015043333204816632L;
    @JsonIgnore
    private final AdjacentSystemType adjacentSystemType = AdjacentSystemType.NCOK;
    @Builder.Default
    private AdjacentSystemStatus systemStatus = AdjacentSystemStatus.UNDEFINED;
    private NppaOperatingMode operatingMode;
    private BaseProperty tvNcok;
    private boolean isNcokConnected;
    private boolean isSutoConnected;
    private BaseProperty nppaTestResult;
    private boolean appPresence; //private NcokApp ncokApp;
    private boolean otr1AppPresence;
    private boolean otr2AppPresence;

    private BaseProperty otr1TestResult;
    private BaseProperty otr2TestResult;
    private BaseProperty isOtr1Lunched;
    private BaseProperty isOtr2Lunched;
    private BaseProperty otr1BinsInitialSetup;
    private BaseProperty otr2BinsInitialSetup;
    private BaseProperty otrBinsPreciseSetup;
    private BaseProperty otr1tvNoIns;
    private BaseProperty otr2tvNoIns;
    private BasSnsStatus otr1BasSnsPz;
    @Builder.Default
    private BasSnsStatus otr2BasSnsPz=BasSnsStatus.UNDEFINED;

    public Ncok() {
        super();
    }

    @JsonIgnore
    @Override
    public AdjacentSystemType getAdjacentSystemType() {
        return AdjacentSystemType.NCOK;
    }

    @JsonIgnore
    @Override
    public String getErrorMessage() {
        throw new NotImplementedException();
    }

    @JsonIgnore
    @Override
    public boolean isMalfunctionPresent() {
        return !nppaTestResult.equals(BaseProperty.ERROR) && otr1AppPresence && otr2AppPresence
                && !otr1TestResult.equals(BaseProperty.ERROR) && !otr2TestResult.equals(BaseProperty.ERROR);
    }

    @JsonIgnore
    @Override
    public AdjacentSystemStatus getStatus() {
        return systemStatus != null ? systemStatus : AdjacentSystemStatus.UNDEFINED;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Ncok ncok)) return false;

        if (isNcokConnected() != ncok.isNcokConnected()) return false;
        if (isSutoConnected() != ncok.isSutoConnected()) return false;
        if (isAppPresence() != ncok.isAppPresence()) return false;
        if (isOtr1AppPresence() != ncok.isOtr1AppPresence()) return false;
        if (isOtr2AppPresence() != ncok.isOtr2AppPresence()) return false;
        if (getAdjacentSystemType() != ncok.getAdjacentSystemType()) return false;
        if (getSystemStatus() != ncok.getSystemStatus()) return false;
        if (getOperatingMode() != ncok.getOperatingMode()) return false;
        if (getTvNcok() != ncok.getTvNcok()) return false;
        if (getNppaTestResult() != ncok.getNppaTestResult()) return false;
        if (getOtr1TestResult() != ncok.getOtr1TestResult()) return false;
        if (getOtr2TestResult() != ncok.getOtr2TestResult()) return false;
        if (getIsOtr1Lunched() != ncok.getIsOtr1Lunched()) return false;
        if (getIsOtr2Lunched() != ncok.getIsOtr2Lunched()) return false;
        if (getOtr1BinsInitialSetup() != ncok.getOtr1BinsInitialSetup()) return false;
        if (getOtr2BinsInitialSetup() != ncok.getOtr2BinsInitialSetup()) return false;
        if (getOtrBinsPreciseSetup() != ncok.getOtrBinsPreciseSetup()) return false;
        if (getOtr1tvNoIns() != ncok.getOtr1tvNoIns()) return false;
        if (getOtr2tvNoIns() != ncok.getOtr2tvNoIns()) return false;
        if (getOtr1BasSnsPz() != ncok.getOtr1BasSnsPz()) return false;
        return getOtr2BasSnsPz() == ncok.getOtr2BasSnsPz();
    }

    @Override
    public int hashCode() {
        int result = getAdjacentSystemType().hashCode();
        result = 31 * result + (getSystemStatus() != null ? getSystemStatus().hashCode() : 0);
        result = 31 * result + (getOperatingMode() != null ? getOperatingMode().hashCode() : 0);
        result = 31 * result + (getTvNcok() != null ? getTvNcok().hashCode() : 0);
        result = 31 * result + (isNcokConnected() ? 1 : 0);
        result = 31 * result + (isSutoConnected() ? 1 : 0);
        result = 31 * result + (getNppaTestResult() != null ? getNppaTestResult().hashCode() : 0);
        result = 31 * result + (isAppPresence() ? 1 : 0);
        result = 31 * result + (isOtr1AppPresence() ? 1 : 0);
        result = 31 * result + (isOtr2AppPresence() ? 1 : 0);
        result = 31 * result + (getOtr1TestResult() != null ? getOtr1TestResult().hashCode() : 0);
        result = 31 * result + (getOtr2TestResult() != null ? getOtr2TestResult().hashCode() : 0);
        result = 31 * result + (getIsOtr1Lunched() != null ? getIsOtr1Lunched().hashCode() : 0);
        result = 31 * result + (getIsOtr2Lunched() != null ? getIsOtr2Lunched().hashCode() : 0);
        result = 31 * result + (getOtr1BinsInitialSetup() != null ? getOtr1BinsInitialSetup().hashCode() : 0);
        result = 31 * result + (getOtr2BinsInitialSetup() != null ? getOtr2BinsInitialSetup().hashCode() : 0);
        result = 31 * result + (getOtrBinsPreciseSetup() != null ? getOtrBinsPreciseSetup().hashCode() : 0);
        result = 31 * result + (getOtr1tvNoIns() != null ? getOtr1tvNoIns().hashCode() : 0);
        result = 31 * result + (getOtr2tvNoIns() != null ? getOtr2tvNoIns().hashCode() : 0);
        result = 31 * result + (getOtr1BasSnsPz() != null ? getOtr1BasSnsPz().hashCode() : 0);
        result = 31 * result + (getOtr2BasSnsPz() != null ? getOtr2BasSnsPz().hashCode() : 0);
        return result;
    }
}
