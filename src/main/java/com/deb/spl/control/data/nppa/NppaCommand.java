package com.deb.spl.control.data.nppa;

import com.deb.spl.control.data.AdjacentSystemCommand;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.sae.CommandState;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;


@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(name = "nppa_command_record")
public class NppaCommand extends AdjacentSystemCommand {
    @Builder
    public NppaCommand(Long id,
                       @NotNull String command,
                       @NotNull String caption,
                       CommandState commandState,
                       @NotNull AdjacentSystemType adjacentSystem,
                       LocalDateTime generationTime,
                       LocalDateTime executionTime,
                       String originator) {
        super(id, command, caption, commandState, adjacentSystem, generationTime, executionTime, originator);
    }


    @Override
    public String toStringUa() {
        return "Команда {" +
                "id команди - " + getId() +
                ", команда - ' " + super.getCommand() + '\'' +
                " , опис - " + getCaption() +
                ", ситема - " + getAdjacentSystem() +
                ", час створення - " + super.getGenerationTime() +
                ", час виконання - " + super.getExecutionTime() +
                '}';
    }
}
