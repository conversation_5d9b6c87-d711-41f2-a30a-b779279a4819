package com.deb.spl.control.data.nppa;

import com.deb.spl.control.controller.ServerErrorException;
import com.deb.spl.control.data.AdjacentSystem;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.asku.LaunchInitialData;
import com.deb.spl.control.data.asku.RocketFormData;
import com.deb.spl.control.data.suto.SutoSettings;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.NotImplementedException;

import javax.annotation.Nullable;
import java.io.Serializable;
import java.util.Arrays;


@Getter
@Setter
@Builder
@AllArgsConstructor
@ToString
@Slf4j
public class Nppa implements AdjacentSystem, Serializable {
    private static final long serialVersionUID = 3511502670152569069L;

    @Builder.Default
    private AdjacentSystemStatus status = AdjacentSystemStatus.WARNING;
    @JsonIgnore
    private final AdjacentSystemType adjacentSystemType = AdjacentSystemType.NPPA;
    private Ncok ncok;
    private Byn byn;
    private RocketFormData leftRocketFormData;
    private RocketFormData rightRocketFormData;
    private LaunchInitialData leftRocketInitialData;
    private LaunchInitialData rightRocketInitialData;


    @Nullable
    SutoSettings sutoSettings;

    @Override
    public AdjacentSystemType getAdjacentSystemType() {
        return this.adjacentSystemType;
    }

    public Nppa() {
        super();
    }

    public static Nppa copy(Nppa original) {
        Nppa copy;
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            copy = objectMapper.readValue(objectMapper.writeValueAsString(original), Nppa.class);
        } catch (JsonProcessingException e) {
            log.error(e.getMessage() + " \n" + Arrays.toString(e.getStackTrace()));
            throw new ServerErrorException(e.getMessage());
        }
        return copy;
    }

    @JsonIgnore
    @Override
    public String getErrorMessage() {
        throw new NotImplementedException(this.toString());
    }

    @JsonIgnore
    @Override
    public boolean isMalfunctionPresent() { // TODO: 17.07.2023 implement all subpartas of nppa
        return ncok.isMalfunctionPresent();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Nppa nppa)) return false;

        if (getStatus() != nppa.getStatus()) return false;
        if (getAdjacentSystemType() != nppa.getAdjacentSystemType()) return false;
        if (getNcok() != null ? !getNcok().equals(nppa.getNcok()) : nppa.getNcok() != null) return false;
        if (getByn() != null ? !getByn().equals(nppa.getByn()) : nppa.getByn() != null) return false;
        if (getLeftRocketFormData() != null ? !getLeftRocketFormData().equals(nppa.getLeftRocketFormData()) : nppa.getLeftRocketFormData() != null)
            return false;
        if (getRightRocketFormData() != null ? !getRightRocketFormData().equals(nppa.getRightRocketFormData()) : nppa.getRightRocketFormData() != null)
            return false;
        if (getLeftRocketInitialData() != null ? !getLeftRocketInitialData().equals(nppa.getLeftRocketInitialData()) : nppa.getLeftRocketInitialData() != null)
            return false;
        if (getRightRocketInitialData() != null ? !getRightRocketInitialData().equals(nppa.getRightRocketInitialData()) : nppa.getRightRocketInitialData() != null)
            return false;
        return getSutoSettings() != null ? getSutoSettings().equals(nppa.getSutoSettings()) : nppa.getSutoSettings() == null;
    }

    @Override
    public int hashCode() {
        int result = getStatus() != null ? getStatus().hashCode() : 0;
        result = 31 * result + getAdjacentSystemType().hashCode();
        result = 31 * result + (getNcok() != null ? getNcok().hashCode() : 0);
        result = 31 * result + (getByn() != null ? getByn().hashCode() : 0);
        result = 31 * result + (getLeftRocketFormData() != null ? getLeftRocketFormData().hashCode() : 0);
        result = 31 * result + (getRightRocketFormData() != null ? getRightRocketFormData().hashCode() : 0);
        result = 31 * result + (getLeftRocketInitialData() != null ? getLeftRocketInitialData().hashCode() : 0);
        result = 31 * result + (getRightRocketInitialData() != null ? getRightRocketInitialData().hashCode() : 0);
        result = 31 * result + (getSutoSettings() != null ? getSutoSettings().hashCode() : 0);
        return result;
    }
}
