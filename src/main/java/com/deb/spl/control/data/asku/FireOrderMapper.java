package com.deb.spl.control.data.asku;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",
        unmappedSourcePolicy = ReportingPolicy.WARN,
        uses = {LaunchInitialDataMapper.class, OrderInfoMapper.class})
public interface FireOrderMapper {
    @Mapping(source = "launchInitialDataDto", target = "launchInitialData")
    @Mapping(source = "orderInfoDto", target = "orderInfo")
    FireOrderDto map(LaunchInitialDataDto launchInitialDataDto, OrderInfoDto orderInfoDto);

    @Mapping(source = "launchInitialData", target = "launchInitialData")
    @Mapping(source = "orderInfo", target = "orderInfo")
    FireOrderDto map(LaunchInitialData launchInitialData, OrderInfo orderInfo);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "loadTemperature", constant = "-999.9")
    @Mapping(target = "latitudeRad", source = "launchInitialData.latitudeRad")
    @Mapping(target = "longitudeRad", source = "launchInitialData.longitudeRad")
    @Mapping(target = "altitude", source = "launchInitialData.altitude")
    @Mapping(target = "inclinationAngle", source = "launchInitialData.inclinationAngle")
    @Mapping(target = "trajectory", source = "launchInitialData.trajectory")
    @Mapping(target = "readiness", source = "launchInitialData.readiness")
    @Mapping(target = "proDetected", source = "launchInitialData.isProDetected")
    @Mapping(target = "missileOperatingMode", source = "launchInitialData.missileOperatingMode")
    @Mapping(target = "tlCode", source = "launchInitialData.tlCode")
    @Mapping(target = "validatedByTlc", source = "launchInitialData.validatedByTlc")
    @Mapping(target = "scheduled", source = "launchInitialData.scheduled")
    @Mapping(target = "startTime", source = "launchInitialData.startTime")
    @Mapping(target = "orderInfo", ignore = true)
    LaunchInitialData getInitialData(FireOrderDto value);

    @Mapping(target = "orderEntityId", source = "orderInfo.entityId")
    @Mapping(target = "validUntil", source = "orderInfo.validUntil")
    OrderInfo getOrderInfo(FireOrderDto value);

}
