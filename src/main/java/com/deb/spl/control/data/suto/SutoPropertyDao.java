package com.deb.spl.control.data.suto;

import com.deb.spl.control.data.AdjacentSystemPropertyDAO;
import com.deb.spl.control.data.PropertyType;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

@Entity
@Table(name = "suto_properties")
public class SutoPropertyDao extends AdjacentSystemPropertyDAO {
    @Enumerated(EnumType.STRING)
    @NotNull
    @Column(name = "property_type")
    public PropertyType propertyType;
}
