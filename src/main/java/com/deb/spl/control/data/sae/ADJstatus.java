package com.deb.spl.control.data.sae;

import com.deb.spl.control.data.AdjacentSystemWithDescriptionUa;

import java.util.Arrays;
import java.util.Optional;

public enum ADJstatus implements AdjacentSystemWithDescriptionUa {
    OK("Норма"),
    ERROR("Не норма"),
    NOT_ACTIVE("Не виконується");


    private String sourceName;

    ADJstatus(String sourceName) {
        this.sourceName = sourceName;
    }

    public static ADJstatus fromValueUa(String uaVal) {
        Optional<ADJstatus> status= Arrays.stream(ADJstatus.values())
                .filter(e->e.getValueUa().equalsIgnoreCase(uaVal)).findFirst();
        return status.orElse(ERROR);
    }

    public String getValueUa(){return sourceName;}

}
