 __     __   _     _      _         ____            _             _   ____                  _
 \ \   / /__| |__ (_) ___| | ___   / ___|___  _ __ | |_ _ __ ___ | | / ___|  ___ _ ____   _(_) ___ ___  ___
  \ \ / / _ \ '_ \| |/ __| |/ _ \ | |   / _ \| '_ \| __| '__/ _ \| | \___ \ / _ \ '__\ \ / / |/ __/ _ \/ __|
   \ V /  __/ | | | | (__| |  __/ | |__| (_) | | | | |_| | | (_) | |  ___) |  __/ |   \ V /| | (_|  __/\__ \
    \_/ \___|_| |_|_|\___|_|\___|  \____\___/|_| |_|\__|_|  \___/|_| |____/ \___|_|    \_/ |_|\___\___||___/

${application.title} ${application.version}
Powered by Spring Boot ${spring-boot.version}
java version ${java.version}