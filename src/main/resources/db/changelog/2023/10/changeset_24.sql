-- liquibase formatted sql

-- changeset liquibase:24

UPDATE public.nppa_commands
SET has_blocker = true
WHERE id = 33016;

UPDATE public.nppa_commands
SET has_blocker = true
WHERE id = 33029;

UPDATE public.nppa_commands
SET has_blocker = true
WHERE id = 33020;

UPDATE public.nppa_commands
SET has_blocker = true
WHERE id = 33018;

UPDATE public.nppa_commands
SET has_blocker = true
WHERE id = 10002;

UPDATE public.nppa_commands
SET has_blocker = true
WHERE id = 33014;

UPDATE public.nppa_commands
SET has_blocker = true
WHERE id = 33023;

UPDATE public.nppa_commands
SET has_blocker = true
WHERE id = 33022;

UPDATE public.nppa_commands
SET has_blocker = true
WHERE id = 33015;

UPDATE public.nppa_commands
SET has_blocker = true
WHERE id = 33017;

UPDATE public.nppa_commands
SET has_blocker = true
WHERE id = 33019;

UPDATE public.nppa_commands
SET has_blocker = true
WHERE id = 10008;

UPDATE public.nppa_commands
SET has_blocker = true
WHERE id = 10007;

UPDATE public.nppa_commands
SET has_blocker = true
WHERE id = 10005;

UPDATE public.nppa_commands
SET has_blocker = true
WHERE id = 33021;

UPDATE public.nppa_commands
SET has_blocker = true
WHERE id = 33025;

UPDATE public.nppa_commands
SET has_blocker = true
WHERE id = 33024;

UPDATE public.nppa_commands
SET has_blocker = true
WHERE id = 33027;

UPDATE public.nppa_commands
SET has_blocker = true
WHERE id = 10004;

UPDATE public.nppa_commands
SET has_blocker = true
WHERE id = 10006;

UPDATE public.nppa_commands
SET has_blocker = true
WHERE id = 10003;

UPDATE public.nppa_commands
SET has_blocker = true
WHERE id = 10001;

UPDATE public.nppa_commands
SET has_blocker = true
WHERE id = 33026;

