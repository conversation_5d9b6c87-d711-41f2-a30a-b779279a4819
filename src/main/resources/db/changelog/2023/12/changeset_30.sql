-- liquibase formatted sql

-- changeset liquibase:30

alter table public.nppa_command_record
    add adjacent_system_string varchar(255) default 'UNDEFINED';

update  nppa_command_record
    set adjacent_system_string =
        case adjacent_system
            when 0 then 'BINS'
            when 1 then 'MSU'
            when 2 then 'NPPA'
            when 3 then 'NCOK'
            when 4 then 'BYN'
            when 5 then 'SAE'
            when 6 then 'SUTO'
            when 7 then 'PPO'
            when 8 then 'PLC'
            when 9 then 'ASKU'
            when 10 then 'TPC'
            when 11 then 'ASKU'
            when 12 then 'TPC'
            when 13 then 'UNCLASSIFIED'
            when 14 then 'UNDEFINED'
            when 15 then 'NSD'
            else 'UNDEFINED'
        end;

alter table public.nppa_command_record
drop column adjacent_system;

alter table public.nppa_command_record
    rename column adjacent_system_string to adjacent_system;

