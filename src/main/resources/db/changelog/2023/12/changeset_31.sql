-- liquibase formatted sql

-- changeset liquibase:31

-- suto
alter table public.suto_record
    add adjacent_system_string varchar(255) default 'UNDEFINED';

update  suto_record
    set adjacent_system_string =
        case adjacent_system
            when 0 then 'BINS'
            when 1 then 'MSU'
            when 2 then 'NPPA'
            when 3 then 'NCOK'
            when 4 then 'BYN'
            when 5 then 'SAE'
            when 6 then 'SUTO'
            when 7 then 'PPO'
            when 8 then 'PLC'
            when 9 then 'ASKU'
            when 10 then 'TPC'
            when 11 then 'ASKU'
            when 12 then 'TPC'
            when 13 then 'UNCLASSIFIED'
            when 14 then 'UNDEFINED'
            when 15 then 'NSD'
            else 'UNDEFINED'
        end;

alter table public.suto_record
drop column adjacent_system;

alter table public.suto_record
    rename column adjacent_system_string to adjacent_system;


-- SAE
alter table public.sae_record
    add adjacent_system_string varchar(255) default 'UNDEFINED';

update  sae_record
set adjacent_system_string =
        case adjacent_system
            when 0 then 'BINS'
            when 1 then 'MSU'
            when 2 then 'NPPA'
            when 3 then 'NCOK'
            when 4 then 'BYN'
            when 5 then 'SAE'
            when 6 then 'SUTO'
            when 7 then 'PPO'
            when 8 then 'PLC'
            when 9 then 'ASKU'
            when 10 then 'TPC'
            when 11 then 'ASKU'
            when 12 then 'TPC'
            when 13 then 'UNCLASSIFIED'
            when 14 then 'UNDEFINED'
            when 15 then 'NSD'
            else 'UNDEFINED'
            end;

alter table public.sae_record
drop column adjacent_system;

alter table public.sae_record
    rename column adjacent_system_string to adjacent_system;

-- PPO
alter table public.ppo_record
    add adjacent_system_string varchar(255) default 'UNDEFINED';

update  ppo_record
set adjacent_system_string =
        case adjacent_system
            when 0 then 'BINS'
            when 1 then 'MSU'
            when 2 then 'NPPA'
            when 3 then 'NCOK'
            when 4 then 'BYN'
            when 5 then 'SAE'
            when 6 then 'SUTO'
            when 7 then 'PPO'
            when 8 then 'PLC'
            when 9 then 'ASKU'
            when 10 then 'TPC'
            when 11 then 'ASKU'
            when 12 then 'TPC'
            when 13 then 'UNCLASSIFIED'
            when 14 then 'UNDEFINED'
            when 15 then 'NSD'
            else 'UNDEFINED'
            end;

alter table public.ppo_record
drop column adjacent_system;

alter table public.ppo_record
    rename column adjacent_system_string to adjacent_system;

-- ASKU
alter table public.asku_command_record
    add adjacent_system_string varchar(255) default 'UNDEFINED';

update  asku_command_record
set adjacent_system_string =
        case adjacent_system
            when 0 then 'BINS'
            when 1 then 'MSU'
            when 2 then 'NPPA'
            when 3 then 'NCOK'
            when 4 then 'BYN'
            when 5 then 'SAE'
            when 6 then 'SUTO'
            when 7 then 'PPO'
            when 8 then 'PLC'
            when 9 then 'ASKU'
            when 10 then 'TPC'
            when 11 then 'ASKU'
            when 12 then 'TPC'
            when 13 then 'UNCLASSIFIED'
            when 14 then 'UNDEFINED'
            when 15 then 'NSD'
            else 'UNDEFINED'
            end;

alter table public.asku_command_record
drop column adjacent_system;

alter table public.asku_command_record
    rename column adjacent_system_string to adjacent_system;