-- liquibase formatted sql

-- changeset liquibase:33
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ1 у БГ2а', command = 'Bg1toBg2aToggle', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33027;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ1 у БГ3', command = 'Bg1toBg3Toggle', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33026;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ2а в БГ1 з увімкненням ОТР1 та ОТР2', command = 'Bg2aToBg1SwitchWithOtr1Otr2SwOn', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 10007;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ2а в БГ1 з увімкненням ОТР1', command = 'Bg2aToBg1SwitchWithOtr1SwOn', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33022;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ2а в БГ1 з увімкненням ОТР2', command = 'Bg2aToBg1SwitchWithOtr2SwOn', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33023;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ2а у БГ3', command = 'Bg2atoBg3Toggle', used_in_combat_mode = false, used_in_test_mode = false, used_in_workflow = false, has_blocker = false WHERE id = 10011;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ2б у БГ3', command = 'Bg2btoBg3Toggle', used_in_combat_mode = false, used_in_test_mode = false, used_in_workflow = false, has_blocker = false WHERE id = 10012;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ3 в БГ1 з увімкненням ОТР1 та ОТР2', command = 'Bg3ToBg1SwitchWithOtr1Otr2SwOn', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 10008;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ3 в БГ1 з увімкненням ОТР1', command = 'Bg3ToBg1SwitchWithOtr1SwOn', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33024;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ3 в БГ1 з увімкненням ОТР2', command = 'Bg3ToBg1SwitchWithOtr2SwOn', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33025;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ3 у БГ2а', command = 'Bg3toBg2aToggle', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 10001;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ3 у БГ2б', command = 'Bg3toBg2bToggle', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 10002;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ3 у БГ4', command = 'Bg3toBg4Toggle', used_in_combat_mode = false, used_in_test_mode = false, used_in_workflow = false, has_blocker = false WHERE id = 10010;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ4 у БГ3', command = 'Bg4toBg3Toggle', used_in_combat_mode = false, used_in_test_mode = false, used_in_workflow = false, has_blocker = false WHERE id = 10009;
UPDATE public.nppa_commands SET adjacent_system = 'BYN', caption = 'Режим роботи БУН – основний', command = 'BynCombatMode', used_in_combat_mode = false, used_in_test_mode = false, used_in_workflow = true, has_blocker = false WHERE id = 34001;
UPDATE public.nppa_commands SET adjacent_system = 'BYN', caption = 'Режим роботи БУН – перевірний', command = 'BynTestMode', used_in_combat_mode = false, used_in_test_mode = false, used_in_workflow = true, has_blocker = false WHERE id = 34002;
UPDATE public.nppa_commands SET adjacent_system = 'BYN', caption = 'Скасування режиму перевірок', command = 'CancelBynTestMode', used_in_combat_mode = false, used_in_test_mode = false, used_in_workflow = true, has_blocker = false WHERE id = 34021;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Скасування режиму перевірок', command = 'CancelNcokTestMode', used_in_combat_mode = false, used_in_test_mode = true, used_in_workflow = false, has_blocker = false WHERE id = 33531;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Режим роботи НЦОК бойовий', command = 'NcokCombatMode', used_in_combat_mode = false, used_in_test_mode = false, used_in_workflow = true, has_blocker = false WHERE id = 33001;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Режим роботи НЦОК перевірний', command = 'NcokTestMode', used_in_combat_mode = false, used_in_test_mode = false, used_in_workflow = true, has_blocker = false WHERE id = 33002;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Режим перевірок НППА з ЕБАСУ1 і ЕБАСУ2', command = 'NppaEbasu1Ebasu2TestMode', used_in_combat_mode = false, used_in_test_mode = true, used_in_workflow = false, has_blocker = false WHERE id = 33530;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Режим перевірок НППА з ЕБАСУ1', command = 'NppaEbasu1TestMode', used_in_combat_mode = false, used_in_test_mode = true, used_in_workflow = false, has_blocker = false WHERE id = 33528;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Режим перевірок НППА з ЕБАСУ2', command = 'NppaEbasu2TestMode', used_in_combat_mode = false, used_in_test_mode = true, used_in_workflow = false, has_blocker = false WHERE id = 33529;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Режим перевірення ОТР1 після перезарядження', command = 'Otr1AfterReloadingTestMode', used_in_combat_mode = false, used_in_test_mode = true, used_in_workflow = false, has_blocker = false WHERE id = 33524;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Пуск ОТР1 із БГ1', command = 'Otr1FromBg1Launch', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33020;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Пуск ОТР1 із БГ2а', command = 'Otr1FromBg2aLaunch', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33016;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Пуск ОТР1 із БГ2б', command = 'Otr1FromBg2bLaunch', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33018;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Пуск ОТР1 із БГ3', command = 'Otr1FromBg3Launch', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33014;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'ВД на пуск для ОТР1', command = 'Otr1InitialData', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = false WHERE id = 33030;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Скасування пуску ОТР1', command = 'Otr1LaunchCancell', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = false WHERE id = 33032;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Пуск ОТР1 та ОТР2 із БГ1', command = 'Otr1Otr2FromBg1Launch', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 10006;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Пуск ОТР1 та ОТР2 із БГ2а', command = 'Otr1Otr2FromBg2aLaunch', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 10004;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Пуск ОТР1 та ОТР2 із БГ2б', command = 'Otr1Otr2FromBg2bLaunch', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 10005;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Пуск ОТР1 та ОТР2 із БГ3', command = 'Otr1Otr2FromBg3Launch', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 10003;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Режим перевірок ОТР1', command = 'Otr1TestMode', used_in_combat_mode = false, used_in_test_mode = true, used_in_workflow = false, has_blocker = false WHERE id = 33526;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Режим перевірення ОТР2 після перезарядження', command = 'Otr2AfterReloadingTestMode', used_in_combat_mode = false, used_in_test_mode = true, used_in_workflow = false, has_blocker = false WHERE id = 33525;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Пуск ОТР2 із БГ1', command = 'Otr2FromBg1Launch', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33021;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Пуск ОТР2 із БГ2а', command = 'Otr2FromBg2aLaunch', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33017;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Пуск ОТР2 із БГ2б', command = 'Otr2FromBg2bLaunch', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33019;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Пуск ОТР2 із БГ3', command = 'Otr2FromBg3Launch', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33015;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'ВД на пуск для ОТР2', command = 'Otr2InitialData', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = false WHERE id = 33031;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Скасування пуску ОТР2', command = 'Otr2LaunchCancell', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = false WHERE id = 33033;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Режим перевірок ОТР2', command = 'Otr2TestMode', used_in_combat_mode = false, used_in_test_mode = true, used_in_workflow = false, has_blocker = false WHERE id = 33527;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Продовження ППіП із БГ2б', command = 'PPiPWithBg2bContinuation', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33029;
UPDATE public.nppa_commands SET adjacent_system = 'BYN', caption = 'Вимкнення живлення НЦОК', command = 'TurnOffNcok', used_in_combat_mode = false, used_in_test_mode = false, used_in_workflow = true, has_blocker = false WHERE id = 34011;
UPDATE public.nppa_commands SET adjacent_system = 'BYN', caption = 'Увімкнення живлення НЦОК', command = 'TurnOnNcok', used_in_combat_mode = false, used_in_test_mode = false, used_in_workflow = true, has_blocker = false WHERE id = 34004;
