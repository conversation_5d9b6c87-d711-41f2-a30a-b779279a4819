-- liquibase formatted sql

-- changeset liquibase:6

CREATE SEQUENCE IF NOT EXISTS hibernate_sequence START WITH 2 INCREMENT BY 1;

CREATE TABLE ncok
(
    id                   BIGINT                     NOT NULL,
    readiness_started_at TIMESTAMP WITHOUT TIME ZONE,
    updated_at           TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    system_status        VARCHAR(255) DEFAULT 'UNDEFINED',
    operating_mode       VARCHAR(255) DEFAULT 'NOT_SELECTED',
    tv_ncok              VARCHAR(255) DEFAULT 'UNDEFINED',
    is_ncok_connected    BOOLEAN      DEFAULT FALSE NOT NULL,
    is_suto_connected    BOOLEAN      DEFAULT FALSE NOT NULL,
    nppa_test_result     VARCHAR(255) DEFAULT 'UNDEFINED',
    app_presence         BOOLEAN      DEFAULT FALSE NOT NULL,
    otr1_app_presence    BOOLEAN      DEFAULT FALSE NOT NULL,
    otr2_app_presence    BOOLEAN      DEFAULT FALSE NOT NULL,
    otr1_test_result     VARCHAR(255) DEFAULT 'UNDEFINED',
    otr2_test_result     VARCHAR(255) DEFAULT 'UNDEFINED',
    is_otr1_lunched      VARCHAR(255) DEFAULT 'UNDEFINED',
    is_otr2_lunched      VARCHAR(255) DEFAULT 'UNDEFINED',
    CONSTRAINT pk_ncok PRIMARY KEY (id)
);

CREATE SEQUENCE IF NOT EXISTS hibernate_sequence START WITH 2 INCREMENT BY 1;

CREATE TABLE byn
(
    id                     BIGINT                     NOT NULL,
    updated_at             TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    system_status          VARCHAR(255) DEFAULT 'UNDEFINED',
    operating_mode         VARCHAR(255) DEFAULT 'NOT_SELECTED',
    tv_byn                 VARCHAR(255) DEFAULT 'UNDEFINED',
    is_connected           BOOLEAN      DEFAULT FALSE NOT NULL,
    is_ncok                BOOLEAN      DEFAULT FALSE NOT NULL,
    is_rg_out_ncok         BOOLEAN      DEFAULT FALSE NOT NULL,
    is_buve_f2             BOOLEAN      DEFAULT FALSE NOT NULL,
    is_buve_f4             BOOLEAN      DEFAULT FALSE NOT NULL,
    is_basu_otr1_f3        BOOLEAN      DEFAULT FALSE NOT NULL,
    is_basu_otr2_f3        BOOLEAN      DEFAULT FALSE NOT NULL,
    is_f1                  BOOLEAN      DEFAULT FALSE NOT NULL,
    is_f2                  BOOLEAN      DEFAULT FALSE NOT NULL,
    is_f3                  BOOLEAN      DEFAULT FALSE NOT NULL,
    is_f4                  BOOLEAN      DEFAULT FALSE NOT NULL,
    is_f5                  BOOLEAN      DEFAULT FALSE NOT NULL,
    is_nppa_connected      BOOLEAN      DEFAULT FALSE NOT NULL,
    is_basu_otr1_connected BOOLEAN      DEFAULT FALSE NOT NULL,
    is_basu_otr2_connected BOOLEAN      DEFAULT FALSE NOT NULL,
    CONSTRAINT pk_byn PRIMARY KEY (id)
);

insert into ncok (id, readiness_started_at, updated_at, system_status, operating_mode, tv_ncok,
                         is_ncok_connected, is_suto_connected, nppa_test_result, app_presence, otr1_app_presence,
                         otr2_app_presence, otr1_test_result, otr2_test_result, is_otr1_lunched, is_otr2_lunched)
values (1, null, '2023-08-05 15:48:39.000000', 'UNDEFINED', 'NOT_SELECTED', 'UNDEFINED', false, false, 'UNDEFINED',
        false, false, false, 'UNDEFINED', 'UNDEFINED', 'UNDEFINED', 'UNDEFINED');

insert into byn (id, updated_at, system_status, operating_mode, tv_byn, is_connected, is_ncok, is_rg_out_ncok,
                 is_buve_f2, is_buve_f4, is_basu_otr1_f3, is_basu_otr2_f3, is_f1, is_f2, is_f3, is_f4, is_f5,
                 is_nppa_connected, is_basu_otr1_connected, is_basu_otr2_connected)
values (1, '2023-08-05 15:38:59.000000', 'UNDEFINED', 'NOT_SELECTED', 'UNDEFINED', false, false, false,
        false, false, false, false, false, false, false, false, false, false, false, false);