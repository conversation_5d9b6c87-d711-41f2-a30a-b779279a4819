-- liquibase formatted sql

-- changeset liquibase:14


-- alter table asku
--     alter column system_status set not null;


INSERT INTO public.rocket_from_data (id, alp_type, crc, generation_date, gsn_type, is_archived, is_telemetry_integrated,
                                     plant_missile, purpose_type, warhead)
VALUES (232, 'NO_ALP', null, '2023-08-29 18:00:03.622129', 'NO_GSN', false, false, '83', 'COMBAT', 'MFBCH');

INSERT INTO public.launch_initial_data (id, altitude, generation_date, inclination_angle, is_pro_detected, latitude,
                                        longitude, missile_operating_mode, readiness, trajectory, tl_signature,
                                        loaded_to_plc, load_temperature, validated_by_tlc)
VALUES (283, 117, '2023-08-29 21:08:59.260942', -80, true, '0.32546546', '0.35515121', 'BASE_SNS', 'BG_2A',
        'AERO_BALLISTIC', 'sdaasdagsadaxxcvdawsdasfgasd', false, -999.9, true);
-- left
INSERT INTO public.rocket (id, date_release_m, date_use_m, initial_data_source, technical_condition, updated_at,
                           form_data_id, initial_data_id, initial_datasource_description, initial_data_time_stamp,
                           launch_result, sensor_temperature)
VALUES (208, null, null, 'MSG', true, '2023-08-29 21:09:27.931253', 232, 283, 'asd', '2023-08-29 21:09:27.931253', null,
        0);
insert into public.rocket_stored_tl_keys (rocket_id, stored_tl_keys)
values (1, 'werwer23443dasdasdasdas234rrrwsssdfgdasd==='),
       (1, '0123465798/*--!@#$%^&fsf3wffffffffffffffff===');
-- right
INSERT INTO public.rocket_from_data (id, alp_type, crc, generation_date, gsn_type, is_archived, is_telemetry_integrated,
                                     plant_missile, purpose_type, warhead)
VALUES (235, 'NO_ALP', null, '2023-08-29 18:33:51.501015', 'NO_GSN', false, false, '84', 'COMBAT', 'CBCH');

INSERT INTO public.rocket (id, date_release_m, date_use_m, initial_data_source, technical_condition, updated_at,
                           form_data_id, initial_data_id, initial_datasource_description, initial_data_time_stamp,
                           launch_result, sensor_temperature)
VALUES (204, null, null, 'MSG', true, '2023-08-29 18:33:51.535187', 235, null, 'asd', null, null, -999.9);

INSERT INTO public.tpc (id, updated_at, tpc_load_state)
VALUES (255, '2023-08-29 18:33:51.542816', 'TPC_WITH_ROCKET'),
       (250, '2023-08-29 18:00:03.648008', 'TPC_WITH_ROCKET');

insert into public.rocket_stored_tl_keys (rocket_id, stored_tl_keys)
values (208, 'werwer23443dasdasdasdas234rrrwsssdfgdasd==='),
       (208, '0123465798/654sdf7e33d$3wffffffffffffffff==='),
       (204, 'qweqweqw23131aassssssssssssss///=====');

insert into public.asku (id, system_status, plc_status, spl_id, plate_number, spl_readiness, readiness_started_at,
                         asku_status, unit_title, updated_at, left_rocket_id, right_rocket_id,
                         tpc_left_id, tpc_right_id, started_date)
values (4, 'UNDEFINED', 'UNDEFINED', '2aa5d069-10e9-4a2c-b9d2-25b9eeeb109d', 'АЕ 1010 MI', 'BG_4', null,
        'UNDEFINED', ' ''Батр1 СПУ1 Green Goose''', '2023-09-04 19:44:40.244183', 1,
        204, 1, 255, null);