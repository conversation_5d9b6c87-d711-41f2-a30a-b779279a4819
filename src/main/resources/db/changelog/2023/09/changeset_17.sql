-- liquibase formatted sql

-- changeset liquibase:17

CREATE TABLE sae
(
    id                                 BIGINT GENERATED BY DEFAULT AS IDENTITY   NOT NULL,
    sae_status                         VARCHAR(20)                 DEFAULT 'NOT_CONNECTED',
    readiness                          VARCHAR(20)                 DEFAULT 'UNDEFINED',
    energized_by_adj                   VARCHAR(20)                 DEFAULT 'OFF',
    energized_by_hds                   VARCHAR(20)                 DEFAULT 'OFF',
    energized_by_external_power_source VARCHAR(20)                 DEFAULT 'OFF',
    adj_start                          VARCHAR(20)                 DEFAULT 'NOT_ACTIVE',
    adj_stop                           VARCHAR(20)                 DEFAULT 'NOT_ACTIVE',
    adj_lock                           VARCHAR(20)                 DEFAULT 'NOT_ACTIVE',
    adj_unlock                         VARCHAR(20)                 DEFAULT 'NOT_ACTIVE',
    hds_status                         VARCHAR(20)                 DEFAULT 'NOT_ACTIVE',
    voltage_status                     VARCHAR(20)                 DEFAULT 'NOT_ACTIVE',
    external_power_source_voltage      VARCHAR(20)                 DEFAULT 'NOT_ACTIVE',
    bs_voltage                         VARCHAR(20)                 DEFAULT 'NOT_ACTIVE',
    feeder_1_status                    VARCHAR(20)                 DEFAULT 'UNDEFINED',
    feeder_2_status                    VARCHAR(20)                 DEFAULT 'UNDEFINED',
    feeder_3_status                    VARCHAR(20)                 DEFAULT 'UNDEFINED',
    feeder_4_status                    VARCHAR(20)                 DEFAULT 'UNDEFINED',
    feeder_5_status                    VARCHAR(20)                 DEFAULT 'UNDEFINED',
    feeder_6_status                    VARCHAR(20)                 DEFAULT 'UNDEFINED',
    feeder_nppa_1_status               VARCHAR(20)                 DEFAULT 'UNDEFINED',
    updated_at                         TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
    CONSTRAINT pk_sae PRIMARY KEY (id)
);

alter table sae
    owner to postgres;

INSERT INTO public.sae (sae_status, readiness, energized_by_adj, energized_by_hds, energized_by_external_power_source,
                        adj_start, adj_stop, adj_lock, adj_unlock, hds_status, voltage_status,
                        external_power_source_voltage, bs_voltage, feeder_1_status, feeder_2_status, feeder_3_status,
                        feeder_4_status, feeder_5_status, feeder_6_status, feeder_nppa_1_status, updated_at)
VALUES (DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT,
        DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, '1917-09-04 14:52:15.000000');

