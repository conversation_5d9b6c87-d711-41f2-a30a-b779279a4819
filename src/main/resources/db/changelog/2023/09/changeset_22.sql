-- liquibase formatted sql

-- changeset liquibase:22

-- alter table sae

insert into public.nppa_commands (id, adjacent_system, caption, command, used_in_combat_mode, used_in_test_mode, used_in_workflow, has_blocker)
values  (10003, 'NCOK', 'Пуск ОТР1 та ОТР2 із БГ3', 'Otr1Otr2FromBg3Launch', true, false, false, false),
        (10004, 'NCOK', 'Пуск ОТР1 та ОТР2 із БГ2а', 'Otr1Otr2FromBg2aLaunch', true, false, false, false),
        (10005, 'NCOK', 'Пуск ОТР1 та ОТР2 із БГ2б', 'Otr1Otr2FromBg2bLaunch', true, false, false, false),
        (10006, 'NCOK', 'Пуск ОТР1 та ОТР2 із БГ1', 'Otr1Otr2FromBg1Launch', true, false, false, false),
        (10007, 'NCOK', 'Перехід із БГ2а в БГ1 з увімкненням ОТР1 та ОТР2', 'Bg2aToBg1SwitchWithOtr1Otr2SwOn', true, false, false, false),
        (10008, 'NCOK', 'Перехід із БГ3 в БГ1 з увімкненням ОТР1 та ОТР2', 'Bg3ToBg1SwitchWithOtr1Otr2SwOn', true, false, false, false);