-- liquibase formatted sql

-- changeset liquibase:39

UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ4 у БГ3', command = 'Bg4toBg3Toggle', used_in_combat_mode = false, used_in_test_mode = false, used_in_workflow = true, has_blocker = true WHERE id = 10009;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ3 в БГ1 з увімкненням ОТР1 та ОТР2', command = 'Bg3ToBg1SwitchWithOtr1Otr2SwOn', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 10008;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ3 в БГ1 з увімкненням ОТР1', command = 'Bg3ToBg1SwitchWithOtr1SwOn', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33024;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ3 в БГ1 з увімкненням ОТР2', command = 'Bg3ToBg1SwitchWithOtr2SwOn', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33025;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ3 у БГ2а', command = 'Bg3toBg2aToggle', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 10001;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ3 у БГ2б', command = 'Bg3toBg2bToggle', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 10002;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ3 у БГ4', command = 'Bg3toBg4Toggle', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 10010;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ2б у БГ3', command = 'Bg2btoBg3Toggle', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 10012;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ2б у БГ4', command = 'Bg2btoBg4Toggle', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 10014;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ2а у БГ4', command = 'Bg2atoBg4Toggle', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 10013;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ2а в БГ1 з увімкненням ОТР1 та ОТР2', command = 'Bg2aToBg1SwitchWithOtr1Otr2SwOn', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 10007;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ2а в БГ1 з увімкненням ОТР1', command = 'Bg2aToBg1SwitchWithOtr1SwOn', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33022;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ2а в БГ1 з увімкненням ОТР2', command = 'Bg2aToBg1SwitchWithOtr2SwOn', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33023;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ2а у БГ3', command = 'Bg2atoBg3Toggle', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 10011;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ1 у БГ2а', command = 'Bg1toBg2aToggle', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33027;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ1 у БГ3', command = 'Bg1toBg3Toggle', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33026;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ1 у БГ4', command = 'Bg1toBg4Toggle', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 10015;