-- liquibase formatted sql

-- changeset liquibase:40


UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ3 у БГ4', command = 'Bg3toBg4Toggle', used_in_combat_mode = false, used_in_test_mode = false, used_in_workflow = true, has_blocker = true WHERE id = 10010;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ2б у БГ4', command = 'Bg2btoBg4Toggle', used_in_combat_mode = false, used_in_test_mode = false, used_in_workflow = true, has_blocker = true WHERE id = 10014;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Перехід із БГ2а у БГ4', command = 'Bg2atoBg4Toggle', used_in_combat_mode = false, used_in_test_mode = false, used_in_workflow = true, has_blocker = true WHERE id = 10013;
