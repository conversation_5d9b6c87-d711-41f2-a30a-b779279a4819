-- liquibase formatted sql

-- changeset liquibase:36
alter table public.spl_navigation_rest_log
    alter column id drop identity;

alter table public.spl_navigation_rest_log
    alter column cached_payload type varchar(8096) using cached_payload::varchar(8096);

drop sequence if exists public.spl_navigation_rest_log_id_seq;

create sequence if not exists public.spl_navigation_rest_log_id_seq minvalue 1000;

alter sequence public.spl_navigation_rest_log_id_seq owner to postgres;