-- liquibase formatted sql

-- changeset liquibase:48

CREATE TABLE order_info
(
    id              BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    order_entity_id UUID                                    NOT NULL,
    valid_until     TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_order_info PRIMARY KEY (id)
);
alter table order_info
    owner to postgres;

alter table public.launch_initial_data
    add scheduled boolean NOT NULL default 'false',
    add start_time      timestamp,
    add order_info_id   bigint
        constraint fk5p07s83h60upefufty4lsy4sn
        references order_info;

