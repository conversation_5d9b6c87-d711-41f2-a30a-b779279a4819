-- liquibase formatted sql

-- changeset liquibase:46
delete from public.nppa_commanddao_available_at_readiness where nppa_commanddao_id = 10004;
delete from public.nppa_commanddao_available_at_readiness where nppa_commanddao_id = 33016;
delete from public.nppa_commanddao_available_at_readiness where nppa_commanddao_id = 33017;
delete from public.nppa_commands where  id = 10004;
delete from public.nppa_commands where  id = 33016;
delete from public.nppa_commands where  id = 33017;

UPDATE public.nppa_commands  SET adjacent_system = 'NCOK', caption = 'Пуск ОТР1 із БГ2', command = 'Otr1FromBg2bLaunch', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33018;
UPDATE public.nppa_commands  SET adjacent_system = 'NCOK', caption = 'Перехід із БГ3 у БГ2', command = 'Bg3toBg2bToggle', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 10002;
UPDATE public.nppa_commands  SET adjacent_system = 'NCOK', caption = 'Перехід із БГ2 у БГ3', command = 'Bg2btoBg3Toggle', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 10012;
UPDATE public.nppa_commands  SET adjacent_system = 'NCOK', caption = 'Перехід із БГ2 у БГ4', command = 'Bg2btoBg4Toggle', used_in_combat_mode = false, used_in_test_mode = false, used_in_workflow = true, has_blocker = true WHERE id = 10014;
UPDATE public.nppa_commands  SET adjacent_system = 'NCOK', caption = 'Пуск ОТР1 та ОТР2 із БГ2', command = 'Otr1Otr2FromBg2bLaunch', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 10005;
UPDATE public.nppa_commands  SET adjacent_system = 'NCOK', caption = 'Пуск ОТР2 із БГ2', command = 'Otr2FromBg2bLaunch', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33019;
UPDATE public.nppa_commands  SET adjacent_system = 'NCOK', caption = 'Продовження ППіП із БГ2', command = 'PPiPWithBg2bContinuation', used_in_combat_mode = true, used_in_test_mode = false, used_in_workflow = false, has_blocker = true WHERE id = 33029;




