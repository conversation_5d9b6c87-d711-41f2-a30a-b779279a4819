-- liquibase formatted sql

-- changeset liquibase:45

insert into public.nppa_commands (id, adjacent_system, caption, command, used_in_combat_mode, used_in_test_mode, used_in_workflow, has_blocker)
values  (20000, 'NCOK', 'Автоматична Перевірка НППА з ЕБАСУ1 і ЕБАСУ2', 'AutoNppaEbasu1Ebasu2TestMode', false, false, true, true),
        (20001, 'NCOK', 'Автоматична Перевірка НППА з ЕБАСУ1', 'AutoNppaEbasu1TestMode', false, false, true, true),
        (20002, 'NCOK', 'Автоматична Перевірка НППА з ЕБАСУ2', 'AutoNppaEbasu2TestMode', false, false, true, true),
        (20003, 'NCOK', 'Автоматична Перевірка ОТР1 після перезарядження', 'AutoOtr1AfterReloadingTestMode', false, false, true, true),
        (20004, 'NCOK', 'Автоматична Перевірка ОТР1', 'AutoOtr1TestMode', false, false, true, true),
        (20005, 'NCOK', 'Автоматична Перевірка ОТР2 після перезарядження', 'AutoOtr2AfterReloadingTestMode', false, false, true, true),
        (20006, 'NCOK', 'Автоматична Перевірка ОТР2', 'AutoOtr2TestMode', false, false, true, true);

INSERT INTO public.nppa_commanddao_available_at_readiness (nppa_commanddao_id, available_at_readiness)
VALUES (20000, 'BG_4'),
       (20001, 'BG_4'),
       (20002, 'BG_4'),
       (20003, 'BG_4'),
       (20004, 'BG_4'),
       (20005, 'BG_4'),
       (20006, 'BG_4');

