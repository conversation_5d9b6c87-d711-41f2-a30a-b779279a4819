-- liquibase formatted sql

-- changeset liquibase:53

CREATE TABLE launch_reference_points_record
(
    id              BIGINT GENERATED BY DEFAULT AS IDENTITY (minvalue 1000) NOT NULL,
    reference_point VARCHAR                     DEFAULT '',
    time_stamp      TIM<PERSON><PERSON>MP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
    plant_missile   VARCHAR(255)                              NOT NULL,
    CONSTRAINT pk_launch_reference_points_record PRIMARY KEY (id)
);

-- ALTER TABLE launch_reference_points_record
--     update column id set minvalue 1000;

ALTER TABLE launch_reference_points_record
    ADD COLUMN rocket_id BIGINT,
    ADD CONSTRAINT fk_rocket FOREIGN KEY (rocket_id) REFERENCES rocket (id);

