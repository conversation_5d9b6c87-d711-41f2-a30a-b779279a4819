-- liquibase formatted sql

-- changeset liquibase:50

UPDATE public.sae_commands
SET caption = 'Відключити фідер 1 (Апаратурний модуль 1) [OFF]'
WHERE id = 3;

UPDATE public.sae_commands
SET caption = 'Відключити фідер 2 (К<PERSON><PERSON><PERSON>на) [OFF]'
WHERE id = 5;

UPDATE public.sae_commands
SET caption = 'Відключити фідер 3 (Апаратурний модуль 2) [OFF]'
WHERE id = 6;

UPDATE public.sae_commands
SET caption = 'Відключити фідер 4 (УКХ радіостанція) [OFF]'
WHERE id = 7;

UPDATE public.sae_commands
SET caption = 'Відключити фідер 5 (Радіорелейна станція) [OFF]'
WHERE id = 8;

UPDATE public.sae_commands
SET caption = 'Відключити фідер 6 (НАП СНС та УКНСС) [OFF]'
WHERE id = 9;

UPDATE public.sae_commands
SET caption = 'Включити фідер 1 (Апаратурний модуль 1) [ON]'
WHERE id = 10;

UPDATE public.sae_commands
SET caption = 'Включити фідер 2 (Кабіна) [ON]'
WHERE id = 12;

UPDATE public.sae_commands
SET caption = 'Включити фідер 3 (Апаратурний модуль 2) [ON]'
WHERE id = 13;

UPDATE public.sae_commands
SET caption = 'Включити фідер 4 (УКХ радіостанція) [ON]'
WHERE id = 14;

UPDATE public.sae_commands
SET caption = 'Включити фідер 5 (Радіорелейна станція) [ON]'
WHERE id = 15;

UPDATE public.sae_commands
SET caption = 'Включити фідер 6 (НАК СНС та УКННС) [ON]'
WHERE id = 16;

UPDATE  public.sae_commands SET caption = 'Заблокуванти АДЖ SAE' WHERE id = 1;
UPDATE  public.sae_commands SET caption = 'Розблокуванти АДЖ SAE' WHERE id = 2;
UPDATE  public.sae_commands SET caption = 'Глушення АДЖ САE' WHERE id = 17;

UPDATE public.sae_commands
SET caption = 'Відключити фідер 1 НППА СУ [OFF]'
WHERE id = 4;

UPDATE public.sae_commands
SET caption = 'Включити фідер 1 НППА СУ  [ON]'
WHERE id = 11;

UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Режим роботи НЦОК перевірочний', command = 'NcokTestMode', used_in_combat_mode = false, used_in_test_mode = false, used_in_workflow = true, has_blocker = false WHERE id = 33002;
UPDATE public.nppa_commands SET adjacent_system = 'BYN', caption = 'Режим роботи БВН – перевірочний', command = 'BynTestMode', used_in_combat_mode = false, used_in_test_mode = false, used_in_workflow = true, has_blocker = false WHERE id = 34002;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Режим перевірок ОТР1 після перезарядження', command = 'Otr1AfterReloadingTestMode', used_in_combat_mode = false, used_in_test_mode = true, used_in_workflow = false, has_blocker = false WHERE id = 33524;
UPDATE public.nppa_commands SET adjacent_system = 'NCOK', caption = 'Режим перевірок ОТР2 після перезарядження', command = 'Otr2AfterReloadingTestMode', used_in_combat_mode = false, used_in_test_mode = true, used_in_workflow = false, has_blocker = false WHERE id = 33525;