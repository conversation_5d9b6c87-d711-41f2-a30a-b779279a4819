-- liquibase formatted sql

-- changeset liquibase:52

create table launch_reference_points
(
    id              bigint       not null
        primary key,
    adjacent_system varchar(255),
    caption         varchar(255) not null,
    command         varchar(255) not null
        constraint uk_caption
            unique
);

alter table launch_reference_points
    owner to postgres;

INSERT INTO public.launch_reference_points (id, adjacent_system, caption, command)
VALUES (1::bigint, 'PLC'::varchar(255), 'Стріла піднята'::varchar(255), 'ARROW_UP_LAUNCH'::varchar(255));

INSERT INTO public.launch_reference_points (id, adjacent_system, caption, command)
VALUES (2::bigint, 'PLC'::varchar(255), 'Шасі в горизонтальному положенні'::varchar(255),
        'CHASSIS_HORIZONTAL_POSITION_STATE'::varchar(255));

INSERT INTO public.launch_reference_points (id, adjacent_system, caption, command)
VALUES (3::bigint, 'PLC'::varchar(255), 'Пуск ОТР1/ОТР2/ОТР1 та ОТР2 з БГ3/БГ2/БГ1'::varchar(255),
        'LAUNCH_COMMAND'::varchar(255));

INSERT INTO public.launch_reference_points (id, adjacent_system, caption, command)
VALUES (4::bigint, 'PLC'::varchar(255), 'Пуск ОТР1 та ОТР2 з БГ2'::varchar(255), 'LAUNCH_COMMAND_2HOTR'::varchar(255));

INSERT INTO public.launch_reference_points (id, adjacent_system, caption, command)
VALUES (5::bigint, 'PLC'::varchar(255), 'Пуск ОТР1 з БГ1'::varchar(255), 'LAUNCH_COMMAND_OTR1'::varchar(255));

INSERT INTO public.launch_reference_points (id, adjacent_system, caption, command)
VALUES (6::bigint, 'PLC'::varchar(255), 'Пуск ОТР2 з БГ1'::varchar(255), 'LAUNCH_COMMAND_OTR2'::varchar(255));

INSERT INTO public.launch_reference_points (id, adjacent_system, caption, command)
VALUES (7::bigint, 'PLC'::varchar(255), 'Аутригери в похідному положені (ПП)'::varchar(255),
        'OUTRIGGERS_IN_MOBILE_STATE'::varchar(255));

INSERT INTO public.launch_reference_points (id, adjacent_system, caption, command)
VALUES (8::bigint, 'PLC'::varchar(255), 'Продовження ППіП із БГ2'::varchar(255), 'PPIP_WITH_2B'::varchar(255));

INSERT INTO public.launch_reference_points (id, adjacent_system, caption, command)
VALUES (9::bigint, 'PLC'::varchar(255), 'Закінчення точного приведення'::varchar(255), 'PRECISE_DRIVE'::varchar(255));

INSERT INTO public.launch_reference_points (id, adjacent_system, caption, command)
VALUES (10::bigint, 'PLC'::varchar(255), 'Розпочато початкове виставлення СК БІНС'::varchar(255),
        'STARTED_INITIAL_SET_SK_BINS'::varchar(255));

INSERT INTO public.launch_reference_points (id, adjacent_system, caption, command)
VALUES (11::bigint, 'PLC'::varchar(255), 'ТВ НЦОК (Норма)'::varchar(255), 'TV_NCOK_LAUNCH'::varchar(255));

INSERT INTO public.launch_reference_points (id, adjacent_system, caption, command)
VALUES (12::bigint, 'PLC'::varchar(255), 'Схід з СПУ ОТР1 (Норма)'::varchar(255), 'LAUNCH_OTR_LEFT_PDP '::varchar(255));

INSERT INTO public.launch_reference_points (id, adjacent_system, caption, command)
VALUES (13::bigint, 'PLC'::varchar(255), 'Схід з СПУ ОТР2 (Норма)'::varchar(255), 'LAUNCH_OTR_RIGHT_PDP'::varchar(255));