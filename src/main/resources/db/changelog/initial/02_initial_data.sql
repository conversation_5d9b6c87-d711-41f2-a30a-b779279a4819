-- liquibase formatted sql

-- changeset liquibase:2

insert into public.ppo_unit (id, bay_type, unit_alias, unit_name, unit_state, unit_type)
values  (1, 'ASKU', 'BALLOON_ASKU_1', 'балон 1', 'UNDEFINED', 'BALLOON'),
        (2, 'ASKU', 'BALLOON_ASKU_2', 'балон 2', 'UNDEFINED', 'BALLOON'),
        (3, 'NPPA', 'BALLOON_NPPA_1', 'балон 1', 'UNDEFINED', 'BALLOON'),
        (4, 'NPPA', 'BALLOON_NPPA_2', 'балон 2', 'UNDEFINED', 'BALLOON'),
        (5, 'DEA', 'BALLOON_DEA_1', 'балон 1', 'UNDEFINED', 'BALLOON'),
        (6, 'DEA', 'BALLOON_DEA_2', 'балон 2', 'UNDEFINED', 'BALLOON'),
        (7, 'TO', 'BALLOON_TO_1', 'балон 1', 'UNDEFINED', 'BALLOON'),
        (8, 'TO', 'BALLOON_TO_2', 'балон 2', 'UNDEFINED', 'BALLOON'),
        (9, 'ASKU', 'OPTICAL_SENSOR_ASKU_1', 'ОД1', 'UNDEFINED', 'OPTICAL_SENSOR'),
        (10, 'ASKU', 'OPTICAL_SENSOR_ASKU_2', 'ОД2', 'UNDEFINED', 'OPTICAL_SENSOR'),
        (11, 'ASKU', 'OPTICAL_SENSOR_ASKU_3', 'ОД3', 'UNDEFINED', 'OPTICAL_SENSOR'),
        (12, 'ASKU', 'OPTICAL_SENSOR_ASKU_4', 'ОД4', 'UNDEFINED', 'OPTICAL_SENSOR'),
        (13, 'NPPA', 'OPTICAL_SENSOR_NPPA_1', 'ОД1', 'UNDEFINED', 'OPTICAL_SENSOR'),
        (14, 'NPPA', 'OPTICAL_SENSOR_NPPA_2', 'ОД2', 'UNDEFINED', 'OPTICAL_SENSOR'),
        (15, 'NPPA', 'OPTICAL_SENSOR_NPPA_3', 'ОД3', 'UNDEFINED', 'OPTICAL_SENSOR'),
        (16, 'DEA', 'THERMAL_SENSOR_DEA_1', 'ТД1', 'UNDEFINED', 'THERMAL_SENSOR'),
        (17, 'DEA', 'THERMAL_SENSOR_DEA_2', 'ТД2', 'UNDEFINED', 'THERMAL_SENSOR'),
        (18, 'DEA', 'THERMAL_SENSOR_DEA_3', 'ТД3', 'UNDEFINED', 'THERMAL_SENSOR'),
        (19, 'TO', 'THERMAL_SENSOR_TO_1', 'ТД1', 'UNDEFINED', 'THERMAL_SENSOR'),
        (20, 'TO', 'THERMAL_SENSOR_TO_2', 'ТД2', 'UNDEFINED', 'THERMAL_SENSOR'),
        (21, 'TO', 'THERMAL_SENSOR_TO_3', 'ТД3', 'UNDEFINED', 'THERMAL_SENSOR');

insert into public.ppo_bay (id, fire_present, ppo_bay_type)
values  (1, false, 'ASKU'),
        (2, false, 'NPPA'),
        (3, false, 'DEA'),
        (4, false, 'TO');
insert into public.ppo_commands (id, adjacent_system, caption, command)
values  (1, 'PPO', 'Двигун відсіку ДЕА зупинений', 'TurnOffDEAEngine'),
        (2, 'PPO', 'Дозволити запуск другої групи відсіку ДЕА', 'permitLaunchDEA'),
        (3, 'PPO', 'Дозволити запуск другої групи відсіку ТО', 'permitLaunchTO'),
        (4, 'PPO', 'Дозволити запуск другої групи відсіку АСКУ', 'permitLaunchASKU'),
        (5, 'PPO', 'Дозволити запуск другої групи відсіку НППА', 'permitLaunchNPPA');


insert into public.ppo_bay_units_inppo_bay (ppo_bay_id, units_inppo_bay_id)
values  (1, 1),
        (1, 2),
        (1, 9),
        (1, 10),
        (1, 11),
        (1, 12),
        (2, 3),
        (2, 4),
        (2, 13),
        (2, 14),
        (2, 15),
        (3, 5),
        (3, 6),
        (3, 16),
        (3, 17),
        (3, 18),
        (4, 7),
        (4, 8),
        (4, 19),
        (4, 20),
        (4, 21);

insert into public.sae_commands (id, adjacent_system, caption, command)
values  (1, 'SAE', 'Заблокуванти АДЖ SAE', 'LockAdj'),
        (2, 'SAE', 'Розблокуванти АДЖ SAE', 'UnlockAdj'),
        (3, 'SAE', 'Відключити фідер 1', 'TurnOffFeeder1'),
        (4, 'SAE', 'Відключити фідер 1 НППА СУ', 'TurnOffFeeder1NPPA'),
        (5, 'SAE', 'Відключити фідер 2', 'TurnOffFeeder2'),
        (6, 'SAE', 'Відключити фідер 3', 'TurnOffFeeder3'),
        (7, 'SAE', 'Відключити фідер 4', 'TurnOffFeeder4'),
        (8, 'SAE', 'Відключити фідер 5', 'TurnOffFeeder5'),
        (9, 'SAE', 'Відключити фідер 6', 'TurnOffFeeder6'),
        (10, 'SAE', 'Включити фідер 1', 'TurnOnFeeder1'),
        (11, 'SAE', 'Включити фідер 1 НППА СУ', 'TurnOnFeeder1NPPA'),
        (12, 'SAE', 'Включити фідер 2', 'TurnOnFeeder2'),
        (13, 'SAE', 'Включити фідер 3', 'TurnOnFeeder3'),
        (14, 'SAE', 'Включити фідер 4', 'TurnOnFeeder4'),
        (15, 'SAE', 'Включити фідер 5', 'TurnOnFeeder5'),
        (16, 'SAE', 'Включити фідер 6', 'TurnOnFeeder6'),
        (17, 'SAE', 'Глушення АДЖ САE', 'TurnOffAdj'),
        (18, 'SAE', 'Зробити запит готовності САЄ', 'RequestSaeReadiness');

insert into public.suto_commands (id, adjacent_system, caption, command)
values  (1, 'SUTO', 'Запуск двигуна СПУ', 'StartSPLEngine'),
        (2, 'SUTO', 'Увімкнення основного насоса ГС (Н1)', 'TurnOnMainPump'),
        (3, 'SUTO', 'Режим работи від НППА', 'ControlModeFromNPPA'),
        (4, 'SUTO', 'Режим работи від АСКУ', 'ControlModeFromASKU'),
        (5, 'SUTO', 'Вимкнення основного насоса ГС (Н1)', 'TurnOffMainPump'),
        (6, 'SUTO', 'Зупинка двигуна СПУ', 'StopSPLEngine'),
        (7, 'SUTO', 'Стоп', 'StopAll'),
        (8, 'SUTO', 'Запит стану СУТО (холоста команда)', 'RefreshStateRequest'),
        (9, 'SUTO', 'Вивішування та горизонтування шасі', 'HangingAndLevelingChassis'),
        (10, 'SUTO', 'Переведення шасі в похідне положення (ПП)', 'SwitchChassisMobileMode'),
        (11, 'SUTO', 'Висунути газовідбивачі для перезаряджання', 'ExtendGasReFlectorForReloading'),
        (12, 'SUTO', 'Підйом стріли', 'RaiseArm'),
        (13, 'SUTO', 'Опускання 2-х газовідбивачів', 'LowerAllGasReflectors'),
        (14, 'SUTO', 'Підйом 2-х газовідбивачів', 'Lifting of 2 gas deflectors'),
        (15, 'SUTO', 'Опускання лівого газовідбивача', 'LowerLeftGasReflector'),
        (16, 'SUTO', 'Опускання правого газовідбивача', 'LowerRightGasReflector'),
        (17, 'SUTO', 'Підйом лівого газовідбивача', 'LiftLeftGasReflector'),
        (18, 'SUTO', 'Підйом правого газовідбивача', 'LiftRightGasReflector'),
        (19, 'SUTO', 'Опускання стріли', 'LowerArm');

insert into public.suto_properties (id, caption, name, property_type)
values  (1, 'Проводиться горизонтування шасі', 'chassisHorizontalAlignment', 'REGULAR'),
        (2, 'Шасі в горизонтальному положенні', 'isChassisHorizontal', 'REGULAR'),
        (3, 'Горизонтування неможливе', 'isAlignmentImpossible', 'REGULAR'),
        (4, 'Виконується переведення аутригерів у ПП', 'isOutriggersMovingToPP', 'REGULAR'),
        (5, 'Аутригери в похідному положенні (ПП)', 'isOutriggersInMobileState', 'REGULAR'),
        (6, 'Нештатна ситуація вивішування та горизонту-вання', 'isEmergencyHangingAndAlignment', 'MALFUNCTION'),
        (7, 'Вихідне (похідне) положення аутригера лівої передньої опори', 'leftFrontOutriggerInitialPosition', 'REGULAR'),
        (8, 'Вихідне (похідне) положення аутригера правої передньої опори', 'rightFrontOutriggerInitialPosition', 'REGULAR'),
        (9, 'Вихідне (похідне) положення аутригера правої задньої опори', 'rightRearOutriggerInitialPosition', 'REGULAR'),
        (10, 'Вихідне (похідне) положення аутригера лівої задньої опори', 'leftRearOutriggerInitialPosition', 'REGULAR'),
        (11, 'Стріла розстопорена', 'armUnlocked', 'REGULAR'),
        (12, 'Виконується підйом стріли', 'armRaising', 'REGULAR'),
        (13, 'Стріла піднята', 'armRaised', 'REGULAR'),
        (14, 'Лівий газовідбивач розстопорений', 'leftGasSpringUnlocked', 'REGULAR'),
        (15, 'Правий газовідбивач розстопорений', 'rightGasSpringUnlocked', 'REGULAR'),
        (16, 'Опускання лівого газовідбивача', 'isLoweringLeftGasSpring', 'REGULAR'),
        (17, 'Опускання правого газовідбивача', 'isLoweringRightGasSpring', 'REGULAR'),
        (18, 'Нештатна ситуація (стріли та газовідбивачів)', 'emergencySituationForArmAndGasSprings', 'REGULAR'),
        (19, 'Лівий газовідбивач опущений', 'leftGasSpringLowered', 'REGULAR'),
        (20, 'Правий газовідбивач опущений', 'rightGasSpringLowered', 'REGULAR'),
        (21, 'Проводиться підйом лівого газовідбивача', 'isRaisingLeftGasSpring', 'REGULAR'),
        (22, 'Проводиться підйом правого газовідбивача', 'isRaisingRightGasSpring', 'REGULAR'),
        (23, 'Лівий газовідбивач у похідному положенні (ІП)', 'leftGasSpringInMobileState', 'REGULAR'),
        (24, 'Правий газовідбивач у похідному положенні (ІП)', 'rightGasSpringInMobileState', 'REGULAR'),
        (25, 'Лівий газовідбивач застопорений', 'leftGasSpringLocked', 'REGULAR'),
        (26, 'Правий газовідбивач застопорений', 'rightGasSpringLocked', 'REGULAR'),
        (27, 'Опускання стріли', 'isLoweringArm', 'REGULAR'),
        (28, 'Стріла в похідному положенні (ІП)', 'armInMobileState', 'REGULAR'),
        (29, 'Стріла застопорена', 'boomLocked', 'REGULAR'),
        (30, 'Стріла в кінцевому положенні (КП)', 'boomInEndPosition', 'REGULAR'),
        (31, 'Несправність ЕП стріли', 'boomEPMalfunction', 'MALFUNCTION'),
        (32, 'Несправність ЕП стопоріння стріли', 'boomLockingEPMalfunction', 'MALFUNCTION'),
        (33, 'Несправність ЕП стопоріння лівого газовідвідувача', 'leftGasSpringLockingEPMalfunction', 'MALFUNCTION'),
        (34, 'Несправність ЕП стопоріння правого газовідбивача', 'rightGasSpringLockingEPMalfunction', 'MALFUNCTION'),
        (35, 'Несправність ЕП лівого газовідбивача', 'malfunctionLeftGasReflectorEP', 'MALFUNCTION'),
        (36, 'Несправність ЕП правого газовідбивача', 'malfunctionRightGasReflectorEP', 'MALFUNCTION'),
        (37, 'Максимальний тиск у ЦІ стріли (штокова порожнина)', 'armEmptyStrokeMaxPressure', 'MALFUNCTION'),
        (38, 'Максимальний тиск у ЦІ стріли (поршнева порожнина)', 'armPistonStrokeMaxPressure', 'MALFUNCTION'),
        (39, 'Запуск двигуна СПУ', 'SPLEngineStarting', 'REGULAR'),
        (40, 'Двигун СПУ запущено', 'SPLEngineStarted', 'REGULAR'),
        (41, 'Підключення основного насосу ГС (Н1)', 'mainPumpConnectionToGS_N1', 'REGULAR'),
        (42, 'Основний насос підключений до ГС (Н1)', 'mainPumpConnectedToGS_N1', 'REGULAR'),
        (43, 'Електромагніт розвантаження включений', 'unloadingElectromagnetEnabled', 'REGULAR'),
        (44, 'Виконується зупинка двигуна СПУ', 'SPLEnginestopping', 'REGULAR'),
        (45, 'Нештатна ситуація', 'emergencySituation', 'MALFUNCTION'),
        (46, 'Запуск двигуна неможливий', 'engineStartImpossible', 'MALFUNCTION'),
        (47, 'Запуск двигуна неможливий', 'engineStartImpossible2', 'MALFUNCTION'),
        (48, 'Зупинка двигуна неможлива', 'engineStopImpossible', 'MALFUNCTION'),
        (49, 'Пожежа', 'fire', 'MALFUNCTION'),
        (50, 'Забруднений фільтр ДЗФ-1', 'pollutedFilterDZF1', 'MALFUNCTION'),
        (51, 'Забруднений фільтр ДЗФ-2', 'pollutedFilterDZF2', 'MALFUNCTION'),
        (52, 'Тиск у повітряній магістралі не в нормі', 'airPressureNotNormal', 'MALFUNCTION'),
        (53, 'Передній лівий замок лівого ТПК закритий', 'leftFrontLockLeftTPKClosed', 'REGULAR'),
        (54, 'Передній лівий замок лівого ТПК відкритий', 'leftFrontLockLeftTPKOpened', 'REGULAR'),
        (55, 'Передній правий замок лівого ТПК закритий', 'rightFrontLockLeftTPKClosed', 'REGULAR'),
        (56, 'Передній правий замок лівого ТПК відкритий', 'rightFrontLockLeftTPKOpened', 'REGULAR'),
        (57, 'Задній правий замок лівого ТПК закритий', 'rightRearLockLeftTPKClosed', 'REGULAR'),
        (58, 'Задній правий замок лівого ТПК відкритий', 'rightRearLockLeftTPKOpened', 'REGULAR'),
        (59, 'Задній лівий замок лівого ТПК закритий', 'leftRearLockLeftTPKClosed', 'REGULAR'),
        (60, 'Задній лівий замок лівого ТПК відкритий', 'leftRearLockLeftTPKOpened', 'REGULAR'),
        (61, 'Передній лівий замок правого ТПК закритий', 'leftFrontLockRightTPKClosed', 'REGULAR'),
        (62, 'Передній лівий замок правого ТПК відкритий', 'leftFrontLockRightTPKOpened', 'REGULAR'),
        (63, 'Передній правий замок правого ТПК закритий', 'rightFrontLockRightTPKClosed', 'REGULAR'),
        (64, 'Передній правий замок правого ТПК відкритий', 'rightFrontLockRightTPKOpened', 'REGULAR'),
        (65, 'Задній правий замок правого ТПК закритий', 'rightRearLockRightTPKClosed', 'REGULAR'),
        (66, 'Задній правий замок правого ТПК відкритий', 'rightRearLockRightTPKOpened', 'REGULAR'),
        (67, 'Задній лівий замок правого ТПК закритий', 'leftRearLockRightTPKClosed', 'REGULAR'),
        (68, 'Задній лівий замок правого ТПК відкритий', 'leftRearLockRightTPKOpened', 'REGULAR'),
        (69, 'Стоп від СУТО', 'SUTOStop', 'REGULAR');

insert into public.suto (id, left_front_outrigger_emergency_code, left_rear_outrigger_emergency_code, right_front_outrigger_emergency_code, right_rear_outrigger_emergency_code, arm_lift_stroke_position, leveling_cycles_count, main_pump_rmp, overall_arm_liftings_count, overall_operating_time, pitch, pressure_in_impulse_section, roll, temperature_rr, working_fluid_level, chassis_state, left_tpc_load, right_tpc_load)
values  (1, 'NONE', 'NONE', 'NONE', 'NONE', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'UNKNOWN', 'UNKNOWN', 'UNKNOWN');

insert into public.suto_to_suto_properties (suto_id, properties_id)
values  (1, 1),
        (1, 2),
        (1, 3),
        (1, 4),
        (1, 5),
        (1, 6),
        (1, 7),
        (1, 8),
        (1, 9),
        (1, 10),
        (1, 11),
        (1, 12),
        (1, 13),
        (1, 14),
        (1, 15),
        (1, 16),
        (1, 17),
        (1, 18),
        (1, 19),
        (1, 20),
        (1, 21),
        (1, 22),
        (1, 23),
        (1, 24),
        (1, 25),
        (1, 26),
        (1, 27),
        (1, 28),
        (1, 29),
        (1, 30),
        (1, 31),
        (1, 32),
        (1, 33),
        (1, 34),
        (1, 35),
        (1, 36),
        (1, 37),
        (1, 38),
        (1, 39),
        (1, 40),
        (1, 41),
        (1, 42),
        (1, 43),
        (1, 44),
        (1, 45),
        (1, 46),
        (1, 47),
        (1, 48),
        (1, 49),
        (1, 50),
        (1, 51),
        (1, 52),
        (1, 53),
        (1, 54),
        (1, 55),
        (1, 56),
        (1, 57),
        (1, 58),
        (1, 59),
        (1, 60),
        (1, 61),
        (1, 62),
        (1, 63),
        (1, 64),
        (1, 65),
        (1, 66),
        (1, 67),
        (1, 68),
        (1, 69);

insert into public.nppa_commands (id, adjacent_system, caption, command, used_in_combat_mode, used_in_test_mode, used_in_workflow, has_blocker)
values  (33001, 'NCOK', 'Режим роботи НЦОК бойовий', 'NcokCombatMode', false, false, true, false),
        (33002, 'NCOK', 'Режим роботи НЦОК перевірний', 'NcokTestMode', false, false, true, false),
        (33014, 'NCOK', 'Пуск ОТР1 із БГ3', 'Otr1FromBg3Launch', true, false, false, false),
        (33015, 'NCOK', 'Пуск ОТР2 із БГ3', 'Otr2FromBg3Launch', true, false, false, false),
        (33016, 'NCOK', 'Пуск ОТР1 із БГ2а', 'Otr1FromBg2aLaunch', true, false, false, false),
        (33017, 'NCOK', 'Пуск ОТР2 із БГ2а', 'Otr2FromBg2aLaunch', true, false, false, false),
        (33018, 'NCOK', 'Пуск ОТР1 із БГ2б', 'Otr1FromBg2bLaunch', true, false, false, false),
        (33019, 'NCOK', 'Пуск ОТР2 із БГ2б', 'Otr2FromBg2bLaunch', true, false, false, false),
        (33020, 'NCOK', 'Пуск ОТР1 із БГ1', 'Otr1FromBg1Launch', true, false, false, false),
        (33021, 'NCOK', 'Пуск ОТР2 із БГ1', 'Otr2FromBg1Launch', true, false, false, false),
        (33022, 'NCOK', 'Перехід із БГ2а в БГ1 з увімкненням ОТР1', 'Bg2aToBg1SwitchWithOtr1SwOn', true, false, false, false),
        (33023, 'NCOK', 'Перехід із БГ2а в БГ1 з увімкненням ОТР2', 'Bg2aToBg1SwitchWithOtr2SwOn', true, false, false, false),
        (33024, 'NCOK', 'Перехід із БГ3 в БГ1 з увімкненням ОТР1', 'Bg3ToBg1SwitchWithOtr1SwOn', true, false, false, false),
        (33025, 'NCOK', 'Перехід із БГ3 в БГ1 з увімкненням ОТР2', 'Bg3ToBg1SwitchWithOtr2SwOn', true, false, false, false),
        (33026, 'NCOK', 'Перехід із БГ1 у БГ3', 'Bg1toBg3Toggle', true, false, false, false),
        (33027, 'NCOK', 'Перехід із БГ1 у БГ2а', 'Bg1toBg2aToggle', true, false, false, false),
        (33029, 'NCOK', 'Продовження ППіП із БГ2б', 'PPiPWithBg2bContinuation', true, false, false, false),
        (33030, 'NCOK', 'ВД на пуск для ОТР1', 'Otr1InitialData', true, false, false, false),
        (33031, 'NCOK', 'ВД на пуск для ОТР2', 'Otr2InitialData', true, false, false, false),
        (33032, 'NCOK', 'Скасування пуску ОТР1', 'Otr1LaunchCancell', true, false, false, false),
        (33033, 'NCOK', 'Скасування пуску ОТР2', 'Otr2LaunchCancell', true, false, false, false),
        (33524, 'NCOK', 'Режим перевірення ОТР1 після перезарядження', 'Otr1AfterReloadingTestMode', false, true, false, false),
        (33525, 'NCOK', 'Режим перевірення ОТР2 після перезарядження', 'Otr2AfterReloadingTestMode', false, true, false, false),
        (33526, 'NCOK', 'Режим перевірок ОТР1', 'Otr1TestMode', false, true, false, false),
        (33527, 'NCOK', 'Режим перевірок ОТР2', 'Otr2TestMode', false, true, false, false),
        (33528, 'NCOK', 'Режим перевірок НППА з ЕБАСУ1', 'NppaEbasu1TestMode', false, true, false, false),
        (33529, 'NCOK', 'Режим перевірок НППА з ЕБАСУ2', 'NppaEbasu2TestMode', false, true, false, false),
        (33530, 'NCOK', 'Режим перевірок НППА з ЕБАСУ1 і ЕБАСУ2', 'NppaEbasu1Ebasu2TestMode', false, true, false, false),
        (33531, 'NCOK', 'Скасування режиму перевірок', 'CancelNcokTestMode', false, true, false, false),
        (34001, 'BYN', 'Режим роботи БУН – основний', 'BynCombatMode', false, false, true, false),
        (34002, 'BYN', 'Режим роботи БУН – перевірний', 'BynTestMode', false, false, true, false),
        (34004, 'BYN', 'Увімкнення живлення НЦОК', 'TurnOnNcok', false, false, true, false),
        (34011, 'BYN', 'Вимкнення живлення НЦОК', 'TurnOffNcok', false, false, true, false),
        (34021, 'BYN', 'Скасування режиму перевірок', 'CancelBynTestMode', false, false, true, false);


insert into public.nppa_commanddao_available_at_readiness (nppa_commanddao_id, available_at_readiness)
values  (33029, 'BG_2B'),
        (33016, 'BG_2A'),
        (33020, 'BG_1'),
        (33018, 'BG_2B'),
        (33014, 'BG_3'),
        (33023, 'BG_2A'),
        (33022, 'BG_2A'),
        (33015, 'BG_3'),
        (33017, 'BG_2A'),
        (33019, 'BG_2B'),
        (33021, 'BG_1'),
        (33025, 'BG_3'),
        (33024, 'BG_3'),
        (33027, 'BG_1'),
        (33026, 'BG_1');