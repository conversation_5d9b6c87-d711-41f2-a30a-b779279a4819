-- liquibase formatted sql

-- changeset liquibase:1

create table ppo_bay
(
    id           bigint generated by default as identity (minvalue 1000)
        primary key,
    fire_present boolean default false not null,
    ppo_bay_type varchar(255)
);

alter table ppo_bay
    owner to postgres;

create table ppo_commands
(
    id              bigint generated by default as identity (minvalue 1000)
        primary key,
    adjacent_system varchar(255),
    caption         varchar(255) not null,
    command         varchar(255) not null
        constraint uk_9dxhesnbucnixbegrjb4h5juf
            unique
);

alter table ppo_commands
    owner to postgres;

create table ppo_record
(
    id              bigint generated by default as identity (minvalue 1000)
        primary key,
    adjacent_system integer not null,
    caption         varchar(255),
    command         varchar(255),
    command_state   integer,
    execution_time  timestamp,
    generation_time timestamp,
    originator      varchar(255)
);

alter table ppo_record
    owner to postgres;

create table ppo_unit
(
    id         bigint not null
        primary key,
    bay_type   varchar(255),
    unit_alias varchar(255),
    unit_name  varchar(255),
    unit_state varchar(255) default 'UNDEFINED'::character varying,
    unit_type  varchar(255),
    constraint ukjaupao6r6nulb32fhxx7ssh6u
        unique (unit_name, bay_type)
);

alter table ppo_unit
    owner to postgres;

create table ppo_bay_units_inppo_bay
(
    ppo_bay_id         bigint not null
        constraint fkkvgxukuxvae9tys4ygih277ym
            references ppo_bay,
    units_inppo_bay_id bigint not null
        constraint uk_ke8ygoum0tbqpbwgr9nqoinhl
            unique
        constraint fk98k6ba9hhanh61rv3e5560fu2
            references ppo_unit,
    primary key (ppo_bay_id, units_inppo_bay_id)
);

alter table ppo_bay_units_inppo_bay
    owner to postgres;

create table sae_commands
(
    id              bigint       not null
        primary key,
    adjacent_system varchar(255),
    caption         varchar(255) not null,
    command         varchar(255) not null
        constraint uk_bf3n193x9xkm8sv2btcndyl63
            unique
);

alter table sae_commands
    owner to postgres;

create table sae_record
(
    id              bigint       not null
        primary key,
    adjacent_system integer      not null,
    caption         varchar(255) not null,
    command         varchar(255) not null,
    command_state   integer,
    execution_time  timestamp,
    generation_time timestamp,
    originator      varchar(255)
);

alter table sae_record
    owner to postgres;

create table suto
(
    id                                   bigint generated by default as identity (minvalue 1000)
        primary key,
    left_front_outrigger_emergency_code  varchar(255) default 'NONE'::character varying,
    left_rear_outrigger_emergency_code   varchar(255) default 'NONE'::character varying,
    right_front_outrigger_emergency_code varchar(255) default 'NONE'::character varying,
    right_rear_outrigger_emergency_code  varchar(255) default 'NONE'::character varying,
    arm_lift_stroke_position             integer not null,
    leveling_cycles_count                integer not null,
    main_pump_rmp                        integer,
    overall_arm_liftings_count           integer not null,
    overall_operating_time               integer not null,
    pitch                                integer     default 0,
    pressure_in_impulse_section          integer not null,
    roll                                 integer     default 0,
    temperature_rr                       integer,
    working_fluid_level                  integer not null,
    chassis_state                        varchar(30)  default 'UNKNOWN'::character varying,
    left_tpc_load                        varchar(30)  default 'UNKNOWN'::character varying,
    right_tpc_load                       varchar(30)  default 'UNKNOWN'::character varying
);

alter table suto
    owner to postgres;

create table suto_commands
(
    id              bigint generated by default as identity (minvalue 1000)
        primary key,
    adjacent_system varchar(255),
    caption         varchar(255) not null,
    command         varchar(255) not null
        constraint uk_7y4oq54cu7vn80fj7xbb030xk
            unique
);

alter table suto_commands
    owner to postgres;

create table suto_properties
(
    id            bigint generated by default as identity (minvalue 1000)
        primary key,
    caption       varchar(255) not null,
    name          varchar(255) not null
        constraint uk_ngeqjx86axlxn573x0khu1fn7
            unique,
    property_type varchar(255)
);

alter table suto_properties
    owner to postgres;

create table suto_record
(
    id              bigint generated by default as identity (minvalue 1000)
        primary key,
    adjacent_system integer not null,
    caption         varchar(255),
    command         varchar(255),
    command_state   integer,
    execution_time  timestamp,
    generation_time timestamp,
    originator      varchar(255)
);

alter table suto_record
    owner to postgres;

create table suto_to_suto_properties
(
    suto_id       bigint not null
        constraint fks6hil3b2it0jvghd84fk17j5y
            references suto,
    properties_id bigint not null
--         constraint uk_ossufjafawei64fu4kdm5yawa
--             unique
        constraint fkbmwtcqwceo6ewrechsvya62am
            references suto_properties,
    primary key (suto_id, properties_id)
);
create index uk_ossufjafawei64fu4kdm5yawa
    on suto_to_suto_properties (properties_id);

alter table suto_to_suto_properties
    owner to postgres;

create table bins_record
(
    id                bigint generated by default as identity
        primary key,
    adjacent_system   integer not null,
    direction         integer,
    generation_date   timestamp,
    original_sentence varchar(255),
    originator        varchar(255)
);

alter table bins_record
    owner to postgres;

create table msu_record
(
    id                bigserial
        primary key,
    adjacent_system   integer not null,
    generation_date   timestamp,
    direction         integer,
    original_sentence varchar(255),
    originator        varchar(255)
);

alter table msu_record
    owner to postgres;

create table hibernate_sequences
(
    sequence_name varchar(255) not null
        primary key,
    next_val      bigint
);

alter table hibernate_sequences
    owner to postgres;

create table spl_navigation_rest_log
(
    id                   bigint  not null
        primary key,
    adjacent_system      integer not null,
    cached_payload       varchar(2048),
    generation_date      timestamp,
    direction            integer,
    request_query_string varchar(255),
    url                  varchar(255),
    method               varchar(255),
    user_info            varchar(255)
);

alter table spl_navigation_rest_log
    owner to postgres;

create type subsystem as enum ('none','NCOK','BYN', 'NCVK', 'BVN');

alter type subsystem owner to postgres;


create table msgs_from_ksak
(
    id   bigserial
        primary key,
    ts   timestamp(2) not null,
    dest subsystem,
    code integer,
    text text,
    data bytea
);

alter table msgs_from_ksak
    owner to postgres;

create table msgs_from_nppa
(
    id      bigserial
        primary key,
    ksak_ts timestamp(2) not null,
    nppa_ts double precision,
    src     subsystem,
    code    integer,
    text    text,
    data    bytea
);

alter table msgs_from_nppa
    owner to postgres;

create table nppa_commands
(
    id                  bigint generated by default as identity (minvalue 1000)
        constraint ncok_commands_pkey
            primary key,
    adjacent_system     varchar(255),
    caption             varchar(255)          not null,
    command             varchar(255)          not null
        constraint uk_7he9h8mqvcwnhspumjdbhiiye
            unique,
    used_in_combat_mode boolean default false not null,
    used_in_test_mode   boolean default false not null,
    used_in_workflow    boolean default false not null,
    has_blocker         boolean default false
);

alter table nppa_commands
    owner to postgres;

create table nppa_command_record
(
    id              bigint generated by default as identity
        primary key,
    adjacent_system integer not null,
    caption         varchar(255),
    command         varchar(255),
    command_state   integer,
    execution_time  timestamp,
    generation_time timestamp,
    originator      varchar(255)
);

alter table nppa_command_record
    owner to postgres;

create table nppa_commanddao_available_at_readiness
(
    nppa_commanddao_id     bigint not null
        constraint fkrpc7lc1rpecb2t6y77v2cp1xh
            references nppa_commands,
    available_at_readiness varchar(255)
);

alter table nppa_commanddao_available_at_readiness
    owner to postgres;

create table tpc
(
    id                 bigint generated by default as identity (minvalue 1000)
        primary key,
    date_release_m     timestamp,
    factory_number_tpc varchar(255),
    plant_tpc          varchar(255),
    tpc_load_state     varchar(255) default 'UNKNOWN'::character varying
);

alter table tpc
    owner to postgres;

create table launch_initial_data
(
    id                     bigint           not null
        primary key,
    altitude               double precision,
    generation_date        timestamp,
    inclination_angle      double precision not null,
    is_pro_detected        boolean,
    latitude               double precision,
    longitude              double precision,
    missile_operating_mode varchar(255)     default 'UNKNOWN'::character varying,
    readiness              varchar(255)     default 'UNDEFINED'::character varying,
    trajectory             varchar(255)     default 'UNKNOWN'::character varying,
    tl_signature           varchar(255),
    loaded_to_plc          boolean          default false,
    load_temperature       double precision default '-999.9'::numeric
);

alter table launch_initial_data
    owner to postgres;

create table rocket_from_data
(
    id                      bigint                                              not null
        primary key,
    alp_type                varchar(255) default 'UNDEFINED'::character varying not null,
    crc                     varchar(255),
    generation_date         timestamp,
    gsn_type                varchar(255) default 'UNDEFINED'::character varying not null,
    is_archived             boolean      default false,
    is_telemetry_integrated boolean      default false                          not null,
    plant_missile           varchar(8),
    purpose_type            varchar(255) default 'UNDEFINED'::character varying not null,
    warhead                 varchar(255) default 'UNDEFINED'::character varying not null
);

alter table rocket_from_data
    owner to postgres;

create table rocket
(
    id                             bigint    not null
        primary key,
    date_release_m                 timestamp,
    date_use_m                     timestamp,
    initial_data_source            varchar(255) default 'UNKNOWN'::character varying,
    technical_condition            boolean,
    updated_at                     timestamp not null default now(),
    form_data_id                   bigint
        constraint fk5p07s83h60upefufty4lsy4sn
            references rocket_from_data,
    initial_data_id                bigint
        constraint fkjc7ffvdhxxmx7k4umtd2hkfl3
            references launch_initial_data,
    initial_datasource_description varchar(255) default 'не вказан'::character varying,
    initial_data_time_stamp        timestamp
);

alter table rocket
    owner to postgres;

create table asku
(
    id                   bigint generated by default as identity (minvalue 1000)
        primary key,
    system_status        VARCHAR(255) DEFAULT 'UNDEFINED',
    plc_status           varchar(255) default 'UNDEFINED'::character varying,
    spl_id               uuid,
    plate_number         varchar(255),
    spl_readiness        varchar(255) default 'UNDEFINED'::character varying,
    readiness_started_at timestamp,
    asku_status          varchar(255) default 'UNDEFINED'::character varying,
    unit_title           varchar(255),
    updated_at           timestamp not null default now(),
    left_rocket_id       bigint
        constraint fkp7onlx11ndbg3xw9gb6h05k56
            references rocket,
    right_rocket_id      bigint
        constraint fklhqoueyv5418ykronji8qm891
            references rocket,
    tpc_left_id          bigint
        constraint fk35qaw4k4kn6663jkvj7ao082h
            references tpc,
    tpc_right_id         bigint
        constraint fkliky00nk9fws358lghe333l26
            references tpc,
    started_date         timestamp
);

alter table asku
    owner to postgres;

create table rocket_stored_tl_keys
(
    rocket_id      bigint not null
        constraint fkc7e60vgohqg1py7xlvo2oau0
            references rocket,
    stored_tl_keys varchar(255)
);

alter table rocket_stored_tl_keys
    owner to postgres;

create table asku_command_record
(
    id              bigint generated by default as identity (minvalue 1000)
        primary key,
    adjacent_system integer not null,
    caption         varchar(255),
    command         varchar(255),
    command_state   integer,
    execution_time  timestamp,
    generation_time timestamp,
    originator      varchar(255)
);

alter table asku_command_record
    owner to postgres;

create table asku_commands
(
    id                  bigint generated by default as identity
        primary key,
    adjacent_system     varchar(255),
    caption             varchar(255)          not null,
    command             varchar(255)          not null
        constraint uk_7f7lth99u59dmydgmg1qwji9k
            unique,
    has_blocker         boolean default false,
    used_in_combat_mode boolean default false not null,
    used_in_test_mode   boolean default false not null,
    used_in_workflow    boolean default false not null
);

alter table asku_commands
    owner to postgres;

create table asku_commanddao_available_at_readiness
(
    asku_commanddao_id     bigint not null
        constraint fk4scg8ue10i6oucn5yjuu2li2t
            references asku_commands,
    available_at_readiness varchar(255)
);

alter table asku_commanddao_available_at_readiness
    owner to postgres;


create sequence hibernate_sequence;
alter sequence hibernate_sequence owner to postgres;

insert into public.hibernate_sequences (sequence_name, next_val)
values  ('default', 0);