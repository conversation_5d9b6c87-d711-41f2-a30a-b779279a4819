-- liquibase formatted sql

-- changeset liquibase:56

insert into public.launch_reference_points (id, adjacent_system, caption, command)
values (14, 'PLC', 'Вирішена навігаційна задача ОТР1', 'BAS_SNS_STATUS_OTR1_OK'),
       (15, 'PLC', 'ТВ ненорма чи не вирішена навігаційна задача ОТР1', 'BAS_SNS_STATUS_OTR1_ERROR'),
       (16, 'PLC', 'Вирішена навігаційна задача ОТР2', 'BAS_SNS_STATUS_OTR2_OK'),
       (17, 'PLC', 'ТВ ненорма чи не вирішена навігаційна задача ОТР2', 'BAS_SNS_STATUS_OTR2_ERROR');

insert into nppa_commands (id,
                           adjacent_system,
                           caption,
                           command,
                           used_in_combat_mode,
                           used_in_test_mode,
                           used_in_workflow,
                           has_blocker)
values (20007, 'NCOK', 'Скасування переходу в готовність', 'CancelBgToBgToggle', false, false, true, true),
       (20008, 'NCOK', 'Скасування автоматичної перевірки', 'CancelAutoTestMode', false, false, true, true),
       (20009, 'NCOK', 'Підтвердження автоматичного скасування пуску ОТР1', 'Otr1AutomaticLaunchCancelAcknowledgment', true,
        true, true, false),
       (20010, 'NCOK', 'Підтвердження автоматичного скасування пуску ОТР2', 'Otr2AutomaticLaunchCancelAcknowledgment', true,
        true, true, false);

insert into nppa_commanddao_available_at_readiness (nppa_commanddao_id, available_at_readiness)
values (20008, 'AUTO_TEST_IN_PROGRESS'),
       (20007, 'BG_4_TO_BG_3'),
       (20007, 'BG_3_TO_BG_2A'),
       (20007, 'BG_3_TO_BG_2B'),
       (20007, 'BG_3_TO_BG_1');

