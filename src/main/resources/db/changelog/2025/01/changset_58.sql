-- liquibase formatted sql

-- changeset liquibase:58


create index bins_record_generation_date_index
    on public.bins_record (generation_date desc);


create index bun_updated_at_index
    on public.byn (updated_at desc);

create index launch_initial_data_generation_date_index
    on public.launch_initial_data (generation_date desc);

-- launch_reference_points_record

create index msgs_from_ksak_ts_index
    on public.msgs_from_ksak (ts desc);

create index msgs_from_nppa_ksak_ts_index
    on public.msgs_from_nppa (ksak_ts desc);

create index msu_record_generation_date_index
    on public.msu_record (generation_date desc);

create index ncok_updated_at_index
    on public.ncok (updated_at desc);

create index nppa_command_record_generation_time_index
    on public.nppa_command_record (generation_time desc);

create index ppo_updated_at_index
    on public.ppo (updated_at desc);

create index rocket_updated_at_index
    on public.rocket (updated_at desc);

create index rocket_from_data_generation_date_index
    on public.rocket_from_data (generation_date desc);

create index sae_updated_at_index
    on public.sae (updated_at desc);

create index sae_record_generation_time_index
    on public.sae_record (generation_time desc);

-- spl_navigation_rest_log
create index suto_updated_at_index
    on public.suto (updated_at desc);

create index suto_record_generation_time_index
    on public.suto_record (generation_time desc);

create index tpc_updated_at_index
    on public.tpc (updated_at desc)
