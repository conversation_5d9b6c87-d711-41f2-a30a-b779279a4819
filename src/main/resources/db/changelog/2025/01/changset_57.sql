-- liquibase formatted sql

-- changeset liquibase:57

create table nppa_commands_available_at_ncok_system_status
(
    nppa_command_dao_id bigint not null,
    available_only_at_ncok_status  varchar(255),
    CONSTRAINT fkrpc7lc1rpecb2t6y77v2cp1ll FOREIGN KEY (nppa_command_dao_id) REFERENCES nppa_commands (id)
);

alter table nppa_commands_available_at_ncok_system_status
    owner to postgres;

insert into nppa_commands_available_at_ncok_system_status (nppa_command_dao_id, available_only_at_ncok_status)
values (33018, 'NOT_CONNECTED'),
        (10002, 'NOT_CONNECTED'),
        (33014, 'NOT_CONNECTED'),
        (10008, 'NOT_CONNECTED'),
        (33024, 'NOT_CONNECTED'),
        (33025, 'NOT_CONNECTED'),
        (10001, 'NOT_CONNECTED'),
        (10009, 'NOT_CONNECTED'),
        (10005, 'NOT_CONNECTED'),
        (33019, 'NOT_CONNECTED'),
        (10003, 'NOT_CONNECTED'),
        (33015, 'NOT_CONNECTED');