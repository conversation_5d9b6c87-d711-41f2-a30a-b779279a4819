host.url=***************
ccv.host.url=${host.url}
ccv.host.port=3002
spring.datasource.url=jdbc:postgresql://${host.url}:5532/spl_control
ccv.rocket-status-endpoint.url=http://***************:${ccv.host.port}/api/asku/status/
ccv.app-settings.url=http://${ccv.host.url}:${ccv.host.port}/api/app_settings
ccv.vehicles.url=http://${ccv.host.url}:${ccv.host.port}/api/vehicles
ccv.token.request-parameter-name=camunda-token
ccv.token.value=12345
mmhs.url=http://${host.url}:3002
plc.token.request-parameter-name=Token
plc.request.token=plc12345
plc.response.token=server67890
plc.url=**************
spl.default-spl-id=02d9dd7e-8cda-40f1-b068-b04a23841097
spl.plate-number=DP 0101 UA
spl.unit.title=spl101


nsd.token=12345
nsd.url=/users/create-api,/users/create-admin-api,/users/test


spring.jpa.properties.hibernate.show_sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.highlight_sql=false
spring.jpa.properties.hibernate.generate_statistics=false
logging.level.org.hibernate.SQL=ERROR
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=ERROR
logging.level.org.hibernate.hql.internal.ast.QueryTranslatorImpl=ERROR

