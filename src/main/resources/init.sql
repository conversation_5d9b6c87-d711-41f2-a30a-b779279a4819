insert into ppo_bay (id, fire_present, ppo_bay_type)
VALUES (1, false, 'ASKU'),
       (2, false, 'NPPA'),
       (3, false, 'DEA'),
       (4, false, 'TO')/*,
              (5,false,'UNDEFINED')*/;

insert into ppo_unit (id, unit_name, bay_type, unit_type, unit_alias)
values (1, 'балон 1', 'ASKU', 'BALLOON', 'BALLOON_ASKU_1'),
       (2, 'балон 2', 'ASKU', 'BALLOON', 'BALLOON_ASKU_2'),
       (3, 'балон 1', 'NPPA', 'BALLOON', 'BALLOON_NPPA_1'),
       (4, 'балон 2', 'NPPA', 'BALLOON', 'BALLOON_NPPA_2'),
       (5, 'балон 1', 'DEA', 'BALLOON', 'BALLOON_DEA_1'),
       (6, 'балон 2', 'DEA', 'BALLOON', 'BALLOON_DEA_2'),
       (7, 'балон 1', 'TO', 'BALLOON', 'BALLOON_TO_1'),
       (8, 'балон 2', 'TO', 'BALLOON', 'BALLOON_TO_2'),
       (9, 'ОД1', 'ASKU', 'OPTICAL_SENSOR', 'OPTICAL_SENSOR_ASKU_1'),
       (10, 'ОД2', 'ASKU', 'OPTICAL_SENSOR', 'OPTICAL_SENSOR_ASKU_2'),
       (11, 'ОД3', 'ASKU', 'OPTICAL_SENSOR', 'OPTICAL_SENSOR_ASKU_3'),
       (12, 'ОД4', 'ASKU', 'OPTICAL_SENSOR', 'OPTICAL_SENSOR_ASKU_4'),
       (13, 'ОД1', 'NPPA', 'OPTICAL_SENSOR', 'OPTICAL_SENSOR_NPPA_1'),
       (14, 'ОД2', 'NPPA', 'OPTICAL_SENSOR', 'OPTICAL_SENSOR_NPPA_2'),
       (15, 'ОД3', 'NPPA', 'OPTICAL_SENSOR', 'OPTICAL_SENSOR_NPPA_3'),
       (16, 'ТД1', 'DEA', 'THERMAL_SENSOR', 'THERMAL_SENSOR_DEA_1'),
       (17, 'ТД2', 'DEA', 'THERMAL_SENSOR', 'THERMAL_SENSOR_DEA_2'),
       (18, 'ТД3', 'DEA', 'THERMAL_SENSOR', 'THERMAL_SENSOR_DEA_3'),
       (19, 'ТД1', 'TO', 'THERMAL_SENSOR', 'THERMAL_SENSOR_TO_1'),
       (20, 'ТД2', 'TO', 'THERMAL_SENSOR', 'THERMAL_SENSOR_TO_2'),
       (21, 'ТД3', 'TO', 'THERMAL_SENSOR', 'THERMAL_SENSOR_TO_3');

insert into ppo_bay_units_inppo_bay(ppo_bay_id, units_inppo_bay_id)
values (1, 1),
       (1, 2),
       (1, 9),
       (1, 10),
       (1, 11),
       (1, 12),
       (2, 3),
       (2, 4),
       (2, 13),
       (2, 14),
       (2, 15),
       (3, 5),
       (3, 6),
       (3, 16),
       (3, 17),
       (3, 18),
       (4, 7),
       (4, 8),
       (4, 19),
       (4, 20),
       (4, 21);

insert into ppo_commands(id, command, caption, adjacent_system)
values (1, 'TurnOffDEAEngine', 'Двигун відсіку ДЕА зупинений', 'PPO'),
       (2, 'permitLaunchDEA', 'Дозволити запуск другої групи відсіку ДЕА', 'PPO'),
       (3, 'permitLaunchTO', 'Дозволити запуск другої групи відсіку ТО', 'PPO'),
       (4, 'permitLaunchASKU', 'Дозволити запуск другої групи відсіку АСКУ', 'PPO'),
       (5, 'permitLaunchNPPA', 'Дозволити запуск другої групи відсіку НППА', 'PPO');


insert into sae_commands (id, command, caption, adjacent_system)
values (1, 'LockAdj', 'Заблокуванти АДЖ SAE', 'SAE'),
       (2, 'UnlockAdj', 'Розблокуванти АДЖ SAE', 'SAE'),
       (3, 'TurnOffFeeder1', 'Відключити фідер 1', 'SAE'),
       (4, 'TurnOffFeeder1NPPA', 'Відключити фідер 1 НППА СУ', 'SAE'),
       (5, 'TurnOffFeeder2', 'Відключити фідер 2', 'SAE'),
       (6, 'TurnOffFeeder3', 'Відключити фідер 3', 'SAE'),
       (7, 'TurnOffFeeder4', 'Відключити фідер 4', 'SAE'),
       (8, 'TurnOffFeeder5', 'Відключити фідер 5', 'SAE'),
       (9, 'TurnOffFeeder6', 'Відключити фідер 6', 'SAE'),
       (10, 'TurnOnFeeder1', 'Включити фідер 1', 'SAE'),
       (11, 'TurnOnFeeder1NPPA', 'Включити фідер 1 НППА СУ', 'SAE'),
       (12, 'TurnOnFeeder2', 'Включити фідер 2', 'SAE'),
       (13, 'TurnOnFeeder3', 'Включити фідер 3', 'SAE'),
       (14, 'TurnOnFeeder4', 'Включити фідер 4', 'SAE'),
       (15, 'TurnOnFeeder5', 'Включити фідер 5', 'SAE'),
       (16, 'TurnOnFeeder6', 'Включити фідер 6', 'SAE'),
       (17, 'TurnOffAdj', 'Глушення АДЖ САE', 'SAE'),
       (18, 'RequestSaeReadiness', 'Зробити запит готовності САЄ', 'SAE');


insert into suto_commands(id, command, caption, adjacent_system)
values (1, 'StartSPLEngine', 'Запуск двигуна СПУ', 'SUTO'),
       (2, 'TurnOnMainPump', 'Увімкнення основного насоса ГС (Н1)', 'SUTO'),
       (3, 'ControlModeFromNPPA', 'Режим работи від НППА', 'SUTO'),
       (4, 'ControlModeFromASKU', 'Режим работи від АСКУ', 'SUTO'),
       (5, 'TurnOffMainPump', 'Вимкнення основного насоса ГС (Н1)', 'SUTO'),
       (6, 'StopSPLEngine', 'Зупинка двигуна СПУ', 'SUTO'),
       (7, 'StopAll', 'Стоп', 'SUTO'),
       (8, 'RefreshStateRequest', 'Запит стану СУТО (холоста команда)', 'SUTO'),
       (9, 'HangingAndLevelingChassis', 'Вивішування та горизонтування шасі', 'SUTO'),
       (10, 'SwitchChassisMobileMode', 'Переведення шасі в похідне положення (ПП)', 'SUTO'),
       (11, 'ExtendGasReFlectorForReloading', 'Висунути газовідбивачі для перезаряджання', 'SUTO'),
       (12, 'RaiseArm', 'Підйом стріли', 'SUTO'),
       (13, 'LowerAllGasReflectors', 'Опускання 2-х газовідбивачів', 'SUTO'),
       (14, 'Lifting of 2 gas deflectors', 'Підйом 2-х газовідбивачів', 'SUTO'),
       (15, 'LowerLeftGasReflector', 'Опускання лівого газовідбивача', 'SUTO'),
       (16, 'LowerRightGasReflector', 'Опускання правого газовідбивача', 'SUTO'),
       (17, 'LiftLeftGasReflector', 'Підйом лівого газовідбивача', 'SUTO'),
       (18, 'LiftRightGasReflector', 'Підйом правого газовідбивача', 'SUTO'),
       (19, 'LowerArm', 'Опускання стріли', 'SUTO');

insert into suto_properties(id, name, caption, property_type)
values (1, 'chassisHorizontalAlignment', 'Проводиться горизонтування шасі', 'REGULAR'),
       (2, 'isChassisHorizontal', 'Шасі в горизонтальному положенні', 'REGULAR'),
       (3, 'isAlignmentImpossible', 'Горизонтування неможливе', 'REGULAR'),
       (4, 'isOutriggersMovingToPP', 'Виконується переведення аутригерів у ПП', 'REGULAR'),
       (5, 'isOutriggersInMobileState', 'Аутригери в похідному положенні (ПП)', 'REGULAR'),
       (6, 'isEmergencyHangingAndAlignment', 'Нештатна ситуація вивішування та горизонту-вання', 'MALFUNCTION'),
       (7, 'leftFrontOutriggerInitialPosition', 'Вихідне (похідне) положення аутригера лівої передньої опори',
        'REGULAR'),
       (8, 'rightFrontOutriggerInitialPosition', 'Вихідне (похідне) положення аутригера правої передньої опори',
        'REGULAR'),
       (9, 'rightRearOutriggerInitialPosition', 'Вихідне (похідне) положення аутригера правої задньої опори',
        'REGULAR'),
       (10, 'leftRearOutriggerInitialPosition', 'Вихідне (похідне) положення аутригера лівої задньої опори', 'REGULAR'),
       (11, 'armUnlocked', 'Стріла розстопорена', 'REGULAR'),
       (12, 'armRaising', 'Виконується підйом стріли', 'REGULAR'),
       (13, 'armRaised', 'Стріла піднята', 'REGULAR'),
       (14, 'leftGasSpringUnlocked', 'Лівий газовідбивач розстопорений', 'REGULAR'),
       (15, 'rightGasSpringUnlocked', 'Правий газовідбивач розстопорений', 'REGULAR'),
       (16, 'isLoweringLeftGasSpring', 'Опускання лівого газовідбивача', 'REGULAR'),
       (17, 'isLoweringRightGasSpring', 'Опускання правого газовідбивача', 'REGULAR'),
       (18, 'emergencySituationForArmAndGasSprings', 'Нештатна ситуація (стріли та газовідбивачів)', 'REGULAR'),
       (19, 'leftGasSpringLowered', 'Лівий газовідбивач опущений', 'REGULAR'),
       (20, 'rightGasSpringLowered', 'Правий газовідбивач опущений', 'REGULAR'),
       (21, 'isRaisingLeftGasSpring', 'Проводиться підйом лівого газовідбивача', 'REGULAR'),
       (22, 'isRaisingRightGasSpring', 'Проводиться підйом правого газовідбивача', 'REGULAR'),
       (23, 'leftGasSpringInMobileState', 'Лівий газовідбивач у похідному положенні (ІП)', 'REGULAR'),
       (24, 'rightGasSpringInMobileState', 'Правий газовідбивач у похідному положенні (ІП)', 'REGULAR'),
       (25, 'leftGasSpringLocked', 'Лівий газовідбивач застопорений', 'REGULAR'),
       (26, 'rightGasSpringLocked', 'Правий газовідбивач застопорений', 'REGULAR'),
       (27, 'isLoweringArm', 'Опускання стріли', 'REGULAR'),
       (28, 'armInMobileState', 'Стріла в похідному положенні (ІП)', 'REGULAR'),
       (29, 'boomLocked', 'Стріла застопорена', 'REGULAR'),
       (30, 'boomInEndPosition', 'Стріла в кінцевому положенні (КП)', 'REGULAR'),
       (31, 'boomEPMalfunction', 'Несправність ЕП стріли', 'MALFUNCTION'),
       (32, 'boomLockingEPMalfunction', 'Несправність ЕП стопоріння стріли', 'MALFUNCTION'),
       (33, 'leftGasSpringLockingEPMalfunction', 'Несправність ЕП стопоріння лівого газовідвідувача', 'MALFUNCTION'),
       (34, 'rightGasSpringLockingEPMalfunction', 'Несправність ЕП стопоріння правого газовідбивача', 'MALFUNCTION'),
       (35, 'malfunctionLeftGasReflectorEP', 'Несправність ЕП лівого газовідбивача', 'MALFUNCTION'),
       (36, 'malfunctionRightGasReflectorEP', 'Несправність ЕП правого газовідбивача', 'MALFUNCTION'),
       (37, 'armEmptyStrokeMaxPressure', 'Максимальний тиск у ЦІ стріли (штокова порожнина)', 'MALFUNCTION'),
       (38, 'armPistonStrokeMaxPressure', 'Максимальний тиск у ЦІ стріли (поршнева порожнина)', 'MALFUNCTION'),
       (39, 'SPLEngineStarting', 'Запуск двигуна СПУ', 'REGULAR'),
       (40, 'SPLEngineStarted', 'Двигун СПУ запущено', 'REGULAR'),
       (41, 'mainPumpConnectionToGS_N1', 'Підключення основного насосу ГС (Н1)', 'REGULAR'),
       (42, 'mainPumpConnectedToGS_N1', 'Основний насос підключений до ГС (Н1)', 'REGULAR'),
       (43, 'unloadingElectromagnetEnabled', 'Електромагніт розвантаження включений', 'REGULAR'),
       (44, 'SPLEnginestopping', 'Виконується зупинка двигуна СПУ', 'REGULAR'),
       (45, 'emergencySituation', 'Нештатна ситуація', 'MALFUNCTION'),
       (46, 'engineStartImpossible', 'Запуск двигуна неможливий', 'MALFUNCTION'),
       (47, 'engineStartImpossible2', 'Запуск двигуна неможливий', 'MALFUNCTION'),
       (48, 'engineStopImpossible', 'Зупинка двигуна неможлива', 'MALFUNCTION'),
       (49, 'fire', 'Пожежа', 'MALFUNCTION'),
       (50, 'pollutedFilterDZF1', 'Забруднений фільтр ДЗФ-1', 'MALFUNCTION'),
       (51, 'pollutedFilterDZF2', 'Забруднений фільтр ДЗФ-2', 'MALFUNCTION'),
       (52, 'airPressureNotNormal', 'Тиск у повітряній магістралі не в нормі', 'MALFUNCTION'),
       (53, 'leftFrontLockLeftTPKClosed', 'Передній лівий замок лівого ТПК закритий', 'REGULAR'),
       (54, 'leftFrontLockLeftTPKOpened', 'Передній лівий замок лівого ТПК відкритий', 'REGULAR'),
       (55, 'rightFrontLockLeftTPKClosed', 'Передній правий замок лівого ТПК закритий', 'REGULAR'),
       (56, 'rightFrontLockLeftTPKOpened', 'Передній правий замок лівого ТПК відкритий', 'REGULAR'),
       (57, 'rightRearLockLeftTPKClosed', 'Задній правий замок лівого ТПК закритий', 'REGULAR'),
       (58, 'rightRearLockLeftTPKOpened', 'Задній правий замок лівого ТПК відкритий', 'REGULAR'),
       (59, 'leftRearLockLeftTPKClosed', 'Задній лівий замок лівого ТПК закритий', 'REGULAR'),
       (60, 'leftRearLockLeftTPKOpened', 'Задній лівий замок лівого ТПК відкритий', 'REGULAR'),
       (61, 'leftFrontLockRightTPKClosed', 'Передній лівий замок правого ТПК закритий', 'REGULAR'),
       (62, 'leftFrontLockRightTPKOpened', 'Передній лівий замок правого ТПК відкритий', 'REGULAR'),
       (63, 'rightFrontLockRightTPKClosed', 'Передній правий замок правого ТПК закритий', 'REGULAR'),
       (64, 'rightFrontLockRightTPKOpened', 'Передній правий замок правого ТПК відкритий', 'REGULAR'),
       (65, 'rightRearLockRightTPKClosed', 'Задній правий замок правого ТПК закритий', 'REGULAR'),
       (66, 'rightRearLockRightTPKOpened', 'Задній правий замок правого ТПК відкритий', 'REGULAR'),
       (67, 'leftRearLockRightTPKClosed', 'Задній лівий замок правого ТПК закритий', 'REGULAR'),
       (68, 'leftRearLockRightTPKOpened', 'Задній лівий замок правого ТПК відкритий', 'REGULAR'),
       (69, 'SUTOStop', 'Стоп від СУТО', 'REGULAR');

insert into suto(id,
                 left_front_outrigger_emergency_code,
                 left_rear_outrigger_emergency_code,
                 right_front_outrigger_emergency_code,
                 right_rear_outrigger_emergency_code,
                 arm_lift_stroke_position,
                 leveling_cycles_count,
                 main_pump_rmp,
                 pitch,
                 pressure_in_impulse_section,
                 roll,
                 temperature_rr,
                 working_fluid_level,
                 overall_operating_time,
                 overall_arm_liftings_count)
values (1, 'NONE', 'NONE', 'NONE', 'NONE', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);

insert into suto_to_suto_properties(suto_id, properties_id)
values (1, 1),
       (1, 2),
       (1, 3),
       (1, 4),
       (1, 5),
       (1, 6),
       (1, 7),
       (1, 8),
       (1, 9),
       (1, 10),
       (1, 11),
       (1, 12),
       (1, 13),
       (1, 14),
       (1, 15),
       (1, 16),
       (1, 17),
       (1, 18),
       (1, 19),
       (1, 20),
       (1, 21),
       (1, 22),
       (1, 23),
       (1, 24),
       (1, 25),
       (1, 26),
       (1, 27),
       (1, 28),
       (1, 29),
       (1, 30),
       (1, 31),
       (1, 32),
       (1, 33),
       (1, 34),
       (1, 35),
       (1, 36),
       (1, 37),
       (1, 38),
       (1, 39),
       (1, 40),
       (1, 41),
       (1, 42),
       (1, 43),
       (1, 44),
       (1, 45),
       (1, 46),
       (1, 47),
       (1, 48),
       (1, 49),
       (1, 50),
       (1, 51),
       (1, 52),
       (1, 53),
       (1, 54),
       (1, 55),
       (1, 56),
       (1, 57),
       (1, 58),
       (1, 59),
       (1, 60),
       (1, 61),
       (1, 62),
       (1, 63),
       (1, 64),
       (1, 65),
       (1, 66),
       (1, 67),
       (1, 68),
       (1, 69);

insert into nppa_commands(id, command, caption, adjacent_system, used_in_combat_mode, used_in_test_mode, used_in_workflow)
values (33001,'NcokCombatMode','Режим роботи НЦОК бойовий','NCOK',false,false,true),
       (33002,'NcokTestMode','Режим роботи НЦОК перевірний','NCOK',false,false,true),
       (33014,'Otr1FromBg3Launch','Пуск ОТР1 із БГ3','NCOK',true,false,false),
       (33015,'Otr2FromBg3Launch','Пуск ОТР2 із БГ3','NCOK',true,false,false),
       (33016,'Otr1FromBg2aLaunch','Пуск ОТР1 із БГ2а','NCOK',true,false,false),
       (33017,'Otr2FromBg2aLaunch','Пуск ОТР2 із БГ2а','NCOK',true,false,false),
       (33018,'Otr1FromBg2bLaunch','Пуск ОТР1 із БГ2б','NCOK',true,false,false),
       (33019,'Otr2FromBg2bLaunch','Пуск ОТР2 із БГ2б','NCOK',true,false,false),
       (33020,'Otr1FromBg1Launch','Пуск ОТР1 із БГ1','NCOK',true,false,false),
       (33021,'Otr2FromBg1Launch','Пуск ОТР2 із БГ1','NCOK',true,false,false),
       (33022,'Bg2aToBg1SwitchWithOtr1SwOn','Перехід із БГ2а в БГ1 з увімкненням ОТР1','NCOK',true,false,false),
       (33023,'Bg2aToBg1SwitchWithOtr2SwOn','Перехід із БГ2а в БГ1 з увімкненням ОТР2','NCOK',true,false,false),
       (33024,'Bg3ToBg1SwitchWithOtr1SwOn','Перехід із БГ3 в БГ1 з увімкненням ОТР1','NCOK',true,false,false),
       (33025,'Bg3ToBg1SwitchWithOtr2SwOn','Перехід із БГ3 в БГ1 з увімкненням ОТР2','NCOK',true,false,false),
       (33026,'Bg1toBg3Toggle','Перехід із БГ1 у БГ3','NCOK',true,false,false),
       (33027,'Bg1toBg2aToggle','Перехід із БГ1 у БГ2а','NCOK',true,false,false),
       (33029,'PPiPWithBg2bContinuation','Продовження ППіП із БГ2б','NCOK',true,false,false),
       (33030,'Otr1InitialData','ВД на пуск для ОТР1','NCOK',true,false,false),
       (33031,'Otr2InitialData','ВД на пуск для ОТР2','NCOK',true,false,false),
       (33032,'Otr1LaunchCancell','Скасування пуску ОТР1','NCOK',true,false,false),
       (33033,'Otr2LaunchCancell','Скасування пуску ОТР2','NCOK',true,false,false),
       (33524,'Otr1AfterReloadingTestMode','Режим перевірення ОТР1 після перезарядження','NCOK',false,true,false),
       (33525,'Otr2AfterReloadingTestMode','Режим перевірення ОТР2 після перезарядження','NCOK',false,true,false),
       (33526,'Otr1TestMode','Режим перевірок ОТР1','NCOK',false,true,false),
       (33527,'Otr2TestMode','Режим перевірок ОТР2','NCOK',false,true,false),
       (33528,'NppaEbasu1TestMode','Режим перевірок НППА з ЕБАСУ1','NCOK',false,true,false),
       (33529,'NppaEbasu2TestMode','Режим перевірок НППА з ЕБАСУ2','NCOK',false,true,false),
       (33530,'NppaEbasu1Ebasu2TestMode','Режим перевірок НППА з ЕБАСУ1 і ЕБАСУ2','NCOK',false,true,false),
       (33531,'CancelNcokTestMode','Скасування режиму перевірок','NCOK',false,true,false),
       (34001,'BynCombatMode','Режим роботи БВН – основний','BYN',false,false,true),
       (34002,'BynTestMode','Режим роботи БВН – перевірний','BYN',false,false,true),
       (34004,'TurnOnNcok','Увімкнення живлення НЦОК','BYN',false,false,true),
       (34011,'TurnOffNcok','Вимкнення живлення НЦОК','BYN',false,false,true),
       (34021,'CancelBynTestMode','Скасування режиму перевірок','BYN',false,false,true);

INSERT INTO public.nppa_commanddao_available_at_readiness (nppa_commanddao_id, available_at_readiness) VALUES (33029, 'BG_2B');
INSERT INTO public.nppa_commanddao_available_at_readiness (nppa_commanddao_id, available_at_readiness) VALUES (33016, 'BG_2A');
INSERT INTO public.nppa_commanddao_available_at_readiness (nppa_commanddao_id, available_at_readiness) VALUES (33020, 'BG_1');
INSERT INTO public.nppa_commanddao_available_at_readiness (nppa_commanddao_id, available_at_readiness) VALUES (33018, 'BG_2B');
INSERT INTO public.nppa_commanddao_available_at_readiness (nppa_commanddao_id, available_at_readiness) VALUES (33014, 'BG_3');
INSERT INTO public.nppa_commanddao_available_at_readiness (nppa_commanddao_id, available_at_readiness) VALUES (33023, 'BG_2A');
INSERT INTO public.nppa_commanddao_available_at_readiness (nppa_commanddao_id, available_at_readiness) VALUES (33022, 'BG_2A');
INSERT INTO public.nppa_commanddao_available_at_readiness (nppa_commanddao_id, available_at_readiness) VALUES (33015, 'BG_3');
INSERT INTO public.nppa_commanddao_available_at_readiness (nppa_commanddao_id, available_at_readiness) VALUES (33017, 'BG_2A');
INSERT INTO public.nppa_commanddao_available_at_readiness (nppa_commanddao_id, available_at_readiness) VALUES (33019, 'BG_2B');
INSERT INTO public.nppa_commanddao_available_at_readiness (nppa_commanddao_id, available_at_readiness) VALUES (33021, 'BG_1');
INSERT INTO public.nppa_commanddao_available_at_readiness (nppa_commanddao_id, available_at_readiness) VALUES (33025, 'BG_3');
INSERT INTO public.nppa_commanddao_available_at_readiness (nppa_commanddao_id, available_at_readiness) VALUES (33024, 'BG_3');
INSERT INTO public.nppa_commanddao_available_at_readiness (nppa_commanddao_id, available_at_readiness) VALUES (33027, 'BG_1');
INSERT INTO public.nppa_commanddao_available_at_readiness (nppa_commanddao_id, available_at_readiness) VALUES (33026, 'BG_1');

