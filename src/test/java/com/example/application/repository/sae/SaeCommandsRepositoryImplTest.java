package com.example.application.repository.sae;

import com.deb.spl.control.data.sae.SaeCommandMapper;
import com.deb.spl.control.repository.sae.SaeCommandsRepository;
import com.deb.spl.control.repository.sae.SaeHistoryRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;

//@ExtendWith(SpringExtension.class)
//@DataJpaTest
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
//@EnableAutoConfiguration(exclude = LiquibaseAutoConfiguration.class)
class SaeCommandsRepositoryImplTest {
    @Autowired
    SaeHistoryRepository repository;

    @Autowired
    SaeCommandsRepository commandsRepository;

    @Autowired
    SaeCommandMapper mapper;

//    @BeforeEach
//    void setUp() {
//        int size = 10000;
//        if (repository.findAll().size() >= size) {
//            return;
//        }
//        List<SaeCommand> commands = new ArrayList<>();
//        for (int i = 1; i < size - repository.findAll().size(); i++) {
//            commands.add(createCommandRandomCommand());
//        }
//        repository.saveAllAndFlush(commands);
//    }
//
//    private SaeCommand createCommandRandomCommand() {
//        Random random = new Random();
//        long id = random.nextLong(18L);
//        id = id > 0 ? id : 1;
//        SaeCommand saeCommand = mapper.daoToSaeCommand(commandsRepository.findById(id));
//
//        SaeCommand generatedCommand = SaeCommand.builder()
//                .command(saeCommand.getCommand())
//                .caption(saeCommand.getCaption())
//                .adjacentSystem(saeCommand.getAdjacentSystem())
//                .generationTime(LocalDateTime.now())
//                .build();
//
//        if (saeCommand.getId() % 2 == 0) {
//            generatedCommand.setExecutionTime(LocalDateTime.now().plusSeconds(Math.toIntExact(saeCommand.getId())));
//        }
//
//        return generatedCommand;
//    }

    @Test
    void getAllCommands() {
    }
}