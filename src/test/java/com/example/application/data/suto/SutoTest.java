package com.example.application.data.suto;

import com.deb.spl.control.data.suto.Suto;
import com.example.application.data.mapper.SutoTestUtils;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class SutoTest {

    private final Suto sample = SutoTestUtils.SUTO;

    @Test
    void copy() {
        Suto converted = Suto.copy(sample);
        assertNotNull(converted);
        assertTrue(sample!=converted);
        assertEquals(sample,converted);
    }
}