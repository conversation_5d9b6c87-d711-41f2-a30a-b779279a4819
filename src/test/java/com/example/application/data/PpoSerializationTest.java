package com.example.application.data;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.example.application.data.mapper.PpoTestUtils;
import com.deb.spl.control.data.ppo.Ppo;
import com.deb.spl.control.data.ppo.PpoBay;
import com.deb.spl.control.data.ppo.PpoBayType;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;


public class PpoSerializationTest {


    private Map<PpoBayType, PpoBay> bays;
    private Ppo originalPpo;

    @BeforeEach
    void setUp() {
        bays = new HashMap<>();

        bays.put(PpoTestUtils.PPO_BAY_WITH_ONE_UNIT.getPpoBayType(), PpoTestUtils.PPO_BAY_WITH_ONE_UNIT);

        originalPpo = Ppo.builder()
                .bays(bays)
                .status(AdjacentSystemStatus.ERROR)
                .build();
    }

    @Test
    void whenModifyingOriginalObject_thenJacksonCopyShouldNotChange() throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
//        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        Ppo copy = objectMapper.readValue(objectMapper.writeValueAsString(originalPpo), Ppo.class);

        copy.getBays().get(PpoBayType.ASKU).setFirePresent(true);

        assertThat(copy.getBays().get(PpoBayType.ASKU).isFirePresent() !=
                originalPpo.getBays().get(PpoBayType.ASKU).isFirePresent());
        assertThat(copy.getBays().get(PpoBayType.ASKU)!=originalPpo.getBays().get(PpoBayType.ASKU));
    }

    @Test
    void copyEqualsToOriginal() throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
//        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        Ppo copy = objectMapper.readValue(objectMapper.writeValueAsString(originalPpo), Ppo.class);

        copy.getBays().get(PpoBayType.ASKU).setFirePresent(true);
        assertThat(copy.equals(originalPpo));
    }
}
