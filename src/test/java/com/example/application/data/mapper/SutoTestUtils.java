package com.example.application.data.mapper;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.PropertyType;
import com.deb.spl.control.data.sae.CommandState;
import com.deb.spl.control.data.suto.*;

import java.util.List;
import java.util.Map;


public class SutoTestUtils {
    public static final SutoStats SUTO_STATS = SutoStats.builder()
            .roll(-45)
            .pitch(45)
            .armLiftStrokePosition(556)
            .levelingCyclesCount(1000)
            .pressureInImpulseSection(15)
            .temperatureRR(25)
            .workingFluidLevel(12)
            .mainPumpRPM(15678)
            .overallOperatingTime(165)
            .overallArmLiftingsCount(11)
            .build();

    public static final SutoProperty chassisHorizontalAlignment = new SutoProperty(
            1L,
            "chassisHorizontalAlignment",
            "Проводиться горизонтування шасі",
            CommandState.ON,
            PropertyType.REGULAR);
    public static final SutoProperty isChassisHorizontal = new SutoProperty(
            2L,
            "isChassisHorizontal",
            "Шасі в горизонтальному положенні",
            CommandState.ON,
            PropertyType.REGULAR);
    public static final SutoProperty SUTOStop = new SutoProperty(
            49L,
            "fire",
            "Пожежа",
            CommandState.OFF,
            PropertyType.MALFUNCTION);


    public static final Map<String, SutoProperty> PROPERTIES =
            Map.of(chassisHorizontalAlignment.getName(), chassisHorizontalAlignment,
                    isChassisHorizontal.getName(), isChassisHorizontal,
                    SUTOStop.getName(), SUTOStop);

    public static final SutoSettings SUTO_SETTINGS =
            new SutoSettings(ChassisState.PARKING);
    public static final Suto SUTO = Suto.builder()
            .id(1L)
            .stats(SUTO_STATS)
            .leftFrontOutriggerEmergencyCode(OutriggerEmergencyCode.OUTRIGGER_DRIVE_MALFUNCTION)
            .rightFrontOutriggerEmergencyCode(OutriggerEmergencyCode.OUTRIGGER_DIDNT_REACH_GROUND_OR_BROKEN)
            .leftRearOutriggerEmergencyCode(OutriggerEmergencyCode.OUTRIGGER_STICKED_OUT_AND_REACH_GROUND_2)
            .rightRearOutriggerEmergencyCode(OutriggerEmergencyCode.SENSORS_MALFUNCTION)
            .properties(PROPERTIES)
            .sutoSettings(SUTO_SETTINGS)
            .build();

    //DTO
    public static final SutoStatsDto SUTO_STATS_DTO = new SutoStatsDto(
            -45,
            45,
            556,
            1000,
            15,
            25,
            12,
            15678,
            165,
            11);

    public static final SutoPropertyDto chassisHorizontalAlignment_dto = new SutoPropertyDto(
            1L,
            "chassisHorizontalAlignment",
            CommandState.ON,
            PropertyType.REGULAR);
    public static final SutoPropertyDto isChassisHorizontal_dto = new SutoPropertyDto(
            2L,
            "isChassisHorizontal",
            CommandState.ON,
            PropertyType.REGULAR);
    public static final SutoPropertyDto SUTO_STOP_DTO = new SutoPropertyDto(
            49L,
            "fire",
            CommandState.ON,
            PropertyType.MALFUNCTION);


    public static final List<SutoPropertyDto> SUTO_DTO_PROPERTIES = List.of(
             chassisHorizontalAlignment_dto,
            isChassisHorizontal_dto,
            SUTO_STOP_DTO);

    public static final SutoDto SUTO_DTO = new SutoDto(
            1L,
            AdjacentSystemStatus.OK,
            SUTO_STATS_DTO,
            1,
            3,
            7,
            10,
            SUTO_DTO_PROPERTIES
    );
}
