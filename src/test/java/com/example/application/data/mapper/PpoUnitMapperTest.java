package com.example.application.data.mapper;

import com.deb.spl.control.data.ppo.PpoUnit;
import com.deb.spl.control.data.ppo.PpoUnitDTO;
import com.deb.spl.control.data.ppo.PpoUnitMapper;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

class PpoUnitMapperTest {
    private final PpoUnitMapper mapper = Mappers.getMapper(PpoUnitMapper.class);

    private final PpoUnit samplePpo = PpoTestUtils.PPO_UNIT_ASKU_BALLOON1;

    private final PpoUnitDTO sampleDto = PpoTestUtils.PPO_UNIT_DTO_ASKU_BALLOON1;

    @Test
    void toDto() {
        PpoUnitDTO newDto = mapper.toDto(samplePpo);

        assertThat(newDto).isNotNull();
        assertEquals(sampleDto.unitType(), newDto.unitType());
        assertEquals(sampleDto.unitName(), newDto.unitName());
        assertEquals(sampleDto.bayType(), newDto.bayType());
        assertEquals(sampleDto.unitStatus(), newDto.unitStatus());
    }

    @Test
    void toPpoUnit() {
        PpoUnit unit = mapper.toPpoUnit(sampleDto);

        assertThat(unit).isNotNull();
        assertEquals(unit.getId(),sampleDto.id());
        assertEquals(samplePpo.getUnitType(), unit.getUnitType());
        assertEquals(samplePpo.getUnitName(), unit.getUnitName());
        assertEquals(samplePpo.getBayType(), unit.getBayType());
        assertEquals(samplePpo.getUnitStatus(), unit.getUnitStatus());

    }
}