package com.example.application.data.mapper;

import com.deb.spl.control.data.suto.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {SutoMapperImpl.class,
//        SutoPropertyToDtoMapperImpl.class,
        SutoPropertyMapperImpl.class,
        SutoStatsToDtoMapperImpl.class,
})
class SutoToDtoMapperTest {

    @Autowired
    private SutoMapper sutoToDtoMapper;

    private Suto sampleSuto = SutoTestUtils.SUTO;
    private SutoDto sampleDto = SutoTestUtils.SUTO_DTO;

    @Test
    void toSuto() {
        Suto suto = sutoToDtoMapper.map(sampleDto);
        assertThat(suto).isNotNull();
        assertEquals(sampleSuto.getStats(), suto.getStats());

        assertEquals(sampleDto.properties().size(), suto.getProperties().size());
        int elementsWithCorrectIds = suto.getProperties().values().stream()
                .filter(p -> sampleSuto.getProperties().containsKey(p.getName())).toList().size();
        assertEquals(3, elementsWithCorrectIds);
        assertEquals(sampleSuto.getProperties().values().stream().findFirst().get().propertyType,
                suto.getProperties().values().stream().findFirst().get().propertyType);
    }

    @Test
    void toDto() {
        SutoDto dto = sutoToDtoMapper.toDto(sampleSuto);
        assertThat(dto).isNotNull();
        assertEquals(sampleDto.stats(), dto.stats());

        assertEquals(sampleDto.properties().size(), dto.properties().size());
        int elementsWithCorrectIds = dto.properties().stream()
                .filter(p -> /*sampleDto.properties().contains(p)*/{
                    return sampleDto.properties().stream()
                            .anyMatch(s -> s.id().equals(p.id()) && s.name().equals(p.name()) && s.propertyType().equals(p.propertyType()));
                }).toList().size();
        assertEquals(3, elementsWithCorrectIds);

        assertEquals(sampleDto.properties().stream().findFirst().get().propertyType(),
                dto.properties().stream().findFirst().get().propertyType());
    }
}