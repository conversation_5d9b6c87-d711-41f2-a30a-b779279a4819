package com.example.application.data.mapper;

import com.deb.spl.control.data.ppo.*;

import java.util.List;
import java.util.Map;

public class PpoTestUtils {

    final static PpoUnit PPO_UNIT_ASKU_BALLOON1 = PpoUnit.builder()
            .id(1L)
            .unitType(PpoUnitType.BALLOON)
            .unitName("балон 1")
            .unitAlias("BALLOON_ASKU_1")
            .bayType(PpoBayType.ASKU)
            .unitStatus(PpoUnitStatus.ERROR)
            .build();

    final static PpoUnit PPO_UNIT_ASKU_BALLOON2 = PpoUnit.builder()
            .id(2L)
            .unitType(PpoUnitType.BALLOON)
            .unitName("балон 2")
            .unitAlias("BALLOON_ASKU_2")
            .bayType(PpoBayType.ASKU)
            .unitStatus(PpoUnitStatus.ON)
            .build();

    final static PpoUnit PPO_UNIT_ASKU_OD1 = PpoUnit.builder()
            .id(9L)
            .unitType(PpoUnitType.OPTICAL_SENSOR)
            .unitName("ОД1")
            .unitAlias("OPTICAL_SENSOR_ASKU_1")
            .bayType(PpoBayType.ASKU)
            .unitStatus(PpoUnitStatus.ERROR)
            .build();


    final static PpoUnitDTO PPO_UNIT_DTO_ASKU_BALLOON1 = new PpoUnitDTO(
            1L
            ,PpoUnitType.BALLOON,
            "балон 1",
            "BALLOON_ASKU_1",
            PpoBayType.ASKU,
            PpoUnitStatus.ERROR
    );
    final static PpoUnitDTO PPO_UNIT_DTO_ASKU_BALLOON2 = new PpoUnitDTO(
            2L,
            PpoUnitType.BALLOON,
            "балон 2",
            "BALLOON_ASKU_2",
            PpoBayType.ASKU,
            PpoUnitStatus.ON
    );
    final static PpoUnitDTO PPO_UNIT_DTO_ASKU_OD1 = new PpoUnitDTO(
            3L,
            PpoUnitType.BALLOON,
            "ОД1",
            "OPTICAL_SENSOR_ASKU_1",
            PpoBayType.ASKU,
            PpoUnitStatus.ERROR
    );

    final static Map<String, PpoUnit> ASKU_PPO_UNITS = Map.of(
            PPO_UNIT_ASKU_BALLOON1.getUnitAlias(), PPO_UNIT_ASKU_BALLOON1,
            PPO_UNIT_ASKU_BALLOON2.getUnitAlias(), PPO_UNIT_ASKU_BALLOON2,
            PPO_UNIT_ASKU_OD1.getUnitAlias(), PPO_UNIT_ASKU_OD1);
    final static List<PpoUnitDTO> ASKU_DTO_UNITS = List.of(
            PPO_UNIT_DTO_ASKU_BALLOON1,
            PPO_UNIT_DTO_ASKU_BALLOON2,
            PPO_UNIT_DTO_ASKU_OD1);

    final static PpoBay PPO_BAY = PpoBay.builder()
            .id(1L)
            .ppoBayType(PpoBayType.ASKU)
            .unitsInPPoBay(ASKU_PPO_UNITS)
            .firePresent(true)
            .build();

    final static PpoBayDTO PPO_BAY_DTO = new PpoBayDTO(
            4L,
            PpoBayType.ASKU,
            true,
            ASKU_DTO_UNITS
    );
    public final static PpoBay PPO_BAY_WITH_ONE_UNIT = PpoBay.builder()
            .id(1L)
            .ppoBayType(PpoBayType.ASKU)
            .unitsInPPoBay(Map.of(PPO_UNIT_ASKU_BALLOON1.getUnitAlias(), PPO_UNIT_ASKU_BALLOON1))
            .firePresent(true)
            .build();
    final static PpoBayDTO PPO_BAY_DTO_WITH_ONE_UNIT = new PpoBayDTO(
            5L,
            PpoBayType.ASKU,
            true,
            List.of(PPO_UNIT_DTO_ASKU_BALLOON1)
    );

}
