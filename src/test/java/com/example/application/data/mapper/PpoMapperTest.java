package com.example.application.data.mapper;

import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.ppo.*;
import com.example.application.data.ppo.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {PpoBayMapperImpl.class,
        PpoMapperImpl.class,
        PpoUnitMapperImpl.class
})
class PpoMapperTest {
    @Autowired
    private PpoBayMapper ppoBayMapper;
    @Autowired
    private PpoUnitMapper ppoUnitMapper;
    @Autowired
    private PpoMapper mapper;

    private Map<PpoBayType, PpoBay> bays;
    private Ppo smaplePpo;

    private List<PpoBayDTO> baysDto;
    private PpoDTO sampleDto;

    @BeforeEach
    void setUp() {
        bays = new HashMap<>();

        bays.put(PpoTestUtils.PPO_BAY_WITH_ONE_UNIT.getPpoBayType(), PpoTestUtils.PPO_BAY_WITH_ONE_UNIT);

        smaplePpo = Ppo.builder()
                .bays(bays)
                .status(AdjacentSystemStatus.ERROR)
                .build();

        baysDto = List.of(PpoTestUtils.PPO_BAY_DTO_WITH_ONE_UNIT);

        sampleDto = new PpoDTO(AdjacentSystemStatus.ERROR, baysDto);
    }

    @Test
    void toDto() {
        PpoDTO dto = mapper.toDto(smaplePpo);

        assertThat(dto).isNotNull();
        assertEquals(sampleDto.status(), dto.status());
        assertEquals(sampleDto.ppoBaysList().get(0).unitsInPPoBay(), dto.ppoBaysList().get(0).unitsInPPoBay());
        assertEquals(sampleDto.ppoBaysList().get(0).ppoBayType(), dto.ppoBaysList().get(0).ppoBayType());
        assertEquals(sampleDto.ppoBaysList().get(0).firePresent(), dto.ppoBaysList().get(0).firePresent());
    }

    @Test
    void toPpo() {
        Ppo ppo = mapper.map(sampleDto);

        assertThat(ppo).isNotNull();
        assertThat(ppo.getBays().get(PpoBayType.ASKU)).isNotNull();

        assertEquals(smaplePpo.getStatus(), ppo.getStatus());
        assertEquals(smaplePpo.getBays().get(PpoBayType.ASKU).getUnitsInPPoBay(),
                ppo.getBays().get(PpoBayType.ASKU).getUnitsInPPoBay());
        assertEquals(smaplePpo.getBays().get(PpoBayType.ASKU).getUnitsInPPoBay().size(),
                ppo.getBays().get(PpoBayType.ASKU).getUnitsInPPoBay().size());
        assertEquals(smaplePpo.getBays().get(PpoBayType.ASKU).isFirePresent(),
                ppo.getBays().get(PpoBayType.ASKU).isFirePresent());
    }
}