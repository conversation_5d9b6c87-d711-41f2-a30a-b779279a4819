package com.example.application.data.mapper;

import com.deb.spl.control.data.ppo.PpoBay;
import com.deb.spl.control.data.ppo.PpoBayDTO;
import com.deb.spl.control.data.ppo.PpoBayMapper;
import com.deb.spl.control.data.ppo.PpoUnitMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mapstruct.factory.Mappers;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
class PpoBayMapperTest {

    @Spy
    private PpoUnitMapper ppoUnitMapper = Mappers.getMapper(PpoUnitMapper.class);
    @Spy
    @InjectMocks
    private PpoBayMapper mapper=Mappers.getMapper(PpoBayMapper.class);


    final PpoBay sampleBay = PpoTestUtils.PPO_BAY;
    final PpoBayDTO sampleDto = PpoTestUtils.PPO_BAY_DTO;

    @Test
    void toDto() {
        PpoBayDTO dto = mapper.toDto(sampleBay);

        assertThat(dto).isNotNull();
        assertEquals(sampleDto.ppoBayType(),dto.ppoBayType());
        assertEquals(sampleDto.firePresent(),dto.firePresent());

        assertEquals(sampleDto.unitsInPPoBay().size(),dto.unitsInPPoBay().size());
    }

    @Test
    void toPpoBay() {
        PpoBay bay = mapper.toPpoBay(sampleDto);

        assertThat(bay).isNotNull();
        assertEquals(sampleBay.getPpoBayType(),bay.getPpoBayType());
        assertEquals(sampleBay.isFirePresent(),bay.isFirePresent());

        assertEquals(sampleBay.getUnitsInPPoBay().size(),bay.getUnitsInPPoBay().size());
    }
}