package com.example.application.data.ppo;

import com.deb.spl.control.data.ppo.*;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

class PpoBayListConverterTest {


    Map<String, PpoUnit> askuUnits = Map.of(
            "BALLOON_ASKU_1", new PpoUnit(1L,
                    PpoUnitType.BALLOON,
                    "балон 1",
                    "BALLOON_ASKU_1",
                    PpoBayType.ASKU,
                    PpoUnitStatus.ON),
            "BALLOON_ASKU_2", new PpoUnit(2L,
                    PpoUnitType.BALLOON,
                    "балон 1",
                    "BALLOON_ASKU_1",
                    PpoBayType.ASKU,
                    PpoUnitStatus.OFF)
    );
    PpoBay askuBay = new PpoBay(1L,
            PpoBayType.ASKU,
            false,
            askuUnits);

    Map<String, PpoUnit> deaUnits = Map.of("BALLOON_DEA_1", new PpoUnit(5L,
                    PpoUnitType.BALLOON,
                    "балон 1",
                    "BALLOON_DEA_1",
                    PpoBayType.DEA,
                    PpoUnitStatus.ERROR),
            "BALLOON_DEA_2", new PpoUnit(6L,
                    PpoUnitType.BALLOON,
                    "балон 2",
                    "BALLOON_DEA_2",
                    PpoBayType.DEA,
                    PpoUnitStatus.UNDEFINED));
    PpoBay deaBay = new PpoBay(3L,
            PpoBayType.DEA,
            true,
            deaUnits);


    @Test
    void convertToDatabaseColumn() {
        PpoBayListConverter converter = new PpoBayListConverter();
        String ppo = converter.convertToDatabaseColumn(List.of(askuBay,deaBay));
        assertThat(ppo).isNotBlank();
    }

    @Test
    void convertToEntityAttribute() {
        PpoBayListConverter converter = new PpoBayListConverter();
        String ppo = converter.convertToDatabaseColumn(List.of(askuBay,deaBay));

        List<PpoBay> bays = converter.convertToEntityAttribute(ppo);
        assertThat(bays).size().isEqualTo(2);


    }
}