package com.example.application.service;

import com.deb.spl.control.data.ccv.AppSettingsResource;
import com.deb.spl.control.data.ccv.VehicleResource;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Duration;
import java.util.List;
import java.util.Optional;

import static org.junit.Assert.assertTrue;

//@SpringBootTest
@ExtendWith(SpringExtension.class)
//@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
//@EnableAutoConfiguration(exclude = LiquibaseAutoConfiguration.class)
class CcvCommunicationServiceTest_2 {

    //    @Value("${ccv.token}:spl12345")
    private String ccvToken = "spl12345";
    //    @Value("${ccv.host.url:http://************}")
    private String baseUrl = "http://************";

    //    @Value("${ccv.app-settings.url:http://************/api/app_settings}")
    private String appSettingsUrl = "http://************/api/app_settings";

    //    @Value("${ccv.vehicles.url:http://************/api/vehicles}")
    private String vehiclesUrl = "http://************/api/vehicles";

    @BeforeEach
    void setUp() {

    }

    @Test
    void getAppSettingsResource() {
        WebClient client = WebClient.builder()
                .baseUrl(appSettingsUrl)
                .build();

        ResponseEntity response = client.get()
                .header("Token", ccvToken)
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .toEntity(AppSettingsResponseData.class)
                .block(Duration.ofMillis(2000));


        assert response != null;
        assertTrue(response.getStatusCode().is2xxSuccessful());

        Optional<AppSettingsResource> appSettings = decodeAppSettings(response);
        Assertions.assertTrue(appSettings.isPresent());
    }

    private record AppSettingsResponseData(AppSettingsResource data) {

    }

    private Optional<AppSettingsResource> decodeAppSettings(ResponseEntity response) {

        if (!response.getStatusCode().is2xxSuccessful() || response.getBody() == null) {
            return Optional.empty();
        }

        return Optional.ofNullable(((AppSettingsResponseData) response.getBody()).data());
    }


    @Test
    void getUnitResourceData() {
        WebClient client = WebClient.builder()
                .baseUrl(vehiclesUrl)
                .defaultHeader("Token", ccvToken)
                .build();

        ResponseEntity response = client.get()
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .toEntity(VehicleResourceData.class)
                .block(Duration.ofMillis(2000));

        Assertions.assertTrue(response.getStatusCode().is2xxSuccessful());

        Optional<List<VehicleResource>> vehicleResource = decodeUnit(response);

        assertTrue(vehicleResource.isPresent());
    }

    private record VehicleResourceData(List<VehicleResource> data) {

    }

    private Optional<List<VehicleResource>> decodeUnit(ResponseEntity response) {
        if (!response.getStatusCode().is2xxSuccessful() || response.getBody() == null) {
            return Optional.empty();
        }

        return Optional.ofNullable(((VehicleResourceData) response.getBody()).data());
    }
}