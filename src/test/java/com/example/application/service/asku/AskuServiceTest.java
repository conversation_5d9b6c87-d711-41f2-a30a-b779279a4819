package com.example.application.service.asku;

import com.deb.spl.control.data.asku.LaunchResult;
import com.deb.spl.control.service.asku.AskuService;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@SpringBootTest
class AskuServiceTest {
    //    @Mock
//    private EntityManager em;
//
//    @Mock
//    private TLService tlService;
//
//    @Mock
//    private AskuRepository askuRepository;
//    @Mock
//    TpcRepository tpcRepository;
//    @Mock
//    RocketRepository rocketRepository;
//    @Mock
//    RocketFormDataRepository rocketFormDataRepository;
//    @Mock
//    LaunchInitialDataRepository initialDataRepository;
//    @Mock
//    NavigationRestRequestRepository restRepository;
//
//    // ... Mock other dependencies
//
//    @Mock
//    private CcvCommunicationService ccvCommunicationService;
//
//    @Mock
//    private BinsService binsService;
//
//    @Mock
//    private CommandsUtils commandsUtils;
//
//    @InjectMocks
//    private AskuService askuService;
    @Autowired
    AskuService askuService;
    private final String defaultPlantMissile = "001";
    private final LaunchResult resultLaunch = LaunchResult.LAUNCH;
    private final LaunchResult resultFailure = LaunchResult.FAILURE;

//    @Test
//    void postRocketFormData() {
//        Boolean response = askuService.updateRocketInfoInRemote(defaultPlantMissile, resultLaunch);
//        Assert.assertTrue(response);
//    }

//    @Test
//    void postWrongRocketFormData() {
//        Boolean response = askuService.updateRocketInfoInRemote(defaultPlantMissile, resultLaunch);
//        Assert.assertTrue(response);
//
//    }

}