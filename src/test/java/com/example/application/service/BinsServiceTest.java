package com.example.application.service;

import com.deb.spl.control.data.BinsDto;
import com.deb.spl.control.data.UtcDateTime;
import com.deb.spl.control.service.BinsService;
import net.sf.marineapi.nmea.util.Position;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class BinsServiceTest {

    @Autowired
    BinsService binsService;

    @Test
    void getDateTime() {
        Optional<UtcDateTime> result= binsService.getDateTime();
        assertNotNull(result.get());
    }

    @Test
    void getBinsDto() {
        Optional<BinsDto> result= binsService.getBinsDto();
        assertNotNull(result.get());
    }

    @Test
    void getBinsPosition() {
        Optional<com.deb.spl.control.data.bins.Position> result= binsService.getBinsPosition(false);
        assertNotNull(result.get());
    }

    @Test
    void sendStartCommand() {
        ResponseEntity response= binsService.sendStart();
        assertNotNull(response );
        assertEquals(HttpStatus.OK,response.getStatusCode());
    }

    @Test
    void sendReset() {
        ResponseEntity response= binsService.sendReset();
        assertNotNull(response );
        assertEquals(HttpStatus.OK,response.getStatusCode());
    }

    @Test
    void sendCalibrate() {
        ResponseEntity response= binsService.sendCalibrate();
        assertNotNull(response );
        assertEquals(HttpStatus.OK,response.getStatusCode());

    }

    @Test
    void sendCoordinatesManually() {
        final Position position = new Position(46.3221,-30.6033,0.2);
        ResponseEntity response= binsService.sendCoordinatesManually(position);
        assertNotNull(response );
        assertEquals(HttpStatus.OK,response.getStatusCode());
    }

    @Test
    void sendSetupMessages() {
        final String validMessage ="GPGGA";
        final String invalidMessage ="GPGGAA";

        ResponseEntity response= binsService.sendSetupMessages(validMessage,true);
        assertEquals(HttpStatus.OK,response.getStatusCode());

        ResponseEntity invalidMessageResponse= binsService.sendSetupMessages(invalidMessage,true);
        assertEquals(HttpStatus.BAD_REQUEST,invalidMessageResponse.getStatusCode());

    }
}