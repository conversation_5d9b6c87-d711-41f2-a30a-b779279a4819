package com.example.application.service;

import com.deb.spl.control.data.ccv.AppSettingsResource;
import com.deb.spl.control.data.ccv.VehicleResource;
import com.deb.spl.control.repository.bins.NavigationRestRequestRepository;
import com.deb.spl.control.service.CcvCommunicationService;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.utils.CommandsUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.*;

@ExtendWith(MockitoExtension.class)
//@TestPropertySource(properties = {"ccv.token=12345",
//        "ccv.rocket-status-endpoint.url=http://3.124.113.75/api/asku/status/",
//        "ccv.app-settings.url=http://3.124.113.75/api/app_settings",
//        "ccv.vehicles.url=http://3.124.113.75/api/vehicles"})
class CcvCommunicationServiceTest {

    @Mock
    NavigationRestRequestRepository restRequestRepository;

    @Mock
    AskuService askuService;
    @Mock
    CommandsUtils commandsUtils;

    //    @InjectMocks
    CcvCommunicationService ccvCommunicationService;
    //    private String ccvToken = "spl12345";
//    private String ccvRocketStatusUr = "http://3.124.113.75/api/asku/status/";
//    private String ccvAppSettingsUrl = "http://3.124.113.75/api/app_settings";
//    private String ccvVehiclesUrl = "http://3.124.113.75/api/vehicles";
//    private String tokenRequestParameterName = "Token";
    private String ccvToken = "12345";
    private String ccvRocketStatusUr = "http://192.168.201.111:3002/api/asku/status/";
    private String ccvAppSettingsUrl = "http://192.168.201.111:3002/api/app_settings";
    private String ccvVehiclesUrl = "http://192.168.201.111:3002/api/vehicles";

    private String tokenRequestParameterName = "camunda-token";
    private String ccvTypeJsonPropertyValue = "CCV";

    @BeforeEach
    void setUp() {
        ccvCommunicationService = new CcvCommunicationService(askuService,
                restRequestRepository,
                commandsUtils,
                tokenRequestParameterName,
                ccvToken,
                ccvRocketStatusUr,
                ccvAppSettingsUrl,
                ccvVehiclesUrl,
                ccvTypeJsonPropertyValue);
    }

    @Test
    void loadAppSettingsResourceTest() throws ExecutionException, InterruptedException, TimeoutException {
        CompletableFuture<Optional<AppSettingsResource>> future = ccvCommunicationService.loadAppSettingsResource(2000);

        Optional<AppSettingsResource> loaded = future.get();

        Assertions.assertTrue(loaded.isPresent());
    }

    @Test
    void loadVehiclesResourcesTest() throws ExecutionException, InterruptedException, TimeoutException {
        Future<List<VehicleResource>> future = ccvCommunicationService.loadVehiclesResources(true, 20000);
        List<VehicleResource> resources = future.get(300000, TimeUnit.MILLISECONDS);
        Assertions.assertFalse(resources.isEmpty());
    }

    @Test
    void checkConnectionStatusTest() throws ExecutionException, InterruptedException, TimeoutException {
        CompletableFuture<Optional<VehicleResource>> masterCcvFuture = ccvCommunicationService.loadAppSettingsResource(2000)
                .thenApply((dependantAppSettings) -> {
                    if (dependantAppSettings.isEmpty() || dependantAppSettings.get().getVehicleId() == null) {
                        return Optional.empty();
                    }
                    List<VehicleResource> vehiclesDictionary;
                    try {
                        vehiclesDictionary = ccvCommunicationService.loadVehiclesResources(true, 2000).get(60000, TimeUnit.SECONDS);
                        if (vehiclesDictionary == null || vehiclesDictionary.isEmpty()) {
                            return Optional.empty();
                        }

                        Optional<VehicleResource> dependantVehicle = vehiclesDictionary.stream()
                                .filter(vehicle -> vehicle.getEntityId().equals(dependantAppSettings.get().getVehicleId()))
                                .findFirst();
                        if (dependantVehicle.isEmpty()) {
                            return Optional.empty();
                        }
//"parent_id": "542393cb-ca36-4b8d-a627-d89b75a25177",
                        UUID parentId = dependantVehicle.get().getUnit().getParentId();
                        return vehiclesDictionary.stream()
                                .filter(vehicle -> vehicle.getUnitId().equals(parentId))
                                .findFirst();
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                });
        Optional<VehicleResource> masterCcv = masterCcvFuture.get(60000, TimeUnit.SECONDS);
        Assertions.assertTrue(masterCcv.isPresent());
    }

}