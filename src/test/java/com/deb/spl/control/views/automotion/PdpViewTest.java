package com.deb.spl.control.views.automotion;

import com.deb.spl.control.data.asku.AskuDto;
import com.deb.spl.control.data.asku.AskuMapper;
import com.deb.spl.control.data.bins.Position;
import com.deb.spl.control.utils.AskuUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Duration;
import java.util.UUID;

import static net.sf.marineapi.nmea.util.Datum.WGS84;
import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest(webEnvironment= SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("dev")
class PdpViewTest {
    @Value("${plc.request.token}")
    private String plcRequestToken;
    @Value("${plc.token.request-parameter-name}")
    private String plcTokenName;
    @Value("${server.port}:8079")
    private int serverPort;
    @Value("${host.url}:127.0.0.1")
    private String hostUrl;
    String rootUrl = "http://" + hostUrl + ":" + serverPort + "/api/v1/asku/";
    WebClient nppaClient;
    WebClient askuCient;
    WebClient sutoClient;
    WebClient ccvClient;

    WebClient rocketClient;
    private AskuDto initialAsku;
    @Value("${spl.default-spl-id}")
    UUID splUid;

    @Value("${spl.plate-number}")
    String splPlateNUmber;
    AskuMapper askuMapper = Mappers.getMapper(AskuMapper.class);

    @BeforeEach
    void setUp() {
        initialAsku = askuMapper.toDto(
                AskuUtils.getDefaultAsku(
                        splUid,
                        splPlateNUmber,
                        new Position(46.22592300, 30.57707298, 0.2, WGS84)));
        rocketClient = WebClient.builder()
                .baseUrl(rootUrl + "rocket/")
                .defaultHeader(plcTokenName, plcRequestToken)
                .build();


    }

    @Test
    void initRocketWirthOrder() {

        AskuDto updatedAskuDto = rocketClient.post()
                .uri(uriBuilder -> uriBuilder
                        .queryParam("isLeft", false)
                        .build())
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue("""
                        {
                            "technicalCondition": true,
                            "dateUseM": null,
                            "formData": {
                                "plantMissile": "11",
                                "warhead": "CBCH",
                                "gsnType": "NO_GSN",
                                "alpType": "NO_ALP",
                                "isTelemetryIntegrated": true, 
                                "purposeType": "COMBAT"
                            },
                            "initialData": null,
                            "initialDataTS": null,
                            "initialDataSource": "MSG",
                            "initialDataSourceDescription": "КМУ Лесь Підервянский",
                            "storedTlKeys": ["123","123---sdfsdfsfd1465ssdfsd===="]
                        }
                        """)
                .retrieve()
                .bodyToMono(AskuDto.class)
                .block(Duration.ofMillis(300));
        assertThat(updatedAskuDto != null);

    }
}