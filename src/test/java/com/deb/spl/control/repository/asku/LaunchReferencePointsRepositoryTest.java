package com.deb.spl.control.repository.asku;

import com.deb.spl.control.data.asku.LaunchReferencePointDao;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.util.Optional;

import static org.junit.Assert.assertTrue;

@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("dev")
//@TestPropertySource(properties = "spring.liquibase.enabled=false")
class LaunchReferencePointsRepositoryTest {

    @Autowired
    LaunchReferencePointsRepository referencePointsRepository;
    private LaunchReferencePointDao arrowUpLaunchReferencePoint;
    private final String ARROW_UP_LAUNCH = "ARROW_UP_LAUNCH";

    @BeforeEach
    void setUp() {
        arrowUpLaunchReferencePoint = referencePointsRepository.findById(1L).get();


    }

    @Test
    void findCaption() {
        Optional<LaunchReferencePointDao> found = referencePointsRepository.findFirstByCommand(ARROW_UP_LAUNCH);
        Assertions.assertTrue(found.isPresent());
        assertTrue(!found.get().getCaption().isBlank());

    }
}