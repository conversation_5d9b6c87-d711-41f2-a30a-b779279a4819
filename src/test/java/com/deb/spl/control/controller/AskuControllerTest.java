package com.deb.spl.control.controller;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.*;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.time.Duration;

import static org.junit.jupiter.api.Assertions.assertEquals;


@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("dev")
class AskuControllerTest {

    //    @LocalServerPort
    int serverPort = 8079;

    @Value("${plc.request.token}")
    String plcRequestToken;
    @Value("${plc.token.request-parameter-name}")
    String plcTokenName;
    @Autowired
    TestRestTemplate restTemplate;
    private WebClient webClient;
    final String temperatureParamName = "temperature";
    final String isLeftParamName = "isLeft";

    @BeforeEach
    void beforeAll() {
        String url = "http://localhost:" + serverPort + "/api/v1/asku/rocket/sensors";

        webClient = WebClient
                .builder()
                .baseUrl(url)
                .defaultHeader(plcTokenName, plcRequestToken)
                .build();

    }

    @Test
    void setTemperature() throws InterruptedException {
        String url = "http://localhost:" + serverPort + "/api/v1/asku/rocket/sensors";
        double rightRocketTemp = 5.0;
        for (double leftRocketTemperature = 20.0; leftRocketTemperature <= 86.0; ) {

            // Change behavior for rocketTemperature >= 20.0
            leftRocketTemperature += 0.4;  // Increase temperature with step of 0.4
            double finalLeftRocketTemperature = leftRocketTemperature;
            webClient.post()
                    .uri(uriBuilder -> uriBuilder
                            .queryParam(isLeftParamName, true)
                            .queryParam(temperatureParamName, String.format("%.1f", finalLeftRocketTemperature))
                            .build())
                    .retrieve()
                    .bodyToMono(String.class)
                    .block(Duration.ofMillis(1510));

            Thread.sleep(550);

            if (leftRocketTemperature >= 20.0) {
                rightRocketTemp += 0.5;  // Increase temperature with step of 0.5
                double finalRightRocketTemp = rightRocketTemp;
                webClient.post()
                        .uri(uriBuilder -> uriBuilder
                                .queryParam(isLeftParamName, false)
                                .queryParam(temperatureParamName, String.format("%.1f",finalRightRocketTemp))
                                .build())
                        .retrieve()
                        .bodyToMono(String.class)
                        .block(Duration.ofMillis(1500));
            }

            Thread.sleep(510);
        }
    }
}