@import url('./main-layout.css');
@import url('./adjacent-systems-status-panel.css');
/*@import url('./about.css');*/
@import url('line-awesome/dist/line-awesome/css/line-awesome.min.css');

@media only screen and (max-width: 1909px) {
    html {
        --lumo-secondary-text-color: #000;
        --lumo-body-text-color: null;
        --lumo-space-m: 0.55em;
        /*--lumo-font-family: DejaVuSerif italic;*/
        --lumo-font-family: Ubuntu-LI;
        --lumo-font-size-m: 15px;
        --lumo-font-size-s: 14px;
        --lumo-line-height-s: 1.375;

        /*--lumo-contrast-10pct: #2a2a2b;*/
        --_lumo-grid-secondary-border-color: red;
    }
}
@media only screen and (min-width: 1910px) {
    vaadin-combo-box-overlay::part(overlay){
        --lumo-font-size-m: 18px;
        --lumo-font-size-s: 18px;
    }
    html {
        --lumo-secondary-text-color: #000;
        --lumo-body-text-color: null;
        --lumo-space-m: 0.55em;
        /*--lumo-font-family: DejaVuSerif italic;*/
        --lumo-font-family: Ubuntu-LI;
        --lumo-font-size-m: 15px;
        --lumo-font-size-s: 14px;
        --lumo-line-height-s: 1.375;

        /*--lumo-contrast-10pct: #2a2a2b;*/
        --_lumo-grid-secondary-border-color: red;
    }
}
:host([theme~='column-borders']) {
    border-right:  solid #2a2a2b 1px;
}
vaadin-text-field::part(input-field){
    background-color: var(--lumo-contrast-10pct);
    color: #2a2a2b;
}

:host [part="input-field"]{
    background-color: #dadada
}
vaadin-grid.styled{
    border-right:  solid #2a2a2b 1px;
}

vaadin-text-field[readonly]::part(input-field){
    background-color: #dedede;
    color: #2a2a2b;
}
vaadin-combo-box[readonly]::part(input-field){

    color: #2a2a2b;
    background-color: #dadada;
}
vaadin-combo-box::part(input-field){
    background-color: var(--lumo-contrast-10pct);
}