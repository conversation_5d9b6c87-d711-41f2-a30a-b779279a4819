@media only screen and (max-width: 1909px) {
.launch-OTR-sensor{
    margin-bottom: 10px;
    width: 100%;
    display:flex;
    justify-content: end;
    padding-right: 50px;

}
    .basu-temperature-otr1-pdp{
        margin-top: -25px;
        margin-left: 0px;
        padding: 0;
        text-align: center;
    }
    .basu-temperature-otr2-pdp{
        margin-top: -25px;
        margin-left: 0px;
        padding: 0;
        text-align: center;
    }
    .basu-temperature{
        margin-top: 12px;
    }
    .note-precise-drive{
        margin-right: 108px;
    }
    .feeder1-readiness{
        width: 200px;
    }
    .feeder2-readiness{
        width: 200px;
    }
    .feeder3-readiness{
        width: 200px;
    }
    .feeder4-readiness{
        width: 200px;
    }
    .feeder5-readiness{
        width: 200px;
    }
    .feeder6-readiness{
        width: 200px;
    }

    .feeder-nppa-su-readiness{
        width: 200px;
    }
    .blocking-aj-sae-readiness{
        width: 200px;
    }
    .second-column-status{
        border: solid #363A40 1px;
        padding: 0 10px;
        border-radius: 5px;
        gap: 0.2em;
        height: 570px;
    }
    .sae-status-tab-fourth-column{
        display: flex;
        align-items: center;
    }
    .sae-status-tab-third-column{
        display: flex;
        align-items: center;
    }
    .sae-status-tab-second-column{
        display: flex;
        align-items: center;
    }
    .sae-status-tab-first-column{
        display: flex;
        align-items: center;
    }
    .sae-status-tab-first-row{
        gap: 1em;
        min-width: 100%;
    }
    .sae-status-tab-second-row{
        gap: 1em;
        min-width: 100%;
        justify-content: space-between;

    }
    .initial-data-altitude-pdp{
        width: 120px;
        margin-right: 15px;
        padding-top: 0;
        margin-top: 5px;
    }
    .initial-data-altitude-pdp > input:placeholder-shown{
        color: rgb(135 135 135);
    }
    .launch-OTR-readiness-2{
        display: flex;
        align-items: center;
        margin-top: -18px;
    }
    .launch-precise-drive{
        display: flex;
        align-items: center;
        width: 100%;
    }
    .note-sk-bins{
        margin-top: -10px;

    }
    .launch-started-initial-sk-bins{
        display: flex;
        align-items: center;

    }
    .launch-tv-no-ins-otr1{
        width: 180px;
        margin-top: -15px;
    }
    .launch-tv-no-ins-otr2{
        width: 180px;
        margin-top: -15px;
    }
    .launch-bas-sns-pz-otr1{
        width: 180px;
        margin-top: -15px;
        /*margin-left: 80px;*/
    }
    .launch-bas-sns-pz-otr2{
        width: 180px;
        margin-top: -15px;
        /*margin-left: 80px;*/
    }
    .launch-tv-no-bas-sns{
        width: 940px;
        display: flex;
        justify-content: end;
        align-items: baseline;
        padding-right: 17.8px;
    }
    .launch-otr-caption{
        padding: 8.7px 8.7px 0 17.4px;
    }
    .preparation-for-launch-caption{
        padding: 8.7px 8.7px 0 17.6px;
    }
    .preparation-for-launch-second-row-OTR1{
        /*height: 300px;*/
        margin-top: -25px;
    }
    .preparation-for-launch-second-row-OTR2{
        /*height: 300px;*/
        margin-top: -25px;
    }
    .plant-missile-form-data{
        padding-top: 0;
        height: 50px;
    }
    .created-at{
        padding-top: 0;
        height: 50px;
    }
    .launch-planned-time-pdp{
        width: 205px;
        margin-left: 15px;
        padding-top: 0;

    }
    .order-valid-until-pdp{
        width: 205px;

        padding-top: 0;
        margin-right: 15px ;
    }
    .initial-data-time-stamp-pdp{
        width: 205px;
        margin-right: 15px;
        padding-top: 0;
    }
    .initial-data-readiness-pdp{
        width: 205px;
        padding-top: 0;
        margin-left: 15px;
    }
    .initial-data-readiness-pdp::part(label){

        width: 200px !important;
    }
    .initial-data-latitude-pdp{
        width: 270px;
        margin-left: 15px;
        padding-top: 0;
        margin-top: 5px;
    }
    .initial-data-latitude-pdp::part(label){
        margin-top: 2px !important;
    }
    .initial-data-latitude-pdp > input:placeholder-shown{
        color: rgb(135 135 135);
    }
    .initial-data-longitude-pdp{
        width: 270px;
        margin-left: 15px;
        padding-top: 0;
    }
    .initial-data-longitude-pdp > input:placeholder-shown{
        color: rgb(135 135 135);
    }
    .initial-data-inclination-angle-pdp{
        width: 120px;
        margin-right: 15px;
        padding-top: 0;
    }
    .initial-data-inclination-angle-pdp > input:placeholder-shown{
        color: rgb(135 135 135);
    }
    .change-initial-data{
        border-radius: 4px;
        background-color:  #4F4F4F;
        color: white;
        margin-top: 5px;
        margin-bottom: 10px;
        margin-left: 15px;
    }
    .change-initial-data-pdp{
        border-radius: 4px;
        background-color:  #4F4F4F;
        color: white;
        margin-top: 22px;

        margin-right: 15px;
    }
    .rocket-form-data-pdp-view{
        border: solid 1px #363A40;
        border-radius: 5px;
        width: 390px !important;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        flex-direction: initial;
        height: 250px;
        gap: 0.6em;

    }
    .otr-vd-layout-pdp{
        border: solid 1px #363A40;
        border-radius: 5px;
        width: 450px !important;
        display: flex;
        flex-wrap: wrap;
        gap: 0.1em;
        justify-content:space-between;

    }
    .check-otr-right-pdp{
        margin-top: -25px;
    }
    .check-otr-left-pdp{
        margin-top: -25px;
    }
    .tv-byn-pdp{
        margin-top: -25px;
    }
    .latitude{
        margin-top: -25px;
    }
    .altitude{
        margin-top: -25px;
    }
    .longitude{
        margin-top: -25px;
    }
    .launch-OTR1-first-row{
        display: flex;
        align-items: center;
        width: 1235px!important;
        flex-wrap: wrap;

    }
    .launch-check-step-1{
        margin: 15px;
    }
    .note-launch-OTR1-from-BG3-caption{
        margin-left: 10%;
    }
    .launch-cancellation-OTR1{
        margin-left: 15px;
        margin-right: 10px;
    }
    .launch-cancellation-OTR2{
        margin-left: 15px;
        margin-right: 10px;
    }
    .outriggers-horizontal-position{
        margin-right: 80px;
    }

    .app-nppa-pdp{
        margin-top: -35px;
        margin-left: 35px;
    }
    .launch-OTR1-ten-row{
        width: 1230px;
        display: flex;
        justify-content: end;
        align-items: center;
        padding-right: 48px;
    }
    .launch-OTR1-eleven-row{
        width: 1230px;
        display: flex;
        justify-content: end;
        padding-right: 90px;
        align-items: center;
    }

    .launch-OTR1-seven-row{
        display: flex;
        align-items: center;
    }
    .launch-check-step-7{
        margin: 15px;
    }
    .note-arrow-up{
        margin-left: 550px ;
    }

    .launch-OTR1-eight-row{
        display: flex;
        align-items: center;
    }
    .launch-check-step-8{
        margin: 15px;
    }

    .launch-otr-left-pdp{
        margin-top: -25px;
        width: 180px;
        padding: 0;
        height: 50px;
    }
    .launch-otr-left-pdp::part(input-field){
        height: 32px;
    }
    .time-of-launch-otr-left{
        margin-top: -25px;
        width: 180px;
        padding: 0;
        height: 50px;
        margin-left: 50px;
    }
    .time-of-launch-otr-left::part(input-field){
        height: 32px;
    }
    .launch-OTR1-second-row{
        display: flex;
        align-items: center;
    }
    .launch-check-step-2{
        margin: 15px;
    }

    .tv-ncok-pdp{
        margin-top: -25px;
        padding: 0;
    }
    .tv-ncok-pdp::part(input-field){
    }
    .folding-OTR1-tab{
        padding: 0;
    }
    .launch-OTR1-third-row{
        display: flex;
        align-items: center;
        width: 1235px!important;
        flex-wrap: wrap;
    }
    .operation-mode-ncok-pdp{
        margin-top: -25px;
        width: 180px;
        padding: 0;

        margin-right: 0px;
    }
    .operation-mode-ncok-pdp::part(input-field){
        height: 32px;
    }
    .app-otr-left-pdp{
        margin-top: -35px;
    }
    .app-otr-right-pdp{
        margin-top: -35px;
        margin-right: 0px;
    }

    .operation-mode-ncok-pdp::part(input-field){
        height: 32px;
    }
    .launch-check-step-3{
        margin: 15px;
    }
    .launch-OTR1-nine-row{
        display: flex;
        align-items: center;
        padding-bottom: 5px;
    }
    .launch-check-step-9{
        margin: 15px;
    }
    .time-launch-otr-right{
        margin-top: -25px;
        width: 180px;
        padding: 0;
        height: 50px;
        margin-left: 50px;
    }
    .time-of-launch-otr-right::part(input-field){
        height: 32px;
    }
    .launch-otr-right-pdp{
        width: 180px;
        padding: 0;
        height: 50px;
        margin-top: -25px;
    }
    .launch-otr-right-pdp::part(input-field){
        height: 32px;
    }
    .launch-OTR1-five-row{
        display: flex;
        align-items: center;
    }
    .launch-check-step-5{
        margin: 15px;
    }
    .note-outriggers-derived-position{
        margin-left: 80px;
    }
    .launch-OTR1-sixth-row{
        display: flex;
        align-items: center;
    }
    .launch-check-step-6{
        margin: 15px;
    }
    .note-outriggers-derived-position-after{
        margin-left: 80px;
    }
    .launch-OTR1-fourth-row{
        display: flex;
        align-items: center;
        width: 1235px!important;
        flex-wrap: wrap;
    }
    .launch-check-step-4{
        margin: 15px;
    }
    .launch-OTR1-layout{
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        width: 1235px !important;
        gap: 0.3em;
    }

    .launch-OTR2-layout{
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        width: 1235px !important;
        gap: 0.3em;
    }
    .launch-2hOTR-layout{
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        width: 1235px !important;
        gap: 0.3em;
    }
    .launch-OTR1-tab{
        padding: 0;
    }
    .launch-OTR2-tab{
        padding: 0;
    }
    .launch-2hOTR-tab{
        padding: 0;
    }

    .launch-otr1-tab{
        padding: 0;
    }
    .launch-otr1-tab:active{
        color: var(--lumo-body-text-color);
    }
    .launch-otr2-tab{
        padding: 0;
    }
    .launch-otr2-tab:active{
        color: var(--lumo-body-text-color);
    }
    .launch-2hotr-tab{
        padding: 0;
    }
    .launch-2hotr-tab:active{
        color: var(--lumo-body-text-color);
    }
    .pdp-layout{
        padding: 0;
    }
    .preparation-for-launch{
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        width: 1235px !important;
    }
    .rocket-form-data-view{
        border: solid 1px #363A40;
        border-radius: 5px;
        gap: 1.5em;
    }
    .otr-purpose-type{
        height: 50px;
        padding: 0;
        margin-top: -5px;
    }
    .otr-purpose-type::part(label){
        align-self: center;
    }
    .is-telemetry-integrated{
        height: 50px;
        padding: 0;
        margin-top: -5px;
    }
    .is-telemetry-integrated::part(label){
        align-self: center;
    }
    .alp-type{
        height: 50px;
        margin-top: -5px;
        padding: 0;
    }
    .alp-type::part(label){
        align-self: center;
    }
    .gsn-type{
        height: 50px;
        text-align: center;
        padding: 0;
        margin-top: -5px;
    }
    .gsn-type::part(label){
        align-self: center;
    }
    .warhead-type{
        height: 50px;
        padding: 0;
        margin-top: -5px;
    }
    .warhead-type::part(label){
        align-self: center;
    }
    .plant-missile-form-data{
        padding-top: 0;
    }
    .created-at{
        padding-top: 0;
    }
    .bins-gps-status{
        margin-top: -25px;
    }
    .bins-calibration-status{
        margin-top: -25px;
    }
    .coordinate-SPL{
        margin: 0px 50px 0 13px;
    }
    .ncok-commands-tab-layout{
        max-height: 580px;
        width: 100% !important;
    }
    .ncok-commands-tab{
        padding: 0;
        width: 100% !important;
    }
    .third-column-readiness{
        padding: 8px;
        margin-left: -300px;
    }
    .readiness-command-grid{
        margin-top: 290px;
        width: 600px;
        margin-left: 6px;
        max-height: 389px;
        border-color: #2a2a2b;
        border-width: 2px !important;
        --lumo-contrast-10pct: #2a2a2b;
        --lumo-font-size-m: 14px;
    }
    .navigation-rest-log-tab{
        padding: 7.8px;
        height: 637px !important;
    }
    .commands-grid-sae{
        color: #363A40;
        height: 595px;
        border-color: #2a2a2b;
        border-width: 2px !important;
        --lumo-contrast-10pct: #2a2a2b;
    }
    .commands-grid-sae::part(cell header-cell){
        border-right: solid #2a2a2b 1px;
        border-bottom:solid #2a2a2b 2px ;
    }
    .readines-tips-text-area{
        width: 390px;
        margin-top: -200px;
        font-size: 14px;
    }
    .first-row-readiness{
        display: flex;
        justify-content: space-between;

        width: 750px;
    }
    .first-column-second-row-readiness{
        position: absolute;
        top: 350px;
        left: 400px;
        width: 200px !important;
        font-size: 13px;
    }
    .readiness-combobox{
        width: 220px;
    }
    .send-bt-row-readiness{
        position: absolute;
        top: 116px;
        left: 640px;
        width: 200px !important;
    }
    .filter-text-field{
        margin-left: 5px;
    }
    .system-filter-cb{
        width: 590px;
    }
    .status-cb{
        width: 433px;
    }
    .status-cb::part(input){
        width: 100px;
    }
    .clear-button{
        margin-top: 10px;
        margin-left: 15px;
    }
    .clear-button::part(input-field){
        width: 150px;
    }
    .refresh-button{
        width: 140px;
        margin-top: 10px;
    }
    .refresh-export-button{
        padding: 0;
        width: 200px !important;
        margin-left: 25px;
    }
    .grid-layout-log{
        width: 1225px;
        margin: 8.8px 10px;
    }
    .grid-filters-buttons-layout{
        margin-left: 8.8px;
    }
    .grid-filter-layout{

    }
    .log-layout-all{
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        /*height: 862px;*/
        gap: 0;
        height: 761px;
        width: 1245px !important;
        /* overflow-y: hidden;*/
        margin: 8.8px 8.8px 0px 8.8px;
    }
    .nav-rest-log-tab{
        height: 567px;
        width: 814px;
    }
    .bins-log-tab{
        height: 570px;
    }
    .log-tab{
        padding: 0;
        height: 637px;
    }
    .msu-log-grid{
        height: 590px;
        color: rgb(54, 58, 64);
    }
    .adj-rest-log-tab{
        padding: 0px 5px;

    }
    .msu-log-tab{
        height: 585px;
    }
    .msu-rest-log-tab{
        height: 580px;
    }
    .coord-start-down{
        display: flex;
        justify-content:space-between;
        padding-top: 35px;
    }
    .coord-button-down{
        margin-left: 710px;
    }
    .bins-data-layout{
        padding: 0;
    }
    .command-tab-bins{
        padding: 0;
        padding-left: 8.8px;
        margin-left: 22px;
    }


    .commands-caption{
        color: #1C2E45;
    }
    .commands-grid-suto{
        color: #363A40;
        height: 525px;
        /*width: 810px;*/
        border-color: #2a2a2b;
        border-width: 2px !important;
        --lumo-contrast-10pct: #2a2a2b;
    }
    .commands-grid-suto::part(cell header-cell){
        height: 45px;
        border-right: solid #2a2a2b 1px;
        border-bottom:solid #2a2a2b 2px ;
    }
    .suto-status{
        gap: 0em;
        padding: 0px;
        margin-top: -17.4px;
    }
    .pillar-BR{
        width: 400px;
        padding: 0;
        height: 50px;
        padding-top:8.8px;
        margin-bottom: 8.8px;
    }
    .pillar-BR::part(input-field){
        height: 32px;
    }
    .pillar-BL{
        width: 400px;
        padding: 0;
        height: 50px;
        padding-top:8.8px;
        margin-bottom: 8.8px;

    }
    .pillar-BL::part(input-field){
        height: 32px;
    }
    .pillar-FL{
        width: 400px;
        padding: 0;
        height: 50px;
        padding-top:17.6px;
        margin-top: -8.8px;
    }
    .pillar-FL::part(input-field){
        height: 32px;
    }
    .pillar-FR{
        width: 400px;
        padding: 0;
        height: 50px;
        padding-top:17.6px;
        margin-top: -8.8px;
        margin-bottom: 8.8px;
    }
    .pillar-FR::part(input-field){
        height: 32px;
    }
    .roll-SUTO{
        width: 160px;
        padding: 0;
        height: 50px;
        padding-top:17.6px;
        margin-top: -8.8px;
    }
    .roll-SUTO::part(input-field){
        height: 32px;
    }
    .pitch-SUTO{
        width: 160px;
        padding: 0;
        height: 50px;
        padding-top:17.6px;
        margin-top: -8.8px;
    }
    .pitch-SUTO::part(input-field){
        height: 32px;
    }
    .arrow-SUTO{
        width: 160px;
        padding: 0;
        height: 65px;
        padding-top:17.6px;
        margin-top: -23.8px;
    }
    .arrow-SUTO::part(input-field){
        height: 32px;
    }
    .temperature-RR{
        width: 160px;
        padding: 0;
        height: 50px;
        padding-top:17.6px;
        margin-top: -8.8px;
    }
    .temperature-RR::part(input-field){
        height: 32px;
    }
    .level-working-fluid{
        width: 160px;
        padding: 0;
        height: 50px;
        padding-top:17.6px;
        margin-top: -8.8px;
    }
    .level-working-fluid::part(input-field){
        height: 32px;
    }
    .main-pump-speed{
        width: 160px;
        padding: 0;
        height: 50px;
        padding-top:17.6px;
        margin-top: -8.8px;
    }
    .main-pump-speed::part(input-field){
        height: 32px;
    }
    .pressure-impulse-section{
        width: 100px;
        padding: 0;
        height: 50px;
        padding-top:17.6px;
        margin-top: -8.8px;
    }
    .pressure-impulse-section::part(input-field){
        height: 32px;
    }
    .number-leveling-cycles{
        width: 200px;
        padding: 0;
        height: 50px;
        padding-top:17.6px;
        margin-top: -8.8px;
    }
    .number-leveling-cycles::part(input-field){
        height: 32px;
    }
    .runtime-counter{
        width: 180px;
        padding: 0;
        height: 50px;
        padding-top:17.6px;
        margin-top: -8.8px;
    }
    .runtime-counter::part(input-field){
        height: 32px;
    }
    .number-arrow-lifts{
        width: 160px;
        padding: 0;
        height: 50px;
        padding-top:17.6px;
        margin-top: -8.8px;
    }
    .number-arrow-lifts::part(input-field){
        height: 32px;
    }
    .ppo-status-tab-first{
        padding: 0;
        gap:0;
        height: 210px;
    }
    .ppo-status-tab-first-column{
        display: flex;
        justify-content: space-between;
        width: 100%;
        gap:0.1em;
    }
    .status-cell-ASKU{
        align-self: center;
    }
    .ppo-status-tab-second-column{
        display: flex;
        flex-wrap:wrap;
        border: solid 1px #363A40;
        justify-content:space-around;
        height: 160px;
        padding: 0;
        width: 100%;
        gap:0.1em;

    }
    .status-of-cell-ASKU{
        width: 160px;
        padding: 0;
        height: 28px;
    }
    .status-of-cell-ASKU::part(input-field){
        height: 25px;
    }
    .balloon-ASKU-1{
        width: 160px;
        padding: 0;
        padding-top:17.6px;
        height: 41px;
        margin-top: -7px;
        font-size: 13px;
    }
    .balloon-ASKU-1::part(input-field){
        height: 25px;
        font-size: 13px;
    }
    .balloon-ASKU-1::part(label){
        font-size: 13px;
    }
    .balloon-ASKU-2{
        width: 160px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        padding-top:17.6px;
        font-size: 13px;
    }
    .balloon-ASKU-2::part(input-field){
        height: 25px;
        font-size: 13px;
    }
    .balloon-ASKU-2::part(label){
        font-size: 13px;
    }
    .optical-sensor-ASKU-1{
        width: 160px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        font-size: 13px;
    }
    .optical-sensor-ASKU-1::part(input-field){
        height: 25px;
        font-size: 13px;
    }
    .optical-sensor-ASKU-1::part(label){
        font-size: 12px;
        width: 160px;
    }
    .optical-sensor-ASKU-2{
        height: 41px;
        margin-top: -7px;
        padding: 0;
        width: 160px;
        font-size: 13px;
    }
    .optical-sensor-ASKU-2::part(input-field){
        height: 25px;
        font-size: 13px;
    }
    .optical-sensor-ASKU-2::part(label){
        font-size: 12px;
        width: 160px;
    }
    .optical-sensor-ASKU-3{
        height: 41px;
        margin-top: -7px;
        padding: 0;
        width: 160px;
        font-size: 13px;
    }
    .optical-sensor-ASKU-3::part(input-field){
        height: 25px;
        font-size: 13px;
    }
    .optical-sensor-ASKU-3::part(label){
        font-size: 12px;
        width: 160px;
    }
    .optical-sensor-ASKU-4{
        height: 41px;
        margin-top: -7px;
        padding: 0;
        width: 160px;
        font-size: 13px;
    }
    .optical-sensor-ASKU-4::part(input-field){
        height: 25px;
        font-size: 13px;
    }
    .optical-sensor-ASKU-4::part(label){
        font-size: 12px;
        width: 160px;
    }
    .ppo-status-tab-second{
        padding: 0;
        gap:0;
        height: 210px;
        margin-left: 25px;

    }
    .ppo-status-tab-third-column{
        display: flex;
        justify-content: space-between;
        width: 100%;
        gap:0.1em;
    }
    .status-cell-NPPA{
        align-self: center;
    }
    .ppo-status-tab-fourth-column{
        display: flex;
        flex-wrap:wrap;
        border: solid 1px #363A40;
        justify-content:space-around;
        height: 160px;
        padding: 0;
        width: 100%;
        gap:0.1em;
    }
    .status-of-cell-NPPA{
        width: 160px;
        padding: 0;
        height: 28px;
    }
    .status-of-cell-NPPA::part(input-field){
        height: 25px;
    }
    .balloon-NPPA-1{
        width: 160px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        padding-top:17.6px;
        font-size: 13px;
    }
    .balloon-NPPA-1::part(input-field){
        height: 25px;
        font-size: 13px;
    }
    .balloon-NPPA-1::part(label){
        font-size: 13px;
    }
    .balloon-NPPA-2{
        width: 160px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        padding-top:17.6px;
        font-size: 13px;
    }
    .balloon-NPPA-2::part(input-field){
        height: 25px;
        font-size: 13px;
    }
    .balloon-NPPA-2::part(label){
        font-size: 13px;
    }
    .optical-sensor-NPPA-1{
        height: 41px;
        margin-top: -7px;
        padding: 0;
        width: 160px;
        font-size: 13px;
    }
    .optical-sensor-NPPA-1::part(input-field){
        height: 25px;
        font-size: 13px;
    }
    .optical-sensor-NPPA-1::part(label){
        font-size: 12px;
        width: 160px;;
    }
    .optical-sensor-NPPA-2{
        height: 41px;
        margin-top: -7px;
        padding: 0;
        width: 160px;
        font-size: 13px;

    }
    .optical-sensor-NPPA-2::part(input-field){
        height: 25px;
        font-size: 13px;
    }
    .optical-sensor-NPPA-2::part(label){
        font-size: 12px;
        width: 160px;
    }
    .optical-sensor-NPPA-3{
        height: 41px;
        margin-top: -7px;
        padding: 0;
        width: 160px;
        font-size: 13px;
    }
    .optical-sensor-NPPA-3::part(input-field){
        height: 25px;
        font-size: 13px;
    }
    .optical-sensor-NPPA-3::part(label){
        font-size: 12px;
        width: 160px;
    }
    .ppo-status-tab-third{
        padding: 0;
        gap:0;
    }
    .ppo-status-tab-fifth-column{
        display: flex;
        justify-content: space-between;
        width: 100%;
        margin-top: -14.7px;
    }
    .status-cell-ASKU{
        align-self: center;
    }
    .ppo-status-tab-sixth-column{
        display: flex;
        flex-wrap:wrap;
        border: solid 1px #363A40;
        justify-content:space-around;
        height: 160px;
        padding: 0;
        width: 100%;
        gap:0;
    }
    .status-of-cell-DEA{
        width: 160px;
        padding: 0;
        height: 28px;
    }
    .status-of-cell-DEA::part(input-field){
        height: 25px;
    }
    .balloon-DEA-1{
        width: 160px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        padding-top:17.6px;
        font-size: 13px;
    }
    .balloon-DEA-1::part(input-field){
        height: 25px;
        font-size: 13px;
    }
    .balloon-DEA-1::part(label){
        font-size: 13px;
    }
    .balloon-DEA-2{
        width: 160px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        padding-top:17.6px;
        font-size: 13px;
    }
    .balloon-DEA-2::part(input-field){
        height: 25px;
        font-size: 13px;
    }
    .balloon-DEA-2::part(label){
        font-size: 13px;
    }
    .thermal-sensor-DEA-1{
        width: 160px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        font-size: 13px;
    }
    .thermal-sensor-DEA-1::part(input-field){
        height: 25px;
        font-size: 13px;
    }
    .thermal-sensor-DEA-1::part(label){
        font-size: 13px;
    }
    .thermal-sensor-DEA-2{
        width: 160px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        font-size: 13px;
    }
    .thermal-sensor-DEA-2::part(input-field){
        height: 25px;
        font-size: 13px;
    }
    .thermal-sensor-DEA-2::part(label){
        font-size: 13px;
    }
    .thermal-sensor-DEA-3{
        width: 160px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        font-size: 13px;
    }
    .thermal-sensor-DEA-3::part(input-field){
        height: 25px;
        font-size: 13px;
    }
    .thermal-sensor-DEA-3::part(label){
        font-size: 13px;
    }
    .ppo-status-tab-third{
        padding: 0;
        gap:0;
    }
    .ppo-status-tab-fourth{
        padding: 0;
        gap:0;
        margin-left: 25px;
    }
    .ppo-status-tab-seventh-column{
        display: flex;
        justify-content: space-between;
        width: 100%;
        margin-top: -14.7px;
    }
    .status-cell-TO{
        align-self: center;
    }
    .ppo-status-tab-eighth-column{
        display: flex;
        flex-wrap:wrap;
        border: solid 1px #363A40;
        justify-content:space-around;
        height: 160px;
        padding: 0;
        width: 100%;
        gap:0;
    }
    .status-of-cell-TO{
        width: 160px;
        padding: 0;
        height: 28px;
    }
    .status-of-cell-TO::part(input-field){
        height: 25px;
    }
    .balloon-TO-1{
        width: 160px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        padding-top:17.6px;
        font-size: 13px;
    }
    .balloon-TO-1::part(input-field){
        height: 25px;
        font-size: 13px;
    }
    .balloon-TO-1::part(label){
        font-size: 13px;
    }
    .balloon-TO-2{
        width: 160px;
        padding: 0;
        height: 41px;
        margin-top: -7px;;
        padding-top:17.6px;
        font-size: 13px;
    }
    .balloon-TO-2::part(input-field){
        height: 25px;
        font-size: 13px;
    }
    .balloon-TO-2::part(label){
        font-size: 13px;
    }
    .thermal-sensor-TO-1{
        width: 160px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        font-size: 13px;
    }
    .thermal-sensor-TO-1::part(input-field){
        height: 25px;
        font-size: 13px;
    }
    .thermal-sensor-TO-1::part(label){
        font-size: 13px;
    }
    .thermal-sensor-TO-2{
        width: 160px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        font-size: 13px;
    }
    .thermal-sensor-TO-2::part(input-field){
        height: 25px;
        font-size: 13px;
    }
    .thermal-sensor-TO-2::part(label){
        font-size: 13px;
    }
    .thermal-sensor-TO-3{
        width: 160px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        font-size: 13px;
    }
    .thermal-sensor-TO-3::part(input-field){
        height: 25px;
        font-size: 13px;
    }
    .thermal-sensor-TO-3::part(label){
        font-size: 13px;
    }
    .ppo-status{
        display: flex;
        flex-wrap: wrap;
        gap: 0em;
        margin-bottom: 33px;
    }
    .msu-null-row{
        gap: 0.8em !important;
    }
    .is-normal{
        width: 195px;
    }
    .wind-direction{
        width: 195px;
    }
    .wind-speed{
        width: 195px;
    }
    .air-temperature{
        width: 195px;
    }
    .msu-first-row{
        gap: 0.8em !important;
    }
    .voltage{
        width: 195px;
    }
    .msu-temperature{
        width: 195px;
    }
    .posture::part(input-field){
        text-transform: capitalize !important;
        width: 195px;
    }
    .posture{
        width: 195px;
    }
    .heating-mode{
        width: 195px;
    }
    .heating-mode-permission{
        width: 195px;
    }
    .temperature-sensor-blowing{
        width: 195px;
    }
    .msu-second-row{
        gap: 0.8em !important;
    }
    .end-switches-malfunction{
        width: 220px;
    }
    .end-switches-malfunction::part(label){
        width: 220px;
        font-size: 14px !important;
    }
    .posture-switching-malfunction{
        width: 195px;
    }
    .posture-switching-malfunction::part(label){
        width: 195px;
        font-size: 14px !important;
    }
    .wind-speed-sensor-malfunction{
        width: 220px;
    }
    .wind-speed-sensor-malfunction::part(label){
        width: 220px;
    }
    .data-exchange-malfunction{
        width: 195px;
    }
    .data-exchange-malfunction::part(label){
        width: 195px;
        font-size: 14px !important;
    }
    .msu-third-row{
        gap: 0.8em !important;
    }
    .temperature-sensor-malfunction{
        width: 195px;
    }
    .electrical-drive-malfunction{
        width: 195px;
    }
    .voltage-malfunction{
        width: 195px;
    }
    .msu-fourth-row{
        gap: 0.8em !important;
    }
    .systems-tab-panel{
        color: #1C2E45;
        padding:0;
        position: sticky;
        top: 0px;
        z-index: 100;
    }
    .connection-byn{
        width: 180px;
        padding: 0;
        height: 50px;
    }
    .connection-byn::part(input-field){
        height: 32px;
    }
    .tl-keys-plc-grid{
        height: 610px;
        margin: 0 5px;
        width: 827.5px;
    }
    .form-data-header-left{
        padding: 0;
        display: flex;
        align-items: center;
    }
    .form-data-header-right{
        padding: 0;
        display: flex;
        align-items: center;
    }
    .otr-tab{
        padding: 20px 0px;
        gap:0.1em;
        display: flex;
        align-items: center;
    }
    .initial-data-latitude{
        width: 240px;
        margin-left: 15px;
        padding-top: 0;
    }
    .initial-data-latitude::part(label){
        font-size: 13px !important;
        width: 220px !important;

    }
    .initial-data-latitude > input:placeholder-shown{
        color: rgb(135 135 135);
    }
    .initial-data-altitude{
        width: 120px;
        margin-right: 15px;
        padding-top: 0;
    }
    .initial-data-altitude::part(label){
        font-size: 13px !important;
        width: 220px !important;
    }
    .initial-data-altitude > input:placeholder-shown{
        color: rgb(135 135 135);
    }
    .initial-data-longitude{
        width: 240px;
        margin-left: 15px;
        padding-top: 0;
    }
    .initial-data-longitude > input:placeholder-shown{
        color: rgb(135 135 135);
    }
    .initial-data-inclination-angle{
        width: 120px;
        margin-right: 15px;
        padding-top: 0;
    }
    .initial-data-inclination-angle > input:placeholder-shown{
        color: rgb(135 135 135);
    }
    .initial-data-missile-operating-mode{
        width: 200px;
        margin-left: 15px;
        padding-top: 0;
    }
    .initial-data-is-pro-detected{
        width: 170px;
        margin-left: 15px;
        padding-top: 0;
    }
    .initial-data-is-pro-detected::part(label){
        font-size: 13px !important;
        width: 200px !important;
    }
    .initial-data-load-temperature{
        width: 150px;
        margin-right: 15px;
        padding-top: 0;
    }
    .initial-data-load-temperature::part(label){
        font-size: 14px !important;
        width: 200px;
    }
    .initial-data-readiness{
        width: 180px;
        padding-top: 0;
        margin-right: 15px;
    }
    .initial-data-readiness::part(label){
        font-size: 13px !important;
        width: 200px !important;
    }
    .initial-data-trajectory{
        width: 230px;
        margin-left: 15px;
        padding-top: 0;
    }
    .initial-data-time-stamp{
        width: 227px;
        margin-left: 15px;
        padding-top: 0;

    }
    .is-launch-scheduled{
        width: 130px;
        margin-right: 15px;
        padding-top: 0;

    }
    .launch-planned-time{
        width: 227px;
        margin-left: 15px;
        padding-top: 0;
        margin-right: 80px ;
    }
    .order-valid-until{
        width: 227px;
        margin-left: 15px;
        padding-top: 0;
        margin-right: 80px ;
    }
    .enable-initial-data-editing{
        margin-left: 15px;
        /*border: solid 0.5px  #363A40;*/
    }
    .change-initial-data{
        border-radius: 4px;
        background-color:  #4F4F4F;
        color: white;
        margin-bottom: 10px;
        margin-left: 15px;
    }
    .initial-data-dialog{
        gap:0em;
        z-index: 1;
    }
    .title-initial-data{
        padding-left: 10px;
    }
    .launch-form-data-view{
        border: solid 1px #363A40;
        border-radius: 5px;
        gap: 0.6em;
        height: 622px;
    }
    .otr-vd-layout{
        border: solid 1px #363A40;
        border-radius: 5px;
        width: 405px !important;
        display: flex;
        flex-wrap: wrap;
        gap: 0.1em;
        justify-content:space-between;
        --lumo-font-size-m: 14px;
        padding-top: 5px;
    }
    .missile-right{
        margin: 3px 0px 0px 50px;
        height: 25px;
    }
    .app-otr-right{
        width: 180px;
        padding: 0;
        height: 50px;
    }
    .app-otr-right::part(input-field){
        height: 32px;
    }
    .launch-otr-right{
        width: 180px;
        padding: 0;
        height: 50px;
    }
    .launch-otr-right::part(input-field){
        height: 32px;
    }
    .check-otr-right{
        width: 180px;
        padding: 0;
        height: 50px;
    }
    .check-otr-right::part(input-field){
        height: 32px;
    }
    .app-otr-left{
        width: 180px;
        padding: 0;
        height: 50px;
    }
    .app-otr-left::part(input-field){
        height: 32px;
    }
    .launch-otr-left{
        width: 180px;
        padding: 0;
        height: 50px;
    }
    .launch-otr-left::part(input-field){
        height: 32px;
    }
    .check-otr-left{
        width: 180px;
        padding: 0;
        height: 50px;
    }
    .check-otr-left::part(input-field){
        height: 32px;
    }
    .app-nppa{
        width: 180px;
        padding: 0;
        height: 50px;
    }
    .app-nppa::part(input-field){
        height: 32px;
    }
    .check-nppa{
        width: 180px;
        padding: 0;
        height: 50px;
    }
    .check-nppa::part(input-field){
        height: 32px;
    }
    .connection-suto{
        width: 180px;
        padding: 0;
        height: 50px;
    }
    .connection-suto::part(input-field){
        height: 32px;
    }
    .tv-ncok{
        width: 180px;
        padding: 0;
        height: 50px;
    }
    .tv-ncok::part(input-field){
        height: 32px;
    }
    .connection-ncok{
        width: 180px;
        padding: 0;
        height: 50px;
    }
    .connection-ncok::part(input-field){
        height: 32px;
    }
    .readiness-layout{
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        gap: 0;
        height: 761px;
        width: 100%;
        margin: 8.8px 20px 0px 390px;
    }
    .system-layout-tlc{
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        height: 761px;
        gap: 0;
        width: 100%;
        margin: 8.8px 0px 0px 390px;
    }
    .system-layout-suto{
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        height: 761px;
        gap: 0;
        width: 100%;
        margin: 8.8px 0px 0px 390px;
    }
    .system-layout-sae{
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        height: 761px;
        gap: 0;
        width: 100%;
        margin: 8.8px 0px 0px 390px;
    }
    .system-layout-msu{
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        gap: 0;
        height: 761px;
        width: 863px;
        margin: 8.8px 20px 0px 390px;
    }
    .system-layout-ppo{
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        /*height: 862px;*/
        gap: 0;
        height: 761px;
        width: 100%;
        /* overflow-y: hidden;*/
        margin: 8.8px 20px 0px 390px;
    }
    .system-layout-bins{
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        /*height: 862px;*/
        gap: 0;
        /*height: 1500px;*/
        width: 100%;
        /* overflow-y: hidden;*/
        margin: 8.8px 20px 0px 390px;
    }
    .byn-log-tab{
        padding: 0;
        height: 575px;
        width: 815px!important;
    }
    .byn-log-tab-layout{
        padding: 0;
        width: 840px!important;
    }
    .system-layout-nppa{
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        height: 761px;
        gap: 0;
        width: 100%;
        margin: 8.8px 10px 0px 390px;
    }
    .top-of-tab-ncok{
        box-shadow: inset 0 -1px 0 0 var(--lumo-contrast-10pct);
        width: 100%;
        gap:45%;
    }
    .ncok-command-status-grid{
        height: 388px;
        width: 100%;
        margin-left: 8.8px;
        border-color: #2a2a2b;
        border-width: 2px !important;
        --lumo-contrast-10pct: #2a2a2b;
    }
    .adjacent-systems-nav-list{
        display: flex;
        margin: 5px 0 0 0 ;
        border-radius: 5px 5px 0px 0px;
        height: 3.5% ;
        text-align: center;
    }
    .spl-name-out{
        padding: 0 8.8px;
    }
    .vehicle-id[readonly]{
        width: 150px;
        color: #FFFFFF;
        flex: 0 1 25%;
        height: 50px;
        padding-bottom: 0;
        margin: 0px 0px 0px 22.3px;
    }
    .vehicle-id::part(label){
        align-self: center;
        width: 55px;
        text-align: center;
        color: #000;
        height: 15px;
        padding-left: 10px;
        padding-top: 0px ;
    }
    .vehicle-id[readonly]::part(input-field){
        height: 32px;
        width: 160px;
        align-self: center;
    }
    .ccv-master-id{
        width: 150px;
        color: #FFFFFF;
        flex: 0 1 25%;
        height: 50px !important;
        padding-bottom: 0;
        margin: 0px 0px 0px 17.3px;
    }
    .ccv-master-id::part(label){
        align-self: center;
        width: 50px;
        text-align: center;
        color: #000;
        height: 15px !important;
        padding-left: 10px;
        padding-top: 0px ;
    }
    .ccv-master-id[readonly]::part(input-field){
        height: 32px;
        width: 160px;
        align-self: center;
    }
    .start-date{
        height: 50px;
        flex: 0 1 25%;
        width: 150px;
        color: #FFFFFF;
        margin: 0px 0px 0px 17.3px;
        padding: 0px;
        text-align: center;

    }
    .start-date[readonly]::part(input-field){
        height: 32px;
        width: 160px;
    }
    .start-date::part(label){
        display: flex !important;
        align-self: center!important;
        width: 170px!important;
        text-align: center !important;
        color: #000;
        padding-right: 0px;
        font-size: 13px !important;
    }
    .start-time{
        height: 50px;
        flex: 0 1 25%;
        width: 150px;
        color: #FFFFFF;
        margin: 0px 0px 0px 17.3px;
        padding: 0px;
    }
    .start-time[readonly]::part(input-field){
        height: 32px;
        width: 160px;
    }
    .start-time::part(label){
        display: flex;
        align-self: center;
        width: 170px;
        text-align: center;
        color: #000;
        padding-bottom: 10px;

        font-size: 13px !important;
    }
    .plc-state{
        height: 50px;
        flex: 0 1 25%;
        width: 150px;
        color: #FFFFFF;
        margin: 0px 0px 0px 17.3px;
        padding: 0px;

    }
    .plc-state[readonly]::part(input-field){
        background-color: #00853D;
        height: 32px;
        width: 160px;
        color: white;
    }
    .plc-state::part(label){
        display: flex;
        align-self: center;
        width: 110px;
        text-align: center;
        color: #000;
        padding-left: 20px;
    }
    .ppo-state{
        height: 50px;
        flex: 0 1 25%;
        width: 150px;
        color: #FFFFFF;
        margin: 0px 0px 0px 17.3px;
        padding: 0px;
    }
    .ppo-state[readonly]::part(input-field){
        background-color: #00853D;
        height: 32px;
        width: 160px;
        color: white;
    }
    .ppo-state::part(label){
        display: flex;
        align-self: center;
        width: 50px;
        text-align: center;
        color: #000;
        padding-left: 15px;
    }
    .sae-state{
        height: 50px;
        flex: 0 1 25%;
        width: 150px;
        color: #FFFFFF;
        margin: 0px 0px 0px 17.3px;
        padding: 0px;
    }
    .sae-state[readonly]::part(input-field){
        background-color: #00853D;
        height: 32px;
        width: 160px;
        color: white;
    }
    .sae-state::part(label) {
        display: flex;
        align-self: center;
        width: 50px;
        text-align: center;
        color: #000;
        padding-left: 15px;

    }
    .bins-state{
        height: 50px;
        flex: 0 1 25%;
        width: 150px;
        color: #FFFFFF;
        margin: 0px 0px 0px 17.3px;
        padding: 0px;
    }
    .bins-state[readonly]::part(input-field){
        background-color: #00853D;
        height: 32px;
        width: 160px;
        color: white;
    }
    .bins-state::part(label){
        display: flex;
        align-self: center;
        width: 80px;
        text-align: center;
        color: #000;
        padding-left: 15px;
    }
    .suto-state{
        height: 50px;
        flex: 0 1 25%;
        width: 150px;
        color: #FFFFFF;
        margin: 0px 0px 0px 17.3px;
        padding: 0px;
    }
    .suto-state[readonly]::part(input-field){
        background-color: #00853D;
        height: 32px;
        width: 160px;
        color: white;
    }
    .suto-state::part(label){
        display: flex;
        align-self: center;
        width: 50px;
        text-align: center;
        color: #000;
        padding-left: 10px;
    }
    .meteo-state {
        height: 50px;
        flex: 0 1 25%;
        width: 150px;
        color: #FFFFFF;
        margin: 0px 0px 0px 17.3px;
        padding: 0px;
    }
    .meteo-state[readonly]::part(input-field){
        background-color: #00853D;
        height: 32px;
        width: 160px;
        color: white;
    }
    .meteo-state::part(label){
        display: flex;
        align-self: center;
        width: 120px;
        text-align: center;
        color: #000;
        padding-left: 15px;
        font-size: 14px !important;
    }
    .meteo-state[readonly]::part(input-field){
        background-color: #00853D;
    }
    .byn-tf{
        height: 50px;
        flex: 0 1 25%;
        width: 150px;
        color: #FFFFFF;
        margin: 0px 0px 0px 17.3px;
        padding: 0px;
    }
    .byn-tf[readonly]::part(input-field){
        background-color: #00853D;
        height: 32px;
        width: 160px;
        color: white;
    }
    .byn-tf::part(label){
        display: flex;
        align-self: center;
        width: 50px;
        text-align: center;
        color: #000;
        padding-left: 10px;
    }
    .ncok-command-commands-grid{
        /*width: 815px;*/
        height: 100% !important;
        border-color: #2a2a2b;
        border-width: 2px !important;
        --lumo-contrast-10pct: #2a2a2b;
    }
    .ncok-log-tab{
        padding: 0;
        height: 575px;
        width: 815px!important;
    }
    .ncok-log-tab-layout{
        padding: 0;
        width: 840px!important;
    }
    .byn-tab{
        padding: 0px;
        margin-top: -8.7px;
        width: 840px !important;
    }
    .command-tab-byn{
        padding: 0px;
    }
    .top-of-tab-byn{
        box-shadow: inset 0 -1px 0 0 var(--lumo-contrast-10pct);
        gap:24.5em;
    }
    .toggle-button-byn::part(label){
        margin-right: 20px;
        width:200px;
    }
    .byn-status-tab{
        padding: 0;
        width: 840px !important;
    }
    .byn-status-tab-layout{
        padding: 0;
        width: 840px !important;
    }
    .byn-status-tab-first-column{
        width: 470px !important;
        margin-top: -8.7px;
        padding: 0 0;
    }
    .byn-status-tab-second-column{
        border: solid 1px #363A40;
        border-radius: 5px;
        margin-top: -12.4px;
        margin-left: -8.7px;
        width: 350px!important;
        height: 565px;

    }
    .byn-supply-label{
        width: 270px !important;
        gap: 0.86em;
        padding: 0;
    }
    .availability-of-power{
        font-size: 14px !important;
        align-self: center;
    }
    .availability-of-power::part(label){
        font-size: 14px !important;
        width: 200px;
    }
    .tv-byn{
        width: 180px;
        padding: 0;
        height: 50px;
        margin-bottom: 58px;
    }
    .tv-byn::part(input-field){
        height: 32px;
    }
    .readiness-layout-out{
        padding: 0;
    }
}

@media only screen and (min-width: 1910px) {

    .launch-OTR-sensor{
        margin-bottom: 10px;
        width: 930px;
        display:flex;
        justify-content: end;
        padding-right: 8.7px;
    }
    .basu-temperature-otr1-pdp{
        margin-top: -25px;

        padding: 0;
        width: 150px;
    }
    .basu-temperature-otr2-pdp{
        margin-top: -25px;

        padding: 0;
        width: 150px;
    }
    .basu-temperature{
        margin-top: 12px;
    }
    .second-column-status{
        border: solid #363A40 1px;
        padding: 0 10px;
        border-radius: 5px;
        gap: 0.2em;
        height: 600px;
    }
    .notification-warning{
        --lumo-font-size-m: 18px;
    }

    .notification-commit{
        --lumo-font-size-m: 18px;
    }
    .notification-error{
        --lumo-font-size-m: 18px;
    }

    .readiness-layout-out{
        padding: 0;
    }
    .sae-tab-container{
        padding-bottom: 0;
    }
    .tab-layout-nppa{
        padding: 0;
    }
    .launch-OTR-readiness-2{
        display: flex;
        align-items: center;
        margin-top: -18px;
    }
    .outriggers-horizontal-position{
        margin-right: 20px;
        width: 180px;

    }
    .suto-status-tab{
        padding: 0;
    }
    .launch-precise-drive{
        display: flex;
        align-items: center;
        width: 100%;
    }
    .note-sk-bins{
        margin-top: -10px;

    }
    .launch-started-initial-sk-bins{
        display: flex;
        align-items: center;

    }
    .launch-tv-no-ins-otr1{
        width: 150px;
        margin-top: -15px;
    }
    .launch-tv-no-ins-otr2{
        width: 150px;
        margin-top: -15px;
    }
    .launch-bas-sns-pz-otr1{
        width: 150px;
        margin-top: -15px;
    }
    .launch-bas-sns-pz-otr2{
        width: 150px;
        margin-top: -15px;
    }
    .launch-tv-no-bas-sns{
        width: 940px;
        display: flex;
        justify-content: end;
        align-items: baseline;
        padding-right: 17.8px;
    }
    .preparation-three-in-row-for-launch{
        gap: 0;
    }
    .check-otr-right-pdp{
        margin-top: -25px;
        width: 150px;
    }
    .check-otr-left-pdp{
        margin-top: -25px;
        width: 150px;
    }

    .tv-byn-pdp{
        margin-top: -25px;
        width: 150px;
    }
    .tv-ncok-pdp{
        margin-top: -25px;
        padding: 0;
        width: 150px;
    }
    .tv-ncok-pdp::part(input-field){
    }
    .launch-otr-caption{
        padding: 8.7px 8.7px 0 17.4px;
    }
    .preparation-for-launch-caption{
        padding: 8.7px 8.7px 0 17.6px;
    }
    .launch-OTR1-first-row{
        display: flex;
        align-items: center;
        width: 940px!important;
        flex-wrap: wrap;

    }
    .launch-check-step-1{
        margin: 15px;
    }
    .note-launch-OTR1-from-BG3-caption{
        margin-left: 100px;
    }
    .launch-cancellation-OTR1{

    }
    .launch-cancellation-OTR2{

    }

    .app-nppa-pdp{
        margin-top: -35px;
        width: 150px;

    }
    .launch-OTR1-ten-row{
        width: 930px;
        display: flex;
        justify-content: end;
        align-items: center;
        padding-right: 8.7px;
    }
    .launch-OTR1-eleven-row{
        width: 940px;
        display: flex;
        justify-content: end;
        padding-right: 150px;
        align-items: center;
    }
    .launch-OTR1-seven-row{
        display: flex;
        align-items: center;
        width: 100%;
    }
    .launch-check-step-7{
        margin: 15px;
    }
    .note-arrow-up{
        margin-left: 380px ;
    }

    .launch-OTR1-eight-row{
        display: flex;
        align-items: center;
    }
    .launch-check-step-8{
        margin: 15px;
    }

    .launch-otr-left-pdp{
        margin-top: -25px;
        width: 150px;
        padding: 0;
        height: 50px;
    }
    .launch-otr-left-pdp::part(input-field){
        height: 32px;
    }
    .time-of-launch-otr-left{
        margin-top: -25px;
        width: 180px;
        padding: 0;
        height: 50px;
        margin-left: 50px;
    }
    .time-of-launch-otr-left::part(input-field){
        height: 32px;
    }
    .launch-OTR1-second-row{
        display: flex;
        align-items: center;
    }
    .launch-check-step-2{
        margin: 15px;
    }
    .folding-OTR1-tab{
        padding: 0;
    }
    .launch-OTR1-third-row{
        display: flex;
        align-items: center;
        width: 1235px!important;
        flex-wrap: wrap;
    }
    .operation-mode-ncok-pdp{
        margin-top: -25px;
        width: 180px;
        padding: 0;

        margin-right: 0px;
    }
    .operation-mode-ncok-pdp::part(input-field){
        height: 32px;
    }
    .app-otr-left-pdp{
        margin-top: -35px;

        width: 150px;
    }
    .app-otr-right-pdp{
        margin-top: -35px;
        margin-right: 0px;
        width: 150px;
    }

    .operation-mode-ncok-pdp::part(input-field){
        height: 32px;
    }
    .launch-check-step-3{
        margin: 15px;
    }
    .launch-OTR1-nine-row{
        display: flex;
        align-items: center;
        padding-bottom: 5px;
    }
    .launch-check-step-9{
        margin: 15px;
    }
    .time-launch-otr-right{
        margin-top: -25px;
        width: 180px;
        padding: 0;
        height: 50px;
        margin-left: 50px;
    }
    .time-of-launch-otr-right::part(input-field){
        height: 32px;
    }
    .launch-otr-right-pdp{
        width: 150px;
        padding: 0;
        height: 50px;
        margin-top: -25px;
    }
    .launch-otr-right-pdp::part(input-field){
        height: 32px;
    }
    .launch-OTR1-five-row{
        display: flex;
        align-items: center;
    }
    .launch-check-step-5{
        margin: 15px;
    }
    .note-outriggers-derived-position{
        margin-left: 155px;
    }
    .launch-OTR1-sixth-row{
        display: flex;
        align-items: center;
    }
    .launch-check-step-6{
        margin: 15px;
    }
    .note-outriggers-derived-position-after{
        margin-left: 265px;
    }
    .launch-OTR1-fourth-row{
        display: flex;
        align-items: center;
        width: 940px!important;
        flex-wrap: wrap;

    }
    .launch-check-step-4{
        margin: 15px;
    }
    .launch-OTR1-layout{
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        width: 940px !important;
        gap: 0.3em;
    }
    .launch-OTR2-layout{
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        width: 940px !important;
        gap: 0.3em;
    }
    .launch-2hOTR-layout{
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        width: 940px !important;
        gap: 0.3em;
    }
    .launch-OTR1-tab{
        padding: 0;
        flex-direction: row;
       /* display: contents;*/
    }
    .launch-OTR2-tab{
        padding: 0;
        flex-direction: row;
    }
    .launch-2hOTR-tab{
        padding: 0;
        flex-direction: row;
    }

    .launch-otr1-tab{
        padding: 0;
    }
    .launch-otr1-tab:active{
        color: var(--lumo-body-text-color);
    }
    .launch-otr2-tab{
        padding: 0;
    }
    .launch-otr2-tab:active{
        color: var(--lumo-body-text-color);
    }
    .launch-2hotr-tab{
        padding: 0;
    }
    .launch-2hotr-tab:active{
        color: var(--lumo-body-text-color);
    }
    .pdp-layout{
        padding: 0;
        gap: 0;
    }
    .preparation-for-launch{
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        width: 945px !important;
        gap: 0.3em;
    }
    .change-initial-data-pdp{
        border-radius: 4px;
        background-color:  #4F4F4F;
        color: white;
        margin-top: 22px;

        margin-right: 15px;
    }
    .initial-data-time-stamp-pdp{
        width: 190px;
        margin-right: 15px;
        padding-top: 0;
    }

    .launch-planned-time-pdp{
        width: 190px;
        margin-left: 15px;
        padding-top: 0;

    }
    .order-valid-until-pdp{
        width: 190px;
        margin-right: 15px;
        padding-top: 0;

    }
    .initial-data-readiness-pdp{
        width: 190px;
        padding-top: 0;
        margin-left: 15px;
    }
    .initial-data-readiness-pdp::part(label){

        width: 200px !important;
    }
    .initial-data-latitude-pdp{
        width: 190px;
        margin-left: 15px;
        padding-top: 0;
        margin-top: 5px;
    }
    .initial-data-latitude-pdp::part(label){
        margin-top: 2px !important;
    }
    .initial-data-latitude-pdp > input:placeholder-shown{
        color: rgb(135 135 135);
    }
    .initial-data-altitude-pdp{
        width: 120px;
        margin-right: 15px;
        padding-top: 0;
        margin-top: 5px;
    }
    .initial-data-altitude-pdp > input:placeholder-shown{
        color: rgb(135 135 135);
    }
    .initial-data-longitude-pdp{
        width: 190px;
        margin-left: 15px;
        padding-top: 0;
    }
    .initial-data-longitude-pdp > input:placeholder-shown{
        color: rgb(135 135 135);
    }
    .initial-data-inclination-angle-pdp{
        width: 120px;
        margin-right: 15px;
        padding-top: 0;
    }
    .initial-data-inclination-angle-pdp > input:placeholder-shown{
        color: rgb(135 135 135);
    }

    .otr-vd-layout-pdp{
        border: solid 1px #363A40;
        border-radius: 5px;
        width: 420px !important;
        display: flex;
        flex-wrap: wrap;
        gap: 0.1em;
        justify-content:space-between;
        font-size: 16px !important;
    }
    .rocket-form-data-view{
        border: solid 1px #363A40;
        border-radius: 5px;
        gap: 1.5em;
    }
    .otr-purpose-type{
        padding: 0;
        margin-top: -10px;
    }
    .otr-purpose-type::part(label){
        align-self: center;
    }
    .is-telemetry-integrated{
        padding: 0;
        margin-top: -10px;
    }
    .is-telemetry-integrated::part(label){
        align-self: center;
    }
    .alp-type{
        padding: 0;
        margin-top: -10px;
    }
    .alp-type::part(label){
        align-self: center;
    }
    .gsn-type{

        padding: 0;
        margin-top: -10px;
    }
    .gsn-type::part(label){
        align-self: center;
    }
    .warhead-type{

        padding: 0;
        margin-top: -10px;
    }
    .warhead-type::part(label){
        align-self: center;
    }
    .plant-missile-form-data{
        padding-top: 0;
    }
    .created-at{
        padding-top: 0;
    }
    .rocket-form-data-pdp-view{
        border: solid 1px #363A40;
        border-radius: 5px;
        width: 390px !important;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        flex-direction: initial;
        height: 250px;
        gap: 0.6em;

    }
    .latitude{
        margin-top: -25px;
        --lumo-font-size-m:  15px;
        --lumo-font-size-s: 14px;
        height: 45px;
        width: 160px !important;
    }
    .latitude::part(input-field){
        height: 30px;
    }
    .altitude{
        margin-top: -25px;
        --lumo-font-size-m:  15px;
        --lumo-font-size-s: 14px;
        height: 45px;
        width: 160px !important;
    }
    .altitude::part(input-field){
        height: 30px;
    }
    .longitude{
        margin-top: -25px;
        --lumo-font-size-m:  15px;
        --lumo-font-size-s: 14px;
        height: 45px;
        width: 160px !important;
    }
    .longitude::part(input-field){
        height: 30px;
    }
    .bins-gps-status{
        --lumo-font-size-m:  15px;
        --lumo-font-size-s: 14px;
        margin-top: -25px;
        height: 45px;
        width: 160px !important;
    }
    .bins-gps-status::part(input-field){
        height: 30px;
    }
    .bins-calibration-status{
        --lumo-font-size-m:  15px;
        --lumo-font-size-s: 14px;
        margin-top: -25px;
        height: 45px;
        width: 160px !important;
    }
    .bins-calibration-status::part(input-field){
        height: 30px;
    }
    .coordinate-SPL{
        margin: 0px 25px 0 15px;
        position: absolute;
        top: 30px;
        left: 430px;
    }
    .ncok-commands-tab-layout{
        max-height: 570px;
        width: 100% !important;
    }
    .ncok-commands-tab{
        padding: 0;
        width: 100% !important;
    }
    .third-column-readiness{
        padding: 8px;
    }
    .readiness-command-grid{
        margin-top: 45.5%;
        width: 600px;
        margin-left: 70%;
        max-height: 389px;
        border-color: #2a2a2b;
        border-width: 2px !important;
        --lumo-contrast-10pct: #2a2a2b;
    }
    .navigation-rest-log-tab{
        padding: 7.8px;
        --lumo-font-size-m:  16px;
        --lumo-font-size-s: 16px;
    }
    .commands-grid-sae{
        color: #363A40;
        height: 595px;
        border-color: #2a2a2b;
        border-width: 2px !important;
        --lumo-contrast-10pct: #2a2a2b;
        --lumo-font-size-m:  18px;
        --lumo-font-size-s: 18px;
    }
    .commands-grid-sae::part(cell header-cell){
        border-right: solid #2a2a2b 1px;
        border-bottom:solid #2a2a2b 2px ;
    }
    .readines-tips-text-area{
        width: 350px;

    }
    .first-row-readiness{
        display: flex;
        justify-content: space-between;
        margin-right: 130px;
        --lumo-font-size-m:  18px;
        --lumo-font-size-s: 18px;
        --lumo-font-size-xs: 18px;
        --lumo-font-size-xxs: 18px;
    }

    .first-column-second-row-readiness{
        position: absolute;
        top: 16%;
        left: 23%;
        width: 200px !important;
        --lumo-font-size-m:  18px;
        --lumo-font-size-s: 18px;
    }
    .readiness-combobox{
        width: 220px;
        --lumo-font-size-m:  18px;
        --lumo-font-size-s: 18px;
    }
    .readiness-combobox::part(overlay){
        --lumo-font-size-m:  18px;
        --lumo-font-size-s: 18px;
    }
    .readiness-combobox::part(input-field){
        height: 35px;
    }
    .send-bt-row-readiness{
        position: absolute;
        top: 116px;
        left: 750px;
        width: 250px !important;
        --lumo-font-size-m:  18px;
        --lumo-font-size-s: 18px;
        --lumo-font-size-xs: 18px;
        --lumo-font-size-xxs: 18px;
        z-index: 4;
    }
    .send-sentence-button-command{
        height: 35px;
        --lumo-font-size-s:  18px;
    }
    .send-sentence-button{

        --lumo-font-size-s:  18px;
    }
    .filter-text-field{
        margin-left: 12px;
        width: 18%;
    }
    .filter-text-field::part(input-field){
        height: 42px;
    }
    .system-filter-cb{
        width: 40%;
    }
    .system-filter-cb::part(input){
        width: 30%;
    }
    .system-filter-cb::part(input-field){
        height: 42px;
    }
    .vaadin-multi-select-combo-box-container{

    }
    .status-cb{
        width: 40%;
    }
    .status-cb::part(input){
        width: 30%;
    }
    .status-cb::part(input-field){
        height: 42px;
    }

    .clear-button{
        margin-top: 8px;
        margin-left: 33%;
    }
    .clear-button::part(input-field){
        width: 150px;
    }
    .refresh-button{
        width: 140px;
        margin-left: 15px;
    }
    .refresh-export-button{
        padding: 0;
        width: 200px !important;
        margin-left: 25px;
    }
    .grid-layout-log{
        width: 99%;
        margin: 8.8px 10px;
        hight: 550px;
    }
    .grid-filters-buttons-layout{
        width: 100%;
        margin-left: 8.8px;
        display: flex;
        align-items: center;
    }
    .grid-filter-layout{
        width: 100%;

    }
    .log-layout-all{
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        /*height: 862px;*/
        gap: 0;
        height: 835px;
        width: 99% !important;
        /* overflow-y: hidden;*/
        margin: 8.8px 8.8px 0px 8.8px;
        --lumo-font-size-m:  18px;
        --lumo-font-size-s: 18px;
        --lumo-font-size-xs: 18px;
        --lumo-font-size-xxs: 18px
    }
    .nav-rest-log-tab{
        height: 644px;
    }
    .bins-log-tab{
        height: 660px;
    }
    .log-tab{
        padding: 0;
    }
    .msu-log-grid{
        height: 590px;
        color: rgb(54, 58, 64);
    }
    .adj-rest-log-tab{
        /*padding: 7.8px;*/
        --lumo-font-size-m: 16px;
        --lumo-font-size-s: 16px;
    }
    .msu-log-tab{
        height: 585px;
    }
    .msu-rest-log-tab{
        height: 580px;
    }
    .coord-start-down{
        display: flex;
        justify-content:center;
        width: 100% !important;
    }

    .coord-button-down{
        display: flex;
        justify-content: end;
        width: 100%;
        padding-right: 20%;
    }
    .coord-manual{
        display: flex;
        justify-content: center;
        width: 100%;
    }
    .coord-sentence{
        display: flex;
        justify-content: center;
        width: 100%;
    }
    .pmp-text{
        display: flex;
        align-self: center;
        width: 100%;
    }
    .hpr-text{
        display: flex;
        align-self: center;
        width: 100%;
    }
    .rmc-text{
        display: flex;
        align-self: center;
        width: 100%;
    }
    .gga-text{
        display: flex;
        align-self: center;
        width: 100%;
    }
    .coord-data{
        display: flex;
        justify-content: center;
        width: 100%;
    }
    .precision-layout{
        display: flex;
        justify-content: center;
        width: 100%;
    }
    .bins-data-layout{
        padding: 0;
        max-width: 100%;
        display: flex;
        align-items: center;
    }
   .coord-part{
        display: flex;
        justify-content: center;
        width: 100%;
    }
    .command-tab-bins{
        padding: 0;
        padding-left: 8.8px;
       --lumo-font-size-s: 18px;
        --lumo-font-size-m: 18px;
       font-size: 18px;
    }
    .coord-time{
        display: flex;
        justify-content: center;
        width: 100%;
    }
    .coord-status{
        display: flex;
        justify-content: center;
        width: 100%;
    }
    .sae-status-tab-fourth-column{
        display: flex;
        align-items: center;
        gap: 0.1em;
    }
    .sae-status-tab-third-column{
        display: flex;
        align-items: center;
        gap: 0.1em;
    }
    .sae-status-tab-second-column{
        display: flex;
        align-items: center;
        gap: 0.1em;
    }
    .sae-status-tab-first-column{
        display: flex;
        align-items: center;
        gap: 0.1em;
    }
    .sae-status-tab-first-row{
        gap: 0.1em;
        min-width: 100%;
        font-size: 18px;
        --lumo-font-size-s: 18px;
    }
    .sae-status-tab-second-row{
        gap: 0.1em;
        min-width: 100%;
        justify-content: space-between;
       padding: 0 3.5%;
        font-size: 18px;
        --lumo-font-size-s: 18px;
    }
    .command-tab-suto{
        padding: 0;
    }
    .commands-caption{
        color: #1C2E45;
        font-size: 18px;
    }
    .commands-grid-suto{
        color: #363A40;
        height: 525px;
        width: 100%;
        border-color: #2a2a2b;
        border-width: 2px !important;
        --lumo-contrast-10pct: #2a2a2b;
        font-size: 18px;
    }
    .commands-grid-suto::part(cell header-cell){
        height: 45px;
        border-right: solid #2a2a2b 1px;
        border-bottom:solid #2a2a2b 2px ;
    }
    .tpcs{
        display: flex;
        justify-content: space-between;
    }
    .suto-status-tab-second{
        gap: 6em;
    }
    .suto-status-tab-third{
        gap: 6em;
    }
    .suto-status-tab-first-column{
        gap: 22em;
    }
    .suto-status-tab-second-column{
        gap: 22em;
    }
    .suto-status{
        gap: 0em;
        padding: 0px;
        margin-top: -17.4px;
    }
    .pillar-BR{
        width: 400px;
        padding: 0;
        height: 55px;
        padding-top:8.8px;
        margin-bottom: 8.8px;
    }
    .pillar-BR::part(input-field){
        height: 32px;
    }
    .pillar-BL{
        width: 400px;
        padding: 0;
        height: 55px;
        padding-top:8.8px;
        margin-bottom: 8.8px;

    }
    .pillar-BL::part(input-field){
        height: 32px;
    }
    .pillar-FL{
        width: 400px;
        padding: 0;
        height: 55px;
        padding-top:17.6px;
        margin-top: -8.8px;
    }
    .pillar-FL::part(input-field){
        height: 32px;
    }
    .pillar-FR{
        width: 400px;
        padding: 0;
        height: 55px;
        padding-top:17.6px;
        margin-top: -8.8px;
        margin-bottom: 8.8px;
    }
    .pillar-FR::part(input-field){
        height: 32px;
    }
    .roll-SUTO{
        width: 180px;
        padding: 0;
        height: 55px;
        padding-top:17.6px;
        margin-top: -8.8px;
    }
    .roll-SUTO::part(input-field){
        height: 32px;
    }
    .pitch-SUTO{
        width: 180px;
        padding: 0;
        height: 55px;
        padding-top:17.6px;
        margin-top: -8.8px;
    }
    .pitch-SUTO::part(input-field){
        height: 32px;
    }
    .arrow-SUTO{
        width: 230px;
        padding: 0;
        height: 55px;
        padding-top:17.6px;
        margin-top: -17.6px;
    }
    .arrow-SUTO::part(input-field){
        height: 32px;
    }
    .temperature-RR{
        width: 180px;
        padding: 0;
        height: 55px;
        padding-top:17.6px;
        margin-top: -8.8px;
    }
    .temperature-RR::part(input-field){
        height: 32px;
    }
    .level-working-fluid{
        width: 180px;
        padding: 0;
        height: 55px;
        padding-top:17.6px;
        margin-top: -8.8px;
    }
    .level-working-fluid::part(input-field){
        height: 32px;
    }
    .main-pump-speed{
        width: 180px;
        padding: 0;
        height: 55px;
        padding-top:17.6px;
        margin-top: -8.8px;
    }
    .main-pump-speed::part(input-field){
        height: 32px;
    }
    .pressure-impulse-section{
        width: 180px;
        padding: 0;
        height: 55px;
        padding-top:17.6px;
        margin-top: -8.8px;
    }
    .pressure-impulse-section::part(input-field){
        height: 32px;
    }
    .number-leveling-cycles{
        width: 230px;
        padding: 0;
        height: 55px;
        padding-top:17.6px;
        margin-top: -8.8px;
    }
    .number-leveling-cycles::part(input-field){
        height: 32px;
    }
    .runtime-counter{
        width: 180px;
        padding: 0;
        height: 55px;
        padding-top:17.6px;
        margin-top: -8.8px;
    }
    .runtime-counter::part(input-field){
        height: 32px;
    }
    .number-arrow-lifts{
        width: 180px;
        padding: 0;
        height: 55px;
        padding-top:17.6px;
        margin-top: -8.8px;
    }
    .number-arrow-lifts::part(input-field){
        height: 32px;
    }
    .ppo-status-tab-first{
        padding: 0;
        gap:0;
        height: 210px;
        margin-left: 50px;
    }
    .ppo-status-tab-first-column{
        display: flex;
        justify-content: space-between;
        width: 100%;
        gap:0.1em;
    }
    .status-cell-ASKU{
        align-self: center;
        font-size: 16px;
    }
    .ppo-status-tab-second-column{
        display: flex;
        flex-wrap:wrap;
        border: solid 1px #363A40;
        justify-content:space-around;
        height: 160px;
        padding: 0;
        width: 100%;
        gap:0.1em;
    }
    .status-of-cell-ASKU{
        width: 180px;
        padding: 0;
        height: 28px;
    }
    .status-of-cell-ASKU::part(input-field){
        height: 25px;
    }
    .balloon-ASKU-1{
        width: 180px;
        padding: 0;
        padding-top:17.6px;
        height: 41px;
        margin-top: -7px;
        font-size: 16px;
    }
    .balloon-ASKU-1::part(input-field){
        height: 25px;
        font-size: 16px;
    }
    .balloon-ASKU-1::part(label){
        font-size: 16px;
    }
    .balloon-ASKU-2{
        width: 180px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        padding-top:17.6px;
        font-size: 16px;
    }
    .balloon-ASKU-2::part(input-field){
        height: 25px;
        font-size: 16px;
    }
    .balloon-ASKU-2::part(label){
        font-size: 16px;
    }
    .optical-sensor-ASKU-1{
        width: 180px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        font-size: 16px;
    }
    .optical-sensor-ASKU-1::part(input-field){
        height: 25px;
        font-size: 16px;
    }
    .optical-sensor-ASKU-1::part(label){
        font-size: 16px;
        width: 180px;
    }
    .optical-sensor-ASKU-2{
        height: 41px;
        margin-top: -7px;
        padding: 0;
        width: 180px;
        font-size: 16px;
    }
    .optical-sensor-ASKU-2::part(input-field){
        height: 25px;
        font-size: 16px;
    }
    .optical-sensor-ASKU-2::part(label){
        font-size: 16px;
        width: 180px;
    }
    .optical-sensor-ASKU-3{
        height: 41px;
        margin-top: -7px;
        padding: 0;
        width: 180px;
        font-size: 16px;
    }
    .optical-sensor-ASKU-3::part(input-field){
        height: 25px;
        font-size: 16px;
    }
    .optical-sensor-ASKU-3::part(label){
        font-size: 16px;
        width: 180px;
    }
    .optical-sensor-ASKU-4{
        height: 41px;
        margin-top: -7px;
        padding: 0;
        width: 180px;
        font-size: 16px;
    }
    .optical-sensor-ASKU-4::part(input-field){
        height: 25px;
        font-size: 16px;
    }
    .optical-sensor-ASKU-4::part(label){
        font-size: 16px;
        width: 180px;
    }
    .ppo-status-tab-second{
        padding: 0;
        gap:0;
        height: 210px;
        margin-left: 50px;

    }
    .ppo-status-tab-third-column{
        display: flex;
        justify-content: space-between;
        width: 100%;
        gap:0.1em;
    }
    .status-cell-NPPA{
        align-self: center;
    }
    .ppo-status-tab-fourth-column{
        display: flex;
        flex-wrap:wrap;
        border: solid 1px #363A40;
        justify-content:space-around;
        height: 160px;
        padding: 0;
        width: 100%;
        gap:0.1em;
    }
    .status-of-cell-NPPA{
        width: 180px;
        padding: 0;
        height: 28px;
    }
    .status-of-cell-NPPA::part(input-field){
        height: 25px;
    }
    .balloon-NPPA-1{
        width: 180px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        padding-top:17.6px;
        font-size: 16px;
    }
    .balloon-NPPA-1::part(input-field){
        height: 25px;
        font-size: 16px;
    }
    .balloon-NPPA-1::part(label){
        font-size: 16px;
    }
    .balloon-NPPA-2{
        width: 180px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        padding-top:17.6px;
        font-size: 16px;
    }
    .balloon-NPPA-2::part(input-field){
        height: 25px;
        font-size: 13px;
    }
    .balloon-NPPA-2::part(label){
        font-size: 16px;
    }
    .optical-sensor-NPPA-1{
        height: 41px;
        margin-top: -7px;
        padding: 0;
        width: 180px;
        font-size: 16px;
    }
    .optical-sensor-NPPA-1::part(input-field){
        height: 25px;
        font-size: 16px;
    }
    .optical-sensor-NPPA-1::part(label){
        font-size: 16px;
        width: 180px;
    }
    .optical-sensor-NPPA-2{
        height: 41px;
        margin-top: -7px;
        padding: 0;
        width: 180px;
        font-size: 16px;

    }
    .optical-sensor-NPPA-2::part(input-field){
        height: 25px;
        font-size: 16px;
    }
    .optical-sensor-NPPA-2::part(label){
        font-size: 16px;
        width: 180px;
    }
    .optical-sensor-NPPA-3{
        height: 41px;
        margin-top: -7px;
        padding: 0;
        width: 180px;
        font-size: 16px;
    }
    .optical-sensor-NPPA-3::part(input-field){
        height: 25px;
        font-size: 16px;
    }
    .optical-sensor-NPPA-3::part(label){
        font-size: 16px;
        width: 180px;
    }
    .ppo-status-tab-third{
        padding: 0;
        gap:0;
        margin-left: 45px;
    }
    .ppo-status-tab-fifth-column{
        display: flex;
        justify-content: space-between;
        width: 100%;
        margin-top: -14.7px;
    }
    .status-cell-ASKU{
        align-self: center;
    }
    .ppo-status-tab-sixth-column{
        display: flex;
        flex-wrap:wrap;
        border: solid 1px #363A40;
        justify-content:space-around;
        height: 160px;
        padding: 0;
        width: 100%;
        gap:0;
    }
    .status-of-cell-DEA{
        width: 180px;
        padding: 0;
        height: 28px;
    }
    .status-of-cell-DEA::part(input-field){
        height: 25px;
    }
    .balloon-DEA-1{
        width: 180px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        padding-top:17.6px;
        font-size: 16px;
    }
    .balloon-DEA-1::part(input-field){
        height: 25px;
        font-size: 16px;
    }
    .balloon-DEA-1::part(label){
        font-size: 16px;
    }
    .balloon-DEA-2{
        width: 180px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        padding-top:17.6px;
        font-size: 16px;
    }
    .balloon-DEA-2::part(input-field){
        height: 25px;
        font-size: 16px;
    }
    .balloon-DEA-2::part(label){
        font-size: 16px;
    }
    .thermal-sensor-DEA-1{
        width: 180px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        font-size: 16px;
    }
    .thermal-sensor-DEA-1::part(input-field){
        height: 25px;
        font-size: 16px;
    }
    .thermal-sensor-DEA-1::part(label){
        font-size: 16px;
    }
    .thermal-sensor-DEA-2{
        width: 180px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        font-size: 16px;
    }
    .thermal-sensor-DEA-2::part(input-field){
        height: 25px;
        font-size: 16px;
    }
    .thermal-sensor-DEA-2::part(label){
        font-size: 16px;
    }
    .thermal-sensor-DEA-3{
        width: 180px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        font-size: 16px;
    }
    .thermal-sensor-DEA-3::part(input-field){
        height: 25px;
        font-size: 16px;
    }
    .thermal-sensor-DEA-3::part(label){
        font-size: 16px;
    }
    .ppo-status-tab-third{
        padding: 0;
        gap:0;
    }
    .ppo-status-tab-fourth{
        padding: 0;
        gap:0;
        margin-left: 45px;
    }
    .ppo-status-tab-seventh-column{
        display: flex;
        justify-content: space-between;
        width: 100%;
        margin-top: -14.7px;
    }
    .status-cell-TO{
        align-self: center;
    }
    .ppo-status-tab-eighth-column{
        display: flex;
        flex-wrap:wrap;
        border: solid 1px #363A40;
        justify-content:space-around;
        height: 160px;
        padding: 0;
        width: 100%;
        gap:0;
    }
    .status-of-cell-TO{
        width: 180px;
        padding: 0;
        height: 28px;
    }
    .status-of-cell-TO::part(input-field){
        height: 25px;
    }
    .balloon-TO-1{
        width: 180px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        padding-top:17.6px;
        font-size: 16px;
    }
    .balloon-TO-1::part(input-field){
        height: 25px;
        font-size: 16px;
    }
    .balloon-TO-1::part(label){
        font-size: 16px;
    }
    .balloon-TO-2{
        width: 180px;
        padding: 0;
        height: 41px;
        margin-top: -7px;;
        padding-top:17.6px;
        font-size: 16px;
    }
    .balloon-TO-2::part(input-field){
        height: 25px;
        font-size: 16px;
    }
    .balloon-TO-2::part(label){
        font-size: 16px;
    }
    .thermal-sensor-TO-1{
        width: 180px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        font-size: 16px;
    }
    .thermal-sensor-TO-1::part(input-field){
        height: 25px;
        font-size: 16px;
    }
    .thermal-sensor-TO-1::part(label){
        font-size: 16px;
    }
    .thermal-sensor-TO-2{
        width: 180px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        font-size: 16px;
    }
    .thermal-sensor-TO-2::part(input-field){
        height: 25px;
        font-size: 16px;
    }
    .thermal-sensor-TO-2::part(label){
        font-size: 16px;
    }
    .thermal-sensor-TO-3{
        width: 180px;
        padding: 0;
        height: 41px;
        margin-top: -7px;
        font-size: 16px;
    }
    .thermal-sensor-TO-3::part(input-field){
        height: 25px;
        font-size: 16px;
    }
    .thermal-sensor-TO-3::part(label){
        font-size: 16px;
    }
    .ppo-status{
        display: flex;
        flex-wrap: wrap;
        gap: 0em;
        margin-bottom: 33px;
        --lumo-font-size-s: 16px;
        --lumo-font-size-m: 16px;
        justify-content: space-around;
    }
    .msu-null-row{
        gap: 9em !important;
    }
    .is-normal{

    }
    .wind-direction{

    }
    .wind-speed{

    }
    .air-temperature{

    }
    .msu-first-row{
        gap: 9em !important;
    }
    .voltage{

    }
    .msu-temperature{

    }
    .posture::part(input-field){
        text-transform: capitalize !important;

    }
    .posture{

    }
    .heating-mode{

    }
    .heating-mode-permission{

    }
    .temperature-sensor-blowing{

    }
    .msu-second-row{
        gap: 9em !important;
    }
    .end-switches-malfunction{

    }
    .end-switches-malfunction::part(label){

    }
    .posture-switching-malfunction{

    }
    .posture-switching-malfunction::part(label){

    }
    .wind-speed-sensor-malfunction{

    }
    .wind-speed-sensor-malfunction::part(label){

    }
    .data-exchange-malfunction{

    }
    .data-exchange-malfunction::part(label){


    }
    .msu-third-row{
        gap: 9em !important;
    }
    .temperature-sensor-malfunction{

    }
    .electrical-drive-malfunction{

    }
    .voltage-malfunction{

    }
    .msu-fourth-row{
        gap: 9em !important;
    }
    .systems-tab-panel{
        color: #1C2E45;
        padding:0;
        position: sticky;
        top: 0px;
        z-index: 100;
    }
    .tl-keys-plc-main-layout{
        margin-left: -5px;
    }
    .tl-keys-plc-grid{
        height: 610px;
        width: 100%;
    }
    .connection-byn{
        width: 180px;
        padding: 0;
        height: 55px;
    }
    .connection-byn::part(input-field){
        height: 32px;
    }
    .connection-byn::part(label){
        font-size: 18px;
    }
    .form-data-header-left{
        padding: 0;
        display: flex;
        align-items: center;
        font-size: 18px;
        gap: 0.5em;
    }
    .form-data-header-right{
        padding: 0;
        display: flex;
        align-items: center;
        font-size: 18px;
    }
    .otr-tab{
        padding: 20px 0px;
        gap:0.1em;
        display: flex;
        align-items: center;
        font-size: 18px;
    }
    .initial-data-latitude{
        width: 300px;
        margin-left: 15px;
        padding-top: 0;
        margin-top: 5px;
    }
    .initial-data-latitude::part(label){
        margin-top: 2px !important;
    }
    .initial-data-latitude > input:placeholder-shown{
        color: rgb(135 135 135);
    }
    .initial-data-altitude{
        width: 120px;
        margin-right: 15px;
        padding-top: 0;
        margin-top: 5px;
    }
    .initial-data-altitude > input:placeholder-shown{
        color: rgb(135 135 135);
    }
    .initial-data-longitude{
        width: 300px;
        margin-left: 15px;
        padding-top: 0;
    }
    .initial-data-longitude > input:placeholder-shown{
        color: rgb(135 135 135);
    }
    .initial-data-inclination-angle{
        width: 120px;
        margin-right: 15px;
        padding-top: 0;
    }
    .initial-data-inclination-angle > input:placeholder-shown{
        color: rgb(135 135 135);
    }
    .initial-data-missile-operating-mode{
        width: 300px;
        margin-left: 15px;
        padding-top: 0;
    }
    .initial-data-is-pro-detected{
        width: 250px;
        margin-right: 15px;
        padding-top: 0;
    }
    .initial-data-is-pro-detected::part(label){

        width: 200px !important;
    }
    .initial-data-load-temperature{
        width: 200px;
        margin-left: 15px;
        padding-top: 0;
    }
    .initial-data-load-temperature::part(label){

        width: 200px;
    }
    .initial-data-readiness{
        width: 200px;
        padding-top: 0;
        margin-left: 15px;
    }
    .initial-data-readiness::part(label){

        width: 200px !important;
    }
    .initial-data-trajectory{
        width: 230px;
        margin-right: 15px;
        padding-top: 0;
    }
    .initial-data-time-stamp{
        width: 227px;
        margin-left: 15px;
        padding-top: 0;
        margin-right: 80px ;
    }
    .is-launch-scheduled{
        width: 130px;
        margin-right: 15px;
        padding-top: 0;

    }
    .launch-planned-time{
        width: 227px;
        margin-left: 15px;
        padding-top: 0;

    }
    .order-valid-until{
        width: 227px;
        margin-right: 15px;
        padding-top: 0;

    }
    .enable-initial-data-editing{
        margin-left: 15px;
        /*border: solid 0.5px  #363A40;*/
    }
    .change-initial-data{
        border-radius: 4px;
        background-color:  #4F4F4F;
        color: white;
        margin-top: 5px;
        margin-bottom: 10px;
        margin-left: 15px;
    }
    .form-data-otr{

    }
    .preparation-for-launch-second-row-OTR1{
        /*height: 300px;*/
        margin-top: -25px;
    }
    .preparation-for-launch-second-row-OTR2{
        /*height: 300px;*/
        margin-top: -25px;
    }
    .initial-data-dialog{
        gap:0em;
        z-index: 1;

    }
    .title-initial-data{
        padding-left: 10px;
    }
    .launch-form-data-view{
        border: solid 1px #363A40;
        border-radius: 5px;
        gap: 0.6em;
        height: 622px;
    }
    .otr-vd-layout{
        border: solid 1px #363A40;
        border-radius: 5px;
        width: 506px !important;
        display: flex;
        flex-wrap: wrap;
        gap: 0.1em;
        justify-content:space-between;
        font-size: 16px !important;
    }
    .missile-right{
        margin: 3px 0px 0px 43px;
        height: 25px;
    }
    .app-otr-right{
        width: 180px;
        padding: 0;
        height: 55px;
    }
    .app-otr-right::part(input-field){
        height: 32px;
    }
    .launch-otr-right{
        width: 180px;
        padding: 0;
        height: 55px;
    }
    .launch-otr-right::part(input-field){
        height: 32px;
    }
    .check-otr-right{
        width: 180px;
        padding: 0;
        height: 55px;
    }
    .check-otr-right::part(input-field){
        height: 32px;
    }
    .app-otr-left{
        width: 180px;
        padding: 0;
        height: 55px;
    }
    .app-otr-left::part(input-field){
        height: 32px;
    }
    .launch-otr-left{
        width: 180px;
        padding: 0;
        height: 55px;
    }
    .launch-otr-left::part(input-field){
        height: 32px;
    }
    .check-otr-left{
        width: 180px;
        padding: 0;
        height: 55px;
    }
    .check-otr-left::part(input-field){
        height: 32px;
    }
    .app-nppa{
        width: 180px;
        padding: 0;
        height: 55px;
    }
    .app-nppa::part(input-field){
        height: 32px;
    }
    .check-nppa{
        width: 180px;
        padding: 0;
        height: 55px;
    }
    .check-nppa::part(input-field){
        height: 32px;
    }
    .connection-suto{
        width: 180px;
        padding: 0;
        height: 55px;
    }
    .connection-suto::part(input-field){
        height: 32px;
    }
    .tv-ncok{
        width: 180px;
        padding: 0;
        height: 55px;
    }
    .tv-ncok::part(input-field){
        height: 32px;
    }
    .connection-ncok{
        width: 180px;
        padding: 0;
        height: 55px;
    }
    .connection-ncok::part(input-field){
        height: 32px;
    }
    .readiness-layout{
        scale: 110% 110%;
        margin-top: 2.5%;
        margin-left: 26%;
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        gap: 0;
        height: 761px;
        width: 100%;
        display: flex;
        justify-content: space-between;
    }
    .system-layout-tlc{
        scale: 110% 110%;
        margin-top: 2.5%;
        margin-left: 26%;
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        height: 761px;
        gap: 0;
        width: 100%;
        --lumo-font-size-m: 18px;
    }
    .system-layout-suto{
        scale: 110% 110%;
        margin-top: 2.5%;
        margin-left: 26%;
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        height: 761px;
        gap: 0;
        width: 100%;
        --lumo-font-size-m: 18px;
        --lumo-font-size-s: 18px;
    }
    .system-layout-sae{
        scale: 110% 110%;
        margin-top: 2.5%;
        margin-left: 26%;
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        height: 761px;
        gap: 0;
        width: 100%;
        --lumo-font-size-m: 18px;
        /*--lumo-size-s: 20px;*/
    }
    .system-layout-msu{
        scale: 110% 110%;
        margin-top: 2.5%;
        margin-left: 26%;
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        gap: 0;
        height: 761px;
        width: 100%;
        --lumo-font-size-m: 18px;
        --lumo-font-size-s: 16px;
    }
    .system-layout-ppo{
        scale: 110% 110%;
        margin-top: 2.5%;
        margin-left: 26%;
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        gap: 0;
        height: 761px;
        width: 100%;
        --lumo-font-size-m: 18px;
    }
    .system-layout-bins {
        /*scale: 110% 110%;*/
        margin-top: 0.5%;
        margin-left: 22.7%;
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        gap: 0;
        width: 100%;
        min-width: 77%;
        --lumo-font-size-m: 18px;
    }
    .systems-info {
        scale: 110%;
        margin-top: 2%;
        margin-left: 1%;
        --lumo-font-size-m: 18px;
        --lumo-font-size-s: 18px;
        margin-bottom: 0;
    }
    .system-layout-nppa{
        scale: 110% 110%;
        margin-top: 2.5%;
        margin-left: 26%;
        border: solid #363A40 1px;
        padding: 0;
        border-radius: 5px;
        height: 761px;
        gap: 0;
        width: 100%;
        --lumo-font-size-m: 18px;
        --lumo-font-size-s: 18px;
    }
    .top-of-tab-ncok{
        box-shadow: inset 0 -1px 0 0 var(--lumo-contrast-10pct);
        width: 100%;
        gap:60%;
    }
    .ncok-command-status-grid{
        height: 278px;
        width: 100%;
        border-color: #2a2a2b;
        border-width: 2px !important;
        --lumo-contrast-10pct: #2a2a2b;
        font-size: 18px;
    }
    .ncok-status-fields{
        margin-left: 5%;
        display: flex;
        align-self: stretch;
        --lumo-space-m : 2em;
    }
    .adjacent-systems-nav-list{
        display: flex;
        margin: 5px 0 0 0 ;
        border-radius: 5px 5px 0px 0px;
        height: 4.5% ;
        text-align: center;
    }
    .spl-name-out{
        padding: 0 8.8px;
        font-size: 18px;
    }
    .top-of-tab-ncok tablist{
        font-size: 18px;
    }
    .vehicle-id{
        width: 150px;
        color: #FFFFFF;
        flex: 0 1 25%;
        height: 55px !important;
        padding-bottom: 0;
        margin: -5px 0px 0px 23px;
    }
    .vehicle-id::part(label){
        align-self: center;
        width: 55px;
        text-align: center;
        color: #000;
        height: 20px !important;
        padding-left: 10px;
        padding-top: 0px ;
    }
    .vehicle-id[readonly]::part(input-field){
        height: 32px;
        width: 160px;
        align-self: center;
    }
    .ccv-master-id{
        width: 150px;
        color: #FFFFFF;
        flex: 0 1 25%;
        height: 55px !important;
        padding-bottom: 0;
        margin: -5px 0px 0px 17.3px;
    }
    .ccv-master-id::part(label){
        align-self: center;
        width: 55px;
        text-align: center;
        color: #000;
        height: 20px !important;
        padding-left: 10px;
        padding-top: 0px ;
    }
    .ccv-master-id[readonly]::part(input-field){
        height: 32px;
        width: 160px;
        align-self: center;
    }
    .start-date{
        height: 50px;
        flex: 0 1 25%;
        width: 150px;
        color: #FFFFFF;
        margin: 0px 0px 0px 23px;
        padding: 0px;
        text-align: center;

    }
    .start-date[readonly]::part(input-field){
        height: 32px;
        width: 160px;
        align-self: center;
    }
    .start-date::part(label){
        display: flex !important;
        align-self: center!important;
        width: 150px!important;
        text-align: center !important;
        color: #000;
        padding-right: 0px;
        font-size: 16px !important;
    }
    .start-time{
        height: 50px;
        flex: 0 1 25%;
        width: 150px;
        color: #FFFFFF;
        margin: 0px 0px 0px 11px;
        padding: 0px;
    }
    .start-time[readonly]::part(input-field){
        height: 32px;
        width: 160px;
    }
    .start-time::part(label){
        display: flex;
        align-self: center;
        width: 170px;
        text-align: center;
        color: #000;
        padding-bottom: 10px;

        font-size: 16px !important;
    }
    .plc-state{
        height: 50px;
        flex: 0 1 25%;
        width: 150px;
        color: #FFFFFF;
        margin: 0px 0px 0px 17.3px;
        padding: 0px;

    }
    .plc-state[readonly]::part(input-field){
        background-color: #00853D;
        height: 32px;
        width: 160px;
        color: white;
    }
    .plc-state::part(label){
        display: flex;
        align-self: center;
        width: 110px;
        text-align: center;
        color: #000;
        padding-left: 20px;
        font-size: 16px;
    }
    .ppo-state{
        height: 50px;
        flex: 0 1 25%;
        width: 150px;
        color: #FFFFFF;
        margin: 0px 0px 0px 17.3px;
        padding: 0px;
    }
    .ppo-state[readonly]::part(input-field){
        background-color: #00853D;
        height: 32px;
        width: 160px;
        color: white;
    }
    .ppo-state::part(label){
        display: flex;
        align-self: center;
        width: 50px;
        text-align: center;
        color: #000;
        padding-left: 15px;
        font-size: 16px;
    }
    .sae-state{
        height: 50px;
        flex: 0 1 25%;
        width: 150px;
        color: #FFFFFF;
        margin: 0px 0px 0px 17.3px;
        padding: 0px;
    }
    .sae-state[readonly]::part(input-field){
        background-color: #00853D;
        height: 32px;
        width: 160px;
        color: white;
    }
    .sae-state::part(label) {
        display: flex;
        align-self: center;
        width: 50px;
        text-align: center;
        color: #000;
        padding-left: 10px;
        font-size: 16px;
    }
    .bins-state{
        height: 50px;
        flex: 0 1 25%;
        width: 150px;
        color: #FFFFFF;
        margin: 0px 0px 0px 17.3px;
        padding: 0px;
    }
    .bins-state[readonly]::part(input-field){
        background-color: #00853D;
        height: 32px;
        width: 160px;
        color: white;
    }
    .bins-state::part(label){
        display: flex;
        align-self: center;
        width: 80px;
        text-align: center;
        color: #000;
        padding-left: 15px;
        font-size: 16px;
    }
    .suto-state{
        height: 50px;
        flex: 0 1 25%;
        width: 150px;
        color: #FFFFFF;
        margin: 0px 0px 0px 17.3px;
        padding: 0px;
    }
    .suto-state[readonly]::part(input-field){
        background-color: #00853D;
        height: 32px;
        width: 160px;
        color: white;
    }
    .suto-state::part(label){
        display: flex;
        align-self: center;
        width: 50px;
        text-align: center;
        color: #000;
        font-size: 16px;
    }
    .meteo-state {
        height: 50px;
        flex: 0 1 25%;
        width: 150px;
        color: #FFFFFF;
        margin: 0px 0px 0px 17.3px;
        padding: 0px;
    }
    .meteo-state[readonly]::part(input-field){
        background-color: #00853D;
        height: 32px;
        width: 160px;
        color: white;
    }
    .meteo-state::part(label){
        display: flex;
        align-self: center;
        width: 120px;
        text-align: center;
        color: #000;
        padding-left: 15px;
        font-size: 16px !important;
    }
    .meteo-state[readonly]::part(input-field){
        background-color: #00853D;
    }
    .byn-tf{
        height: 50px;
        flex: 0 1 25%;
        width: 150px;
        color: #FFFFFF;
        margin: 0px 0px 0px 17.3px;
        padding: 0px;
    }
    .byn-tf[readonly]::part(input-field){
        background-color: #00853D;
        height: 32px;
        width: 160px;
        color: white;
    }
    .byn-tf::part(label){
        display: flex;
        align-self: center;
        width: 50px;
        text-align: center;
        color: #000;
        padding-left: 10px;
        font-size: 16px;
    }
    .ncok-command-commands-grid{
        width: 100%;
        height: 100% !important;
        border-color: #2a2a2b;
        border-width: 2px !important;
        --lumo-contrast-10pct: #2a2a2b;
    }
    .ncok-log-tab{
        padding: 0;
        height: 575px;
        width: 100%!important;
    }
    .ncok-log-tab-layout{
        padding: 0;
        width: 100%!important;
    }
    .byn-tab{
        padding: 0px;
        margin-top: -8.7px;
        width: 100% !important;
    }
    .command-tab-byn{
        padding: 0px;
    }
    .top-of-tab-byn{
        box-shadow: inset 0 -1px 0 0 var(--lumo-contrast-10pct);
        gap:60em;
        width: 100%;
    }
    .toggle-button-byn::part(label){
        margin-right: 20px;
        width:200px;
    }
    .byn-status-tab{
        padding: 0;
        width: 100% !important;
    }
    .byn-status-tab-layout{
        padding: 0;
        width:  100% !important;
    }
    .byn-status-tab{
        width:  100% !important;
    }
    .byn-status-tab-first-column{
        width: 75% !important;
        margin-top: -8.7px;
        padding: 0 0;
    }
    .byn-status-tab-second-column{
        border: solid 1px #363A40;
        border-radius: 5px;
        margin-top: 0px;
        margin-left: -8.7px;
        width: 25% !important;
        height: 565px;
    }
    .byn-supply-label{
        width: 100% !important;
        gap: 0.7em;
        padding: 0;
        font-size: 16px;

    }
    .byn-availability-of-power-column{
        width: 100%;
    }
    .availability-of-power{
        font-size: 16px !important;
        align-self: center;
    }
    .availability-of-power::part(label){
        font-size: 16px !important;
        width: 200px;
    }
    .operating_mode::part(label){
        font-size: 16px;
    }
    .tv-byn{
        width: 180px;
        padding: 0;
        height: 55px;
        margin-bottom: 6.5%;
    }
    .tv-byn::part(input-field){
        height: 32px;
    }
    .byn-log-tab{
        padding: 0;
        height: 575px;
        width: 100%!important;
    }
    .byn-log-tab-layout{
        padding: 0;
        width: 100%!important;
    }
}

.setup-suto-properties-grid{

}

.close-button-notify-info{
    margin-left: 320px;
    color: black;
}
.close-button-notify-commit{
    margin-left: 185px;
    background-color: #ffffff;
    color: black;
}

.close-button-notify-warning{
    margin-left: 300px;
    background-color: #ffffff !important;
    border: solid white 1px;
    height: 32px;
    width: 100px;

}
.close-button-notify-error{
    margin-left: 339px;
    background-color: #ffffff;
    color: black;
}
.close-button-msg{
    margin-left: 439px;
    background-color: #ffffff;
    color: black;
}
.caption-message{
    align-items: center;
}
.plc-msg-notification{
    z-index: 10;
}
.dialog-nppa{
    z-index: 1111 ;
}
.dialog-bins{
    z-index: 1111;
}
.dialog-sae{
    z-index: 1111;
}
.dialog-update-suto-settings{
    z-index: 1111;
}
.dialog-suto{
    z-index: 1111;
}
.dialog-ppo{
    z-index: 1111;
}
.dialog-msu{
    z-index: 1111;
}
.plc-msg-notification::part(overlay){
    width: 500px;
}
.initial-data-validation-msg-notification{
    width: 600px;
}
.id-validation-msg-notification{
    width: 600px;
    z-index: 1112;
}
.plc-msg-notification::part(content){
    overflow-wrap: normal;
}
.adjacent-systems-status-panel{
    display: flex;
    flex-wrap:wrap;
    border: 1px solid #2a2a2b ;
    background: #FFFFFF;
    border-radius: 5px;
    height: 900px;
    gap: 0.3em;
    max-width: 369px;
    width: 369px;
    padding-bottom: 5px;

}

.systems-info{
position: fixed;
    padding-bottom: 0 ;
}
.spl_image{
    height: 174px;
    width: 369px;
    background-color: #9a9a9a;
    border-radius: 5px;
}

.ncok-tf{
    height: 50px;
    flex: 0 1 25%;
    width: 150px;
    color: #FFFFFF;
    margin: 0px 0px 0px 17.3px;
    padding: 0px;
}
.ncok-tf[readonly]::part(input-field){
    background-color: #00853D;
    height: 32px;
    width: 160px;
    color: white;
}
.ncok-tf::part(label){
    padding-left: 25px;
    align-self: center;
    width: 100px;
    text-align: center;
    color: #000;
}





.missile-h4-left{
    align-self: center;
    flex: 0 1 35%;
    margin: 10px 0px -10px 40px;
    font-weight: 500 !important;
}
.missile-h4-right{
    align-self: center;
    flex: 0 1 40%;
    margin: 10px 0px -10px 35px;
    font-weight: 500 !important;
}


.missile-state-left{
    height: 30px;
    flex: 0 1 25%;
    width: 80px;
    color: #FFFFFF;
    margin: 0px;
    padding: 0px;
}
.missile-state-left[readonly]::part(input-field){
    background-color: #00853D;
    height: 25px;
    width: 130px;
    color: white;
}
.missile-state-left::part(label) {
    display: flex;
    align-self: center;
    width: 50px;
    text-align: center;
    color: #000;
    padding-left: 10px;
}
.missile-left{
    margin: 3px 0px 0px 10px;
    height: 25px;
}
.missile-state-right{
    height: 30px;
    flex: 0 1 25%;
    width: 80px;
    color: #FFFFFF;
    margin: 0px;
    padding: 0px;
}
.missile-state-right[readonly]::part(input-field){
    background-color: #00853D;
    height: 25px;
    width: 130px;
    color: white;
}
.missile-state-right::part(label) {
    display: flex;
    align-self: center;
    width: 50px;
    text-align: center;
    color: #000;
    padding-left: 10px;
}



.missile-info-left{
    height: 28px;
  /*  flex: 0 1 45%;*/
    width: 160px;
    color: #FFFFFF;
    padding: 0px;
    background-color: #006AF5;
    margin: 5px 0 5px 17px;

}
.missile-info-left:hover{
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
}
.missile-info-right{
    height: 28px;
/*    flex: 0 1 45%;*/
    width: 160px;
    color: #FFFFFF;
    margin: 5px 0 5px 8px;
    padding: 0px;
    background-color: #006AF5;
}
.missile-info-right:hover{
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
}

.missile-left-icon{
    width: 60px;
    height: 60px;
    position: relative;
    top: 5px;
    left: 55px;
    color: rgba(0,133,61,0.65);
    transform: rotate(-45deg);
    border: 1px solid #363A40;
    padding: 10px;
    margin: 10px;
    flex-shrink: 0;
}

.missile-right-icon{
    width: 60px;
    height: 60px;
    position: relative;
    top: 5px;
    left: 145px;
    color: grey;
    transform: rotate(-45deg);
    border: 1px solid #363A40;
    padding: 10px;
    margin: 10px 60px 10px 10px;
}
.Icon-green{
    color: rgba(0,133,61,0.65);
}
.Icon-yellow{
    color: white;
}
.Icon-grey{
    color: white;
    border: 1px dashed #363A40;
}
.Icon-grey{
    color: white;
    border: 1px dashed #363A40;
}
.Icon-undefined{
    color: grey;
}

.missile-present-left{
    height: 38px;
    flex: 0 1 25%;
    width: 150px;
    color: #FFFFFF;
    margin: 12px 0px 0px 18px;
    padding: 0px;
}
.missile-present-left::part(input-field){
    background-color: #00853D;
    height: 32px;
    width: 160px;
    color: white;
}

.missile-present-right{
    height: 38px;
    flex: 0 1 25%;
    width: 150px;
    color: #FFFFFF;
    margin: 12px 0px 0px 17.3px;
    padding: 0px;

}
.missile-present-right::part(input-field){
    background-color: #C9BCBC;
    height: 32px;
    width: 160px;
    color: #363A40;
    border: 1px solid rgba(0, 0, 0, 0.2);
}

li {
    list-style-type: none; /* Убираем маркеры */
}
ul {
    padding-left: 0; /* Сдвигаем список влево */
    padding-bottom: 0;
    color: rgb(255,255,255, 0.6);
}

.adjacent-systems-nav nav a:focus-visible {
    box-shadow: 0 0 0 2px var(--lumo-primary-color-50pct);
}


.adjacent-systems-nav-list li a[highlight]{
    background-color: #FFFFFF;
    color: #363A40 ;
}


nav a:where(:any-link){
    color: rgb(255,255,255, 0.6);
    text-decoration: none;
    padding: 0 var(--lumo-space-s);
    outline: 0;
    background-color: #FFFFFF1A;
    border-radius: 5px 5px 0px 0px;
    height: 30px;
}

.adjacent-systems-nav-list li{

    flex-basis: 16.6%;
    border-radius: 5px 5px 0px 0px;
    height: 30px;
    text-decoration: none;
    padding: 0; /* var(--lumo-space-s);*/
    outline: 0;
    background-color: #FFFFFF1A;
    margin: 5px 0 0 0;
    display:flex;
    justify-content:center;
    border-right: 1px solid #363A40;
    border-left: 1px solid #363A40;

}
.adjacent-systems-nav-list li a{
    width: 100%;
    justify-content: center;
    align-items: center;
    display: flex;
}

.adjacent-systems-nav-list li:hover{
    border-radius: 5px 5px 0px 0px;
    background-color: grey;
    opacity: 0.8;
    height: 30px;
    margin: 5px 0 0 0;
    border-right: 1px solid #363A40 ;
    border-left: 1px solid #363A40 ;
}

.adjacent-systems-nav{
    width:100%;
    background-color: #363A40;
    min-height: 100%;
    height: 10%;
}

.system-layout{
    border: solid #363A40 1px;
    padding: 0;
    border-radius: 5px;
    /*height: 862px;*/
    gap: 0;
    height: 861px;
    width: 100%;
    /* overflow-y: hidden;*/
    margin: 8.8px 20px 0px 390px;
}


.tab-container{
    padding: 0;
    margin-bottom: -8.8px;
}

.command-tab{
    padding-top: 0;
    gap: 0.05em;
}
.tab-panel{
    padding: 0px;
}
.status-layout-v{
    padding: 0px ;
    gap: 0.2em;
    margin-top: -4px;
    margin-left: -8px;
    display: flex;
    align-items: center;
}



.msu-log-grid{
    color: #363A40;
}


.supply-from-28v{
    padding: 0px 8.8px;
}
.supply-from-aj-sae{
    padding: 0px 8.8px;
}

.supply-from-hds-sae{
    padding: 0px 8.8px;
}

.urgent-malfunction{
    padding: 0px 8.8px;
}
.status-tab{
    padding-left: 0;
    padding-top: 0;
}


.tab-panel-ppo{
    padding: 0;
    margin-top: -4px;
}

.command-tab-ppo{
    padding: 0;
    padding-left: 8.8px;

}

.commands-layout-h{
gap:0.1em;
}
.commands-layout-h-sae{
    margin-top: -8.7px;
}
.commands-layout-v{
    gap:0.1em;
}

vaadin-grid-cell-content{
    padding: 0 10px;
}
.ppo-commands-tab{
    padding: 0px 8.8px 0px 0px;
}
.commands-grid{
    color: #363A40;
    height: 205px;
    border-color: #2a2a2b;
    border-width: 2px !important;
    --lumo-contrast-10pct: #2a2a2b;
}
.commands-grid::part(cell header-cell){
    border-bottom:solid #2a2a2b 2px ;
}
.commands-grid-ppo{
    color: #363A40;
    height: 241px;
    border-color: #2a2a2b;
    border-width: 2px !important;
    --lumo-contrast-10pct: #2a2a2b;
}
.commands-grid-ppo::part(cell header-cell){
    border-bottom:solid #2a2a2b 2px ;
}



.suto-commands-tab{
    padding: 0px;
}
.sae-log-grid{
    height: 570px;
    color: #363A40;
    width: 815px;
}
.ppo-log-tab{
    height: 575px;
    color: #363A40;
}
.suto-log-tab{
    height: 570px;
    color: rgb(54, 58, 64);
}
.sae-log-tab{
    height: 580px;
    color: rgb(54, 58, 64);
}



.command-tab-sae{
    padding: 0;
}
.sae-command-tab{
    padding: 0;
}




.rest-log-tab{
    padding: 0;
}

.configure-sentence-time{
    padding: 0;
}
.navigation-data{
    width: 882px;
}

.sentence-name-bins{
    width: 130px;
}
.gga-text{
    width: 800px;
    max-width: 800px;
}
.rmc-text{
    width: 800px;
    max-width: 800px;
}
.hpr-text{
    width: 800px;
    max-width: 800px;
}
.pmp-text{
    width: 800px;
    max-width: 800px;
}


.button-box-upper{
    margin-top: 25px;
    margin-left: 270px;
}



.bins-calibrate-button{
    border: solid 1px #9a9a9a;
}

.update-tpc-bt{
    margin-top: 28px;
    margin-left: 95px;
}
.properties-grid{

    height: 315px;
    border-color: #2a2a2b;
    border-width: 2px !important;
    --lumo-contrast-10pct: #2a2a2b;
}
.properties-grid::part(cell header-cell){
    border-bottom:solid #2a2a2b 2px ;
}
.ncok-tab{
    padding: 0px;
    min-height: 100%;
    margin-top: -8.7px;
}
.command-tab-ncok{
    padding: 0px;
}

.toggle-button-ncok::part(label){
    margin-right: 20px;
    width: 200px;
}
.ncok-status-tab{
    padding: 0;
    width: 100%!important;
}

.ncok-status-tab-layout{
    padding: 0;
    /*width: 100%!important;*/
}

.ncok-command-status-grid::part(cell header-cell){
    border-bottom:solid #2a2a2b 2px ;
}





.ncok-command-commands-grid::part(cell header-cell){
    border-bottom:solid #2a2a2b 2px ;
}

.byn-field{
    width: 52px;
    height: 30px;
    padding: 0;
}
.byn-field::part(input-field){
    width: 52px;
    height: 30px;
}


.byn-supply-field{
    width: 55px !important;
    padding: 0;
    gap: 0.45em;
}



.byn{
    width: 180px;
    padding: 0;
    height: 50px;
}
.byn::part(input-field){
    height: 32px;
}

.byn-command-grid{
    margin-top: 146px;
    width: 460px;
    max-height: 241px;
    border-color: #2a2a2b;
    border-width: 2px !important;
    --lumo-contrast-10pct: #2a2a2b;
}

.byn-command-grid vaadin-grid::part(first-column-cell){
    width: 280px !important;
}
.byn-command-grid::part(cell header-cell){
    border-bottom:solid #2a2a2b 2px ;
}


.command-tab-otr{
    padding: 0;
    margin-left: 6px;
    display: flex;
    align-items: center;
    }

.rocket-initial-data-layout-left{
    padding: 0;
    display: flex;
    align-items: center;
}
.rocket-initial-data-layout-right{
    padding: 0;
    display: flex;
    align-items: center;
}
.rocket-form-data-view{
    border: solid 1px #363A40;
    border-radius: 5px;
}
.initial-data-source{
    width: 213px;
    margin-left: 15px;
}
.plant-missile{
    width: 140px;
    margin-left: 10px
   }
.plant-missile > input:placeholder-shown{
    color: rgb(135 135 135);
}
.initial-data-description{
    width: 363px;
    margin-left: 15px;
    padding-top: 0;
}
.initial-data-description > input:placeholder-shown{
    color: rgb(135 135 135);
}

.rocket-id{
    padding-top: 0;
}


.command-tab-id{
    padding: 0;
}

.means-of-injury{
display: flex;
align-self: center;
gap: 10em;
}

.tl-keys-plc-buttons{
    margin: 0 5px;
    align-items: flex-end;
}
.tl-signature{
    width: 363px;
    margin-left: 15px;
    padding-top: 0;
}
.tl-signature > input:placeholder-shown{
    color: rgb(135 135 135);
}
.tl-keys-plc-vertical-layout{
    padding: 0;
    margin: 0 5px;
}
.grid-filters-buttons-layout{
    display: flex;
    align-items: center;
}


.export-pdf-button{
    height: 36px;
}



.corrected-course{
    padding-top: 0;
    margin-top: 9px;
}


.send-sentence-button-ppip{
    color: white;
}
.send-sentence-button-command{
    color: white;
}
.check-step-1{
    transform: rotate(0deg);
    fill: #363A40;
    /* filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);*/
}

.launch-fourth-row-bg3{
    border-bottom: dashed #363A40 1px;
}
.send-launch-button{
    color: black;
}
/*.launch-OTR1-sixth-row{
    border-bottom: dashed #363A40 1px;
}*/
.folding-SPL-layout{
    border: solid #363A40 1px;
    padding: 0;
    border-radius: 5px;
    width: 1235px !important;
}
.note-launch-OTR1-from-BG2b-caption{
    width: 400px;
}

.preparation-for-launch-first-row-OTR1{
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-top: 17px;

}
.preparation-check-step-1{
    margin: 15px;
}


.lat-hemisphere{
    margin-top: -25px;
}

.lon-hemisphere{
    margin-top: -25px;
}

.precision-span{
    margin: 0px 25px 0 135px ;
}
.latitude-precision{
    margin-top: -25px;
}
.longitude-precision{
    margin-top: -25px;
}
.altitude-precision{
    margin-top: -25px;
}
.preparation-check-step-2{
    margin: 0px 15px 485px 15px;
}

.preparation-check-step-3{
    margin: 0px 15px 485px 15px;
}
.preparation-check-step-4{
    margin: 15px;
}
.preparation-for-launch-third-row{
    display: flex;
    align-items: center;
}


.preparation-check-step-5{
    margin: 15px;
}
.preparation-for-launch-fourth-row{
    display: flex;
    align-items: center;
}

.preparation-for-launch-five-row{
    display: flex;
    align-items: center;
}
.preparation-check-step-6{
    margin: 15px;
}

.preparation-for-launch-sixth-row{
    display: flex;
    align-items: center;
}
.preparation-check-step-7{
    margin: 15px;
}

.preparation-for-launch-seven-row{
    display: flex;
    align-items: center;
}
.operation-mode-byn-pdp{
    margin-top: -25px;
}
.operation-mode-byn-command-pdp{
    margin-left: 450px;
}
.operation-mode-ncok-command-pdp{
    margin-left: 450px;
}

.folding-SPL-first-row{
    display: flex;
    align-items: center;
}
.folding-check-step-1{
    margin: 15px;
}

.folding-SPL-second-row{
    display: flex;
    align-items: center;
}
.folding-check-step-2{
    margin: 15px;
}
.note-outriggers-DP{
    margin-left: 50px;
}
.bins-is-connected-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 15px 15px;
}
.bins-is-connected-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 15px 15px;
}
.bins-is-connected-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 15px 15px;
}

.ic-for-OTR1-loaded_to_plc-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 15px 15px;
}
.ic-for-OTR1-loaded_to_plc-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 15px 15px;
}
.ic-for-OTR1-loaded_to_plc-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 15px 15px;
}
.ic-for-OTR2-loaded_to_plc-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 15px 15px;
}
.ic-for-OTR2-loaded_to_plc-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 15px 15px;
}
.ic-for-OTR2-loaded_to_plc-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 15px 15px;
}

.check-OTR1-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.check-OTR1-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.check-OTR1-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 5px 15px;
}
.check-OTR2-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.check-OTR2-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.check-OTR2-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 15px;
}
.tv-byn-pre-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.tv-byn-pre-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.tv-byn-pre-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 5px 15px;
}
.mode-of-operation-byn-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.mode-of-operation-byn-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.mode-of-operation-byn-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 5px 15px;
}
.tv-ncok-launch-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.tv-ncok-launch-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.tv-ncok-launch-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 5px 15px;
}
.mode-of-operation-ncok-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.mode-of-operation-ncok-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.mode-of-operation-ncok-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 5px 15px;
}

.arrow-up-launch-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.arrow-up-launch-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.arrow-up-launch-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 5px 15px;
}
.launch-command-otr1-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.launch-command-otr1-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.launch-command-otr1-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 5px 15px;
}
.launch-command-otr1-grey{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.launch-command-otr2-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.launch-command-otr2-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.launch-command-otr2-grey{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.launch-command-otr2-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 5px 15px;
}
.launch-command-2hotr-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.launch-command-2hotr-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.launch-command-2hotr-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 5px 15px;
}
.launch-command-2hotr-grey{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.launch-otr-left-pdp-badge-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.launch-otr-left-pdp-badge-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.launch-otr-left-pdp-badge-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 5px 15px;
}

.launch-otr-right-pdp-badge-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.launch-otr-right-pdp-badge-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.launch-otr-right-pdp-badge-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 5px 15px;
}

.arrow-in-mobile-state-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.arrow-in-mobile-state-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.arrow-in-mobile-state-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 5px 15px;
}

.outriggers-in-mobile-state-otr1-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.outriggers-in-mobile-state-otr1-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.outriggers-in-mobile-state-otr1-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 5px 15px;
}
.outriggers-in-mobile-state-otr2-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.outriggers-in-mobile-state-otr2-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.outriggers-in-mobile-state-otr2-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 5px 15px;
}
.outriggers-in-mobile-state-2hotr-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.outriggers-in-mobile-state-2hotr-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.outriggers-in-mobile-state-2hotr-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 5px 15px;
}
.ppip-with-2b-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.ppip-with-2b-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.ppip-with-2b-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 5px 15px;
}
.ppip-with-2b-grey{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.outriggers-in-mobile-state-folding-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.outriggers-in-mobile-state-folding-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.outriggers-in-mobile-state-folding-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 5px 15px;
}
.chassis-horizontal-position-state-otr1-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.chassis-horizontal-position-state-otr1-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.chassis-horizontal-position-state-otr1-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 5px 15px;
}
.chassis-horizontal-position-state-otr2-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.chassis-horizontal-position-state-otr2-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.chassis-horizontal-position-state-otr2-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 5px 15px;
}
.chassis-horizontal-position-state-2hotr-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.chassis-horizontal-position-state-2hotr-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.chassis-horizontal-position-state-2hotr-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 5px 15px;
}

.precise-drive-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.precise-drive-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.precise-drive-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 5px 15px;
}

.started-initial-set-sk-bins-otr1-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.started-initial-set-sk-bins-otr1-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.started-initial-set-sk-bins-otr1-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 5px 15px;
}
.started-initial-set-sk-bins-otr2-green{
    color: var(--Gray-1, #333);
    filter: invert(23%) sepia(97%) saturate(3799%) hue-rotate(147deg) brightness(92%) contrast(102%);
    margin: 5px 15px;
}
.started-initial-set-sk-bins-otr2-undefined{
    color: var(--Gray-1, #333);
    filter: invert(15%) sepia(26%) saturate(2%) hue-rotate(54deg) brightness(103%) contrast(88%);
    margin: 5px 15px;
}
.started-initial-set-sk-bins-otr2-red{
    color: var(--Gray-1, #333);
    filter: invert(10%) sepia(100%) saturate(7498%) hue-rotate(19deg) brightness(91%) contrast(120%);
    margin: 5px 15px;
}
.operating_mode{
    padding: 0;
    margin-top: -25px;
}


.readines-tips{

}

.grid-cell-blue[readonly]::part(input-field){
    background-color: #2689ce !important;
    color: white;
    text-align: center !important;
}

.outriggers-dp{
    width: 180px;
    padding: 0px;
    height: 53px;
}
.grid-cell-gray::part(input-field){
    background-color: rgb(222, 222, 222)!important;
}




.TextField-green[readonly]::part(input-field){
    background-color: #00853D;
    text-align: center;
    color: white;
}
.TextField-undefined[readonly]::part(input-field){
    background-color: rgb(222, 222, 222);
    text-align: center !important;
    color: rgb(54, 58, 64);;
}
.TextField-red[readonly]::part(input-field){
    background-color: #dc0101;
    color: white;
    text-align: center !important;
}
.TextField-yellow[readonly]::part(input-field){
    background-color: #fae621;
    text-align: center !important;
    color: rgb(54, 58, 64);
}

.TextField-blue[readonly]::part(input-field){
    background-color: #2689ce;
    text-align: center !important;
    color: rgb(54, 58, 64);
}