header {
  background-color: #363A40;
  color: #FFFFFF;
  /*max-width: 1270px;*/
  /*width: 1270px;*/
}
/*body{*/
/*  max-width: 1270px;*/
/*  width: 1270px;*/
/*}*/
@media only screen and (max-width: 1909px) {
  header {
    max-width: 100%; /* Максимальна ширина 100% */
    width: 100%; /* Ширина 100% */
  }

  body {
    max-width: 100%; /* Максимальна ширина 100% */
    width: 100%; /* Ширина 100% */
  }
  .label-countdown-otr1{
    width: 120px !important;
    min-width: 120px;
    position: relative;
    top: 0px;
    left: 3%;
  }
  .label-countdown-otr2{
    width: 120px !important;
    min-width: 120px;
    position: relative;
    top: 0px;
    left: 3%;
  }
  .current-date{
    height: 32px;
    width: 196px;
    color: #FFFFFF;
    position: relative;
    top: 0px;
    left: 1%;
    padding:0px;
    font-size: 16px;
    font-stretch: normal;
  }
  .current-time{
    height: 32px;
    width: 133.33px;
    border-radius: 4px;
    position: relative;
    top: 0% ;
    left: 2%;
    padding:0px;
  }
  .current-readiness{
    height: 32px;
    width: 160px;
    position: relative;
    top: 0px;
    left: 3%;
    padding:0px;
    background-color:#00853D ;
    border-radius: 4px;
  }
  .countdown-otr1-tf{
    height: 32px;
    width: 80px;
    border-radius: 4px;
    position: relative;
    top: 0px;
    left: 3%;
    padding:0px;
  }
  .countdown-otr2-tf {
    height: 32px;
    width: 80px;
    border-radius: 4px;
    position: relative;
    top: 0px;
    left: 3%;
    padding:0px;
  }
  .ccv-name-title{
    position: relative;
    left: 5.5%;
    width: 150px;
  }
  .master-ccv-connection{
    position: relative;
    top: 6px;
    left: 8%;
  }
  .master-ccv-connection::part(input-field){
    width: 20px;
    height: 20px;
    border-radius: 20px;
    font-size: 50px;

  }
  .label-ccv-name{
    position: relative;
    top: 0px;
    left: 7%;
  }
  H1{
    color: #FFFFFF;
    padding-left: 4%;
    padding-top: 1.25px;
    padding-bottom: 1.25px;
    font-size: 18px;
  }
}
@media only screen and (min-width: 1910px) {
  .preparation-three-in-row-for-launch{

  }
  .box-border{
    --lumo-font-size-m:  18px;
    --lumo-font-size-s: 18px;
  }
  header {
    max-width: 100%;
    width: 100%;
  }
  body {
    --lumo-font-size-m:  15px;
    --lumo-font-size-s: 14px;
    max-width: 100%;
    width: 100%;

  }
  .div-header{
    display: flex;
  }
  .label-countdown-otr1{
    width: 180px !important;
    min-width: 180px;
    position: relative;
    top: 0px;
    left: 22%;
    font-size: 18px;
  }
  .label-countdown-otr2{
    width: 180px !important;
    min-width: 180px;
    position: relative;
    top: 0px;
    left: 22%;
    font-size: 18px;

  }
  .current-date{
    height: 32px;
    width: 196px;
    color: #FFFFFF;
    position: relative;
    top: 0px;
    left: 3%;
    padding:0px;
    font-size: 16px;
    font-stretch: normal;
  }
  .current-time{
    height: 32px;
    width: 133.33px;
    border-radius: 4px;
    position: relative;
    top: 0% ;
    left: 4%;
    padding:0px;
  }
  .current-readiness{
    height: 32px;
    width: 160px;
    position: relative;
    top: 0px;
    left: 20%;
    padding:0px;
    background-color:#00853D ;
    border-radius: 4px;
  }
  .countdown-otr1-tf{
    height: 32px;
    width: 80px;
    border-radius: 4px;
    position: relative;
    top: 0px;
    left: 22%;
    padding:0px;
  }
  .countdown-otr2-tf {
    height: 32px;
    width: 80px;
    border-radius: 4px;
    position: relative;
    top: 0px;
    left: 22%;
    padding:0px;
  }
  .ccv-name-title{
    position: relative;
    font-size: 18px;
    left: 24%;
  }
  .master-ccv-connection{
    position: relative;
    height: 32px;
    left: 25%;
    color: white;
  }
  .master-ccv-connection::part(input-field){
    width: 160px;
    height: 32px;
  }
  .label-ccv-name{
    position: relative;
    top: 0px;
    left: 24%;
  }
  H1{
    color: #FFFFFF;
    padding-left: 31%;
    padding-top: 1.25px;
    padding-bottom: 1.25px;
    font-size: 18px;
  }
}

.div-header{
  display: flex;
  align-items: center;
  height: 16%;
}
.itemTabList{
  display: flex;
  margin: 0px;
  border-radius: 5px 5px 0px 0px;
}
.itemTabList li{
  flex-basis: 33.3%;
  border-radius: 5px 5px 0px 0px;
  height: 38px;

}
.itemTabList li a{
  display: flex;
  justify-content: center;
}
.itemTabList li:hover{
  border-radius: 5px 5px 0px 0px;
  background-color: grey;
  opacity: 0.8;
  color:#363A40;
}
.itemTabList li:hover::after{
  content: '';
  opacity: 0;
}

li {
  list-style-type: none; /* Убираем маркеры */
  color: #FFFFFF99;
}
ul {
  padding-left: 0; /* Сдвигаем список влево */
  padding-bottom: 0;
}
.sdo-logo{
  padding-top: 0px;
  padding-left: 16px;
  height: 35px;
}

input{
  text-transform: initial;
}
.current-date[readonly]::part(input-field){
  color: #FFFFFF;
  min-height: 32px;
  text-align: center;
  padding-left: 10px;
  text-transform: capitalize;
  background: rgba(255, 255, 255, 0.1);
}
.dataPrefix{
  color: #ffffff;
  text-transform: capitalize;
  padding-left: 5px;
}

.current-time[readonly]::part(input-field){
  color: #FFFFFF;
  height: 32px;
  background-color: #FFFFFF1A;
  padding-left: 10px;
}

.countdown-otr1-tf[readonly]::part(input-field){
  color: #FFFFFF;
  height: 32px;
  background-color: #FFFFFF1A;
  padding-left: 10px;
}

.countdown-otr2-tf[readonly]::part(input-field){
  color: #FFFFFF;
  height: 32px;
  background-color: #FFFFFF1A;
  padding-left: 10px;
}
.timePrefix{
  color: #FFFFFF;
  padding-left: 5px;
}

vaadin-text-field[readonly]::part(input-field){
  background: #FFFFFF1A;
  color: #FFFFFF;
  padding-left: 0;
}

.current-readiness::part(input-field){
  color: #FFFFFF;
  height: 32px;
  overflow: visible;
  padding-left: 20px;
  background-color:#00853D
}



header nav a:any-link {
  text-decoration: none;
  padding: 0 var(--lumo-space-s);
  outline: 0;
  background-color: #FFFFFF1A;

  border-radius: 5px 5px 0px 0px;
  height: 38px;
}


header nav a:focus-visible {
  box-shadow: 0 0 0 2px var(--lumo-primary-color-50pct);
}


header nav a[highlight] {
  background-color: #FFFFFF;
  color: #363A40;
}

.enabled-to-highlight{
  background-color: #FFFFFF;
  color: #363A40;
}

