/target/
.idea/
.vscode/
.settings
.project
.classpath
docker/sumdu
*.iml
.DS_Store

# The following files are generated/updated by vaadin-maven-plugin
node_modules/
frontend/generated/
pnpmfile.js
vite.generated.ts

# Browser drivers for local integration tests
drivers/
# Error screenshots generated by TestBench for failed integration tests
error-screenshots/
webpack.generated.js

*.gz
*.tar!/docker/spl_control/splNavigation.jar

!/docker/spl_control/vehicle-control-services-0.0.10-SNAPSHOT.jar
!/docker/spl_control/splNavigation.jar

!/docker/spl_control/vehicle-control-services*.jar
!/docker/spl_control/splNavigation*.jar
/docker/commit_lunch.ps1
/docker/get-ppo.ps1
/docker/init_pdp.ps1
/docker/1_ init_pdp_frst_rocket.ps1
/docker/order.ps1
/docker/pdp_byn_with_ncok_4_lucnh.ps1
/docker/set_suto_level.ps1
