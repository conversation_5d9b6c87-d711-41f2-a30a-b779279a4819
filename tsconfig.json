// This TypeScript configuration file is generated by vaadin-maven-plugin.
// This is needed for TypeScript compiler to compile your TypeScript code in the project.
// It is recommended to commit this file to the VCS.
// You might want to change the configurations to fit your preferences
// For more information about the configurations, please refer to http://www.typescriptlang.org/docs/handbook/tsconfig-json.html
{
  "flow_version": "********",
  "compilerOptions": {
    "sourceMap": true,
    "jsx": "react-jsx",
    "inlineSources": true,
    "module": "esNext",
    "target": "es2020",
    "moduleResolution": "node",
    "strict": true,
    "skipLibCheck": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitReturns": true,
    "noImplicitAny": true,
    "noImplicitThis": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "experimentalDecorators": true,
    "useDefineForClassFields": false,
    "baseUrl": "frontend",
    "paths": {
      "@vaadin/flow-frontend": ["generated/jar-resources"],
      "@vaadin/flow-frontend/*": ["generated/jar-resources/*"],
      "Frontend/*": ["*"]
    }
  },
  "include": [
    "frontend/**/*",
    "types.d.ts"
  ],
  "exclude": []
}
