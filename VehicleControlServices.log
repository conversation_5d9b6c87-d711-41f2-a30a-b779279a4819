2025-08-22 11:31:24.805  INFO 3392 --- [restartedMain] com.deb.spl.control.Application          : Starting Application using Java 17.0.4.1 on WIN-ARR4C6ROFQD with PID 3392 (D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes started by admin in D:\GitHub\flow-crm-tutorial\vaadin-header)
2025-08-22 11:31:24.820  INFO 3392 --- [restartedMain] com.deb.spl.control.Application          : The following 1 profile is active: "dev"
2025-08-22 11:31:25.338  INFO 3392 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-22 11:31:25.339  INFO 3392 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-22 11:31:39.507  INFO 3392 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-22 11:31:40.020  INFO 3392 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 489 ms. Found 15 JPA repository interfaces.
2025-08-22 11:31:42.816  INFO 3392 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8079 (http)
2025-08-22 11:31:42.840  INFO 3392 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-22 11:31:42.841  INFO 3392 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-08-22 11:31:43.206  INFO 3392 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-22 11:31:43.207  INFO 3392 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 17866 ms
2025-08-22 11:31:44.720  INFO 3392 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-22 11:31:45.080  INFO 3392 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-22 11:31:45.545  INFO 3392 --- [restartedMain] liquibase.database                       : Set default schema name to public
2025-08-22 11:31:45.802  INFO 3392 --- [restartedMain] liquibase.lockservice                    : Successfully acquired change log lock
2025-08-22 11:31:46.136  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/07/changeset_03.sql
2025-08-22 11:31:46.165  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_04.sql
2025-08-22 11:31:46.182  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_05.sql
2025-08-22 11:31:46.198  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_06.sql
2025-08-22 11:31:46.228  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_07.sql
2025-08-22 11:31:46.236  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_08.sql
2025-08-22 11:31:46.253  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_09.sql
2025-08-22 11:31:46.267  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_10.sql
2025-08-22 11:31:46.277  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_11.sql
2025-08-22 11:31:46.295  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_12.sql
2025-08-22 11:31:46.310  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_13.sql
2025-08-22 11:31:46.315  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_14.sql
2025-08-22 11:31:46.351  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_15.sql
2025-08-22 11:31:46.374  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_16.sql
2025-08-22 11:31:46.394  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_17.sql
2025-08-22 11:31:46.419  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_18.sql
2025-08-22 11:31:46.434  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_19.sql
2025-08-22 11:31:46.439  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_20.sql
2025-08-22 11:31:46.460  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_21.sql
2025-08-22 11:31:46.474  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_22.sql
2025-08-22 11:31:46.492  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_23.sql
2025-08-22 11:31:46.505  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/10/changeset_24.sql
2025-08-22 11:31:46.524  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_26.sql
2025-08-22 11:31:46.537  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_27.sql
2025-08-22 11:31:46.550  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_28.sql
2025-08-22 11:31:46.564  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_29.sql
2025-08-22 11:31:46.583  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_30.sql
2025-08-22 11:31:46.601  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_31.sql
2025-08-22 11:31:46.625  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_33.sql
2025-08-22 11:31:46.650  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_34.sql
2025-08-22 11:31:46.742  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_35.sql
2025-08-22 11:31:46.774  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_36.sql
2025-08-22 11:31:46.795  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_37.sql
2025-08-22 11:31:46.821  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_38.sql
2025-08-22 11:31:46.847  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_39.sql
2025-08-22 11:31:46.910  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_40.sql
2025-08-22 11:31:46.952  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_41.sql
2025-08-22 11:31:46.965  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_42.sql
2025-08-22 11:31:46.983  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_43.sql
2025-08-22 11:31:47.000  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_44.sql
2025-08-22 11:31:47.006  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_45.sql
2025-08-22 11:31:47.022  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_46.sql
2025-08-22 11:31:47.038  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_47.sql
2025-08-22 11:31:47.055  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/05/changset_48.sql
2025-08-22 11:31:47.074  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_49.sql
2025-08-22 11:31:47.087  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_50.sql
2025-08-22 11:31:47.094  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_51.sql
2025-08-22 11:31:47.113  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_52.sql
2025-08-22 11:31:47.139  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_53.sql
2025-08-22 11:31:47.153  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/07/changset_54.sql
2025-08-22 11:31:47.171  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_54.sql
2025-08-22 11:31:47.189  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_55.sql
2025-08-22 11:31:47.224  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_56.sql
2025-08-22 11:31:47.248  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_57.sql
2025-08-22 11:31:47.268  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_58.sql
2025-08-22 11:31:47.291  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/02/changset_59.sql
2025-08-22 11:31:47.486  INFO 3392 --- [restartedMain] liquibase.changelog                      : Reading from public.databasechangelog
2025-08-22 11:31:47.783  INFO 3392 --- [restartedMain] liquibase.lockservice                    : Successfully released change log lock
2025-08-22 11:31:48.035  INFO 3392 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-22 11:31:48.316  INFO 3392 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.14.Final
2025-08-22 11:31:48.768  INFO 3392 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-22 11:31:49.179  INFO 3392 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL10Dialect
2025-08-22 11:31:50.296  INFO 3392 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-08-22 11:31:50.312  INFO 3392 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-08-22 11:31:53.849  INFO 3392 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-22 11:31:53.884  INFO 3392 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-22 11:31:55.181  INFO 3392 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for VaadinAppShell took 55 ms
2025-08-22 11:31:56.595  INFO 3392 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for subclasses and classes with annotations took 1147 ms
2025-08-22 11:31:56.922  INFO 3392 --- [restartedMain] c.v.b.d.startup.DevModeStartupListener   : Starting dev-mode updaters in D:\GitHub\flow-crm-tutorial\vaadin-header folder.
2025-08-22 11:31:57.128  INFO 3392 --- [restartedMain] c.v.f.s.f.s.FullDependenciesScanner      : Visited 153 classes. Took 113 ms.
2025-08-22 11:31:58.697  INFO 3392 --- [ForkJoinPool.commonPool-worker-2] c.v.f.s.frontend.TaskUpdatePackages      : Skipping `npm install` because the frontend packages are already installed in the folder 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules' and the hash in the file 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules\.vaadin\vaadin.json' is the same as in 'package.json'
2025-08-22 11:31:58.698  INFO 3392 --- [ForkJoinPool.commonPool-worker-2] c.v.f.s.frontend.TaskCopyFrontendFiles   : Copying frontend resources from jar files ...
2025-08-22 11:31:58.965  INFO 3392 --- [ForkJoinPool.commonPool-worker-2] c.v.f.s.frontend.TaskCopyFrontendFiles   : Visited 25 resources. Took 266 ms.
2025-08-22 11:32:00.608  WARN 3392 --- [restartedMain] c.d.s.control.service.asku.AskuService   : Номерний знак СПУ змінено з AA 0000 AA на DP 0101 UA
2025-08-22 11:32:00.609  WARN 3392 --- [restartedMain] c.d.s.control.service.asku.AskuService   : Кодову назву СПУ змінено з ??? на spl101
2025-08-22 11:32:00.982  INFO 3392 --- [restartedMain] c.d.s.control.service.asku.AskuService   : ASKU loaded from DB AskuDao{id=1001, splId=02d9dd7e-8cda-40f1-b068-b04a23841097, plateNumber='DP 0101 UA', unitTitle='spl101', startedDate=null, updatedAt=2025-08-22T11:32:00.758851600, splReadiness=ТГ № 4, tpcLeft=Tpc{id=1, dateReleaseM=2020-07-21T12:40:45, tpcLoadState=TPC_WITH_ROCKET}, tpcRight=Tpc{id=255, dateReleaseM=2023-08-29T18:33:51.542816, tpcLoadState=TPC_WITH_ROCKET}, leftRocket=RocketDao{id=1, dateReleaseM=2023-07-21T15:08:25, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=1, createdAt=2023-07-21T15:07:13, plantMissile='1Л', warhead=MFBCH, gsnType=NO_GSN, alpType=FOUR_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='""'}, initialData=LaunchInitialDataDao{id=1, createdAt=2023-07-21T14:57:54, loadTemperature=-999.9, latitudeRad=0.9730396216435608, longitudeRad=0.6565960162985366, altitude=101.11, inclinationAngle=-86.0, trajectory=BALLISTIC, readiness=БГ № 1, isProDetected=false, missileOperatingMode=BASE_SNS, tlCode='gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==, scheduled=false, startTimenull'}null, initialDataSource=MSG, initialDataSourceDescription='не вказан, loadedToPlc='false', storedTlKeys={werwer23443dasdasdasdas234rrrwsssdfgdasd===;0123465798/*--!@#$%^&fsf3wffffffffffffffff===}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, rightRocket=RocketDao{id=204, dateReleaseM=null, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=235, createdAt=2023-08-29T18:33:51.501015, plantMissile='84', warhead=CBCH, gsnType=NO_GSN, alpType=NO_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='null'}, initialData=null, initialDataSource=MSG, initialDataSourceDescription='asd, loadedToPlc='false', storedTlKeys={qweqweqw23131aassssssssssssss///=====}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, status=null, plc=com.deb.spl.control.data.asku.Plc@68b56764}
2025-08-22 11:32:02.810  INFO 3392 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3fea24ea, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@69b55532, org.springframework.security.web.context.SecurityContextPersistenceFilter@45f5b90, org.springframework.security.web.header.HeaderWriterFilter@f5ea1a9e, org.springframework.security.web.authentication.logout.LogoutFilter@b07bdea6, com.deb.spl.control.authorization.AuthenticationFilter@21bd1b1c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@ecfe5049, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@c802a9ab, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@8fac2393, org.springframework.security.web.session.SessionManagementFilter@7d64de20, org.springframework.security.web.access.ExceptionTranslationFilter@c972db18, org.springframework.security.web.access.intercept.AuthorizationFilter@27033de8]
2025-08-22 11:32:02.821  WARN 3392 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/images/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-22 11:32:02.822  INFO 3392 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/images/**']
2025-08-22 11:32:02.823  WARN 3392 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/VAADIN/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-22 11:32:02.823  INFO 3392 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/VAADIN/**']
2025-08-22 11:32:02.823  WARN 3392 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-22 11:32:02.823  INFO 3392 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/']
2025-08-22 11:32:02.823  WARN 3392 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/adjacent-systems/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-22 11:32:02.823  INFO 3392 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/adjacent-systems/**']
2025-08-22 11:32:02.823  WARN 3392 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/automation/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-22 11:32:02.823  INFO 3392 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/automation/**']
2025-08-22 11:32:02.824  WARN 3392 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/readiness/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-22 11:32:02.824  INFO 3392 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/readiness/**']
2025-08-22 11:32:02.824  WARN 3392 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/log_main_view/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-22 11:32:02.824  INFO 3392 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/log_main_view/**']
2025-08-22 11:32:02.824  WARN 3392 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/mmhs']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-22 11:32:02.824  INFO 3392 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/mmhs']
2025-08-22 11:32:02.824  WARN 3392 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/adjacent-systems/bins/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-22 11:32:02.824  INFO 3392 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/adjacent-systems/bins/**']
2025-08-22 11:32:02.824  WARN 3392 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-22 11:32:02.824  INFO 3392 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/']
2025-08-22 11:32:02.824  WARN 3392 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/tlc/open_keys/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-22 11:32:02.824  INFO 3392 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/tlc/open_keys/**']
2025-08-22 11:32:02.825  WARN 3392 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/initial_data/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-22 11:32:02.825  INFO 3392 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/initial_data/**']
2025-08-22 11:32:02.825  WARN 3392 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/fire_order/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-22 11:32:02.825  INFO 3392 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/fire_order/**']
2025-08-22 11:32:02.825  WARN 3392 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-22 11:32:02.825  INFO 3392 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state']
2025-08-22 11:32:02.826  WARN 3392 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-22 11:32:02.826  INFO 3392 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state/']
2025-08-22 11:32:02.826  WARN 3392 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-22 11:32:02.826  INFO 3392 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points/']
2025-08-22 11:32:02.826  WARN 3392 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-22 11:32:02.826  INFO 3392 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points']
2025-08-22 11:32:02.827  WARN 3392 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-22 11:32:02.827  INFO 3392 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-api']
2025-08-22 11:32:02.827  WARN 3392 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-admin-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-22 11:32:02.827  INFO 3392 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-admin-api']
2025-08-22 11:32:02.827  WARN 3392 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/test']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-22 11:32:02.827  INFO 3392 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/test']
2025-08-22 11:32:02.827  WARN 3392 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Regex [pattern='^$']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-22 11:32:02.827  INFO 3392 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Regex [pattern='^$']
2025-08-22 11:32:03.089  WARN 3392 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-22 11:32:04.046  INFO 3392 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-22 11:32:04.469  WARN 3392 --- [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8079 is already in use
2025-08-22 11:32:04.528  INFO 3392 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-22 11:32:04.536  INFO 3392 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-22 11:32:04.546  INFO 3392 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-22 11:32:04.555  INFO 3392 --- [restartedMain] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-22 11:32:04.585  INFO 3392 --- [restartedMain] ConditionEvaluationReportLoggingListener : 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-22 11:32:04.652 ERROR 3392 --- [restartedMain] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8079 was already in use.

Action:

Identify and stop the process that's listening on port 8079 or configure this application to listen on another port.

