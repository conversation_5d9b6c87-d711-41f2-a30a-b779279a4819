FROM eclipse-temurin:17-jre-alpine as spl_control
WORKDIR /app
COPY ./vehicle-control-services-*.jar /app/vehicle-control-services.jar
COPY Ubuntu-LI.ttf /usr/share/fonts/truetype/ubuntu/
RUN apk --no-cache add fontconfig && fc-cache -f -v
EXPOSE 8079
EXPOSE 5005
CMD ["java", "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005", "-jar","-Dspring.profiles.active=production", "/app/vehicle-control-services.jar"]