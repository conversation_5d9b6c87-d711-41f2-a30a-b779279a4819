version: '3.8'
services:
  db:
    extends:
      service: db
      file: common.yml
    container_name: spl_asku_db
    volumes:
      - "/etc/timezone:/etc/timezone:ro"
      - "/etc/localtime:/etc/localtime:ro"

  control:
    extends:
      service: control
      file: common.yml
    container_name: Dockerfile_spl_control
    environment:
      #        UNCOMMENT next 3 [HOST_URL,CCV_ROCKET-STATUS-ENDPOINT_URL,CCV_TOKEN] lines to override default setting
      - HOST_URL=***************
      - CCV_ROCKET-STATUS-ENDPOINT_URL=http://***************:3002/api/asku/status/
      - CCV_TOKEN=spl12345

    volumes:
      - "/etc/timezone:/etc/timezone:ro"
      - "/etc/localtime:/etc/localtime:ro"
    depends_on:
      db:
        condition: service_healthy

  navigation:
    extends:
      service: navigation
      file: common.yml
    container_name: spl_navigation
    environment:
      - SPRING_PROFILES_ACTIVE=production
    privileged: true
    devices:
      - /dev/ttyr00:/dev/ttyr00
      - /dev/ttyr01:/dev/ttyr01
    volumes:
      - "/etc/timezone:/etc/timezone:ro"
      - "/etc/localtime:/etc/localtime:ro"
    depends_on:
      db:
        condition: service_healthy