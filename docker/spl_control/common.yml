version: '3.8'
services:
  db:
    image: spl/db:1.0.2
    build:
      context: .
      dockerfile: Dockerfile_db
    container_name: spl_asku_db
    user: postgres
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=123
    ports:
      - "5532:5432"
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready", "--quiet" ]
      interval: 10s
      timeout: 30s
      retries: 10
    restart: unless-stopped

  control:
    image: spl/control:1.0.1
    build:
      context: .
      dockerfile: Dockerfile_spl_control
    container_name: spl_control
    environment:
      #      application server address
      - HOST_URL=***************
      #      application server port
      - SERVER_PORT=8079
      #      DB settings
      - SPRING_DATASOURCE_URL=********************************
      - SPRING.DATASOURCE.USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=123
      #      DB migration enabled
      - SPRING_LIQUIBASE_ENABLED=true
      #      JPA settings
      - SPRING_JPA_PROPERTIES_HIBERNATE_SHOW_SQL=false
      - SPRING_JPA_PROPERTIES_HIBERNATE_FORMAT_SQL=false
      - SPRING_JPA_PROPERTIES_HIBERNATE_HIGHLIGHT_SQL=false
      - SPRING_JPA_PROPERTIES_HIBERNATE_GENERATE_STATISTICS=false
      - SPRING_JPA_HIBERNATE_DDL-AUTO=validate
      #      logging settings
      - LOGGING_LEVEL_ORG_HIBERNATE_SQL=WARN
      - LOGGING_LEVEL_ORG_HIBERNATE_TYPE_DESCRIPTOR_SQL_BASICBINDER=WARN
      - LOGGING_FILE_PATH=_/
      - LOGGING_FILE_NAME=VehicleControlServices_log
      #logged url
      - ALLOWED_URLS_FOR_LOGGING=/msu,/bins,/api,/users
      #      SPL UUID
      - SPL_DEFAULT-SPL-ID=02d9dd7e-8cda-40f1-b068-b04a23841097
      #      plate number
      - SPL_PLATE-NUMBER=AA 0000 AA
      #      SPL name
      - SPL_UNIT_TITLE=СПУ
      #      synchronize time from UKNSS
      - TIME_FROM-BINS=false
      #      UKNSS SETTINGS
      - BINS_CONNECTION-TIME-OUT-SEC=30
      - BINS_SLEEP-TIME-ON-ERROR-SEC=60
      - BINS_UTC-ZONE-ID=+3
      - BINS_DATA-SOURCE-ENDPOINT_URL=http://navigation:8081/bins
      #      PLC connection timeout
      - PLC_CONNECTION-TIME-OUT-SEC=8
      #      PLC retries count
      - PLC_CONNECTION-ERROR-RETRIES=3
      #      the time at which a SAE command issued by the operator is  valid and  available
      #      at the /adjacent-systems/sae/command endpoint
      - SAE_COMMAND_COMMAND-VALIDITY-TIME-SEC=5
      #      the time at which a PPO command issued by the operator is  valid and  available
      #      at the /adjacent-systems/ppo/command endpoint
      - PPO_COMMAND_COMMAND-VALIDITY-TIME-SEC=5
      #      the time at which a SUTO command issued by the operator is  valid and  available
      #      at the /adjacent-systems/suto/command endpoint
      - SUTO_COMMAND_COMMAND-VALIDITY-TIME-SEC=5
      #      the time at which a NPPA command issued by the operator is  valid and  available
      #      at the /adjacent-systems/nppa/command endpoint
      - NPPA_COMMAND_COMMAND-VALIDITY-TIME-SEC=5
      #      MSU settings
      #      used to prevent MSU from stopping if ANTENNA_ROD_PULLING_IN or ANTENNA_ROD_PULLING_OUT
      - MSU_HEATING-COMMAND-DELAY-MS=5000
      - MSU_CONNECTION-TIME-OUT-SEC=30
      - MSU_SLEEP-TIME-ON-ERROR-SEC=60
      #      MSU service URL (by default navigation is docker-compose service name)
      - MSU_DATA-SOURCE-ENDPOINT_URL=http://navigation:8081
      #      token value used in NSD authorization
      - NSD_TOKEN=123456
    restart: unless-stopped
    ports:
      - "8079:8079"
      - "5005:5005"


  navigation:
    image: spl/navigation:1.0.1
    build:
      context: .
      dockerfile: Dockerfile_navigation
    container_name: spl_navigation
    privileged: true
    environment:
      #      application server port
      - SERVER_PORT= 8081
      #      application name
      - BINS_APPLICATION_NAME=SPL-Navigation-Service
      #      DB settings
      - SPRING_DATASOURCE_URL=********************************
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=123
      #      JPA settings
      - SPRING_JPA_PROPERTIES_HIBERNATE_DIALECT=org.hibernate.dialect.PostgreSQLDialect
      - SPRING.JPA_PROPERTIES_HIBERNATE_SHOW_SQL=false
      - SPRING_JPA_HIBERNATE_DDL-AUTO=validate
      - SPRING_JPA_PROPERTIES_HIBERNATE_SHOW_SQL=false
      - SPRING_JPA_PROPERTIES_HIBERNATE_FORMAT_SQL=false
      - SPRING_JPA_PROPERTIES_HIBERNATE_HIGHLIGHT_SQL=false
      - SPRING_JPA_PROPERTIES_HIBERNATE_GENERATE_STATISTICS=false
      #      Logging settings
      - LOGGING_LEVEL_ORG_HIBERNATE_SQL=info
      - LOGGING_LEVEL_ROOT=INFO
      - ORG_SPRINGFRAMEWORK_WEB_FILTER_COMMONSREQUESTLOGGINGFILTER=ERROR
      - LOGGING_LEVEL_ORG_HIBERNATE_TYPE_DESCRIPTOR_SQL_BASICBINDER=ERROR
      - LOGGING_FILE_PATH=./
      - LOGGING_FILE_NAME=splNavigation.log
      #      UKNSS settings
      - BINS_SERIAL-IN-QUEUE-SIZE=1000
      - BINS_SERIAL-OUT-QUEUE-SIZE=100
      - BINS_LOST-CONNECTION-TIME-OUT-MS=60000
      - BINS_LOST-CONNETION-RETRIES=5
      #      MSU settings
      - MSU_APPLICATION_NAME=MSU-Meteo-Service
      - MSU_SERIAL-IN-QUEUE-SIZE=1000
      - MSU_SERIAL-OUT-QUEUE-SIZE=100
      - MSU_LOST-CONNECTION-TIME-OUT-MS=60000
      - MSU_LOST-CONNETION-RETRIES=5
    restart: unless-stopped
    ports:
      - "8081:8081"
      - "5004:5004"