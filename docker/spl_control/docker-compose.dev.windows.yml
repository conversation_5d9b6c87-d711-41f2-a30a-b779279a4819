version: '3.8'
services:
  db:
    extends:
      service: db
      file: common.yml
    environment:
      TZ: 'Europe/Kyiv'
      PGTZ: 'Europe/Kyiv'
    container_name: spl_asku_db_dev

  control:
    extends:
      service: control
      file: common.yml
    container_name: spl_control_dev
    environment:
#      - CCV_TOKEN_VALUE=new_token
#      - PLC_REQUEST_TOKEN=new_plc_request_token
#      - PLC_RESPONSE_TOKEN=new_plc_response_token
      - SPRING_PROFILES_ACTIVE=dev
      - TZ=Europe/Kyiv
      - PGTZ=Europe/Kyiv
      #logging settings
      - LOGGING_LEVEL_ORG_HIBERNATE_HQL_INTERNAL_AST_QUERYTRANSLATORIMPL=ERROR
      - LOGGING_LEVEL_ORG_HIBERNATE_SQL=ERROR
      - LOGGING_LEVEL_ORG_HIBERNATE_TYPE_DESCRIPTOR_SQL_BASICBINDER=ERROR
      - LOGGING_LEVEL_ORG_HIBERNATE_HQL_INTERNAL_AST_QUERYTRANSLATORIMPL=ERROR
    depends_on:
      db:
        condition: service_healthy

  navigation:
    extends:
      service: navigation
      file: common.yml
    container_name: spl_navigation_dev
    environment:
      - SPRING_PROFILES_ACTIVE=dev
      - TZ=Europe/Kyiv
      - PGTZ=Europe/Kyiv
    #    devices:
    #      - COM7:/dev/ttyr00
    #      - CNCA3:/dev/ttyr01
    depends_on:
      db:
        condition: service_healthy