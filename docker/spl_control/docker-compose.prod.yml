version: '3.8'
services:
  db:
    extends:
      service: db
      file: common.yml
    container_name: spl_asku_db
    environment:
      #      used to set up timezone
      TZ: 'Europe/Kyiv'
      PGTZ: 'Europe/Kyiv'
    volumes:
      - "/etc/timezone:/etc/timezone:ro"
      - "/etc/localtime:/etc/localtime:ro"

  control:
    extends:
      service: control
      file: common.yml
    container_name: spl_control
    environment:
      #      ASKU server ip address
      - HOST_URL=***************
      #      used to set up timezone
      - TZ=Europe/Kyiv
      #      used to set up timezone
      - PGTZ=Europe/Kyiv
      #      DB username
      - SPRING_DATASOURCE_USERNAME=postgres
      #      DB password
      - SPRING_DATASOURCE_PASSWORD=123
      #      port of CCV application client installed in SPL
      - CCV_HOST_PORT=3002
      #      ccv application ip address
      - CCV_HOST_URL=***************
      #      endpoint used to update rocket status displayed in CCV
      - CCV_ROCKET_STATUS_ENDPOINT_URL=http://***************:3002/api/asku/status/
      #      endpoint used for loading CCV APPLICATION SETTINGS
      #               (e.q. UUID unitId
      #                     UUID vehicleId
      #                     Object vehicle
      #                     UUID splUserId)
      - CCV_APP_SETTINGS_URL=http://***************:3002/api/app_settings
      #      used to load available vehicles dictionary (@VehicleResource) from CCV
      - CCV_VEHICLES_URL=http://***************:3002/api/vehicles
      #      used to specify header parameter NAME used in CCV requests authorization
      - CCV_TOKEN_REQUEST_PARAMETER_NAME=camunda-token
      #       used to specify header parameter VALUE used in CCV requests authorization
      - CCV_TOKEN_VALUE=12345
      - CCV_REQUEST_TIME-LIMIT_SEC=40
      #      rote URL in UI
      - MMHS_URL=http://***************:3002
      #      used to specify header parameter NAME used in PLC requests authorization
      - PLC_TOKEN_REQUEST_PARAMETER_NAME=Token
      #      used to specify header parameter VALUE used in PLC requests authorization
      - PLC_REQUEST_TOKEN=plc12345
      #      used to specify header parameter VALUE used in responses to PLC
      - PLC_RESPONSE_TOKEN=server67890
      #      plc IP ADDRESS
      - PLC_URL=**************
      #      SPL UID
      - SPL_DEFAULT_SPL_ID=02d9dd7e-8cda-40f1-b068-b04a23841097
      #      plate number of SPL displayed in the informational panel
      - SPL_PLATE_NUMBER=DP 0101 UA
      #      unit title of SPL displayed in the informational panel
      - SPL_UNIT_TITLE=spl101
      #      token value used for NSD authorization
      - NSD_TOKEN=12345
      #      URL's used for NSD authorization and registration
      - NSD_URL=/users/create-api,/users/create-admin-api,/users/test
      #      the time at which a NPPA command issued by the operator is  valid and  available
      #      at the /adjacent-systems/nppa/command endpoint
      - NPPA_COMMAND_COMMAND-VALIDITY-TIME-SEC=20
      #logging settings
      - LOGGING_LEVEL_ORG_HIBERNATE_HQL_INTERNAL_AST_QUERYTRANSLATORIMPL=ERROR
      - LOGGING_LEVEL_ORG_HIBERNATE_SQL=ERROR
      - LOGGING_LEVEL_ORG_HIBERNATE_TYPE_DESCRIPTOR_SQL_BASICBINDER=ERROR
      - LOGGING_LEVEL_ORG_HIBERNATE_HQL_INTERNAL_AST_QUERYTRANSLATORIMPL=ERROR

    volumes:
      - "/etc/timezone:/etc/timezone:ro"
      - "/etc/localtime:/etc/localtime:ro"
    depends_on:
      db:
        condition: service_healthy

  navigation:
    extends:
      service: navigation
      file: common.yml
    container_name: spl_navigation
    environment:
      #      used to set up timezone
      - TZ=Europe/Kyiv
      - PGTZ=Europe/Kyiv
      #      specify application profile
      - SPRING_PROFILES_ACTIVE=production
      #      DB username
      - SPRING_DATASOURCE_USERNAME=postgres
      #      DB password
      - SPRING_DATASOURCE_PASSWORD=123
    privileged: true
    devices:
      - /dev/ttyr00:/dev/ttyr00
      - /dev/ttyr01:/dev/ttyr01
    volumes:
      - "/etc/timezone:/etc/timezone:ro"
      - "/etc/localtime:/etc/localtime:ro"
    depends_on:
      db:
        condition: service_healthy