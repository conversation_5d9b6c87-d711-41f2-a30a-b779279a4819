$url = "http://localhost:8079/api/v1/asku/plc/launch_timers?action=START"

$headers = @{
    "Token" = "plc12345"
}

$body = @{
    launchType = "FIRST"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri $url -Method Post -Headers $headers -Body $body -ContentType "application/json"
    Write-Host "Server response:"
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "Error during request execution: $_"
}