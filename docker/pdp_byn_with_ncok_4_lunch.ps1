$baseUrl = "http://localhost:8079"

# Define the endpoints
$bynEndpoint = "/api/v1/adjacent-systems/nppa/byn"
$ncokEndpoint = "/api/v1/adjacent-systems/nppa/ncok"

# Define the token
$token = "plc12345"

# Define headers
$headers = @{
    "Token" = $token
    "Content-Type" = "application/json"
}

# Define the BYN request body
$bynBody = @{
    systemStatus = "OK"
    operatingMode = "COMBAT"
    tvByn = "OK"
    isConnected = $true
    isNcok = $true
    isRgOutNcok = $false
    isBuveF2 = $false
    isBuveF4 = $false
    isBasuOtr1F3 = $false
    isBasuOtr2F3 = $false
    isF1 = $false
    isF2 = $false
    isF3 = $false
    isF4 = $false
    isF5 = $false
    isNppaConnected = $true
    isBasuOtr1Connected = $true
    isBasuOtr2Connected = $false
} | ConvertTo-Json

# Define the NCOK request body
$ncokBody = @{
    systemStatus = "OK"
    operatingMode = "COMBAT"
    tvNcok = "OK"
    isNcokConnected = $true
    isSutoConnected = $true
    nppaTestResult = "UNDEFINED"
    appPresence = $false
    otr1AppPresence = $false
    otr2AppPresence = $false
    otr1TestResult = "OK"
    otr2TestResult = "UNDEFINED"
    isOtr1Lunched = "UNDEFINED"
    isOtr2Lunched = "UNDEFINED"
    otr1BinsInitialSetup = "OK"
    otr2BinsInitialSetup = "UNDEFINED"
    otrBinsPreciseSetup = "OK"
    otr1tvNoIns = "OK"
    otr2tvNoIns = "OK"
    otr1BasSnsPz = "OK"
    otr2BasSnsPz = "OK"
} | ConvertTo-Json

# Function to make POST request and handle response
function Invoke-CustomPost {
    param (
        [string]$endpoint,
        [string]$body,
        [string]$description
    )

    try {
        $url = $baseUrl + $endpoint
        Write-Host "Sending $description request to: $url"

        $response = Invoke-WebRequest -Uri $url -Method Post -Headers $headers -Body $body

        Write-Host "$description request successful. Status code: $($response.StatusCode)"
        Write-Host "Response: $($response.Content)"
        Write-Host "--------------------"
    }
    catch {
        Write-Host "Error in $description request: $($_.Exception.Message)"
        Write-Host "Status code: $($_.Exception.Response.StatusCode.value__)"
        Write-Host "--------------------"
    }
}

# Execute both requests
Invoke-CustomPost -endpoint $bynEndpoint -body $bynBody -description "BYN"
Invoke-CustomPost -endpoint $ncokEndpoint -body $ncokBody -description "NCOK"