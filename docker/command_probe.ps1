$url = "http://localhost:8079/api/v1/adjacent-systems/nppa/command"
$headers = @{ "Token" = "plc12345" }

$startTime = [timespan]::Zero
$totalTime = [timespan]::Zero
$isFirst200 = $false


while ($true) {
    try {
        $response = Invoke-WebRequest -Uri $url -Method Get -Headers $headers -ErrorAction Stop

        if ($response.StatusCode -eq 200) {
            if (-not $isFirst200) {
                $isFirst200 = $true
                $startTime = [datetime]::Now
                Write-Host "Foud a command at endpoint"
            }
        } elseif ($response.StatusCode -eq 404) {
            if ($isFirst200) {
                $totalTime = [datetime]::Now - $startTime
                Write-Host "Total command availablity time: $totalTime"
            } else {
                Write-Host "command wasn found"
            }
            break
        }
    } catch {
        Write-Host "request error: $_"
        if($isFirst200){
            $totalTime = [datetime]::Now - $startTime
            Write-Host "Total command availablity time: $totalTime"
            break
        }
    }

    Start-Sleep -Milliseconds 100
}